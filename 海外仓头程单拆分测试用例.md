# 海外仓头程单拆分包装和播种逻辑测试用例

## 测试目标
验证海外仓头程单拆分后的包装和播种逻辑是否与中转仓逻辑保持一致，确保页面复用不会产生业务逻辑冲突。

## 测试范围

### 1. 海外仓单品任务（ASN_FIRST_SINGLE）
- **任务类型代码**: 23
- **包含APV类型**: SS（单品单件）、SM（单品多件）
- **复用页面**: 中转仓包装页面

### 2. 海外仓多品任务（ASN_FIRST_MULTIPLE）
- **任务类型代码**: 24
- **包含APV类型**: MM（多品多件）
- **复用页面**: 中转仓包装页面

### 3. 中转仓对比任务
- **TRANSFER_SINGLESINGLE**: 51（中转仓单品单件）
- **TRANSFER_SINGLEMULTIPLE**: 52（中转仓单品多件）
- **TRANSFER_MULTIPLEMULTIPLE**: 53（中转仓多品多件）

## 测试用例设计

### 测试用例1: 海外仓单品任务包装逻辑验证

#### 1.1 海外仓单品任务复用TRANSFER_SINGLESINGLE包装
**测试步骤**:
1. 创建海外仓单品任务（ASN_FIRST_SINGLE）
2. 模拟包装页面调用，传递pickType=TRANSFER_SINGLESINGLE
3. 验证查询条件设置
4. 检查返回的发货单列表

**关键验证点**:
```java
// 在 WhApvPackController.ssInit() 或相关方法中验证
// 当pickType为TRANSFER_SINGLESINGLE时，查询条件应该只包含SS类型的APV
ApvQueryCondition condition = new ApvQueryCondition();
condition.setApvType("SS"); // 只查询单品单件
condition.setTaskType(PickingTaskType.ASN_FIRST_SINGLE.intCode());
```

**预期结果**: 只返回SS类型的发货单，不包含SM类型

#### 1.2 海外仓单品任务复用TRANSFER_SINGLEMULTIPLE包装
**测试步骤**:
1. 创建海外仓单品任务（ASN_FIRST_SINGLE）
2. 模拟包装页面调用，传递pickType=TRANSFER_SINGLEMULTIPLE
3. 验证查询条件设置
4. 检查返回的发货单列表

**关键验证点**:
```java
// 验证查询条件只包含SM类型
condition.setApvType("SM"); // 只查询单品多件
```

**预期结果**: 只返回SM类型的发货单，不包含SS类型

### 测试用例2: 海外仓多品任务包装逻辑验证

#### 2.1 海外仓多品任务复用TRANSFER_MULTIPLEMULTIPLE包装
**测试步骤**:
1. 创建海外仓多品任务（ASN_FIRST_MULTIPLE）
2. 模拟包装页面调用，传递pickType=TRANSFER_MULTIPLEMULTIPLE
3. 验证查询条件设置
4. 检查返回的发货单列表

**关键验证点**:
```java
// 验证查询条件只包含MM类型
condition.setApvType("MM"); // 只查询多品多件
condition.setTaskType(PickingTaskType.ASN_FIRST_MULTIPLE.intCode());
```

**预期结果**: 只返回MM类型的发货单

### 测试用例3: 海外仓播种逻辑验证

#### 3.1 海外仓单品任务播种验证
**测试位置**: `WhApvGridScanController.scanBox()`

**测试步骤**:
1. 创建海外仓单品任务周转筐
2. 模拟扫描周转筐操作
3. 验证任务类型检查逻辑
4. 检查是否正确路由到海外仓播种页面

**关键验证点**:
```java
// 在 WhApvGridScanController.scanBox() 中的验证逻辑
boolean isAsn = whPickingTask.getIsAsn() != null
        && PickingTaskType.getTransferIntCode().contains(whPickingTask.getIsAsn());

if (isAsn || PickingTaskType.getJITIntCode().contains(whPickingTask.getTaskType())
        || PickingTaskType.getJitAsnIntCode().contains(whPickingTask.getTaskType())) {
    domain.setErrorMsg("中转仓拣货类型，请使用中转仓播种！");
    return GRID_SCAN_VIEW;
}
```

**预期结果**: 
- ASN_FIRST_SINGLE任务应该在海外仓播种页面处理
- 不应该被误判为中转仓任务

#### 3.2 海外仓多品任务播种验证
**测试步骤**:
1. 创建海外仓多品任务周转筐
2. 模拟扫描周转筐操作
3. 验证任务类型检查
4. 检查播种格子分配逻辑

**预期结果**: 
- ASN_FIRST_MULTIPLE任务正常进入播种流程
- 只允许MM类型发货单进行播种操作

### 测试用例4: 中转仓播种逻辑验证（对比基准）

#### 4.1 中转仓任务播种验证
**测试位置**: `TransferGridController.scanBox()`

**关键验证点**:
```java
// 验证ASN_FIRST_SINGLE不需要播种
if (PickingTaskType.ASN_FIRST_SINGLE.intCode().equals(whPickingTask.getTaskType())) {
    domain.setErrorMsg(PickingTaskType.ASN_FIRST_SINGLE.getName() + "拣货类型，不需要播种！");
    return GRID_SCAN_VIEW;
}

// 验证只处理中转仓相关任务
boolean isAsn = whPickingTask.getIsAsn() != null
        && PickingTaskType.getTransferIntCode().contains(whPickingTask.getIsAsn());
        
if (whPickingTask.getTaskType() == null || !isAsn
        && !PickingTaskType.getJITIntCode().contains(whPickingTask.getTaskType())
                && !PickingTaskType.getJitAsnIntCode().contains(whPickingTask.getTaskType())) {
    domain.setErrorMsg("扫描的非中转仓或者仓发任务 ！");
    return GRID_SCAN_VIEW;
}
```

### 测试用例5: APV类型过滤逻辑验证

#### 5.1 包装查询过滤测试
**测试文件**: `ApvPackServiceImpl.findWhApvByPriority()`

**验证点**:
- 不同pickType参数对应的APV类型过滤
- 优先级查询逻辑的正确性
- GPSR运单类型的处理

#### 5.2 播种APV类型验证
**验证APV类型与任务类型的匹配关系**:
- SS类型只能在单品任务中处理
- SM类型只能在单品任务中处理  
- MM类型只能在多品任务中处理

## 测试数据准备

### 1. 任务类型映射
```java
// 海外仓任务类型
ASN_FIRST_SINGLE("海外仓单品", "23")
ASN_FIRST_MULTIPLE("海外仓多品", "24")

// 中转仓任务类型  
TRANSFER_SINGLESINGLE("中转仓单品单件", "51")
TRANSFER_SINGLEMULTIPLE("中转仓单品多件", "52") 
TRANSFER_MULTIPLEMULTIPLE("中转仓多品多件", "53")
```

### 2. APV类型定义
```java
// APV类型
SS - 单品单件 (Single SKU Single piece)
SM - 单品多件 (Single SKU Multiple pieces)  
MM - 多品多件 (Multiple SKU Multiple pieces)
```

### 3. 测试场景矩阵

| 海外仓任务类型 | 复用中转仓类型 | 期望APV类型 | 验证重点 |
|-------------|-------------|-----------|---------|
| ASN_FIRST_SINGLE | TRANSFER_SINGLESINGLE | SS | 包装查询过滤 |
| ASN_FIRST_SINGLE | TRANSFER_SINGLEMULTIPLE | SM | 包装查询过滤 |
| ASN_FIRST_MULTIPLE | TRANSFER_MULTIPLEMULTIPLE | MM | 包装查询过滤 |
| ASN_FIRST_SINGLE | - | SS+SM | 播种类型验证 |
| ASN_FIRST_MULTIPLE | - | MM | 播种类型验证 |

## 执行测试计划

### 阶段1: 单元测试
1. 测试包装查询条件构建逻辑
2. 测试APV类型过滤方法
3. 测试任务类型验证逻辑

### 阶段2: 集成测试  
1. 测试完整的包装流程
2. 测试完整的播种流程
3. 验证页面跳转逻辑

### 阶段3: 对比测试
1. 海外仓 vs 中转仓包装逻辑对比
2. 海外仓 vs 中转仓播种逻辑对比
3. 边界条件和异常情况测试

### 阶段4: 回归测试
1. 验证现有功能不受影响
2. 测试各种APV类型组合
3. 性能影响评估

## 测试工具和方法

### 1. Mock测试
```java
// 模拟不同的pickType参数
@Test
public void testPackagingWithDifferentPickType() {
    // 模拟海外仓单品任务调用中转仓包装页面
    WhApvQueryCondition condition = new WhApvQueryCondition();
    condition.setPickType("TRANSFER_SINGLESINGLE");
    condition.setTaskType(PickingTaskType.ASN_FIRST_SINGLE.intCode());
    
    // 验证查询结果只包含SS类型APV
    List<WhApv> result = apvService.queryForPacking(condition);
    assertTrue(result.stream().allMatch(apv -> "SS".equals(apv.getApvType())));
}
```

### 2. 边界测试
- 测试空数据情况
- 测试混合APV类型情况
- 测试错误参数传递

### 3. 性能测试
- 查询性能对比
- 内存使用情况
- 并发处理能力

## 预期结果验证

### 成功标准
1. **包装逻辑一致性**: 海外仓任务复用中转仓包装页面时，APV类型过滤逻辑与纯中转仓任务完全一致
2. **播种逻辑正确性**: 海外仓任务的播种验证只允许对应类型的APV通过
3. **无业务冲突**: 页面复用不会导致数据查询错误或业务逻辑混乱
4. **向后兼容**: 现有中转仓功能不受影响

### 失败处理
1. 记录详细的测试失败信息
2. 分析根本原因
3. 提供修复建议
4. 重新执行相关测试用例

## 测试报告格式

每个测试用例执行后需要记录：
- 测试用例ID和名称
- 执行时间
- 测试结果（通过/失败）
- 实际结果vs预期结果
- 如有失败，记录详细错误信息
- 修复建议和后续行动项

## 风险和注意事项

1. **数据隔离**: 确保测试数据不影响生产环境
2. **并发处理**: 测试多用户同时操作的情况
3. **异常处理**: 验证各种异常情况的处理逻辑
4. **性能影响**: 评估新逻辑对系统性能的影响
5. **用户体验**: 确保页面复用不会影响用户操作体验