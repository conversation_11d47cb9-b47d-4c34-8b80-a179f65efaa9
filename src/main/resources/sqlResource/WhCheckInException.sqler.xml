<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhCheckInExceptionCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_check_in_exception
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:ids)]>
        <[AND in_id = :in_id]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND purchase_order_no IN (:purchaseOrderNos)]>
        <[AND tracking_number IN (:trackingNos)]>
        <[AND new_purchase_order_no = :new_purchase_order_no]>
        <[AND tracking_number = :tracking_number]>
        <[AND creation_date >= :from_create_date]>
        <[AND creation_date <= :to_create_date]>
        <[AND exception_type = :exception_type]>
        <[AND exception_type IN (:exceptionTypes)]>
        <[AND status = :status]>
        <[AND status IN (:statusList)]>
        <[AND handle_way = :handle_way]>
        <[AND handle_way IN (:handleWays)]>
        <[AND exception_user = :exception_user]>
        <[AND box_no = :box_no]>
        <[AND sku = :sku]>
        <[AND image = :image]>
        <[AND quantity = :quantity]>
        <[AND purchase_user = :purchase_user]>
        <[AND confirm_quantity = :confirm_quantity]>
        <[AND exception_comment = :exception_comment]>
        <[AND last_update_user = :last_update_user]>
        <[AND last_update_date = :last_update_date]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND first_order_type = :first_order_type]>
        <[AND ex_times = :ex_times]>
        <[AND exception_form = :exception_form]>
        <[AND exception_form IN (:exceptionFroms)]>
        <[AND is_carry_product = :is_carry_product]>
        <[AND carry_quantity = :carry_quantity]>
        <[AND return_information_json = :return_information_json]>
        <[AND finish_date >= :from_finish_date]>
        <[AND finish_date <= :to_finish_date]>
        <[AND exception_handle_date >= :from_exception_handle_date]>
        <[AND exception_handle_date <= :to_exception_handle_date]>
        <[AND purchase_handle_date >= :from_purchase_handle_date]>
        <[AND purchase_handle_date <= :to_purchase_handle_date]>
        <[AND purchase_user IN (:purchaseUserList)]>
        <[AND location_number = :location_number]>
        <[AND receive_box_no = :receive_box_no]>
        <[AND wait_check_in_date >= :from_wait_check_in_date]>
        <[AND wait_check_in_date <= :to_wait_check_in_date]>
        <[AND doing_check_in_date >= :from_doing_check_in_date]>
        <[AND doing_check_in_date <= :to_doing_check_in_date]>
        <[AND exception_handled = :exception_handled]>
        <[AND start_qc_handle_date >= :from_start_qc_handle_date]>
        <[AND start_qc_handle_date <= :to_start_qc_handle_date]>
        <[AND completed_qc_handle_date >= :from_completed_qc_handle_date]>
        <[AND completed_qc_handle_date <= :to_completed_qc_handle_date]>
        <[:orderBy]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryNewWhCheckInExceptionCount" >
    <content >
      <![CDATA[
        SELECT COUNT(DISTINCT wce.id)
        FROM wh_check_in_exception wce
        inner join wh_check_in_exce_batch wcb on wce.id = wcb.exce_id
        LEFT JOIN wh_purchase_order p ON wce.purchase_order_no = p.purchase_order_no
        <[:joinSpecialType]>
        WHERE 1 = 1
        <[AND wce.id = :id]>
        <[AND wce.id IN (:ids)]>
        <[AND wce.in_id = :in_id]>
        <[AND wce.purchase_order_no = :purchase_order_no]>
        <[AND wce.purchase_order_no IN (:purchaseOrderNos)]>
        <[AND wce.tracking_number IN (:trackingNos)]>
        <[AND wce.new_purchase_order_no = :new_purchase_order_no]>
        <[AND wce.new_purchase_order_no IN (:newpurchaseOrderNos)]>
        <[AND wce.tracking_number = :tracking_number]>
        <[AND wce.creation_date >= :from_create_date]>
        <[AND wce.creation_date <= :to_create_date]>
        <[AND wce.exception_type = :exception_type]>
        <[AND wce.exception_type IN (:exceptionTypes)]>
        <[AND wce.status = :status]>
        <[AND wce.status IN (:statusList)]>
        <[AND wce.handle_way = :handle_way]>
        <[AND wce.handle_way IN (:handleWays)]>
        <[AND wce.exception_user = :exception_user]>
        <[AND wce.box_no = :box_no]>
        <[AND wce.sku = :sku]>
        <[AND wce.sku in (select sku from wh_sku_extend where main_sku = :spu)]>
        <[AND wce.sku in (select sku from wh_sku_extend where main_sku in (:spuList))]>
        <[AND wce.image = :image]>
        <[AND wce.quantity = :quantity]>
        <[AND wce.purchase_user = :purchase_user]>
        <[AND wce.discarded_user = :discarded_user]>
        <[AND wce.created_by = :created_by]>
        <[AND wce.finish_user = :finish_user]>
        <[AND wce.confirm_quantity = :confirm_quantity]>
        <[AND wce.exception_comment = :exception_comment]>
        <[AND wce.last_update_user = :last_update_user]>
        <[AND wce.last_update_date = :last_update_date]>
        <[AND wce.warehouse_id = :warehouse_id]>
        <[AND wce.first_order_type = :first_order_type]>
        <[AND wce.ex_times = :ex_times]>
        <[AND wce.exception_form = :exception_form]>
        <[AND wce.exception_form IN (:exceptionFroms)]>
        <[AND wce.is_carry_product = :is_carry_product]>
        <[AND wce.carry_quantity = :carry_quantity]>
        <[AND wce.return_information_json = :return_information_json]>
        <[AND wce.finish_date >= :from_finish_date]>
        <[AND wce.finish_date <= :to_finish_date]>
        <[AND wce.exception_handle_date >= :from_exception_handle_date]>
        <[AND wce.exception_handle_date <= :to_exception_handle_date]>
        <[AND wce.start_qc_handle_date >= :from_start_qc_handle_date]>
        <[AND wce.start_qc_handle_date <= :to_start_qc_handle_date]>
        <[AND wce.completed_qc_handle_date >= :from_completed_qc_handle_date]>
        <[AND wce.completed_qc_handle_date <= :to_completed_qc_handle_date]>
        <[AND wce.purchase_handle_date >= :from_purchase_handle_date]>
        <[AND wce.purchase_handle_date <= :to_purchase_handle_date]>
        <[AND wce.purchase_user IN (:purchaseUserList)]>
        <[AND wce.location_number = :location_number]>
        <[AND wce.receive_box_no = :receive_box_no]>
        <[AND wce.id in (select exce_id from wh_check_in_exce_batch where batch_no = :batchNo)]>
        <[AND wce.wait_check_in_date >= :from_wait_check_in_date]>
        <[AND wce.wait_check_in_date <= :to_wait_check_in_date]>
        <[AND wce.doing_check_in_date >= :from_doing_check_in_date]>
        <[AND wce.doing_check_in_date <= :to_doing_check_in_date]>
        <[AND wce.exception_handled = :exception_handled]>
        <[AND FIND_IN_SET(:orderFlag,p.flags)]>
         <[:ORDER_FLAG_SQL]>
        <[:TAG_FILTER_SQL]>
        <[:MARK_STATUS_FILTER_SQL]>
        <[AND wce.mark_reason = :mark_reason]>
        <[AND wce.mark_time >= :from_mark_time]>
        <[AND wce.mark_time <= :to_mark_time]>
        <[AND wce.mark_user_id = :mark_user_id]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCheckInExceptionList" >
    <content >
      <![CDATA[
        SELECT id, in_id, purchase_order_no, new_purchase_order_no, tracking_number, created_by, creation_date, exception_type, status,
        handle_way, exception_user, box_no, sku, image, quantity, purchase_user, confirm_quantity,handled_quantity, exception_comment, last_update_user, last_update_date,
        warehouse_id, exception_form, is_carry_product, carry_quantity, return_information_json, finish_date, discarded_date, discarded_user,doing_check_in_date,
        exception_handle_date, purchase_handle_date, finish_user, receive_box_no, check_in_user, first_order_type, ex_times, location_number, wait_check_in_date
        ,(SELECT COUNT(*) FROM wh_check_in_exception_handle WHERE exception_id = wh_check_in_exception.id AND `status` IN (7,12)) AS wmsHandleTimes,exception_handled,tags
        ,(SELECT flags FROM wh_purchase_order WHERE wh_purchase_order.purchase_order_no = wh_check_in_exception.purchase_order_no LIMIT 1) AS 'flags'
        ,next_generation_exception_ids,mark_reason,mark_time,mark_user_id
        FROM wh_check_in_exception
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:ids)]>
        <[AND in_id = :in_id]>
        <[AND in_id IN (:inIds)]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND purchase_order_no IN (:purchaseOrderNos)]>
        <[AND tracking_number IN (:trackingNos)]>
        <[AND new_purchase_order_no = :new_purchase_order_no]>
        <[AND tracking_number = :tracking_number]>
        <[AND creation_date >= :from_create_date]>
        <[AND creation_date <= :to_create_date]>
        <[AND exception_type = :exception_type]>
        <[AND exception_type IN (:exceptionTypes)]>
        <[AND status = :status]>
        <[AND status IN (:statusList)]>
        <[AND handle_way = :handle_way]>
        <[AND handle_way IN (:handleWays)]>
        <[AND exception_user = :exception_user]>
        <[AND box_no = :box_no]>
        <[AND sku = :sku]>
        <[AND image = :image]>
        <[AND quantity = :quantity]>
        <[AND purchase_user = :purchase_user]>
        <[AND confirm_quantity = :confirm_quantity]>
        <[AND exception_comment = :exception_comment]>
        <[AND last_update_user = :last_update_user]>
        <[AND last_update_date = :last_update_date]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND first_order_type = :first_order_type]>
        <[AND ex_times = :ex_times]>
        <[AND exception_form = :exception_form]>
        <[AND exception_form IN (:exceptionFroms)]>
        <[AND is_carry_product = :is_carry_product]>
        <[AND carry_quantity = :carry_quantity]>
        <[AND return_information_json = :return_information_json]>
        <[AND finish_date >= :from_finish_date]>
        <[AND finish_date <= :to_finish_date]>
        <[AND exception_handle_date >= :from_exception_handle_date]>
        <[AND exception_handle_date <= :to_exception_handle_date]>
        <[AND purchase_handle_date >= :from_purchase_handle_date]>
        <[AND purchase_handle_date <= :to_purchase_handle_date]>
        <[AND purchase_user IN (:purchaseUserList)]>
        <[AND location_number = :location_number]>
        <[AND receive_box_no = :receive_box_no]>
        <[AND wait_check_in_date >= :from_wait_check_in_date]>
        <[AND wait_check_in_date <= :to_wait_check_in_date]>
        <[AND doing_check_in_date >= :from_doing_check_in_date]>
        <[AND doing_check_in_date <= :to_doing_check_in_date]>
        <[AND exception_handled = :exception_handled]>
        <[AND start_qc_handle_date >= :from_start_qc_handle_date]>
        <[AND start_qc_handle_date <= :to_start_qc_handle_date]>
        <[AND completed_qc_handle_date >= :from_completed_qc_handle_date]>
        <[AND completed_qc_handle_date <= :to_completed_qc_handle_date]>
        <[AND sku IN (:skuList)]>
        <[AND first_order_type NOT IN (:exclusiveFirstOrderTypeList)]>
        <[:MARK_STATUS_FILTER_SQL]>
        <[AND mark_reason = :mark_reason]>
        <[AND mark_time >= :from_mark_time]>
        <[AND mark_time <= :to_mark_time]>
        <[AND mark_user_id = :mark_user_id]>
        <[:orderBy]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryNewWhCheckInExceptionList" >
    <content >
      <![CDATA[
        SELECT wce.id, wce.in_id, wce.purchase_order_no, wce.new_purchase_order_no, wce.tracking_number
	    , wce.created_by, wce.creation_date, wce.exception_type, wce.status, wce.handle_way, wce.location_number, wce.wait_check_in_date
	    , wce.exception_user, wce.box_no, wce.sku, wce.image, wce.quantity, wce.first_order_type, wce.ex_times
	    , wce.purchase_user, wce.confirm_quantity, wce.handled_quantity, wce.exception_comment, wce.last_update_user, wce.last_update_date
	    , wce.warehouse_id, wce.exception_form, wce.is_carry_product, wce.carry_quantity, wce.return_information_json
	    , wce.finish_date, wce.discarded_date, wce.discarded_user, wce.exception_handle_date, wce.purchase_handle_date
	    , wce.finish_user, wce.receive_box_no, wce.check_in_user,wce.doing_check_in_date,wcb.batch_no,wce.exception_handled
	    , p.flags AS 'flags',wce.next_generation_exception_ids,wce.mark_reason,wce.mark_time,wce.mark_user_id
	    ,(SELECT COUNT(*) FROM wh_check_in_exception_handle WHERE exception_id = wce.id AND `status` IN (7,12)) AS wmsHandleTimes
	    <[:QUERY_LOCATION_STOCK]>
	    <[:QUERY_TAGS_COLUMN]>
        FROM wh_check_in_exception wce
        inner join wh_check_in_exce_batch wcb on wce.id = wcb.exce_id
        LEFT JOIN wh_purchase_order p ON wce.purchase_order_no = p.purchase_order_no
        <[:joinSpecialType]>
        WHERE 1 = 1
        <[AND wce.id = :id]>
        <[AND wce.id IN (:ids)]>
        <[AND wce.in_id = :in_id]>
        <[AND wce.purchase_order_no = :purchase_order_no]>
        <[AND wce.purchase_order_no IN (:purchaseOrderNos)]>
        <[AND wce.tracking_number IN (:trackingNos)]>
        <[AND wce.new_purchase_order_no = :new_purchase_order_no]>
        <[AND wce.tracking_number = :tracking_number]>
        <[AND wce.creation_date >= :from_create_date]>
        <[AND wce.creation_date <= :to_create_date]>
        <[AND wce.exception_type = :exception_type]>
        <[AND wce.exception_type IN (:exceptionTypes)]>
        <[AND wce.status = :status]>
        <[AND wce.status IN (:statusList)]>
        <[AND wce.handle_way = :handle_way]>
        <[AND wce.handle_way IN (:handleWays)]>
        <[AND wce.exception_user = :exception_user]>
        <[AND wce.created_by = :created_by]>
        <[AND wce.box_no = :box_no]>
        <[AND wce.sku = :sku]>
        <[AND wce.sku in (select sku from wh_sku_extend where main_sku = :spu)]>
        <[AND wce.sku in (select sku from wh_sku_extend where main_sku in (:spuList))]>
        <[AND wce.image = :image]>
        <[AND wce.quantity = :quantity]>
        <[AND wce.purchase_user = :purchase_user]>
        <[AND wce.purchase_user IN (:purchaseUserList)]>
        <[AND wce.discarded_user = :discarded_user]>
        <[AND wce.finish_user = :finish_user]>
        <[AND wce.confirm_quantity = :confirm_quantity]>
        <[AND wce.exception_comment = :exception_comment]>
        <[AND wce.last_update_user = :last_update_user]>
        <[AND wce.last_update_date = :last_update_date]>
        <[AND wce.warehouse_id = :warehouse_id]>
        <[AND wce.first_order_type = :first_order_type]>
        <[AND wce.ex_times = :ex_times]>
        <[AND wce.exception_form = :exception_form]>
        <[AND wce.exception_form IN (:exceptionFroms)]>
        <[AND wce.is_carry_product = :is_carry_product]>
        <[AND wce.carry_quantity = :carry_quantity]>
        <[AND wce.return_information_json = :return_information_json]>
        <[AND wce.finish_date >= :from_finish_date]>
        <[AND wce.finish_date <= :to_finish_date]>
        <[AND wce.exception_handle_date >= :from_exception_handle_date]>
        <[AND wce.exception_handle_date <= :to_exception_handle_date]>
        <[AND wce.start_qc_handle_date >= :from_start_qc_handle_date]>
        <[AND wce.start_qc_handle_date <= :to_start_qc_handle_date]>
        <[AND wce.completed_qc_handle_date >= :from_completed_qc_handle_date]>
        <[AND wce.completed_qc_handle_date <= :to_completed_qc_handle_date]>
        <[AND wce.purchase_handle_date >= :from_purchase_handle_date]>
        <[AND wce.purchase_handle_date <= :to_purchase_handle_date]>
        <[AND wce.id in (select exce_id from wh_check_in_exce_batch where batch_no = :batchNo)]>
        <[AND wce.location_number = :location_number]>
        <[AND wce.receive_box_no = :receive_box_no]>
        <[AND wce.wait_check_in_date >= :from_wait_check_in_date]>
        <[AND wce.wait_check_in_date <= :to_wait_check_in_date]>
        <[AND wce.doing_check_in_date >= :from_doing_check_in_date]>
        <[AND wce.doing_check_in_date <= :to_doing_check_in_date]>
        <[AND wce.exception_handled = :exception_handled]>
        <[AND FIND_IN_SET(:orderFlag,p.flags)]>
        <[:ORDER_FLAG_SQL]>
        <[:TAG_FILTER_SQL]>
        <[:MARK_STATUS_FILTER_SQL]>
        <[AND wce.mark_reason = :mark_reason]>
        <[AND wce.mark_time >= :from_mark_time]>
        <[AND wce.mark_time <= :to_mark_time]>
        <[AND wce.mark_user_id = :mark_user_id]>
        order by wce.creation_date desc
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCheckInExceptionByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, in_id, purchase_order_no, new_purchase_order_no, tracking_number, created_by, creation_date, exception_type, status,
        handle_way, exception_user, box_no, sku, image, quantity, purchase_user, confirm_quantity,handled_quantity, exception_comment, last_update_user, last_update_date,
        warehouse_id, exception_form, is_carry_product, carry_quantity, return_information_json, finish_date, discarded_date, discarded_user,doing_check_in_date,
        exception_handle_date, purchase_handle_date, finish_user, receive_box_no, check_in_user, first_order_type, ex_times, location_number, wait_check_in_date
        ,(SELECT COUNT(*) FROM wh_check_in_exception_handle WHERE exception_id = wh_check_in_exception.id AND `status` IN (7,12)) AS wmsHandleTimes,exception_handled,tags
        ,(SELECT flags FROM wh_purchase_order WHERE wh_purchase_order.purchase_order_no = wh_check_in_exception.purchase_order_no LIMIT 1) AS 'flags',
        next_generation_exception_ids,mark_reason,mark_time,mark_user_id
        FROM wh_check_in_exception
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryWhCheckInException" >
    <content >
      <![CDATA[
        SELECT id, in_id, purchase_order_no, new_purchase_order_no, tracking_number, created_by, creation_date, exception_type, status,
        handle_way, exception_user, box_no, sku, image, quantity, purchase_user, confirm_quantity,handled_quantity, exception_comment, last_update_user, last_update_date,
        warehouse_id, exception_form, is_carry_product, carry_quantity, return_information_json, finish_date, discarded_date, discarded_user, doing_check_in_date,
        exception_handle_date, purchase_handle_date, finish_user, receive_box_no, check_in_user, first_order_type, ex_times, location_number, wait_check_in_date
        ,(SELECT COUNT(*) FROM wh_check_in_exception_handle WHERE exception_id = wh_check_in_exception.id AND `status` IN (7,12)) AS wmsHandleTimes,exception_handled,tags
        ,(SELECT flags FROM wh_purchase_order WHERE wh_purchase_order.purchase_order_no = wh_check_in_exception.purchase_order_no LIMIT 1) AS 'flags',
        next_generation_exception_ids,mark_reason,mark_time,mark_user_id
        FROM wh_check_in_exception
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND id IN (:ids)]>
        <[AND in_id = :in_id]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND new_purchase_order_no = :new_purchase_order_no]>
        <[AND tracking_number = :tracking_number]>
        <[AND creation_date = :creation_date]>
        <[AND exception_type = :exception_type]>
        <[AND status = :status]>
        <[AND handle_way = :handle_way]>
        <[AND exception_user = :exception_user]>
        <[AND box_no = :box_no]>
        <[AND sku = :sku]>
        <[AND image = :image]>
        <[AND quantity = :quantity]>
        <[AND purchase_user = :purchase_user]>
        <[AND purchase_user IN (:purchaseUserList)]>
        <[AND confirm_quantity = :confirm_quantity]>
        <[AND exception_comment = :exception_comment]>
        <[AND last_update_user = :last_update_user]>
        <[AND last_update_date = :last_update_date]>
        <[AND warehouse_id = :warehouse_id]>
        <[AND first_order_type = :first_order_type]>
        <[AND ex_times = :ex_times]>
        <[AND exception_form = :exception_form]>
        <[AND is_carry_product = :is_carry_product]>
        <[AND carry_quantity = :carry_quantity]>
        <[AND return_information_json = :return_information_json]>
        <[AND location_number = :location_number]>
        <[AND exception_handled = :exception_handled]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhCheckInException" >
    <content >
      <![CDATA[
        INSERT INTO wh_check_in_exception (in_id, purchase_order_no, new_purchase_order_no, tracking_number, created_by, check_in_user,
          creation_date, exception_type, status, handle_way, exception_user, box_no, sku, image, quantity, purchase_user, receive_box_no,
          confirm_quantity, exception_comment, last_update_user, last_update_date, warehouse_id, exception_form, is_carry_product,
          carry_quantity, return_information_json, finish_date, discarded_date, discarded_user, exception_handle_date, purchase_handle_date, finish_user,
          first_order_type, ex_times,location_number,exception_handled,tags,next_generation_exception_ids,mark_reason,mark_time,mark_user_id)
        VALUES (:in_id, :purchase_order_no, :new_purchase_order_no, :tracking_number, :created_by, :check_in_user, :creation_date, :exception_type,
          :status, :handle_way, :exception_user, :box_no, :sku, :image, :quantity, :purchase_user, :receive_box_no, :confirm_quantity, :exception_comment,
          :last_update_user, :last_update_date, :warehouse_id, :exception_form, :is_carry_product, :carry_quantity, :return_information_json,
          :finish_date, :discarded_date, :discarded_user, :exception_handle_date, :purchase_handle_date, :finish_user, :first_order_type, :ex_times, :location_number,
          :exception_handled,:tags,:next_generation_exception_ids,:mark_reason,:mark_time,:mark_user_id)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhCheckInExceptionByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_check_in_exception
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhCheckInExceptionByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_check_in_exception
        SET <[in_id = :in_id,]>
          <[purchase_order_no = :purchase_order_no,]>
          <[new_purchase_order_no = :new_purchase_order_no,]>
          <[tracking_number = :tracking_number,]>
          <[created_by = :created_by,]>
          <[creation_date = :creation_date,]>
          <[exception_type = :exception_type,]>
          <[status = :status,]>
          <[handle_way = :handle_way,]>
          <[exception_user = :exception_user,]>
          <[box_no = :box_no,]>
          <[sku = :sku,]>
          <[image = :image,]>
          <[quantity = :quantity,]>
          <[purchase_user = :purchase_user,]>
          <[confirm_quantity = :confirm_quantity,]>
          <[handled_quantity = :handled_quantity,]>
          <[exception_comment = :exception_comment,]>
          <[last_update_user = :last_update_user,]>
          <[last_update_date = :last_update_date,]>
          <[warehouse_id = :warehouse_id,]>
          <[exception_form = :exception_form,]>
          <[is_carry_product = :is_carry_product,]>
          <[carry_quantity = :carry_quantity,]>
          <[return_information_json = :return_information_json,]>
          <[finish_date = :finish_date,]>
          <[finish_user = :finish_user,]>
          <[discarded_date = :discarded_date,]>
          <[discarded_user = :discarded_user,]>
          <[exception_handle_date = :exception_handle_date,]>
          <[purchase_handle_date = :purchase_handle_date,]>
          <[start_qc_handle_date = :start_qc_handle_date,]>
          <[completed_qc_handle_date = :completed_qc_handle_date,]>
          <[first_order_type = :first_order_type,]>
          <[ex_times = :ex_times,]>
          <[location_number = :location_number,]>
          <[wait_check_in_date = :wait_check_in_date,]>
          <[doing_check_in_date = :doing_check_in_date,]>
          <[exception_handled = :exception_handled,]>
          <[tags = :tags,]>
          <[abandon_reason = :abandon_reason,]>
          <[next_generation_exception_ids = :next_generation_exception_ids,]>
          <[mark_reason = :mark_reason,]>
          <[mark_time = :mark_time,]>
          <[mark_user_id = :mark_user_id,]>
        receive_box_no = :receive_box_no,
        id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  <sql datasource="dataSource" id="queryAllHistoryCheckInExceptionCountList">
    <content>
      <![CDATA[
        SELECT
        sku
        ,COUNT(DISTINCT id,if((FIND_IN_SET('13',exception_type) >= 1),TRUE,NULL)) AS size_error_count
        ,COUNT(DISTINCT id,if((FIND_IN_SET('14',exception_type) >= 1),TRUE,NULL)) AS color_error_count
        ,COUNT(DISTINCT id,if((FIND_IN_SET('15',exception_type) >= 1),TRUE,NULL)) AS damage_count
        ,COUNT(DISTINCT id,if((FIND_IN_SET('16',exception_type) >= 1),TRUE,NULL)) AS less_fitting_count
        ,COUNT(DISTINCT id,if((FIND_IN_SET('19',exception_type) >= 1),TRUE,NULL)) AS description_error_count
        ,COUNT(DISTINCT id,if((FIND_IN_SET('21',exception_type) >= 1),TRUE,NULL)) AS function_error_count
        ,COUNT(DISTINCT id,if((FIND_IN_SET('24',exception_type) >= 1),TRUE,NULL)) AS img_not_match_count
        ,COUNT(DISTINCT id,if((FIND_IN_SET('11',exception_type) >= 1),TRUE,NULL)) AS less_quantity_count
        ,COUNT(DISTINCT id,if((FIND_IN_SET('12',exception_type) >= 1),TRUE,NULL)) AS excess_quantity_count
        ,COUNT(DISTINCT id,if((FIND_IN_SET('17',exception_type) >= 1),TRUE,NULL)) AS excess_fitting_count
        ,COUNT(DISTINCT id,if((FIND_IN_SET('18',exception_type) >= 1),TRUE,NULL)) AS less_sku_count
        ,COUNT(DISTINCT id,if((FIND_IN_SET('22',exception_type) >= 1),TRUE,NULL)) AS up_error_count
        ,COUNT(DISTINCT id,if((FIND_IN_SET('23',exception_type) >= 1),TRUE,NULL)) AS no_purchase_order_count
        ,COUNT(DISTINCT id,if((FIND_IN_SET('25',exception_type) >= 1),TRUE,NULL)) AS others_count
        ,COUNT(DISTINCT id,if((FIND_IN_SET('31',exception_type) >= 1),TRUE,NULL)) AS sharp_count
        FROM wh_check_in_exception
        WHERE status NOT IN(1,11)
        AND sku IS NOT NULL AND sku != ''
        GROUP BY sku
      ]]>
    </content>
  </sql>

  <!--根据SKU,异常类型查询最近三条排除草稿、废弃异常记录-->
  <sql datasource="dataSource" id="queryRecentThreeExceptionsByExceptionType">
    <content>
      <![CDATA[
        SELECT e.id,e.creation_date,e.exception_type,e.`status`,e.handle_way,e.image
        ,(SELECT h.handle_comment FROM wh_check_in_exception_handle h WHERE h.exception_id = e.id AND h.`status` = 5 ORDER BY h.creation_date ASC LIMIT 1) AS firstEditHandleComment
        ,(SELECT h.handle_comment FROM wh_check_in_exception_handle h WHERE h.exception_id = e.id AND h.`status` = 7 ORDER BY h.creation_date DESC LIMIT 1) AS lastPurchaseHandleComment
        FROM wh_check_in_exception e
        WHERE e.`status` NOT IN (1,11)
        <[AND e.sku = :sku]>
        <[AND e.purchase_order_no = :purchase_order_no]>
        <[AND e.tracking_number = :tracking_number]>
        <[AND FIND_IN_SET(:exception_type,e.exception_type)]>
        ORDER BY e.creation_date DESC LIMIT 3
      ]]>
    </content>
  </sql>

  <!--根据异常类型查询最近三条已完成异常记录-->
  <sql datasource="dataSource" id="queryRecentFinishedExceptionsByExceptionType">
    <content>
      <![CDATA[
        (SELECT e.id,e.creation_date,e.exception_type,e.`status`,e.handle_way,e.image
        ,(SELECT h.handle_comment FROM wh_check_in_exception_handle h WHERE h.exception_id = e.id AND h.`status` = 5 ORDER BY h.creation_date ASC LIMIT 1) AS firstEditHandleComment
        ,(SELECT h.handle_comment FROM wh_check_in_exception_handle h WHERE h.exception_id = e.id AND h.`status` = 7 ORDER BY h.creation_date DESC LIMIT 1) AS lastPurchaseHandleComment
        FROM wh_check_in_exception e
        WHERE e.`status` = 9
        <[AND e.exception_type = :exception_type]>
        <[AND e.sku LIKE :sku]>
        ORDER BY e.creation_date DESC LIMIT 3)
        <[:UNION_SQL]>
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="batchUpdateLocation">
    <content>
      <![CDATA[
       UPDATE wh_check_in_exception
       SET location_number = :location_number
        WHERE 1 = 1
        AND id IN (:idList)
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="batchCancelMarkWhCheckInException">
    <content>
      <![CDATA[
        UPDATE wh_check_in_exception
        SET mark_reason = NULL,
            mark_time = NULL,
            mark_user_id = NULL,
            last_update_user = :last_update_user,
            last_update_date = :last_update_date
        WHERE 1 = 1
        AND id IN (:idList)
      ]]>
    </content>
  </sql>

  <sql datasource="dataSource" id="queryDistinctMarkReasons">
    <content>
      <![CDATA[
        SELECT DISTINCT mark_reason
        FROM wh_check_in_exception
        WHERE mark_reason IS NOT NULL 
        AND mark_reason != ''
      ]]>
    </content>
  </sql>
</sqlmap>