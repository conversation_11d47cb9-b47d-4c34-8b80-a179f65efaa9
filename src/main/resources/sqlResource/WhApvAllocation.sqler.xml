<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap>
	<sql datasource="dataSource" id="queryApvAllocationSkuList">
		<content>
      <![CDATA[
        SELECT 
        	sku AS 'sku',
        	warehouse_id AS 'warehouseId',
        	IFNULL(location_number, '') AS 'locationNumber',
        	name AS 'skuName',
        	IFNULL((SELECT SUM(surplus_quantity) FROM wh_stock stock WHERE stock.sku=whSku.sku), 0) AS 'surplusQuantity',
        	(SELECT COUNT(allocation_item.sku) FROM wh_apv_allocation_item allocation_item LEFT JOIN wh_apv_allocation allocation ON allocation.allocation_id=allocation_item.allocation_id WHERE allocation_item.sku=whSku.sku AND allocation.allocation_type=1 AND allocation.allocation_status IN (0,1,2,3,4,5,6,7)) AS 'allocationNum',
        	(SELECT COUNT(DISTINCT returnItem.return_id) FROM wh_return_item returnItem LEFT JOIN wh_return whReturn ON returnItem.return_id=whReturn.id WHERE returnItem.sku=whSku.sku AND whReturn.status IN (1,2) AND whReturn.type=1) AS 'waitReturnQuantity',
			(SELECT COUNT(DISTINCT returnItem.return_id) FROM wh_abroad_return_item returnItem LEFT JOIN wh_abroad_return whReturn ON returnItem.return_id=whReturn.id WHERE returnItem.sku=whSku.sku AND whReturn.status IN (1,3)) AS 'waitAbroadReturnQuantity',
			(SELECT COUNT(DISTINCT returnItem.return_id) FROM wh_return_item returnItem LEFT JOIN wh_return whReturn ON returnItem.return_id=whReturn.id WHERE returnItem.sku=whSku.sku AND whReturn.status IN (1,2) AND whReturn.type=2) AS 'waitAllocationQuantity',
        	(SELECT COUNT(DISTINCT checkIn.in_id) FROM wh_check_in_item checkInItem LEFT JOIN wh_check_in checkIn ON checkInItem.in_id=checkIn.in_id WHERE checkInItem.sku=whSku.sku AND checkIn.status IN (3,7,9,11,12)) AS 'waitUpQuantity',
        	(SELECT COUNT(DISTINCT apvItem.apv_id) FROM wh_apv_item apvItem LEFT JOIN wh_apv apv ON apv.id=apvItem.apv_id WHERE apvItem.sku=whSku.sku AND apv.status IN (1,4,6,8,10,12,14) AND (apv.sign_payment IS NULL OR apv.sign_payment = false)) AS 'waitDeliveryQuantity'
        FROM 
        	wh_sku whSku
        WHERE 
        	1=1
        	<[AND whSku.sku IN (:sku_list)]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryApvAllocation">
		<content>
      <![CDATA[
        SELECT 
        	allocation.allocation_id, 
        	allocation.allocation_no, 
        	allocation.delivery_warehouse_id, 
        	allocation.dest_warehouse_id, 
        	allocation.allocation_type, 
        	allocation.transport_type,
        	allocation.freight, 
        	allocation.remark,
        	allocation.is_push,
        	allocation.allocation_status, 
        	allocation.create_by, 
        	allocation.create_time, 
        	allocation.audit_by,
        	allocation.audit_time,
        	allocation.confirm_by,
        	allocation.confirm_time,
        	allocation.update_by, 
        	allocation.update_time
        FROM 
        	wh_apv_allocation allocation
        WHERE 
        	1 = 1
	        <[AND allocation.allocation_id = :allocation.allocation_id]>
        	<[AND allocation.allocation_no = :allocation.allocation_no]>
        	<[AND allocation.delivery_warehouse_id = :allocation.delivery_warehouse_id]>
        	<[AND allocation.dest_warehouse_id = :allocation.dest_warehouse_id]>
        	<[AND allocation.allocation_type = :allocation.allocation_type]>
        	<[AND allocation.transport_type = :allocation.transport_type]>
        	<[AND allocation.freight = :allocation.freight]>
        	<[AND allocation.remark = :allocation.remark]>
        	<[AND allocation.allocation_status = :allocation.allocation_status]>
        	<[AND allocation.create_by = :allocation.create_by]>
        	<[AND allocation.create_time = :allocation.create_time]>
        	<[AND allocation.audit_by  = :allocation.audit_by,]>
        	<[AND allocation.audit_time = :allocation.audit_time,]>
        	<[AND allocation.confirm_by = :allocation.confirm_by,]>
        	<[AND allocation.confirm_time = :allocation.confirm_time,]>
        	<[AND allocation.update_by = :allocation.update_by]>
        	<[AND allocation.update_time = :allocation.update_time]>
        	<[AND allocation.is_push = :allocation.is_push]>
        	
        	<[AND allocation.create_time >= :create_start_time]>
        	<[AND allocation.create_time <= :create_end_time]>
        	<[AND allocation.allocation_id IN (:allocation_id_list)]>
        	<[AND allocation.allocation_no IN (:allocation_no_list)]>
        	<[AND allocation.allocation_status IN (:allocation_status_list)]>
        	<[AND allocation.allocation_id IN (SELECT allocation_item.allocation_id FROM wh_apv_allocation_item allocation_item WHERE allocation_item.allocation_id=allocation.allocation_id AND allocation_item.sku IN (:sku_list))]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryApvAllocationCount">
		<content>
	      <![CDATA[
	        SELECT 
	        	COUNT(1)
	        FROM 
	        	wh_apv_allocation allocation
	        WHERE 
	        	1 = 1
	        	AND allocation.allocation_type = 1
	        	<[AND allocation.allocation_id = :allocation.allocation_id]>
	        	<[AND allocation.allocation_no = :allocation.allocation_no]>
	        	<[AND allocation.delivery_warehouse_id = :allocation.delivery_warehouse_id]>
	        	<[AND allocation.dest_warehouse_id = :allocation.dest_warehouse_id]>
	        	<[AND allocation.allocation_type = :allocation.allocation_type]>
	        	<[AND allocation.transport_type = :allocation.transport_type]>
	        	<[AND allocation.freight = :allocation.freight]>
	        	<[AND allocation.remark = :allocation.remark]>
	        	<[AND allocation.allocation_status = :allocation.allocation_status]>
	        	<[AND allocation.create_by = :allocation.create_by]>
	        	<[AND allocation.create_time = :allocation.create_time]>
	        	<[AND allocation.audit_by  = :allocation.audit_by,]>
	        	<[AND allocation.audit_time = :allocation.audit_time,]>
	        	<[AND allocation.confirm_by = :allocation.confirm_by,]>
	        	<[AND allocation.confirm_time = :allocation.confirm_time,]>
	        	<[AND allocation.update_by = :allocation.update_by]>
	        	<[AND allocation.update_time = :allocation.update_time]>
	        	<[AND allocation.is_push = :allocation.is_push]>
	        	
	        	<[AND allocation.create_time >= :create_start_time]>
        		<[AND allocation.create_time <= :create_end_time]>
        		<[AND allocation.allocation_id IN (:allocation_id_list)]>
	        	<[AND allocation.allocation_no IN (:allocation_no_list)]>
	        	<[AND allocation.allocation_status IN (:allocation_status_list)]>
	        	<[AND allocation.allocation_id IN (SELECT allocation_item.allocation_id FROM wh_apv_allocation_item allocation_item WHERE allocation_item.allocation_id=allocation.allocation_id AND allocation_item.sku IN (:sku_list))]>
	      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryApvAllocationList">
		<content>
      <![CDATA[
        SELECT 
        	allocation.allocation_id, 
        	allocation.allocation_no, 
        	allocation.delivery_warehouse_id, 
        	allocation.dest_warehouse_id, 
        	allocation.allocation_type, 
        	allocation.transport_type, 
        	allocation.freight, 
        	allocation.remark,
        	allocation.is_push,
        	allocation.allocation_status, 
        	allocation.create_by, 
        	allocation.create_time, 
        	allocation.audit_by,
        	allocation.audit_time,
        	allocation.confirm_by,
        	allocation.confirm_time,
        	allocation.update_by, 
        	allocation.update_time,
        	COUNT(DISTINCT allocation_item.box_no) AS boxCount,
        	COUNT(DISTINCT allocation_item.sku) AS skuSpeciesCount,
        	SUM(allocation_item.allocation_num) AS skuPcsCount,
        	(SELECT COUNT(*) FROM wh_allocation_pick_task WHERE allocation_id = allocation.allocation_id) AS subTaskCount,
        	GROUP_CONCAT(DISTINCT allocation_item.delivery_method) AS deliveryMethodList,
        	GROUP_CONCAT(DISTINCT allocation_item.logistics_no) AS logisticsNoList
        FROM
        	wh_apv_allocation allocation
        	LEFT JOIN wh_apv_allocation_item allocation_item ON allocation.allocation_id=allocation_item.allocation_id
        WHERE 
        	1 = 1
        	AND allocation.allocation_type = 1
	        <[AND allocation.allocation_id = :allocation.allocation_id]>
        	<[AND allocation.allocation_no = :allocation.allocation_no]>
        	<[AND allocation.delivery_warehouse_id = :allocation.delivery_warehouse_id]>
        	<[AND allocation.dest_warehouse_id = :allocation.dest_warehouse_id]>
        	<[AND allocation.allocation_type = :allocation.allocation_type]>
        	<[AND allocation.transport_type = :allocation.transport_type]>
        	<[AND allocation.freight = :allocation.freight]>
        	<[AND allocation.remark = :allocation.remark]>
        	<[AND allocation.allocation_status = :allocation.allocation_status]>
        	<[AND allocation.create_by = :allocation.create_by]>
        	<[AND allocation.create_time = :allocation.create_time]>
        	<[AND allocation.audit_by  = :allocation.audit_by,]>
        	<[AND allocation.audit_time = :allocation.audit_time,]>
        	<[AND allocation.confirm_by = :allocation.confirm_by,]>
        	<[AND allocation.confirm_time = :allocation.confirm_time,]>
        	<[AND allocation.update_by = :allocation.update_by]>
        	<[AND allocation.update_time = :allocation.update_time]>
        	<[AND allocation.is_push = :allocation.is_push]>
        	
        	<[AND allocation.create_time >= :create_start_time]>
        	<[AND allocation.create_time <= :create_end_time]>
        	<[AND allocation.allocation_id IN (:allocation_id_list)]>
        	<[AND allocation.allocation_no IN (:allocation_no_list)]>
        	<[AND allocation.allocation_status IN (:allocation_status_list)]>
        	<[AND allocation.allocation_id IN (SELECT item.allocation_id FROM wh_apv_allocation_item item WHERE item.allocation_id=allocation.allocation_id AND item.sku IN (:sku_list))]>
        GROUP BY allocation.allocation_id
        ORDER BY allocation.create_time DESC
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryApvAllocationDetail">
		<content>
      <![CDATA[
        SELECT 
        	allocation.allocation_id, 
        	allocation.allocation_no, 
        	allocation.delivery_warehouse_id, 
        	allocation.dest_warehouse_id, 
        	allocation.allocation_type, 
        	allocation.transport_type, 
        	allocation.freight, 
        	allocation.remark,
        	allocation.is_push,
        	allocation.allocation_status, 
        	allocation.create_by, 
        	allocation.create_time, 
        	allocation.audit_by,
        	allocation.audit_time,
        	allocation.confirm_by,
        	allocation.confirm_time,
        	allocation.update_by, 
        	allocation.update_time,
        	
        	allocation_item.allocation_item_id,
			allocation_item.allocation_id,
			allocation_item.allocation_no,
			allocation_item.sku,
			allocation_item.allocation_num,
			allocation_item.stock_id,
			allocation_item.json_str,
			allocation_item.create_by,
			allocation_item.create_time,
			allocation_item.pick_status,
			allocation_item.pick_num,
			allocation_item.pick_by,
			allocation_item.pick_time,
			allocation_item.box_status,
			allocation_item.box_num,
			allocation_item.box_no,
			allocation_item.box_by,
			allocation_item.box_time,
			allocation_item.board_status,
			allocation_item.board_no,
			allocation_item.board_by,
			allocation_item.board_time,
			allocation_item.load_status,
			allocation_item.load_by,
			allocation_item.load_time,
			allocation_item.up_status,
			allocation_item.up_num,
			allocation_item.up_by,
			allocation_item.up_time,
			allocation_item.is_audit,
			allocation_item.history_pick_num,
			allocation_item.put_status,
			allocation_item.put_by,
			allocation_item.put_time,
			allocation_item.delivery_method,
			allocation_item.logistics_no,
			IFNULL(stock.surplus_quantity, 0) AS 'allocation_item.surplus_quantity',
			whsku.name AS 'allocation_item.sku_name',
			stock.location_number AS 'allocation_item.location_number',
			whsku.image_url AS 'allocation_item.image_url',
			
			whSku.sku AS 'whSku.sku',
			whSku.name AS 'whSku.name',
			whSku.warehouse_id AS 'whSku.warehouse_id',
			whSku.location_number AS 'whSku.location_number',
			whSku.weight AS 'whSku.weight',
			whSku.length AS 'whSku.length',
			whSku.width AS 'whSku.width',
			whSku.height  AS 'whSku.height'
        FROM 
        	wh_apv_allocation allocation 
        	LEFT JOIN wh_apv_allocation_item allocation_item ON allocation.allocation_id=allocation_item.allocation_id
        	LEFT JOIN wh_sku whsku ON whsku.sku=allocation_item.sku
        	LEFT JOIN wh_stock stock ON stock.sku=allocation_item.sku AND stock.id = allocation_item.stock_id
        WHERE 
        	1 = 1
        	AND allocation.allocation_type = 1
	        <[AND allocation.allocation_id = :allocation.allocation_id]>
        	<[AND allocation.allocation_no = :allocation.allocation_no]>
        	<[AND allocation.delivery_warehouse_id = :allocation.delivery_warehouse_id]>
        	<[AND allocation.dest_warehouse_id = :allocation.dest_warehouse_id]>
        	<[AND allocation.allocation_type = :allocation.allocation_type]>
        	<[AND allocation.transport_type = :allocation.transport_type]>
        	<[AND allocation.freight = :allocation.freight]>
        	<[AND allocation.remark = :allocation.remark]>
        	<[AND allocation.allocation_status = :allocation.allocation_status]>
        	<[AND allocation.create_by = :allocation.create_by]>
        	<[AND allocation.create_time = :allocation.create_time]>
        	<[AND allocation.audit_by  = :allocation.audit_by,]>
        	<[AND allocation.audit_time = :allocation.audit_time,]>
        	<[AND allocation.confirm_by = :allocation.confirm_by,]>
        	<[AND allocation.confirm_time = :allocation.confirm_time,]>
        	<[AND allocation.update_by = :allocation.update_by]>
        	<[AND allocation.update_time = :allocation.update_time]>
        	<[AND allocation.is_push = :allocation.is_push]>
        	
        	<[AND allocation_item.sku = :allocation_item.sku]>
        	<[AND allocation_item.box_no = :allocation_item.box_no]>
        	<[AND allocation_item.box_status = :allocation_item.box_status]>
        	<[AND allocation_item.board_no = :allocation_item.board_no]>
        	<[AND allocation_item.board_status = :allocation_item.board_status]>
        	<[AND allocation_item.sku IN (:sku_list)]>
        	<[AND allocation_item.sku IN (:skus)]>
        	<[AND allocation_item.allocation_item_id IN (:allocation_item_id_list)]>
        	<[AND allocation_item.put_status = :allocation_item.put_status]>
	        <[AND allocation_item.put_by = :allocation_item.put_by]>
	        <[AND allocation_item.put_time = :allocation_item.put_time]>
        	
        	<[AND allocation.create_time >= :create_start_time]>
        	<[AND allocation.create_time <= :create_end_time]>
        	<[AND allocation.allocation_id IN (:allocation_id_list)]>
        	<[AND allocation.allocation_no IN (:allocation_no_list)]>
        	<[AND allocation.allocation_status IN (:allocation_status_list)]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryApvAllocationDetailCount">
		<content>
	      <![CDATA[
	        SELECT 
	        	COUNT(1)
	        FROM 
	        	wh_apv_allocation allocation
	        	LEFT JOIN wh_apv_allocation_item allocation_item ON allocation.allocation_id=allocation_item.allocation_id
	        WHERE 
	        	1 = 1
	        	AND allocation.allocation_type = 1
	        	<[AND allocation.allocation_id = :allocation.allocation_id]>
	        	<[AND allocation.allocation_no = :allocation.allocation_no]>
	        	<[AND allocation.delivery_warehouse_id = :allocation.delivery_warehouse_id]>
	        	<[AND allocation.dest_warehouse_id = :allocation.dest_warehouse_id]>
	        	<[AND allocation.allocation_type = :allocation.allocation_type]>
	        	<[AND allocation.transport_type = :allocation.transport_type]>
	        	<[AND allocation.freight = :allocation.freight]>
	        	<[AND allocation.remark = :allocation.remark]>
	        	<[AND allocation.allocation_status = :allocation.allocation_status]>
	        	<[AND allocation.create_by = :allocation.create_by]>
	        	<[AND allocation.create_time = :allocation.create_time]>
	        	<[AND allocation.audit_by  = :allocation.audit_by,]>
	        	<[AND allocation.audit_time = :allocation.audit_time,]>
	        	<[AND allocation.confirm_by = :allocation.confirm_by,]>
	        	<[AND allocation.confirm_time = :allocation.confirm_time,]>
	        	<[AND allocation.update_by = :allocation.update_by]>
	        	<[AND allocation.update_time = :allocation.update_time]>
	        	<[AND allocation.is_push = :allocation.is_push]>
	        	
	        	<[AND allocation_item.sku = :allocation_item.sku]>
	        	<[AND allocation_item.box_no = :allocation_item.box_no]>
	        	<[AND allocation_item.box_status = :allocation_item.box_status]>
	        	<[AND allocation_item.board_no = :allocation_item.board_no]>
	        	<[AND allocation_item.board_status = :allocation_item.board_status]>
	        	<[AND allocation_item.sku IN (:sku_list)]>
	        	<[AND allocation_item.sku IN (:skus)]>
	        	<[AND allocation_item.allocation_item_id IN (:allocation_item_id_list)]>
	        	<[AND allocation_item.put_status = :allocation_item.put_status]>
		        <[AND allocation_item.put_by = :allocation_item.put_by]>
		        <[AND allocation_item.put_time = :allocation_item.put_time]>
	        	
	        	<[AND allocation.create_time >= :create_start_time]>
	        	<[AND allocation.create_time <= :create_end_time]>
	        	<[AND allocation.allocation_id IN (:allocation_id_list)]>
	        	<[AND allocation.allocation_no IN (:allocation_no_list)]>
	        	<[AND allocation.allocation_status IN (:allocation_status_list)]>
	      ]]>
		</content>
	</sql>

		<sql datasource="dataSource" id="queryApvAllocationDetailList">
		<content>
      <![CDATA[
        SELECT 
        	allocation.allocation_id, 
        	allocation.allocation_no, 
        	allocation.delivery_warehouse_id, 
        	allocation.dest_warehouse_id, 
        	allocation.allocation_type, 
        	allocation.transport_type, 
        	allocation.freight, 
        	allocation.remark,
        	allocation.is_push,
        	allocation.allocation_status, 
        	allocation.create_by, 
        	allocation.create_time, 
        	allocation.audit_by,
        	allocation.audit_time,
        	allocation.confirm_by,
        	allocation.confirm_time,
        	allocation.update_by, 
        	allocation.update_time,
        	
        	allocation_item.allocation_item_id,
			allocation_item.allocation_id,
			allocation_item.allocation_no,
			allocation_item.sku,
			allocation_item.allocation_num,
			allocation_item.stock_id,
			allocation_item.json_str,
			allocation_item.create_by,
			allocation_item.create_time,
			allocation_item.pick_status,
			allocation_item.pick_num,
			allocation_item.pick_by,
			allocation_item.pick_time,
			allocation_item.box_status,
			allocation_item.box_num,
			allocation_item.box_no,
			allocation_item.box_by,
			allocation_item.box_time,
			allocation_item.board_status,
			allocation_item.board_no,
			allocation_item.board_by,
			allocation_item.board_time,
			allocation_item.load_status,
			allocation_item.load_by,
			allocation_item.load_time,
			allocation_item.up_status,
			allocation_item.up_num,
			allocation_item.up_by,
			allocation_item.up_time,
			allocation_item.is_audit,
			allocation_item.history_pick_num,
			allocation_item.put_status,
			allocation_item.put_by,
			allocation_item.put_time,
			allocation_item.delivery_method,
			allocation_item.logistics_no,
			IFNULL(stock.surplus_quantity, 0) AS 'allocation_item.surplus_quantity',
			whsku.name AS 'allocation_item.sku_name',
			stock.location_number AS 'allocation_item.location_number',
			whsku.image_url AS 'allocation_item.image_url',
			
			whSku.sku AS 'whSku.sku',
			whSku.name AS 'whSku.name',
			whSku.warehouse_id AS 'whSku.warehouse_id',
			whSku.location_number AS 'whSku.location_number',
			whSku.weight AS 'whSku.weight',
			whSku.length AS 'whSku.length',
			whSku.width AS 'whSku.width',
			whSku.height  AS 'whSku.height'
        FROM 
        	wh_apv_allocation allocation 
        	LEFT JOIN wh_apv_allocation_item allocation_item ON allocation.allocation_id=allocation_item.allocation_id
        	LEFT JOIN wh_sku whsku ON whsku.sku=allocation_item.sku 
        	LEFT JOIN wh_stock stock ON stock.sku=allocation_item.sku AND stock.id = allocation_item.stock_id
        WHERE 
        	1 = 1
        	AND allocation.allocation_type = 1
	        <[AND allocation.allocation_id = :allocation.allocation_id]>
        	<[AND allocation.allocation_no = :allocation.allocation_no]>
        	<[AND allocation.delivery_warehouse_id = :allocation.delivery_warehouse_id]>
        	<[AND allocation.dest_warehouse_id = :allocation.dest_warehouse_id]>
        	<[AND allocation.allocation_type = :allocation.allocation_type]>
        	<[AND allocation.transport_type = :allocation.transport_type]>
        	<[AND allocation.freight = :allocation.freight]>
        	<[AND allocation.remark = :allocation.remark]>
        	<[AND allocation.allocation_status = :allocation.allocation_status]>
        	<[AND allocation.create_by = :allocation.create_by]>
        	<[AND allocation.create_time = :allocation.create_time]>
        	<[AND allocation.audit_by  = :allocation.audit_by,]>
        	<[AND allocation.audit_time = :allocation.audit_time,]>
        	<[AND allocation.confirm_by = :allocation.confirm_by,]>
        	<[AND allocation.confirm_time = :allocation.confirm_time,]>
        	<[AND allocation.update_by = :allocation.update_by]>
        	<[AND allocation.update_time = :allocation.update_time]>
        	<[AND allocation.is_push = :allocation.is_push]>
        	
        	<[AND allocation_item.sku = :allocation_item.sku]>
        	<[AND allocation_item.box_no = :allocation_item.box_no]>
        	<[AND allocation_item.box_status = :allocation_item.box_status]>
        	<[AND allocation_item.board_no = :allocation_item.board_no]>
        	<[AND allocation_item.board_status = :allocation_item.board_status]>
        	<[AND allocation_item.sku IN (:sku_list)]>
        	<[AND allocation_item.sku IN (:skus)]>
        	<[AND allocation_item.allocation_item_id IN (:allocation_item_id_list)]>
        	<[AND allocation_item.put_status = :allocation_item.put_status]>
	        <[AND allocation_item.put_by = :allocation_item.put_by]>
	        <[AND allocation_item.put_time = :allocation_item.put_time]>
        	
        	<[AND allocation.create_time >= :create_start_time]>
        	<[AND allocation.create_time <= :create_end_time]>
        	<[AND allocation.allocation_id IN (:allocation_id_list)]>
        	<[AND allocation.allocation_no IN (:allocation_no_list)]>
        	<[AND allocation.allocation_status IN (:allocation_status_list)]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="createApvAllocation">
		<content>
      <![CDATA[
        INSERT INTO wh_apv_allocation (allocation_no, delivery_warehouse_id, dest_warehouse_id, allocation_type, transport_type, freight, remark, 
          allocation_status, create_by, create_time, update_by, update_time)
        VALUES (:allocation.allocation_no, :allocation.delivery_warehouse_id, :allocation.dest_warehouse_id, :allocation.allocation_type, :allocation.transport_type, :allocation.freight, :allocation.remark, 
          		:allocation.allocation_status, :allocation.create_by, :allocation.create_time, :allocation.update_by, :allocation.update_time)
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="createApvAllocationItem">
		<content>
      <![CDATA[
        INSERT INTO wh_apv_allocation_item (allocation_id, allocation_no, sku, sku_name, allocation_num, create_by, stock_id,json_str,
          create_time, pick_status, pick_num, pick_by, pick_time, box_status, box_num, 
          box_no, box_by, box_time, board_status, board_no, board_by, board_time, 
          load_status, load_by, load_time, up_status, up_num, up_by, up_time)
        VALUES (:allocation_item.allocation_id, :allocation_item.allocation_no, :allocation_item.sku, :allocation_item.sku_name, :allocation_item.allocation_num, :allocation_item.create_by,
        	:allocation_item.stock_id,:allocation_item.json_str,
          :allocation_item.create_time, :allocation_item.pick_status, :allocation_item.pick_num, :allocation_item.pick_by, :allocation_item.pick_time, :allocation_item.box_status, :allocation_item.box_num, 
          :allocation_item.box_no, :allocation_item.box_by, :allocation_item.box_time, :allocation_item.board_status, :allocation_item.board_no, :allocation_item.board_by, :allocation_item.board_time, 
          :allocation_item.load_status, :allocation_item.load_by, :allocation_item.load_time, :allocation_item.up_status, :allocation_item.up_num, :allocation_item.up_by, :allocation_item.up_time)
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="updateApvAllocation">
		<content>
      <![CDATA[
        UPDATE 
        	wh_apv_allocation allocation
        SET 
        	<[allocation.delivery_warehouse_id = :allocation.delivery_warehouse_id,]>
        	<[allocation.dest_warehouse_id = :allocation.dest_warehouse_id,]>
        	<[allocation.allocation_type = :allocation.allocation_type,]>
        	<[allocation.transport_type = :allocation.transport_type,]>
        	<[allocation.freight = :allocation.freight,]>
        	<[allocation.remark = :allocation.remark,]>
        	<[allocation.allocation_status = :allocation.allocation_status,]>
        	<[allocation.audit_by  = :allocation.audit_by,]>
        	<[allocation.audit_time = :allocation.audit_time,]>
        	<[allocation.confirm_by = :allocation.confirm_by,]>
        	<[allocation.confirm_time = :allocation.confirm_time,]>
        	<[allocation.update_by = :allocation.update_by,]>
        	<[allocation.update_time = :allocation.update_time,]>
        	<[allocation.is_push = :allocation.is_push,]>
        	
        	allocation.allocation_id = allocation.allocation_id
        WHERE 
        	1 = 1
        	<[AND allocation.allocation_id = :allocation.allocation_id]>
        	<[AND allocation.allocation_no = :allocation.allocation_no]>
        	<[AND allocation.allocation_no IN (:allocation_no_list)]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="updateApvAllocationItem">
		<content>
      <![CDATA[
        UPDATE 
        	wh_apv_allocation_item allocation_item
        SET 
        	<[allocation_item.allocation_num = :allocation_item.allocation_num,]>
        	<[allocation_item.box_num = :allocation_item.box_num,]>
        	<[allocation_item.pick_num = :allocation_item.pick_num,]>
        	<[allocation_item.is_audit = :allocation_item.is_audit,]>
        	<[allocation_item.history_pick_num = :allocation_item.history_pick_num,]>
        	
        	<[allocation_item.put_status = :allocation_item.put_status,]>
        	<[allocation_item.inventory_status = :allocation_item.inventory_status,]>
        	<[allocation_item.put_by = :allocation_item.put_by,]>
        	<[allocation_item.put_time = :allocation_item.put_time,]>
        	<[allocation_item.stock_id = :allocation_item.stock_id,]>
        	<[allocation_item.json_str = :allocation_item.json_str,]>
        	<[allocation_item.delivery_method = :allocation_item.delivery_method,]>
        	<[allocation_item.logistics_no = :allocation_item.logistics_no,]>

        	allocation_item.allocation_item_id = allocation_item.allocation_item_id
        WHERE 
        	1 = 1
        	AND allocation_item.allocation_item_id = :allocation_item.allocation_item_id
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="deleteApvAllocationItem">
		<content>
	      <![CDATA[
	        DELETE FROM wh_apv_allocation_item WHERE 1 = 1 AND allocation_item_id IN (:allocation_item_id_list)
	      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="updateApvAllocationTrunk">
		<content>
      <![CDATA[
        UPDATE 
        	wh_apv_allocation_item allocation_item
        SET 
        	<[pick_status = :allocation_item.pick_status,]>
			<[pick_num = :allocation_item.pick_num,]>
			<[pick_by = :allocation_item.pick_by,]>
			<[pick_time = :allocation_item.pick_time,]>
        
        	<[inventory_status = :allocation_item.inventory_status,]>

        	<[box_status = :allocation_item.box_status,]>
			<[box_num = :allocation_item.box_num,]>
			<[box_no = :allocation_item.box_no,]>
			<[box_by = :allocation_item.box_by,]>
			<[box_time = :allocation_item.box_time,]>
			
			<[board_status = :allocation_item.board_status,]>
			<[board_no = :allocation_item.board_no,]>
			<[board_by = :allocation_item.board_by,]>
			<[board_time = :allocation_item.board_time,]>
			
			<[load_status = :allocation_item.load_status,]>
			<[load_by = :allocation_item.load_by,]>
			<[load_time = :allocation_item.load_time,]>
			
			<[up_status = :allocation_item.up_status,]>
			<[up_num = :allocation_item.up_num,]>
			<[up_by = :allocation_item.up_by,]>
			<[up_time = :allocation_item.up_time,]>
        	
        	allocation_item.allocation_item_id = allocation_item.allocation_item_id
        WHERE 
        	1 = 1
        	AND allocation_item.allocation_no = :allocation_item.allocation_no
        	<[AND allocation_item.sku = :allocation_item.sku]>
        	<[AND allocation_item.allocation_id = :allocation_item.allocation_id]>
        	<[AND allocation_item.allocation_item_id = :allocation_item.allocation_item_id]>
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="updateApvAllocationStatus">
		<content>
      <![CDATA[
        UPDATE 
        	wh_apv_allocation allocation
        SET 
        	allocation.allocation_status = :allocation.allocation_status
        WHERE 
        	allocation.allocation_no = :allocation.allocation_no
      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="updateApvAllocationStatusByOldStatus">
		<content>
      <![CDATA[
        UPDATE
        	wh_apv_allocation allocation
        SET
        	allocation.allocation_status = :allocation.allocation_status
        WHERE
        	allocation.allocation_no = :allocation.allocation_no
        	AND allocation.allocation_status = :oldStatus
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryApvAllocationByBoardNoOrBoxNo">
		<content>
      <![CDATA[
        SELECT 
        	allocation.allocation_id, 
        	allocation.allocation_no, 
        	allocation.delivery_warehouse_id, 
        	allocation.dest_warehouse_id, 
        	allocation.allocation_type, 
        	allocation.transport_type, 
        	allocation.freight, 
        	allocation.remark,
        	allocation.is_push,
        	allocation.allocation_status, 
        	allocation.create_by, 
        	allocation.create_time, 
        	allocation.audit_by,
        	allocation.audit_time,
        	allocation.confirm_by,
        	allocation.confirm_time,
        	allocation.update_by, 
        	allocation.update_time 
        FROM 
        	wh_apv_allocation allocation 
        WHERE 
        	1 = 1
        	AND allocation.allocation_type = 1
        	<[AND allocation.allocation_id IN (SELECT allocation_item.allocation_id FROM wh_apv_allocation_item allocation_item WHERE allocation_item.box_no = :allocation_item.box_no)]>
        	<[AND allocation.allocation_id IN (SELECT allocation_item.allocation_id FROM wh_apv_allocation_item allocation_item WHERE allocation_item.board_no = :allocation_item.board_no)]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="updateApvAllocationByBoard">
		<content>
      <![CDATA[
        UPDATE 
        	wh_apv_allocation_item allocation_item
        SET 
			<[board_status = :allocation_item.board_status,]>
			<[board_no = :allocation_item.board_no,]>
			<[board_by = :allocation_item.board_by,]>
			<[board_time = :allocation_item.board_time,]>
        	
        	allocation_item.allocation_item_id = allocation_item.allocation_item_id
        WHERE allocation_item.box_no = :allocation_item.box_no
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="updateApvAllocationByLoad">
		<content>
      <![CDATA[
        UPDATE 
        	wh_apv_allocation_item allocation_item
        SET 
			<[load_status = :allocation_item.load_status,]>
			<[load_by = :allocation_item.load_by,]>
			<[load_time = :allocation_item.load_time,]>
			
        	allocation_item.allocation_item_id = allocation_item.allocation_item_id
        WHERE
        	1=1 
        	<[AND allocation_item.board_no = :allocation_item.board_no]>
        	<[AND allocation_item.box_no = :allocation_item.box_no]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryAllocationByLoading">
		<content>
      <![CDATA[
        SELECT
			allocation_id
		FROM
		(
			SELECT 
				allocation_item.allocation_id,
				COUNT(1) AS total,
				COUNT(case WHEN (allocation_item.load_status=1 OR allocation_item.allocation_num=0 OR (allocation_item.box_num = 0 AND allocation_item.is_audit = true)) then TRUE else NULL end ) co
			FROM wh_apv_allocation_item allocation_item 
			WHERE allocation_item.allocation_id IN (:allocation_id_list)
			GROUP BY allocation_item.allocation_id
			HAVING total = co
		) T
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryOrderAllocationByLoading">
		<content>
      <![CDATA[
        SELECT
			allocation_id
		FROM
		(
			SELECT 
				order_item.allocation_id,
				COUNT(1) AS total,
				COUNT(case WHEN order_item.load_status=1 then TRUE else NULL end ) co
			FROM wh_allocation_order_item order_item 
			WHERE order_item.allocation_id IN (:allocation_id_list)
			GROUP BY order_item.allocation_id
			HAVING total = co
		) T
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryApvAllocationDetails" >
    <content >
      <![CDATA[
        SELECT allocation_item.allocation_item_id, 
        allocation_item.allocation_id, 
        allocation_item.allocation_no, 
        allocation_item.sku, 
        allocation_item.sku_name, 
        allocation_item.allocation_num, 
        allocation_item.stock_id,
        allocation_item.json_str,
        allocation_item.create_by,
        allocation_item.create_time, 
        allocation_item.pick_status, 
        allocation_item.pick_num, 
        allocation_item.pick_by, 
        allocation_item.pick_time, 
        allocation_item.box_status, 
        allocation_item.box_num, 
        allocation_item.box_no, 
        allocation_item.box_by, 
        allocation_item.box_time, 
        allocation_item.board_status, 
        allocation_item.board_no, 
        allocation_item.board_by, 
        allocation_item.board_time, 
        allocation_item.load_status, 
        allocation_item.load_by, 
        allocation_item.load_time, 
        allocation_item.up_status, 
        allocation_item.up_num, 
        allocation_item.up_by, 
        allocation_item.up_time,
        allocation_item.is_audit,
        allocation_item.put_status,
        allocation_item.inventory_status,
		allocation_item.put_by,
		allocation_item.put_time,
		allocation_item.delivery_method,
		allocation_item.logistics_no,
        allocation_item.history_pick_num
        FROM wh_apv_allocation_item allocation_item
        WHERE 1 = 1
        <[AND allocation_item.allocation_item_id = :allocation_item.allocation_item_id]>
        <[AND allocation_item.allocation_item_id IN (:allocation_item_id_list)]>
        <[AND allocation_item.allocation_id IN (SELECT allocation_id FROM wh_apv_allocation WHERE allocation_item.allocation_id=allocation_id AND allocation_status IN (:allocation_status_list))]>
        <[AND allocation_item.allocation_id = :allocation_item.allocation_id]>
        <[AND allocation_item.allocation_no = :allocation_item.allocation_no]>
        <[AND allocation_item.sku = :allocation_item.sku]>
        <[AND allocation_item.sku IN (:sku_list)]>
        <[AND allocation_item.sku_name = :allocation_item.sku_name]>
        <[AND allocation_item.allocation_num = :allocation_item.allocation_num]>
        <[AND allocation_item.create_by = :allocation_item.create_by]>
        <[AND allocation_item.create_time = :allocation_item.create_time]>
        <[AND allocation_item.pick_status = :allocation_item.pick_status]>
        <[AND allocation_item.pick_num = :allocation_item.pick_num]>
        <[AND allocation_item.pick_by = :allocation_item.pick_by]>
        <[AND allocation_item.pick_time = :allocation_item.pick_time]>
        <[AND allocation_item.box_status = :allocation_item.box_status]>
        <[AND allocation_item.box_num = :allocation_item.box_num]>
        <[AND allocation_item.box_no = :allocation_item.box_no]>
        <[AND allocation_item.box_by = :allocation_item.box_by]>
        <[AND allocation_item.box_time = :allocation_item.box_time]>
        <[AND allocation_item.board_status = :allocation_item.board_status]>
        <[AND allocation_item.board_no = :allocation_item.board_no]>
        <[AND allocation_item.board_by = :allocation_item.board_by]>
        <[AND allocation_item.board_time = :allocation_item.board_time]>
        <[AND allocation_item.load_status = :allocation_item.load_status]>
        <[AND allocation_item.load_by = :allocation_item.load_by]>
        <[AND allocation_item.load_time = :allocation_item.load_time]>
        <[AND allocation_item.up_status = :allocation_item.up_status]>
        <[AND allocation_item.up_num = :allocation_item.up_num]>
        <[AND allocation_item.up_by = :allocation_item.up_by]>
        <[AND allocation_item.up_time = :allocation_item.up_time]>
        <[AND allocation_item.put_status = :allocation_item.put_status]>
        <[AND allocation_item.put_by = :allocation_item.put_by]>
        <[AND allocation_item.put_time = :allocation_item.put_time]>
      ]]>
    </content>
  </sql>
  
  <!-- 库存调拨->调拨单 已入库 已完成 状态 推送 修改发货仓调拨库存-->
  <sql datasource="dataSource" id="querySkuInventoryByCheckIn">
		<content>
      <![CDATA[
        SELECT 
        	allocation.allocation_no AS allocationNo,
			allocation.delivery_warehouse_id AS warehouseId,
			check_in_item.sku, 
			SUM(check_in_item.quantity) AS goodQuantity, 
			SUM(check_in_item.exception_quantity) AS badQuantity
		FROM 
			wh_allocation_check_in_item check_in_item 
			JOIN wh_allocation_check_in check_in ON check_in_item.in_id=check_in.in_id 
			JOIN wh_apv_allocation allocation ON allocation.allocation_no = check_in.allocation_order_no
		WHERE 
			1=1
			AND allocation.allocation_type = 1
			AND allocation.allocation_status = 11
			AND allocation.allocation_no IN (:allocation_no_list)
		GROUP BY check_in_item.sku, delivery_warehouse_id
      ]]>
		</content>
	</sql>
	
	<!-- 查询调拨需求中已完成的调拨单对应的调拨需求的item-->
    <sql datasource="dataSource" id="queryApvItemByFinish">
		<content>
      <![CDATA[
        SELECT 
        	demand.in_warehouse_id,
        	demand.allocation_no,
			demand_item.apv_id,
			demand_item.apv_no,
			demand_item.apv_item,
			demand_item.apv_data_json
		FROM
			wh_allocation_demand_item demand_item
			JOIN wh_allocation_demand demand ON demand.task_id=demand_item.task_id
			JOIN wh_apv_allocation allocation ON allocation.allocation_no=demand.allocation_no
		WHERE 
			1=1
			AND allocation.allocation_type = 2
			AND allocation.allocation_status != 99
			AND demand_item.is_push=0
		GROUP BY demand_item.apv_no
		ORDER BY RAND()
		LIMIT 1200
      ]]>
		</content>
	</sql>
	
	<!-- 订单调拨->调拨单 已完成 查询调出仓apv是否已交运，若交运则修改调入仓调拨库存-->
    <sql datasource="dataSource" id="queryAlloApvNosOnLoad">
		<content>
      <![CDATA[
        SELECT 
        	apv_no 
        FROM 
        	wh_apv apv 
        WHERE 
        	1=1 
        	<[AND apv.status IN (:apv_status_list)]> 
        	<[AND apv.apv_no IN (:apv_no_list)]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryOrderAllocationByBoardNoOrSubTaskNo">
		<content>
      <![CDATA[
        SELECT 
        	allocation.allocation_id, 
        	allocation.allocation_no, 
        	allocation.delivery_warehouse_id, 
        	allocation.dest_warehouse_id, 
        	allocation.allocation_type, 
        	allocation.transport_type, 
        	allocation.freight, 
        	allocation.remark, 
        	allocation.allocation_status, 
        	allocation.create_by, 
        	allocation.create_time, 
        	allocation.audit_by,
        	allocation.audit_time,
        	allocation.confirm_by,
        	allocation.confirm_time,
        	allocation.update_by,
        	allocation.is_push,
        	allocation.update_time 
        FROM 
        	wh_apv_allocation allocation 
        WHERE 
        	1 = 1
        	AND allocation.allocation_type = 1
        	<[AND allocation.allocation_id IN (SELECT order_item.allocation_id FROM wh_allocation_order_item order_item WHERE order_item.task_no = :order_item.task_no)]>
        	<[AND allocation.allocation_id IN (SELECT order_item.allocation_id FROM wh_allocation_order_item order_item WHERE order_item.board_no = :order_item.board_no)]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="updateOrderAllocationByBoard">
		<content>
      <![CDATA[
        UPDATE 
        	wh_allocation_order_item order_item
        SET 
			<[board_status = :order_item.board_status,]>
			<[board_no = :order_item.board_no,]>
			<[board_by = :order_item.board_by,]>
			<[board_time = :order_item.board_time,]>
        	order_item.id = order_item.id
        WHERE order_item.task_no = :order_item.task_no
        <[AND board_status = :BEFOR_BOARD_STATUS]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="updateOrderAllocationByLoad">
		<content>
      <![CDATA[
        UPDATE 
        	wh_allocation_order_item order_item
        SET 
			<[load_status = :order_item.load_status,]>
			<[load_by = :order_item.load_by,]>
			<[load_time = :order_item.load_time,]>
			
        	order_item.id = order_item.id
        WHERE
        	1=1 
        	<[AND order_item.board_no = :order_item.board_no]>
        	<[AND order_item.task_no = :order_item.task_no]>
        	<[AND order_item.load_status = :befor_load_status]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryOrderAllocationDetailCount">
		<content>
	      <![CDATA[
	        SELECT 
	        	COUNT(1)
	        FROM 
	        	wh_apv_allocation allocation 
	        JOIN wh_allocation_order_item order_item ON allocation.allocation_id=order_item.allocation_id
	        WHERE 
	        	1 = 1
	        	<[AND allocation.allocation_type = :allocation.allocation_type]>
	        	<[AND order_item.task_no = :order_item.task_no]>
	        	<[AND order_item.board_no = :order_item.board_no]>
	      ]]>
		</content>
	</sql>

	<sql datasource="dataSource" id="queryOrderAllocationDetailList">
		<content>
      <![CDATA[
        SELECT 
        	allocation.allocation_id, 
        	allocation.allocation_no, 
        	allocation.delivery_warehouse_id, 
        	allocation.dest_warehouse_id, 
        	allocation.allocation_type, 
        	allocation.transport_type, 
        	allocation.freight, 
        	allocation.remark, 
        	allocation.allocation_status, 
        	allocation.create_by, 
        	allocation.create_time, 
        	allocation.audit_by,
        	allocation.audit_time,
        	allocation.confirm_by,
        	allocation.confirm_time,
        	allocation.update_by, 
        	allocation.update_time,
        	
        	order_item.id,
			order_item.allocation_id,
			order_item.allocation_no,
			order_item.task_id,
			order_item.task_no,
			order_item.box_status,
			order_item.box_no,
			order_item.box_by,
			order_item.box_time,
			order_item.board_status,
			order_item.board_no,
			order_item.board_by,
			order_item.board_time,
			order_item.create_by,
			order_item.created_date,
			order_item.load_status,
			order_item.load_by,
			order_item.load_time, 
			
			order_sku.id, 
        	order_sku.allocation_id, 
        	order_sku.allocation_no, 
        	order_sku.sku, 
        	order_sku.sku_name, 
        	order_sku.location, 
        	order_sku.quantity, 
        	order_sku.pick_quantity, 
        	order_sku.sow_quantity, 
        	order_sku.yet_sow_quantity, 
        	order_sku.allcoation_quantity, 
        	order_sku.status, 
        	order_sku.creation_date,
        	order_sku.last_update_date
        FROM 
        	wh_apv_allocation allocation 
        	JOIN wh_allocation_order_item order_item ON allocation.allocation_id = order_item.allocation_id
        	JOIN wh_allocation_order_sku order_sku ON allocation.allocation_id = order_sku.allocation_id
        WHERE 
        	1 = 1
        	<[AND allocation.allocation_id IN (:allocation_id_list)]>
        	<[AND allocation.allocation_type = :allocation.allocation_type]>
        	<[AND order_item.task_no = :order_item.task_no]>
        	<[AND order_item.board_no = :order_item.board_no]>
        	<[AND allocation.allocation_status != :ISNOTDISCARD]>
      ]]>
		</content>
	</sql>
	
	
	<sql datasource="dataSource" id="queryOrderAllocationDetailSkuList">
		<content>
      <![CDATA[
        SELECT 
        	allocation.allocation_id, 
        	allocation.allocation_no, 
        	allocation.delivery_warehouse_id, 
        	allocation.dest_warehouse_id, 
        	allocation.allocation_type, 
        	allocation.transport_type, 
        	allocation.freight, 
        	allocation.remark, 
        	allocation.allocation_status, 
        	allocation.create_by, 
        	allocation.create_time, 
        	allocation.audit_by,
        	allocation.audit_time,
        	allocation.confirm_by,
        	allocation.confirm_time,
        	allocation.update_by, 
        	allocation.update_time,
 
			order_sku.id, 
        	order_sku.allocation_id, 
        	order_sku.allocation_no, 
        	order_sku.sku, 
        	order_sku.sku_name, 
        	order_sku.location, 
        	order_sku.quantity, 
        	order_sku.pick_quantity, 
        	order_sku.sow_quantity, 
        	order_sku.yet_sow_quantity, 
        	order_sku.allcoation_quantity, 
        	order_sku.status, 
        	order_sku.creation_date,
        	order_sku.last_update_date
        FROM 
        	wh_apv_allocation allocation 
        	JOIN wh_allocation_order_sku order_sku ON allocation.allocation_id = order_sku.allocation_id
        WHERE 
        	1 = 1
        	<[AND allocation.allocation_id IN (:allocation_id_list)]>
        	<[AND allocation.allocation_type = :allocation.allocation_type]>
        	<[AND allocation.allocation_status != :ISNOTDISCARD]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="querySyncAllocationAndItemList">
		<content>
      <![CDATA[
        SELECT 
        	allocation.allocation_no, 
        	allocation.allocation_id, 
        	allocation.delivery_warehouse_id, 
        	allocation.dest_warehouse_id, 
        	allocation.allocation_type, 
        	allocation.allocation_status, 
        	
			allocation_item.allocation_item_id,
			allocation_item.allocation_no,
			allocation_item.sku,
			allocation_item.up_status,
			allocation_item.up_num,
			allocation_item.up_by,
			allocation_item.up_time 
        FROM 
        	wh_apv_allocation allocation
        	LEFT JOIN wh_apv_allocation_item allocation_item ON allocation.allocation_id=allocation_item.allocation_id
        WHERE 
        	1 = 1
        	AND allocation.allocation_type = 1
        	
        	<[AND allocation.allocation_no IN (:allocation_no_list)]>
        	<[AND allocation.allocation_status IN (:allocation_status_list)]>
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="updateSyncAllocationByPrimaryKey">
		<content>
      <![CDATA[
        UPDATE 
        	wh_apv_allocation allocation
        SET 
        	<[allocation.allocation_status = :allocation.allocation_status,]>
        	
        	allocation.allocation_id = allocation.allocation_id
        WHERE 
        	1 = 1
        	AND allocation.allocation_id = :allocation.allocation_id
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="updateSyncAllocationItemByPrimaryKey">
		<content>
      <![CDATA[
        UPDATE 
        	wh_apv_allocation_item allocation_item
        SET 
			<[up_status = :allocation_item.up_status,]>
			<[up_num = :allocation_item.up_num,]>
			<[up_by = :allocation_item.up_by,]>
			<[up_time = :allocation_item.up_time,]>
			<[is_audit = :allocation_item.is_audit,]>
			<[history_pick_num = :allocation_item.history_pick_num,]>
			
        	allocation_item.allocation_item_id = allocation_item.allocation_item_id
        WHERE
        	1=1 
        	AND allocation_item.allocation_item_id = :allocation_item.allocation_item_id
      ]]>
		</content>
	</sql>
	
	<sql datasource="dataSource" id="queryWhApvAllocationItemByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT 
	        allocation_item.allocation_item_id, 
	        allocation_item.allocation_id, 
	        allocation_item.allocation_no, 
	        allocation_item.sku, 
	        allocation_item.sku_name, 
	        allocation_item.allocation_num, 
	        allocation_item.stock_id,
	        allocation_item.json_str,
	        allocation_item.create_by,
	        allocation_item.create_time, 
	        allocation_item.pick_status, 
	        allocation_item.pick_num, 
	        allocation_item.pick_by, 
	        allocation_item.pick_time, 
	        allocation_item.box_status, 
	        allocation_item.box_num, 
	        allocation_item.box_no, 
	        allocation_item.box_by, 
	        allocation_item.box_time, 
	        allocation_item.board_status, 
	        allocation_item.board_no, 
	        allocation_item.board_by, 
	        allocation_item.board_time, 
	        allocation_item.load_status, 
	        allocation_item.load_by, 
	        allocation_item.load_time, 
	        allocation_item.up_status, 
	        allocation_item.up_num, 
	        allocation_item.up_by, 
	        allocation_item.up_time,
	        allocation_item.is_audit,
	        allocation_item.put_status,
	        allocation_item.inventory_status,
			allocation_item.put_by,
			allocation_item.put_time,
			allocation_item.delivery_method,
			allocation_item.logistics_no,
	        allocation_item.history_pick_num
        FROM 
        	wh_apv_allocation_item allocation_item
        WHERE 
        	allocation_item.allocation_item_id = :allocation_item.allocation_item_id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvAllocationItem" >
    <content >
      <![CDATA[
        SELECT 
	        allocation_item.allocation_item_id, 
	        allocation_item.allocation_id, 
	        allocation_item.allocation_no, 
	        allocation_item.sku, 
	        allocation_item.sku_name, 
	        allocation_item.allocation_num, 
	        allocation_item.stock_id,
	        allocation_item.json_str,
	        allocation_item.create_by,
	        allocation_item.create_time, 
	        allocation_item.pick_status, 
	        allocation_item.pick_num, 
	        allocation_item.pick_by, 
	        allocation_item.pick_time, 
	        allocation_item.box_status, 
	        allocation_item.box_num, 
	        allocation_item.box_no, 
	        allocation_item.box_by, 
	        allocation_item.box_time, 
	        allocation_item.board_status, 
	        allocation_item.board_no, 
	        allocation_item.board_by, 
	        allocation_item.board_time, 
	        allocation_item.load_status, 
	        allocation_item.load_by, 
	        allocation_item.load_time, 
	        allocation_item.up_status, 
	        allocation_item.up_num, 
	        allocation_item.up_by, 
	        allocation_item.up_time,
	        allocation_item.is_audit,
	        allocation_item.put_status,
	        allocation_item.inventory_status,
			allocation_item.put_by,
			allocation_item.put_time,
			allocation_item.delivery_method,
			allocation_item.logistics_no,
	        allocation_item.history_pick_num
        FROM 
        	wh_apv_allocation_item allocation_item
        WHERE 
        	1=1
        	<[AND allocation_item.allocation_item_id = :allocation_item.allocation_item_id]>
			<[AND allocation_item.allocation_id = :allocation_item.allocation_id]>
			<[AND allocation_item.allocation_no = :allocation_item.allocation_no]>
			<[AND allocation_item.sku = :allocation_item.sku]>
			<[AND allocation_item.sku_name = :allocation_item.sku_name]>
			<[AND allocation_item.allocation_num = :allocation_item.allocation_num]>
			<[AND allocation_item.create_by = :allocation_item.create_by]>
			<[AND allocation_item.create_time = :allocation_item.create_time]>
			<[AND allocation_item.pick_status = :allocation_item.pick_status]>
			<[AND allocation_item.pick_num = :allocation_item.pick_num]>
			<[AND allocation_item.pick_by = :allocation_item.pick_by]>
			<[AND allocation_item.pick_time = :allocation_item.pick_time]>
			<[AND allocation_item.history_pick_num = :allocation_item.history_pick_num]>
			<[AND allocation_item.box_status = :allocation_item.box_status]>
			<[AND allocation_item.box_num = :allocation_item.box_num]>
			<[AND allocation_item.box_no = :allocation_item.box_no]>
			<[AND allocation_item.box_by = :allocation_item.box_by]>
			<[AND allocation_item.box_time = :allocation_item.box_time]>
			<[AND allocation_item.board_status = :allocation_item.board_status]>
			<[AND allocation_item.board_no = :allocation_item.board_no]>
			<[AND allocation_item.board_by = :allocation_item.board_by]>
			<[AND allocation_item.board_time = :allocation_item.board_time]>
			<[AND allocation_item.load_status = :allocation_item.load_status]>
			<[AND allocation_item.load_by = :allocation_item.load_by]>
			<[AND allocation_item.load_time = :allocation_item.load_time]>
			<[AND allocation_item.up_status = :allocation_item.up_status]>
			<[AND allocation_item.up_num = :allocation_item.up_num]>
			<[AND allocation_item.up_by = :allocation_item.up_by]>
			<[AND allocation_item.up_time = :allocation_item.up_time]>
			<[AND allocation_item.put_status = :allocation_item.put_status]>
			<[AND allocation_item.put_by = :allocation_item.put_by]>
			<[AND allocation_item.put_time = :allocation_item.put_time]>
			<[AND allocation_item.is_audit = :allocation_item.is_audit]>
		LIMIT 1
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhApvAllocationItemList" >
    <content >
      <![CDATA[
        SELECT 
	        allocation_item.allocation_item_id, 
	        allocation_item.allocation_id, 
	        allocation_item.allocation_no, 
	        allocation_item.sku, 
	        allocation_item.sku_name, 
	        allocation_item.allocation_num, 
	        allocation_item.stock_id,
	        allocation_item.json_str,
	        allocation_item.create_by,
	        allocation_item.create_time, 
	        allocation_item.pick_status, 
	        allocation_item.pick_num, 
	        allocation_item.pick_by, 
	        allocation_item.pick_time, 
	        allocation_item.box_status, 
	        allocation_item.box_num, 
	        allocation_item.box_no, 
	        allocation_item.box_by, 
	        allocation_item.box_time, 
	        allocation_item.board_status, 
	        allocation_item.board_no, 
	        allocation_item.board_by, 
	        allocation_item.board_time, 
	        allocation_item.load_status, 
	        allocation_item.load_by, 
	        allocation_item.load_time, 
	        allocation_item.up_status, 
	        allocation_item.up_num, 
	        allocation_item.up_by, 
	        allocation_item.up_time,
	        allocation_item.is_audit,
	        allocation_item.put_status,
	        allocation_item.inventory_status,
			allocation_item.put_by,
			allocation_item.put_time,
			allocation_item.delivery_method,
			allocation_item.logistics_no,
	        allocation_item.history_pick_num
        FROM 
        	wh_apv_allocation_item allocation_item
        WHERE 
        	1=1
        	<[AND allocation_item.allocation_item_id = :allocation_item.allocation_item_id]>
			<[AND allocation_item.allocation_id = :allocation_item.allocation_id]>
			<[AND allocation_item.allocation_no = :allocation_item.allocation_no]>
			<[AND allocation_item.sku = :allocation_item.sku]>
			<[AND allocation_item.sku IN (:allocation_item_sku_list)]>
			<[AND allocation_item.sku_name = :allocation_item.sku_name]>
			<[AND allocation_item.allocation_num = :allocation_item.allocation_num]>
			<[AND allocation_item.create_by = :allocation_item.create_by]>
			<[AND allocation_item.create_time = :allocation_item.create_time]>
			<[AND allocation_item.pick_status = :allocation_item.pick_status]>
			<[AND allocation_item.pick_num = :allocation_item.pick_num]>
			<[AND allocation_item.pick_by = :allocation_item.pick_by]>
			<[AND allocation_item.pick_time = :allocation_item.pick_time]>
			<[AND allocation_item.history_pick_num = :allocation_item.history_pick_num]>
			<[AND allocation_item.box_status = :allocation_item.box_status]>
			<[AND allocation_item.box_num = :allocation_item.box_num]>
			<[AND allocation_item.box_no = :allocation_item.box_no]>
			<[AND allocation_item.box_by = :allocation_item.box_by]>
			<[AND allocation_item.box_time = :allocation_item.box_time]>
			<[AND allocation_item.board_status = :allocation_item.board_status]>
			<[AND allocation_item.board_no = :allocation_item.board_no]>
			<[AND allocation_item.board_by = :allocation_item.board_by]>
			<[AND allocation_item.board_time = :allocation_item.board_time]>
			<[AND allocation_item.load_status = :allocation_item.load_status]>
			<[AND allocation_item.load_by = :allocation_item.load_by]>
			<[AND allocation_item.load_time = :allocation_item.load_time]>
			<[AND allocation_item.up_status = :allocation_item.up_status]>
			<[AND allocation_item.up_num = :allocation_item.up_num]>
			<[AND allocation_item.up_by = :allocation_item.up_by]>
			<[AND allocation_item.up_time = :allocation_item.up_time]>
			<[AND allocation_item.put_status = :allocation_item.put_status]>
			<[AND allocation_item.put_by = :allocation_item.put_by]>
			<[AND allocation_item.put_time = :allocation_item.put_time]>
			<[AND allocation_item.is_audit = :allocation_item.is_audit]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAllocationUpItemCount" >
    <content >
      <![CDATA[
        SELECT 
			COUNT(*) 
		FROM 
			wh_apv_allocation_item allocation_item 
			JOIN wh_apv_allocation allocation ON allocation.allocation_id=allocation_item.allocation_id 
			LEFT JOIN (
				SELECT 
					check_in.in_id,
					check_in.allocation_order_no, 
					check_in.status,
					check_in_item.sku, 
					check_in.create_user, 
					check_in.create_date, 
					check_in_item.quantity, 
					check_in_item.qc_quantity, 
					check_in_item.up_quantity 
				FROM wh_allocation_check_in_item check_in_item JOIN wh_allocation_check_in check_in ON check_in_item.in_id=check_in.in_id WHERE check_in.status IN (3, 5, 7, 9 , 11, 12, 13)
			) checkin ON checkin.allocation_order_no=allocation.allocation_no AND checkIn.sku=allocation_item.sku
		WHERE 
			1=1
			<[AND allocation.allocation_no IN (:allocation_no_list)]>
			<[AND allocation.allocation_status IN (:allocation_status_list)]>
			<[AND allocation_item.box_no = :box_no]>
			<[AND allocation.delivery_warehouse_id = :delivery_warehouse_id]> 
			<[AND allocation.dest_warehouse_id = :dest_warehouse_id]>
			<[AND allocation_item.sku IN (:sku_list)]>
			<[AND allocation_item.box_by = :box_by]>
			<[AND allocation_item.box_time >= :box_start_time]>
			<[AND allocation_item.box_time <= :box_end_time]>
			<[AND checkin.create_user = :put_by]>
			<[AND checkin.create_date >= :put_start_time]>
			<[AND checkin.create_date <= :put_end_time]>
			<[:up_diff_condition]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryAllocationUpItemList" >
    <content >
      <![CDATA[
        SELECT 
			allocation.allocation_no AS allocationNo, 
			allocation.allocation_status AS allocationStatus,
			allocation.delivery_warehouse_id AS deliveryWarehouseId,
			allocation.dest_warehouse_id AS destWarehouseId,
			allocation_item.sku AS sku,
			whSku.name AS skuName,
			whSku.location_number AS locationNumber,
			allocation_item.box_no AS boxNo,
			allocation_item.allocation_num AS allocationNum,
			allocation_item.pick_num AS pickNum,
			allocation_item.box_num AS boxNum,
			allocation_item.up_num AS upNum,
			allocation_item.box_by AS boxBy,
			allocation_item.box_time AS boxTime,
			checkIn.in_id AS inId,
			checkIn.create_user AS checkInCreateBy,
			checkIn.create_date AS checkInCreateTime,
			IF(checkin.status=3, checkin.quantity, 0) AS waitQcQuantity, 
			IF(checkin.status=9, checkin.qc_quantity, 0) AS waitUpQuantity, 
			IF(checkin.status=11, checkin.qc_quantity, 0) AS upingQuantity,
			IF(checkin.status=13, checkin.up_quantity, 0) AS completeUpQuantity
		FROM 
			wh_apv_allocation_item allocation_item 
			JOIN wh_apv_allocation allocation ON allocation.allocation_id=allocation_item.allocation_id 
			LEFT JOIN wh_sku whSku ON allocation_item.sku=whSku.sku
			LEFT JOIN (
				SELECT 
					check_in.in_id,
					check_in.allocation_order_no, 
					check_in.status,
					check_in_item.sku, 
					check_in.create_user, 
					check_in.create_date, 
					check_in_item.quantity, 
					check_in_item.qc_quantity, 
					check_in_item.up_quantity 
				FROM wh_allocation_check_in_item check_in_item JOIN wh_allocation_check_in check_in ON check_in_item.in_id=check_in.in_id WHERE check_in.status IN (3, 5, 7, 9 , 11, 12, 13)
			) checkin ON checkin.allocation_order_no=allocation.allocation_no AND checkIn.sku=allocation_item.sku
		WHERE 
			1=1
			<[AND allocation.allocation_no IN (:allocation_no_list)]>
			<[AND allocation.allocation_status IN (:allocation_status_list)]>
			<[AND allocation_item.box_no = :box_no]>
			<[AND allocation.delivery_warehouse_id = :delivery_warehouse_id]> 
			<[AND allocation.dest_warehouse_id = :dest_warehouse_id]>
			<[AND allocation_item.sku IN (:sku_list)]>
			<[AND allocation_item.box_by = :box_by]>
			<[AND allocation_item.box_time >= :box_start_time]>
			<[AND allocation_item.box_time <= :box_end_time]>
			<[AND checkin.create_user = :put_by]>
			<[AND checkin.create_date >= :put_start_time]>
			<[AND checkin.create_date <= :put_end_time]>
			<[:up_diff_condition]>
      ]]>
    </content>
  </sql>

	<sql datasource="dataSource" id="batchUpdateAllocationByLoad">
		<content>
		  <![CDATA[
			UPDATE
				wh_apv_allocation_item allocation_item
			SET
				<[load_status = :allocation_item.load_status,]>
				<[load_by = :allocation_item.load_by,]>
				<[load_time = :allocation_item.load_time,]>
				<[delivery_method = :allocation_item.delivery_method,]>
				<[logistics_no = :allocation_item.logistics_no,]>

				allocation_item.allocation_item_id = allocation_item.allocation_item_id
			WHERE
				allocation_item.load_status IS NOT NULL AND allocation_item.load_status != 1
				<[AND allocation_item.allocation_no IN (:allocation_no_list)]>
				<[AND allocation_item.box_no IN (:box_no_list)]>
		  ]]>
		</content>
	</sql>
</sqlmap>