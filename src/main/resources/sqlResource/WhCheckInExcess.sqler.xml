<?xml version="1.0" encoding="UTF-8" ?>
<sqlmap >
  <sql datasource="dataSource" id="queryWhCheckInExcessCount" >
    <content >
      <![CDATA[
        SELECT COUNT(1)
        FROM wh_check_in_excess
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND in_id = :in_id]>
        <[AND sub_id = :sub_id]>
        <[AND sku = :sku]>
        <[AND excess_quantity = :excess_quantity]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND purchase_user = :purchase_user]>
        <[AND purchase_quantity = :purchase_quantity]>
        <[AND matched_quantity = :matched_quantity]>
        <[AND processing_method = :processing_method]>
        <[AND operation = :operation]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[AND qc_quantity = :qc_quantity]>
        <[AND up_quantity = :up_quantity]>
        <[AND transition_up_quantity = :transition_up_quantity]>
        <[AND weight = :weight]>
        <[AND shipping_cost = :shipping_cost]>
        <[AND tracking_number = :tracking_number]>
        <[AND supplier_id = :supplier_id]>
        <[AND qc_num = :qc_num]>
        <[AND vendor_name = :vendor_name]>
        <[AND difference_quantity = :difference_quantity]>
        <[AND in_id in (:inIdList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCheckInExcessList" >
    <content >
      <![CDATA[
        SELECT id, in_id, sub_id, sku, excess_quantity, purchase_order_no, purchase_user, 
        purchase_quantity, matched_quantity, processing_method, operation, create_time, update_time,
        qc_quantity, up_quantity, transition_up_quantity, weight, shipping_cost, tracking_number,
        supplier_id, qc_num, vendor_name, difference_quantity
        FROM wh_check_in_excess
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND in_id = :in_id]>
        <[AND sub_id = :sub_id]>
        <[AND sku = :sku]>
        <[AND excess_quantity = :excess_quantity]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND purchase_user = :purchase_user]>
        <[AND purchase_quantity = :purchase_quantity]>
        <[AND matched_quantity = :matched_quantity]>
        <[AND processing_method = :processing_method]>
        <[AND operation = :operation]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[AND qc_quantity = :qc_quantity]>
        <[AND up_quantity = :up_quantity]>
        <[AND transition_up_quantity = :transition_up_quantity]>
        <[AND weight = :weight]>
        <[AND shipping_cost = :shipping_cost]>
        <[AND tracking_number = :tracking_number]>
        <[AND supplier_id = :supplier_id]>
        <[AND qc_num = :qc_num]>
        <[AND vendor_name = :vendor_name]>
        <[AND difference_quantity = :difference_quantity]>
        <[AND in_id in (:inIdList)]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCheckInExcessByPrimaryKey" >
    <content >
      <![CDATA[
        SELECT id, in_id, sub_id, sku, excess_quantity, purchase_order_no, purchase_user, 
        purchase_quantity, matched_quantity, processing_method, operation, create_time, update_time,qc_num, vendor_name,
         qc_quantity, up_quantity, transition_up_quantity, weight, shipping_cost,tracking_number, supplier_id, difference_quantity
        FROM wh_check_in_excess
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="queryWhCheckInExcess" >
    <content >
      <![CDATA[
        SELECT id, in_id, sub_id, sku, excess_quantity, purchase_order_no, purchase_user, 
        purchase_quantity, matched_quantity, processing_method, operation, create_time, update_time,qc_num, vendor_name,
        qc_quantity, up_quantity, transition_up_quantity, weight, shipping_cost,tracking_number, supplier_id, difference_quantity
        FROM wh_check_in_excess
        WHERE 1 = 1
        <[AND id = :id]>
        <[AND in_id = :in_id]>
        <[AND sub_id = :sub_id]>
        <[AND sku = :sku]>
        <[AND excess_quantity = :excess_quantity]>
        <[AND purchase_order_no = :purchase_order_no]>
        <[AND purchase_user = :purchase_user]>
        <[AND purchase_quantity = :purchase_quantity]>
        <[AND matched_quantity = :matched_quantity]>
        <[AND processing_method = :processing_method]>
        <[AND operation = :operation]>
        <[AND create_time = :create_time]>
        <[AND update_time = :update_time]>
        <[AND qc_quantity = :qc_quantity]>
        <[AND up_quantity = :up_quantity]>
        <[AND transition_up_quantity = :transition_up_quantity]>
        <[AND weight = :weight]>
        <[AND shipping_cost = :shipping_cost]>
        <[AND tracking_number = :tracking_number]>
        <[AND supplier_id = :supplier_id]>
        <[AND qc_num = :qc_num]>
        <[AND vendor_name = :vendor_name]>
        <[AND difference_quantity = :difference_quantity]>
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="createWhCheckInExcess" >
    <content >
      <![CDATA[
        INSERT INTO wh_check_in_excess (in_id, sub_id, sku, excess_quantity, purchase_order_no, purchase_user, 
          purchase_quantity, matched_quantity, processing_method, operation, create_time, 
          update_time, qc_quantity, up_quantity, transition_up_quantity, weight, shipping_cost,
          tracking_number, supplier_id, qc_num, vendor_name, difference_quantity)
        VALUES (:in_id, :sub_id, :sku, :excess_quantity, :purchase_order_no, :purchase_user, 
          :purchase_quantity, :matched_quantity, :processing_method, :operation, :create_time, 
          :update_time, :qc_quantity, :up_quantity, :transition_up_quantity, :weight, :shipping_cost,
          :tracking_number, :supplier_id, :qc_num, :vendor_name, :difference_quantity)
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="deleteWhCheckInExcessByPrimaryKey" >
    <content >
      <![CDATA[
        DELETE FROM wh_check_in_excess
        WHERE id = :id
      ]]>
    </content>
  </sql>
  
  <sql datasource="dataSource" id="updateWhCheckInExcessByPrimaryKey" >
    <content >
      <![CDATA[
        UPDATE wh_check_in_excess
        SET <[in_id = :in_id,]>
          <[sub_id = :sub_id,]>
          <[sku = :sku,]>
          <[excess_quantity = :excess_quantity,]>
          <[purchase_order_no = :purchase_order_no,]>
          <[purchase_user = :purchase_user,]>
          <[purchase_quantity = :purchase_quantity,]>
          <[matched_quantity = :matched_quantity,]>
          <[processing_method = :processing_method,]>
          <[operation = :operation,]>
          <[create_time = :create_time,]>
          <[update_time = :update_time,]>
          <[qc_quantity = :qc_quantity,]>
          <[up_quantity = :up_quantity,]>
          <[transition_up_quantity = :transition_up_quantity,]>
          <[weight = :weight,]>
          <[shipping_cost = :shipping_cost,]>
          <[tracking_number = :tracking_number,]>
          <[supplier_id = :supplier_id,]>
          <[qc_num = :qc_num,]>
          <[vendor_name = :vendor_name,]>
          <[difference_quantity = :difference_quantity,]>
            id = id
        WHERE 1 = 1
        AND id = :id
      ]]>
    </content>
  </sql>
</sqlmap>