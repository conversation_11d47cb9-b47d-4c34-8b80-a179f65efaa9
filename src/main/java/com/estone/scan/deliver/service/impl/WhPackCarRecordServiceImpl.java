package com.estone.scan.deliver.service.impl;

import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.allocation.util.WarehouseProperties;
import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.apv.service.WhApvService;
import com.estone.checkin.enums.ShippingCompanyEnum;
import com.estone.common.enums.LogModule;
import com.estone.common.enums.WarehousePropertyEnum;
import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.scan.deliver.action.WhPackCarRecordController;
import com.estone.scan.deliver.bean.*;
import com.estone.scan.deliver.dao.WhPackCarRecordDao;
import com.estone.scan.deliver.enums.*;
import com.estone.scan.deliver.service.DeliverOrderService;
import com.estone.scan.deliver.service.WhPackCarRecordService;
import com.estone.scan.deliver.service.WhScanShipmentService;
import com.estone.scan.deliver.service.WhScanShipmentToApvService;
import com.estone.scan.deliver.util.TranslateApvToDeliverOrder2TmsDTOUtils;
import com.estone.transfer.bean.WhFbaAllocation;
import com.estone.transfer.bean.WhFbaAllocationItem;
import com.estone.transfer.service.WhFbaAllocationService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;

import lombok.extern.slf4j.Slf4j;

@Service("whPackCarRecordService")
@Slf4j
public class WhPackCarRecordServiceImpl implements WhPackCarRecordService {
    private Logger logger = LoggerFactory.getLogger(WhPackCarRecordServiceImpl.class);

    final static SystemLogUtils SCANSHIPMENTLOG = SystemLogUtils.create(LogModule.SCANSHIPMENT.getCode());

    private final static ExecutorService executors = ExecutorUtils.newFixedThreadPool(5);

    @Resource
    private WhPackCarRecordDao whPackCarRecordDao;

    @Resource
    private WhScanShipmentService whScanShipmentService;

    @Resource
    private WhApvService whApvService;

    @Resource
    private WhScanShipmentToApvService whScanShipmentToApvService;

    @Resource
    private DeliverOrderService deliverOrderService;

    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    /**
     * This method corresponds to the database table wh_pack_car_record
     *
     * @mbggenerated Thu Dec 20 15:57:20 CST 2018
     */
    public WhPackCarRecord getWhPackCarRecord(Integer id) {
        WhPackCarRecord whPackCarRecord = whPackCarRecordDao.queryWhPackCarRecord(id);
        return whPackCarRecord;
    }

    /**
     * This method corresponds to the database table wh_pack_car_record
     *
     * @mbggenerated Thu Dec 20 15:57:20 CST 2018
     */
    public WhPackCarRecord getWhPackCarRecordDetail(Integer id) {
        WhPackCarRecord whPackCarRecord = whPackCarRecordDao.queryWhPackCarRecord(id);
        // 关联查询
        return whPackCarRecord;
    }

    /**
     * This method corresponds to the database table wh_pack_car_record
     *
     * @mbggenerated Thu Dec 20 15:57:20 CST 2018
     */
    public WhPackCarRecord queryWhPackCarRecord(WhPackCarRecordQueryCondition query) {
        Assert.notNull(query);
        WhPackCarRecord whPackCarRecord = whPackCarRecordDao.queryWhPackCarRecord(query);
        return whPackCarRecord;
    }

    /**
     * This method corresponds to the database table wh_pack_car_record
     *
     * @mbggenerated Thu Dec 20 15:57:20 CST 2018
     */
    public List<WhPackCarRecord> queryAllWhPackCarRecords() {
        return whPackCarRecordDao.queryWhPackCarRecordList();
    }

    /**
     * This method corresponds to the database table wh_pack_car_record
     *
     * @mbggenerated Thu Dec 20 15:57:20 CST 2018
     */
    public List<WhPackCarRecord> queryWhPackCarRecords(WhPackCarRecordQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whPackCarRecordDao.queryWhPackCarRecordCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhPackCarRecord>();
            }
        }
        List<WhPackCarRecord> whPackCarRecords = whPackCarRecordDao.queryWhPackCarRecordList(query, pager);
        return whPackCarRecords;
    }

    /**
     * This method corresponds to the database table wh_pack_car_record
     *
     * @mbggenerated Thu Dec 20 15:57:20 CST 2018
     */
    public void createWhPackCarRecord(WhPackCarRecord whPackCarRecord) {
        try {
            whPackCarRecordDao.createWhPackCarRecord(whPackCarRecord);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_pack_car_record
     *
     * @mbggenerated Thu Dec 20 15:57:20 CST 2018
     */
    public void batchCreateWhPackCarRecord(List<WhPackCarRecord> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whPackCarRecordDao.batchCreateWhPackCarRecord(entityList);
            } catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_pack_car_record
     *
     * @mbggenerated Thu Dec 20 15:57:20 CST 2018
     */
    public void deleteWhPackCarRecord(Integer id) {
        try {
            whPackCarRecordDao.deleteWhPackCarRecord(id);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_pack_car_record
     *
     * @mbggenerated Thu Dec 20 15:57:20 CST 2018
     */
    public void updateWhPackCarRecord(WhPackCarRecord whPackCarRecord) {
        try {
            whPackCarRecordDao.updateWhPackCarRecord(whPackCarRecord);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_pack_car_record
     *
     * @mbggenerated Thu Dec 20 15:57:20 CST 2018
     */
    public void batchUpdateWhPackCarRecord(List<WhPackCarRecord> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whPackCarRecordDao.batchUpdateWhPackCarRecord(entityList);
            } catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public WhPackCarRecord scanToPackCar(WhScanShipment scanShipment, WhCollectCompany collectCompany,
                                         String shippingCompanyCode, Integer warehouseId) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String dateStr = simpleDateFormat.format(new Date());
        Date today = null;
        try {
            today = simpleDateFormat.parse(dateStr);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        WhPackCarRecord packCarRecord = null;

        // 保存装车记录
        if (StringUtils.isBlank(shippingCompanyCode)) {
            WhPackCarRecordQueryCondition query = new WhPackCarRecordQueryCondition();
            query.setShippingCompanyCode(collectCompany.getCode());
            query.setLoadDate(new Timestamp(today.getTime()));
            query.setWarehouseId(warehouseId);
            packCarRecord = this.queryWhPackCarRecord(query);
            if (packCarRecord == null) {
                packCarRecord = new WhPackCarRecord();
                packCarRecord.setLoadDate(new Timestamp(today.getTime()));
                packCarRecord.setLoadUser(DataContextHolder.getUserId());
                packCarRecord.setTotalBagNum(1);
                packCarRecord.setShippingCompanyCode(collectCompany.getCode());
                packCarRecord.setShippingCompanyName(collectCompany.getName());
                packCarRecord.setWarehouseId(warehouseId);
                String loadNo = this.generateLoadCodeCode(warehouseId, today);
                packCarRecord.setLoadNo(loadNo);
                whPackCarRecordDao.createWhPackCarRecord(packCarRecord);
            } else {
                packCarRecord.setTotalBagNum(packCarRecord.getTotalBagNum() + 1);
            }
        } else {
            WhPackCarRecordQueryCondition query = new WhPackCarRecordQueryCondition();
            query.setShippingCompanyCode(shippingCompanyCode);
            query.setLoadDate(new Timestamp(today.getTime()));
            query.setWarehouseId(warehouseId);
            packCarRecord = this.queryWhPackCarRecord(query);
            if (packCarRecord == null) {
                return null;
            }
            packCarRecord.setTotalBagNum(packCarRecord.getTotalBagNum() + 1);
        }
        whPackCarRecordDao.updateWhPackCarRecord(packCarRecord);


        // 保存信息 设置装车人及状态
        scanShipment.setLoadUser(DataContextHolder.getUserId());
        scanShipment.setStatus(ScanShipmentStatus.LOADED.intCode());
        scanShipment.setLoadDate(new Timestamp(new Date().getTime()));
        //whScanShipmentService.updateWhScanShipment(scanShipment);
        // 保存结袋卡装车ID
        scanShipment.setPackCarId(packCarRecord.getId());
        whScanShipmentService.updateWhScanShipment(scanShipment);
        SCANSHIPMENTLOG.log(scanShipment.getId(), ScanShipmentLogType.LOADED.getName());
        logger.info("结袋卡装车: bagNo[" + scanShipment.getBagNo() + "]");

        scanShipment.setLocalTruck(true);
        pushScanDataToTms(packCarRecord, scanShipment, false);

        long startTimeMillis = System.currentTimeMillis();
        String apvStep = "Load On Truck";
        String orderProcessStep = "Load On Truck";
        deliverOrderService.updateAllocationProductVoucherStepByScanShipmentId(scanShipment.getId(), apvStep);
        logger.warn("AllocationProductVoucher消耗时间：" + (System.currentTimeMillis() - startTimeMillis));
        return packCarRecord;
    }

    @Override
    public int undoIntoCar(WhPackCarRecord packCarRecord, WhScanShipment scanShipment) {
        // 推送南宁仓的需要先撤回 接口有幂等，放前面
        if (StringUtils.equalsIgnoreCase("YT-EXPRESS",packCarRecord.getShippingCompanyCode())) {
            WhPackCarRecordQueryCondition queryCondition = new WhPackCarRecordQueryCondition();
            queryCondition.setId(packCarRecord.getId());
            queryCondition.setIsNanNingShop(true);
            int packCarRecordCount = whPackCarRecordDao.queryWhPackCarRecordCount(queryCondition);
            //是否推送南宁仓装车信息
            boolean bool = packCarRecordCount > 0 && WarehousePropertyEnum.HHD.intCode()
                    == CacheUtils.getLocalWarehouseId();
            if(bool) {
                WarehouseProperties warehouseProperties = WarehouseProperties.getWarehouseProperties();
                String url =warehouseProperties.getWarehouseIpUrl().entrySet().stream()
                        .filter(entry -> entry.getKey() != warehouseProperties.getLocalWarehouseId())
                        .map(Map.Entry::getValue)
                        .findFirst().get();
                ApiResult apiResult = HttpExtendUtils.get(url+"/scan/whPackCarRecord/undoIntoCarSpan?bagNo="+scanShipment.getBagNo(),
                        HttpUtils.ACCESS_TOKEN, ApiResult.class,30000,30000);
                if (!apiResult.isSuccess())
                    throw new RuntimeException("南宁仓撤回装车失败！");
            }

        }
        int result = 0;
        if (packCarRecord.getTotalBagNum() > 1) {
            packCarRecord.setTotalBagNum(packCarRecord.getTotalBagNum() - 1);
            whPackCarRecordDao.updateWhPackCarRecord(packCarRecord);
        } else {
            whPackCarRecordDao.deleteWhPackCarRecord(packCarRecord.getId());
        }
        whScanShipmentService.undoIntoCar(scanShipment.getId());
        logger.info("结袋卡撤回装车: scanShipmentId[" + scanShipment.getId() + "]");

        pushScanDataToTms(packCarRecord, whScanShipmentService.getWhScanShipment(scanShipment.getId()), true);

        // 修改回状态
        String apvStep = "Shipped Products";
        String orderProcessStep = "Deliver Products";
        deliverOrderService.updateAllocationProductVoucherStepByScanShipmentId(scanShipment.getId(), apvStep);
        return result;
    }

    @Override
    public void pushScanDataToTms(WhPackCarRecord packCarRecord, WhScanShipment scanShipment, boolean isUndo) {
        try {
            executors.execute(new PushScanDataToTms(packCarRecord, scanShipment, isUndo));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public class PushScanDataToTms implements Runnable {
        private WhPackCarRecord packCarRecord;
        private WhScanShipment scanShipment;
        private boolean isUndo;

        public PushScanDataToTms(WhPackCarRecord packCarRecord, WhScanShipment scanShipment, boolean isUndo) {
            this.packCarRecord = packCarRecord;
            this.scanShipment = scanShipment;
            this.isUndo = isUndo;
        }

        @SuppressWarnings("static-access")
        @Override
        public void run() {
            ScanShipment2TmsDTO scanShipmentDto = null;
            List<DeliverOrder2TmsDTO> deliverOrders = null;
            // 撤回装车
            if (isUndo) {
                scanShipmentDto = new ScanShipment2TmsDTO(scanShipment, null, null);
                whScanShipmentService.pushTmsShipmentInfo(scanShipmentDto, null, scanShipment);
                return;
            }
            WhScanShipmentToApvQueryCondition query = new WhScanShipmentToApvQueryCondition();
            query.setScanShipmentId(scanShipment.getId());
            List<String> apvNos = whScanShipmentToApvService.queryApvNoList(query);
            if (CollectionUtils.isEmpty(apvNos)) {
                logger.info("结袋卡apvNos为空: scanShipment[" + scanShipment + "]");
                return;
            }
            WhApvQueryCondition queryApv = new WhApvQueryCondition();
            queryApv.setApvNo(StringUtils.join(apvNos, ","));
            queryApv.setDownload(true);
            queryApv.setExportType("1");
            List<WhApv> apvs = whApvService.queryWhApvAndItemList(queryApv, null);
            // 存心新yst时，则获取新的yst 推送给TMS
            List<String> newApvNo = apvs.stream().map(a ->
                    StringUtils.isNotBlank(a.getPaymentStatus()) ? a.getPaymentStatus() : a.getApvNo()).collect(Collectors.toList());
            // 获取追踪号
            String trackingNumbers = apvs.stream().map(a -> (StringUtils.isNotBlank(a.getTrackingNumber()) ? a.getTrackingNumber() : a.getServiceProviderNo()))
                    .collect(Collectors.joining(","));
            scanShipmentDto = new ScanShipment2TmsDTO(scanShipment, packCarRecord, newApvNo, trackingNumbers);

            deliverOrders = TranslateApvToDeliverOrder2TmsDTOUtils.translateApvToDeliverOrder(apvs, scanShipment, scanShipmentDto);
            if (CollectionUtils.isEmpty(deliverOrders)) {
                logger.info("结袋卡deliverOrders为空: scanShipment[" + scanShipment + "]");
                return;
            }

            boolean bool = true;
            if (scanShipment.getLocalTruck()) {
                try {
                    whScanShipmentService.pushTmsShipmentInfo(scanShipmentDto, deliverOrders, scanShipment);
                } catch (Exception e) {
                    bool = false;
                    logger.error("消息队列推送结袋卡数据到TMS失败!" + e.getMessage());
                }

            } else {
                //HTTP 请求
                ApiResult apiResult1 = whScanShipmentService.pushScanShipmentToTms(scanShipmentDto);
                ApiResult apiResult2 = whScanShipmentService.pushScanDeliverOrdersToTms(deliverOrders);
                WhScanShipment updateScanShipment = new WhScanShipment();
                updateScanShipment.setId(scanShipment.getId());
                updateScanShipment.setPushToTmsQuantity(
                        scanShipment.getPushToTmsQuantity() == null ? 0 : scanShipment.getPushToTmsQuantity() + 1);
                if (!apiResult1.isSuccess() || !apiResult2.isSuccess()) {
                    bool = false;
                }

            }

            // 重试机制,重试5次
            if (!bool) {
                boolean success = false;
                for (int i = 0; i < 5; i++) {
                    if (!success) {
                        try {
                            // 五秒后重试(失败重试5次)
                            Thread.currentThread().sleep(5000);
                            logger.info("等待5秒后重试推送结袋卡到TMS: scanShipment[" + scanShipment + "]");
                        } catch (InterruptedException e) {
                            log.error(e.getMessage(), e);
                        }
                        if (scanShipment.getLocalTruck()) {
                            try {
                                success = true;
                                whScanShipmentService.pushTmsShipmentInfo(scanShipmentDto, deliverOrders, scanShipment);
                            } catch (Exception e) {
                                success = false;
                                logger.error("消息队列推送结袋卡及装车数据到TMS失败!" + e.getMessage());
                            }
                        } else {
                            success = whScanShipmentService.pushScanShipmentToTmsById(scanShipment.getId());
                        }
                    } else {
                        break;
                    }
                }
            }
        }

    }

    /**
     * 生成发货编码
     *
     * @param warehouseId
     * @param today
     * @return
     */
    @Override
    public String generateLoadCodeCode(Integer warehouseId, Date today) {
        WhPackCarRecordQueryCondition query = new WhPackCarRecordQueryCondition();
        query.setLoadDate(new Timestamp(today.getTime()));
        query.setWarehouseId(warehouseId);
        List<WhPackCarRecord> packCarRecords = this.queryWhPackCarRecords(query, null);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String dateStr = simpleDateFormat.format(today);
        String warehouseCode = "";
        if (warehouseId.equals(1)) {
            warehouseCode = "HHD";
        } else {
            warehouseCode = "MJ";
        }
        if (packCarRecords != null && packCarRecords.size() > 0) {
            Collections.sort(packCarRecords, new Comparator<WhPackCarRecord>() {
                @Override
                public int compare(WhPackCarRecord o1, WhPackCarRecord o2) {
                    return o2.getLoadNo().compareTo(o1.getLoadNo());
                }
            });
            String currentMaxLoadNo = packCarRecords.get(0).getLoadNo();
            if (StringUtils.isEmpty(currentMaxLoadNo) || currentMaxLoadNo.length() < 4) {
                return null;
            }
            String lastLoadNo = currentMaxLoadNo.substring(currentMaxLoadNo.length() - 3, currentMaxLoadNo.length());// 获取后三位

            if (lastLoadNo != null && lastLoadNo.length() > 0) {
                // 如果最后一组没有数字(也就是不以数字结尾)，抛NumberFormatException异常
                int n = lastLoadNo.length();// 取出字符串的长度
                int num = Integer.parseInt(lastLoadNo) + 1;// 将该数字加一
                String added = String.valueOf(num);
                n = Math.min(n, added.length());
                return currentMaxLoadNo.subSequence(0, currentMaxLoadNo.length() - n) + added;// 拼接字符串
            } else {
                return null;
            }
        } else {
            return warehouseCode + dateStr + "001";
        }
    }

    /**
     * @return void
     * @Description 装车
     * <AUTHOR>
     * @date 2020/8/5 9:40
     * @param: scanShipment
     * @param: type {local,transfer,temu}
     */
    @Override
    public void updateToload(WhScanShipment scanShipment, String type) {
        // 保存信息 设置装车人及状态
        scanShipment.setLoadUser(DataContextHolder.getUserId());
        scanShipment.setStatus(ScanShipmentStatus.LOADED.intCode());
        scanShipment.setLoadDate(new Timestamp(System.currentTimeMillis()));
        whScanShipmentService.updateWhScanShipment(scanShipment);
        logger.info("结袋卡装车: bagNo[" + scanShipment.getBagNo() + "] type:" + type);
        String apvStep = "Load On Truck";
        if ("transfer".equals(type)) {
            deliverOrderService.updateAllocationProductVoucherStepByScanShipmentIdTransfer(scanShipment.getId(), apvStep);
        } else if ("temu".equals(type)) {
            deliverOrderService.updateAllocationProductVoucherStepByScanShipmentIdTemu(scanShipment.getId(), apvStep);
        } else {
            deliverOrderService.updateAllocationProductVoucherStepByScanShipmentId(scanShipment.getId(), apvStep);
        }
    }


    @Override
    public WhPackCarRecord doGeneratePackCarRecord(List<WhScanShipment> whScanShipments, WhCollectCompany collectCompany, Integer isTransfer) {
        Date today = DateUtils.stringToDate(DateUtils.dateToString(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
        Integer warehouseId = CacheUtils.getLocalWarehouseId();
        WhPackCarRecord packCarRecord = new WhPackCarRecord();
        packCarRecord.setLoadDate(new Timestamp(today.getTime()));
        packCarRecord.setLoadUser(DataContextHolder.getUserId());
        packCarRecord.setTotalBagNum(whScanShipments.size());
        packCarRecord.setShippingCompanyCode(collectCompany.getCode());
        packCarRecord.setShippingCompanyName(collectCompany.getName());
        packCarRecord.setWarehouseId(warehouseId);
        packCarRecord.setIsTransfer(isTransfer);
        String loadNo = generateLoadCodeCode(warehouseId, today);
        packCarRecord.setLoadNo(loadNo);
        if (LoadTypeEnum.LOCAL.getCode().equals(isTransfer)){
            String expressCompany = whScanShipments.get(0).getExpressCompany();
            if (StringUtils.isBlank(expressCompany)) {
                // 通过普通装车提交的
                packCarRecord.setDeliveryType(DeliveryTypeEnum.TAKE.intCode());
            } else {
                if (ShippingCompanyEnum.HUOLALA.getCode().equals(expressCompany)) {
                    // 通过快递自发装车且快递公司为货拉拉
                    packCarRecord.setDeliveryType(DeliveryTypeEnum.HUOLALA.intCode());
                } else {
                    // 通过快递自发装车且快递公司非货拉拉
                    packCarRecord.setDeliveryType(DeliveryTypeEnum.EXPRESS.intCode());
                }
            }
        }
        if (LoadTypeEnum.TRANSFER.getCode().equals(isTransfer)){
            String platform = whScanShipments
                    .stream()
                    .map(WhScanShipment::getPlatform)
                    .filter(StringUtils::isNotBlank)
                    .findFirst()
                    .orElse(null);
            if ("TEMU".equals(platform)) {
                packCarRecord.setPlatformType(PlatformTypeEnum.PDD.intCode());
            }
            if ("SHEIN".equals(platform)) {
                packCarRecord.setPlatformType(PlatformTypeEnum.SHEIN.intCode());
            }
            if ("SMTJIT".equals(platform)) {
                packCarRecord.setPlatformType(PlatformTypeEnum.STM_WAREHOUSE.intCode());
            }
            if ("ASN".equals(platform)) {
                packCarRecord.setPlatformType(PlatformTypeEnum.STM_WAREHOUSE.intCode());
            }
        }

        whPackCarRecordDao.createWhPackCarRecord(packCarRecord);

        for (WhScanShipment whScanShipment : whScanShipments) {
            //本地装车
            whScanShipment.setLocalTruck(true);
            // 保存结袋卡装车ID
            whScanShipment.setPackCarId(packCarRecord.getId());
            whScanShipmentService.updateWhScanShipment(whScanShipment);
            logger.info("结袋卡装车汇总: bagNo[ " + whScanShipment.getBagNo() + " ]");
            if(!LoadTypeEnum.TRANSFER.getCode().equals(isTransfer)) {
                pushScanDataToTms(packCarRecord, whScanShipment, false);
            }
        }

        if (StringUtils.equalsIgnoreCase("YT-EXPRESS",packCarRecord.getShippingCompanyCode())) {
            WhPackCarRecordQueryCondition queryCondition = new WhPackCarRecordQueryCondition();
            queryCondition.setId(packCarRecord.getId());
            queryCondition.setIsNanNingShop(true);
            int packCarRecordCount = whPackCarRecordDao.queryWhPackCarRecordCount(queryCondition);
            //是否南宁仓装车信息
            // 深圳仓装车南宁仓单据时，将装车数据同步到南宁仓
            boolean bool = packCarRecordCount > 0 && WarehousePropertyEnum.HHD.intCode()
                    == CacheUtils.getLocalWarehouseId();
            Map<String,Object> packCarRecordMap = new HashMap<>(2);
            List<Map<String,Object>> whScanShipmentList = new ArrayList<>(whScanShipments.size());
            if (bool) {
                packCarRecordMap.put("whPackCarRecord",JSONObject.toJSONString(packCarRecord));
                for (WhScanShipment whScanShipment : whScanShipments) {
                    Map<String,Object> whScanShipmentMap = new HashMap<>();
                    WhScanShipmentToApvQueryCondition apvQueryCondition = new WhScanShipmentToApvQueryCondition();
                    apvQueryCondition.setScanShipmentId(whScanShipment.getId());
                    List<WhScanShipmentToApv> apvs = whScanShipmentToApvService.queryWhScanShipmentToApvs(apvQueryCondition,null);
                    whScanShipmentMap.put("whScanShipment",JSONObject.toJSONString(whScanShipment));
                    whScanShipmentMap.put("apvs",JSONObject.toJSONString(apvs));
                    whScanShipmentList.add(whScanShipmentMap);
                }
                packCarRecordMap.put("whScanShipmentList",JSONObject.toJSONString(whScanShipmentList));
                WarehouseProperties warehouseProperties = WarehouseProperties.getWarehouseProperties();
                String url =warehouseProperties.getWarehouseIpUrl().entrySet().stream()
                        .filter(entry -> entry.getKey() != warehouseProperties.getLocalWarehouseId())
                        .map(Map.Entry::getValue)
                        .findFirst().get();
                Map<String, String> headers = new HashMap<>();
                headers.put("Authorization", HttpUtils.ACCESS_TOKEN);
                headers.put("Content-Type", "application/json; charset=utf-8");
                String responseStr = HttpExtendUtils.post(url+"/scan/whPackCarRecord/acceptPackCarRecord",
                        headers, JSONObject.toJSONString(packCarRecordMap), String.class,30000,30000);
                ApiResult response = JSON.parseObject(responseStr, ApiResult.class);
                if (response == null || !response.isSuccess()) {
                    log.error("同步装车数据到南宁仓失败，"+JSONObject.toJSONString(response));
                    throw new BusinessException("同步装车数据到南宁仓失败，"+JSONObject.toJSONString(response));
                }
            }
        }
        return packCarRecord;
    }

    // 接收深圳装车数据
    @Override
    public void doAcceptackCarRecord(Map<String, Object> params) throws IOException {
        log.info("doAcceptackCarRecord param"+ JSONObject.toJSONString(params));
        ObjectMapper objectMapper = new ObjectMapper();
        WhPackCarRecord whPackCarRecord = objectMapper.readValue(params.get("whPackCarRecord").toString(), WhPackCarRecord.class);
        List<Map<String, Object>> list = objectMapper.readValue(params.get("whScanShipmentList").toString(),
                new TypeReference<List<Map<String, Object>>>(){});
        whPackCarRecord.setId(null);
        createWhPackCarRecord(whPackCarRecord);
        for (Map<String, Object> map :list) {
            WhScanShipment whScanShipment = JSONObject.parseObject(map.get("whScanShipment").toString(), WhScanShipment.class);
            whScanShipment.setId(null);
            whScanShipment.setPackCarId(whPackCarRecord.getId());
            List<WhScanShipmentToApv> whScanShipmentToApvs = objectMapper.readValue(map.get("apvs").toString(),
                    new TypeReference<List<WhScanShipmentToApv>>(){});
            whScanShipmentService.createWhScanShipment(whScanShipment);

            whScanShipmentToApvs.stream().forEach(w -> {
                w.setId(null);
                w.setScanShipmentId(whScanShipment.getId());
            });
            whScanShipmentToApvService.batchCreateWhScanShipmentToApv(whScanShipmentToApvs);
        }
    }

    // 跨仓撤回
    @Override
    public void undoIntoCarSpan(WhPackCarRecord packCarRecord, Integer scanShipmentId) {
        if (packCarRecord != null) {
            if (packCarRecord.getTotalBagNum() > 1) {
                packCarRecord.setTotalBagNum(packCarRecord.getTotalBagNum() - 1);
                whPackCarRecordDao.updateWhPackCarRecord(packCarRecord);
            } else {
                whPackCarRecordDao.deleteWhPackCarRecord(packCarRecord.getId());
            }
        }
        whScanShipmentService.deleteWhScanShipmentAndApv(scanShipmentId);
        SCANSHIPMENTLOG.log(scanShipmentId, "深圳仓撤回装车扫描结袋卡");
        logger.info("结袋卡撤回装车: scanShipmentId[" + scanShipmentId + "]");

    }

    @Override
    public void warehouseSubmissionCarLoad(List<WhFbaAllocation> whFbaAllocationList) {
        String companyName = whFbaAllocationList
                .stream()
                .map(WhFbaAllocation::getItems)
                .flatMap(Collection::stream)
                .map(WhFbaAllocationItem::getCompanyName)
                .filter(StringUtils::isNotBlank)
                .findFirst()
                .orElse(null);
        long count = whFbaAllocationList
                .stream()
                .map(WhFbaAllocation::getItems)
                .flatMap(Collection::stream)
                .map(WhFbaAllocationItem::getTemuTagUrl)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .count();
        Date today = DateUtils.stringToDate(DateUtils.dateToString(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
        Integer warehouseId = WarehouseProperties.getWarehouseProperties().getLocalWarehouseId();
        WhPackCarRecord packCarRecord =new WhPackCarRecord();
        packCarRecord.setLoadDate(new Timestamp(today.getTime()));
        packCarRecord.setLoadUser(DataContextHolder.getUserId());
        packCarRecord.setTotalBagNum((int) count);
        packCarRecord.setShippingCompanyCode(ShippingCompanyEnum.SMTCF.getCode());
        packCarRecord.setShippingCompanyName(ShippingCompanyEnum.SMTCF.getName());
        packCarRecord.setWarehouseId(warehouseId);
        packCarRecord.setIsTransfer(LoadTypeEnum.TRANSFER.getCode());
        String loadNo = generateLoadCodeCode(warehouseId, today);
        packCarRecord.setLoadNo(loadNo);
        packCarRecord.setPlatformType(PlatformTypeEnum.STM_WAREHOUSE.intCode());
        whPackCarRecordDao.createWhPackCarRecord(packCarRecord);

        List<WhFbaAllocation> updateFbaList=new ArrayList<>();
        for (WhFbaAllocation whFbaAllocation : whFbaAllocationList) {
            WhFbaAllocation updateFba=new WhFbaAllocation();
            updateFba.setId(whFbaAllocation.getId());
            updateFba.setLoadId(packCarRecord.getId());
            updateFbaList.add(updateFba);
        }
        if (CollectionUtils.isNotEmpty(updateFbaList)) {
            whFbaAllocationService.batchUpdateWhFbaAllocation(updateFbaList);
        }

    }
}