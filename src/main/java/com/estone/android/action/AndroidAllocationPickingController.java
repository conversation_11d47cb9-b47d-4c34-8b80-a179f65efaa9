package com.estone.android.action;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.estone.allocation.bean.*;
import com.estone.allocation.bean.dto.LoadingSubmitData;
import com.estone.allocation.dao.WhApvAllocationDao;
import com.estone.allocation.enums.*;
import com.estone.allocation.service.*;
import com.estone.android.domain.AndroidProductDo;
import com.estone.android.service.AndroidAllocationPickingService;
import com.estone.apv.common.ApvTaskRedisLock;
import com.estone.common.util.*;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;

/**
 * Android调拨拣货控制器
 * 
 * <p><b>装车接口使用指南：</b></p>
 * <ul>
 *   <li><b>allocationLoading</b> - PDA扫描箱号接口，返回扫描结果和装车进度</li>
 *   <li><b>submitAllocationLoading</b> - 批量装车提交接口（支持扫描模式和直接批量模式）</li>
 * </ul>
 * 
 * <p><b>使用流程：</b></p>
 * <ol>
 *   <li><b>交互式扫描模式</b>：先调用 allocationLoading 扫描箱号，累积扫描后调用 submitAllocationLoading 提交</li>
 *   <li><b>直接批量模式</b>：直接调用 submitAllocationLoading，传入已知的调拨单号和箱号列表</li>
 * </ol>
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping(value = "android/allocationPickings")
public class AndroidAllocationPickingController {

    private Logger logger = LoggerFactory.getLogger(AndroidAllocationPickingController.class);

    @Resource
    private WhApvAllocationService whApvAllocationService;

    @Resource
    private WhApvAllocationBoardService whApvAllocationBoardService;

    @Resource
    private WhApvAllocationOrderService whApvAllocationOrderService;

    @Resource
    private WhApvAllocationDemandService whApvAllocationDemandService;

    @Resource
    private AndroidAllocationPickingService androidAllocationPickingService;

    @Resource
    private WhAllocationPickingService whAllocationPickingService;

    @Resource
    private WhStockAllocationPickingService whStockAllocationPickingService;

    @Resource
    private WhApvAllocationDao whApvAllocationDao;

    @Resource
    private WhApvAllocationPickingService whApvAllocationPickingService;

    @Resource
    private WhAsnAllocationPickingService whAsnAllocationPickingService;

    @Resource
    private WhAllocationHandleService whAllocationHandleService;

    /**
     *
     * @Description: 获取调拨拣货列表
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/03/15
     * @Version: 0.0.1
     */
    @RequestMapping(value = "allocationPickingTaskList", method = { RequestMethod.POST })
    public ResponseJson allocationPickingTaskList(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android allocationPickingTaskList ");
        /**
         * 老库存逻辑
         * */
        /*return androidAllocationPickingService.allocationPickingTaskList2(domain);*/

        // TODO 新库存逻辑
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        try {
            return whAllocationPickingService.updateAndReceiveAllocationPickingTask(domain);
        } catch (Exception e) {
            if (StringUtils.isNotBlank(e.getMessage())) {
                responseJson.setMessage(e.getMessage());
            } else {
                responseJson.setMessage("获取拣货列表失败!");
            }
        }
        return responseJson;
    }

    /**
     *
     * @Description: 刷新调拨列表
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/05/23
     * @Version: 0.0.1
     */
    @RequestMapping(value = "refreshAllocationPickingTaskList", method = { RequestMethod.POST })
    public ResponseJson refreshAllocationPickingTaskList(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android refreshAllocationPickingTaskList ");
        return androidAllocationPickingService.refreshAllocationPickingTaskList(domain);
    }

    /**
     *
     * @Description: 调拨拣货下一步
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/03/26
     * @Version: 0.0.1
     */
    @RequestMapping(value = "allocationPicking", method = { RequestMethod.POST })
    public ResponseJson allocationPicking(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android allocationPicking ");
        /**
         * 老库存逻辑
         *
         * */
        /*return androidAllocationPickingService.allocationPicking2(domain);*/

        // TODO 新库存逻辑
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        try {
            List<String> skus = new ArrayList<>();
            skus.add(domain.getSku());
            return whAllocationPickingService.updateAndCompleteAllocationPickingTaskItem(skus, domain);
        }catch (Exception e) {
            if (StringUtils.isNotBlank(e.getMessage())) {
                responseJson.setMessage(e.getMessage());
            } else {
                responseJson.setMessage("下一步操作修改失败！");
            }
        }
        return responseJson;
    }

    /**
     * @Description: 库存调拨拣货下一步校验预留数量
     *
     * @param domain
     * @return
     * @Author: Administrator
     * @Date: 2019/10/18
     * @Version: 0.0.1
     */
    @RequestMapping(value = "reservedPickQuantity", method = { RequestMethod.POST })
    public ResponseJson reservedPickQuantity(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android reservedPickQuantity ");
        return androidAllocationPickingService.queryReservedPickQuantity(domain.getSku());
    }

    /**
     *
     * @Description:结束调拨拣货
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/03/26
     * @Version: 0.0.1
     */
    @RequestMapping(value = "endAllocationPickingTask", method = { RequestMethod.POST })
    public ResponseJson endAllocationPickingTask(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android endAllocationPickingTask ");
        try {
            /**
             * 老库存逻辑
             * */
            /*return androidAllocationPickingService.endAllocationPickingTask2(domain);*/

            // TODO 新库存逻辑
            return whAllocationPickingService.completeAllocationPickingTask(domain);
        }
        catch (Exception e) {
            logger.error("调拨结束拣货失败", e);
            ResponseJson response = new ResponseJson();
            response.setStatus(StatusCode.FAIL);
            response.setMessage("结束拣货失败，原因：" + e.getMessage());
            return response;
        }
    }

    /**
     *
     * @Description:获取装箱列表
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/03/15
     * @Version: 0.0.1
     */
    @RequestMapping(value = "allocationBoxingList", method = { RequestMethod.POST })
    public ResponseJson allocationBoxingList(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android allocationBoxingList ");
        return androidAllocationPickingService.allocationBoxingList(domain);
    }


    @RequestMapping(value = "checkBoxNumber", method = { RequestMethod.POST })
    public ResponseJson checkBoxNumber(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android checkBoxNumber ");
        return androidAllocationPickingService.checkBoxNumber(domain);
    }
    /**
     *
     * @Description:装箱下一步接口
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/03/15
     * @Version: 0.0.1
     */
    @RequestMapping(value = "allocationBoxing", method = { RequestMethod.POST })
    public ResponseJson allocationBoxing(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android allocationBoxing ");
        return androidAllocationPickingService.allocationBoxing(domain);
    }

    /**
     *
     * @Description: 装箱完成
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/03/15
     * @Version: 0.0.1
     */
    @RequestMapping(value = "boxingComplete", method = { RequestMethod.POST })
    public ResponseJson boxingComplete(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android boxingComplete ");
        /**
         * 老库存逻辑
         * */
        /*return androidAllocationPickingService.boxingComplete(domain);*/

        // TODO 新库存逻辑
        try {
            return whStockAllocationPickingService.updateAndCompleteBoxing(domain);
        }
        catch (Exception e) {
            logger.error("装箱完成失败！", e);
            ResponseJson response = new ResponseJson();
            response.setStatus(StatusCode.FAIL);
            response.setMessage("装箱完成失败，原因：" + e.getMessage());
            return response;
        }

    }

    /**
     *
     * @Description: 打板
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/03/15
     * @Version: 0.0.1
     */
    @RequestMapping(value = "allocationBeating", method = { RequestMethod.POST })
    public ResponseJson allocationBeating(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android allocationBeating ");
        return androidAllocationPickingService.allocationBeating(domain);
    }

    /**
     *
     * @Description: 装车（修改为扫描接口）
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/03/15
     * @Version: 0.0.1
     */
    @PostMapping(value = "allocationLoading")
    public ResponseJson allocationLoading(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android allocationLoading - 扫描箱号");
        
        // 新的扫描箱号逻辑，不执行装车业务
        return whAllocationHandleService.scanAllocationBox(domain);
    }

    /**
     * 提交装车接口
     * 
     * @param submitData 提交数据（包含送货方式、物流单号和LoadingBoxInfo列表）
     * @return 装车处理结果
     */
    @PostMapping(value = "submitAllocationLoading")
    @ResponseBody
    public ResponseJson submitAllocationLoading(@RequestBody LoadingSubmitData submitData) {
        logger.info("android submitAllocationLoading - 批量装车处理");
        
        try {
            // 统一使用扫描服务的提交方法，内部会处理锁机制和业务逻辑
            return whAllocationHandleService.updateAndLoading(submitData);
        }
        catch (Exception e) {
            logger.error("批量装车失败", e);
            ResponseJson response = new ResponseJson();
            response.setStatus(StatusCode.FAIL);
            response.setMessage("装车失败：" + e.getMessage());
            return response;
        }
        finally {
            // 无论成功还是失败都清理Redis扫描记录
            if (submitData != null && CollectionUtils.isNotEmpty(submitData.getItemList())
                    && submitData.getItemList().stream().noneMatch(s -> StringUtils.isBlank(s.getAllocationNo()))) {
                submitData.getItemList().stream().map(LoadingSubmitData.AllocationItem::getAllocationNo).distinct()
                        .forEach(allocationNo -> StringRedisUtils
                                .del(RedisConstant.ALLOCATION_SCAN_KEY_PREFIX + allocationNo));
            }
        }
    }



    /**
     *
     * @Description: 获取订单调拨列表（根据调拨单号查出调拨需求列表）
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/03/30
     * @Version: 0.0.1
     */
    @RequestMapping(value = "orderAllocationList", method = { RequestMethod.POST })
    public ResponseJson orderAllocationList(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android orderAllocationList ");
        return androidAllocationPickingService.orderAllocationList(domain);
    }

    /**
     *
     * @Description: 订单调拨需求(调拨子任务)绑定播种箱号
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/03/30
     * @Version: 0.0.1
     */
    @RequestMapping(value = "orderAllocation", method = { RequestMethod.POST })
    public ResponseJson orderAllocation(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android orderAllocation ");
        return androidAllocationPickingService.orderAllocation(domain);
    }

    /**
     *
     * @Description: 订单调拨播种
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/04/09
     * @Version: 0.0.1
     */
    @RequestMapping(value = "orderAllocationSow", method = { RequestMethod.POST })
    public ResponseJson orderAllocationSow(@ModelAttribute("domain") AndroidProductDo domain) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        logger.info("android orderAllocationSow ");
        //兼容SKU编码和唯一码
        domain.setSku(CompatibleSkuUtils.getSkuForPda(domain.getSku()));
        String allocationNo = domain.getAllocationNo();
        if (StringUtils.isBlank(allocationNo)) {
            responseJson.setMessage("订单调拨单号为空！");
            return responseJson;
        }
        String lockKey = "SOW_ALLOCATION:".concat(allocationNo);
        try {
            if (RedissonLockUtil.tryLock(lockKey, TimeUnit.SECONDS, 30, 180)) {
                responseJson = androidAllocationPickingService.doOrderAllocationSow(domain);
            } else {
                responseJson.setMessage("调拨单：" + allocationNo + "，sku：" + domain.getSku() + "播种加锁失败");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            RedissonLockUtil.unlock(lockKey);
        }
        return responseJson;
    }

    /**
     *
     * @Description: 订单调拨播种完成检测
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/04/09
     * @Version: 0.0.1
     */
    @RequestMapping(value = "orderAllocationSowCheck", method = { RequestMethod.POST })
    public ResponseJson orderAllocationSowCheck(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android orderAllocationSowCheck ");
        return androidAllocationPickingService.orderAllocationSowCheck(domain);
    }

    /**
     *
     * @Description: 订单调拨播种完成
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/04/09
     * @Version: 0.0.1
     */
    @RequestMapping(value = "orderAllocationSowFinish", method = { RequestMethod.POST })
    public ResponseJson orderAllocationSowFinish(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android orderAllocationSowFinish ");
        return androidAllocationPickingService.orderAllocationSowFinish(domain);
    }

    /**
     *
     * @Description: 订单调拨打板
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/04/10
     * @Version: 0.0.1
     */
    @RequestMapping(value = "orderAllocationBeating", method = { RequestMethod.POST })
    public ResponseJson orderAllocationBeating(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android orderAllocationBeating ");
        return androidAllocationPickingService.orderAllocationBeating(domain);
    }

    /**
     *
     * @Description: 订单调拨装车
     *
     * @param domain
     * @return
     * @return: ResponseJson
     * @Author: qinyangkai
     * @Date: 2019/04/10
     * @Version: 0.0.1
     */
    @RequestMapping(value = "orderAllocationLoading", method = { RequestMethod.POST })
    public ResponseJson orderAllocationLoading(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android orderAllocationLoading ");
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        String allocationBoardNo = domain.getAllocationBoardNo();
        String subTaskNo = domain.getSubTaskNo();
        if (StringUtils.isBlank(allocationBoardNo) && StringUtils.isBlank(subTaskNo)) {
            responseJson.setMessage("请输入卡板号或子任务号");
            return responseJson;
        }

        WhApvAllocationBoard allocationBoard = null;

        if (StringUtils.isNotBlank(allocationBoardNo)) {
            Pattern patternBOARD = Pattern.compile("([1-2]{1})([K-K]{1})([B-B]{1})([0-9]{4})([0-9]{0,})");
            Matcher matcherBOARD = patternBOARD.matcher(allocationBoardNo);
            if (!matcherBOARD.matches()) {
                responseJson.setMessage("卡板号无效！");
                return responseJson;
            }

            WhApvAllocationBoardQueryCondition queryBOARD = new WhApvAllocationBoardQueryCondition();
            queryBOARD.setBoardType(AllocationBoardTypeEnum.BOARD.intCode());
            queryBOARD.setBoardNo(domain.getAllocationBoardNo());
            WhApvAllocationBoard BOARD = whApvAllocationBoardService.queryWhApvAllocationBoard(queryBOARD);

            if (BOARD == null) {
                responseJson.setMessage("卡板号错误：请检查卡板号是否可以使用！");
                return responseJson;
            }
            allocationBoard = BOARD;
        }

        if (StringUtils.isNotBlank(subTaskNo)) {
            Pattern patternSubTaskNo = Pattern.compile("([B-B]{1})([J-J]{1})([H-H]{1})([0-9]{0,})");
            Matcher matcherSubTaskNo = patternSubTaskNo.matcher(subTaskNo);
            if (!matcherSubTaskNo.matches()) {
                responseJson.setMessage("子任务号无效！");
                return responseJson;
            }

            WhApvAllocationDemandQueryCondition query = new WhApvAllocationDemandQueryCondition();
            query.setTaskNo(subTaskNo);
            WhApvAllocationDemand Demand = whApvAllocationDemandService.queryWhApvAllocationDemand(query);

            if (Demand == null) {
                responseJson.setMessage("子任务号不存在！");
                return responseJson;
            }

            if (!Demand.getTaskStatus().equals(AllocationDemandTaskStatus.FINISH.intCode())) {
                responseJson.setMessage("子任务号状态必须为已完成！");
                return responseJson;
            }
        }

        if (JedisUtils.exists(
                ApvTaskRedisLock.PDA_ORDER_ALLOCATION_LOADING.getName() + "-" + allocationBoardNo + "-" + subTaskNo)) {
            responseJson.setMessage("该调拨单正在装车，请稍后再试！");
            return responseJson;
        }
        else {
            JedisUtils.set(
                    ApvTaskRedisLock.PDA_ORDER_ALLOCATION_LOADING.getName() + "-" + allocationBoardNo + "-" + subTaskNo,
                    "lock", 600L);
        }

        try {
            responseJson = loading(allocationBoardNo, subTaskNo, allocationBoard);
        }
        catch (Exception e){
            responseJson.setMessage(e.getMessage());
            responseJson.setStatus(StatusCode.FAIL);
        }
        finally {
            JedisUtils.del(ApvTaskRedisLock.PDA_ORDER_ALLOCATION_LOADING.getName() + "-" + allocationBoardNo + "-"
                    + subTaskNo);
        }
        return responseJson;
    }

    public ResponseJson loading(String boardNo, String subTaskNo, WhApvAllocationBoard allocationBoard){
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        WhApvAllocationQueryCondition aQuery = new WhApvAllocationQueryCondition();
        aQuery.setBoardNo(boardNo);
        aQuery.setSubTaskNo(subTaskNo);
        aQuery.setIsDiscard(AllocationStatusEnum.DISCARD.intCode());
        List<WhApvAllocation> list = whApvAllocationDao.queryOrderAllocationDetailList(aQuery, null);

        if (CollectionUtils.isEmpty(list)) {
            response.setMessage("没有绑定该号码的调拨单");
            return response;
        }
        /** 调拨单号 */
        List<Integer> allocationIdList = new ArrayList<Integer>();
        List<String> allocationNoList = new ArrayList<>();
        List<String> skus = new ArrayList<>();
        Map<String, WhApvAllocationOrderSku> orderSkuMap = new HashMap<>();
        for (WhApvAllocation entity : list) {
            Integer status = entity.getAllocationStatus();

            // 如果有不等于待装车状态的 都不可以进行装车
            if (!Integer.valueOf(AllocationOrderStatusEnum.WAIT_LOAD.intCode()).equals(status)) {
                response.setMessage("不可进行装车，调拨单：" + entity.getAllocationNo() + "状态："
                        + AllocationOrderStatusEnum.getNameByCode(status + ""));
                return response;
            }

            List<WhApvAllocationOrderItem> itemList = entity.getAllocationOrderItems();
            for (WhApvAllocationOrderItem item : itemList) {
                Integer loadStatus = item.getLoadStatus();
                if (Integer.valueOf(AllocationLoadStatusEnum.LOADING.intCode()).equals(loadStatus)) {
                    response.setMessage("该号码已装车，不可重复装车，调拨单号" + entity.getAllocationNo());
                    return response;
                }
            }

            allocationIdList.add(entity.getAllocationId());
            allocationNoList.add(entity.getAllocationNo());

            WhApvAllocationDemandQueryCondition queryCondition = new WhApvAllocationDemandQueryCondition();
            queryCondition.setAllocationNo(entity.getAllocationNo());
            queryCondition.setIsPick(1);
            List<WhApvAllocationDemandSku> demandSkuList = whApvAllocationDemandService
                    .queryWhApvAllocationDemandSkuList(queryCondition);
            if (CollectionUtils.isNotEmpty(demandSkuList)){
                for (WhApvAllocationDemandSku demandSku: demandSkuList){
                    if (demandSku.getSowQuantity() > 0){
                        WhApvAllocationOrderSku orderSku = orderSkuMap.get(demandSku.getSku());
                        if (orderSku == null){
                            orderSku = new WhApvAllocationOrderSku();
                            orderSku.setQuantity(0);
                            orderSku.setSku(demandSku.getSku());
                        }
                        orderSku.setQuantity(orderSku.getQuantity() + demandSku.getSowQuantity());
                        orderSku.setAllocationNo(entity.getAllocationNo());
                        if (!skus.contains(demandSku.getSku())){
                            skus.add(demandSku.getSku());
                        }
                        orderSkuMap.put(demandSku.getSku(), orderSku);
                    }
                    // start 2020-07-10 DUKE: 组装拣货>播种的数据, 退(pickQuantity - sowQuantity)至已拣返架
                    /*Integer saleQuantity = demandSku.getQuantity() == null ? 0 : demandSku.getQuantity();
                    Integer pickQuantity = demandSku.getPickQuantity() == null ? 0 : demandSku.getPickQuantity();
                    Integer sowQuantity = demandSku.getSowQuantity() == null ? 0 : demandSku.getSowQuantity();
                    if (saleQuantity.equals(pickQuantity) && pickQuantity > sowQuantity){
                        WhApvAllocationOrderSku orderSku = orderSkuMap.get(demandSku.getSku());
                        if (orderSku == null){
                            orderSku = new WhApvAllocationOrderSku();
                            orderSku.setQuantity(0);
                            orderSku.setPickQuantity(0);
                            orderSku.setSku(demandSku.getSku());
                        }
                        Integer returnPick = orderSku.getPickQuantity() == null ? 0: orderSku.getPickQuantity();
                        orderSku.setPickQuantity(returnPick + (pickQuantity - sowQuantity));
                        orderSku.setAllocationNo(entity.getAllocationNo());
                        if (!skus.contains(demandSku.getSku())){
                            skus.add(demandSku.getSku());
                        }
                        orderSkuMap.put(demandSku.getSku(), orderSku);
                    }*/
                    // end 2020-07-10 DUKE: 组装拣货>播种的数据, 退(pickQuantity - sowQuantity)至已拣返架
                }
            }
        }
        List<WhApvAllocationOrderItem> orderItems = whApvAllocationOrderService.queryWhApvAllocationOrderItems(allocationIdList);
        if (CollectionUtils.isEmpty(orderItems)){
            response.setMessage("调拨单未匹配到订单调任务拨详情" );
            return response;
        }
        int completeCount = 0;
        for (WhApvAllocationOrderItem orderItem: orderItems){
            if (orderItem.getLoadStatus().equals(AllocationLoadStatusEnum.LOADING.intCode())){
                completeCount++;
            }
        }
        try {
            if ((completeCount+1) == orderItems.size()){
                callReturnPickQuantity(list, skus, orderSkuMap);
            }
            if (CollectionUtils.isNotEmpty(skus) && (completeCount+1) == orderItems.size()){
                response = whApvAllocationPickingService.updateAndCompleteLoading(skus, orderSkuMap, boardNo, subTaskNo, allocationBoard, allocationIdList);
            }else {
                response = whApvAllocationPickingService.updateLoadingStatus(boardNo, subTaskNo, allocationBoard, allocationIdList);
            }
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            response.setMessage("装车失败：" + e.getMessage());
        }
        return response;
    }

    /**
     * @Description 计算播种 < 拣货的SKU需要退已拣返架的库存
     * <AUTHOR>
     * @date 2020/8/14 14:39
     * @param: apvAllocationList
     * @param: skus
     * @param: orderSkuMap
     * @return void
     */
    public void callReturnPickQuantity(List<WhApvAllocation> apvAllocationList, List<String> skus,  Map<String, WhApvAllocationOrderSku> orderSkuMap){
        for (WhApvAllocation apvAllocation: apvAllocationList){
            // 重置redis
            // androidAllocationPickingService.reflashRedisData(apvAllocation.getAllocationNo());

            WhApvAllocationDemandQueryCondition queryCondition = new WhApvAllocationDemandQueryCondition();
            queryCondition.setAllocationNo(apvAllocation.getAllocationNo());
            List<WhApvAllocationDemandItem> demandItems = whApvAllocationDemandService.queryWhApvAllocationDemandItemList(queryCondition);
            //Map< String, Integer> map = AllocationApvUtils.getReturnPickStockMap(demandItems);
            String dataJson = StringRedisUtils.get(RedisConstant.APV_ALLOCATION_SOW_NORMAL_RETURN_PICK_KEY + apvAllocation.getAllocationNo());
            Map< String, Integer> map = null;
            if (StringUtils.isNotBlank(dataJson)){
                map = JSON.parseObject(dataJson, HashMap.class);
            }
            if (map != null && CollectionUtils.isNotEmpty(map.keySet())){
                for (String sku: map.keySet()){
                    Integer quantity = map.get(sku);
                    if (quantity != null && quantity > 0){
                        WhApvAllocationOrderSku orderSku = orderSkuMap.get(sku);
                        if (orderSku == null){
                            orderSku = new WhApvAllocationOrderSku();
                            orderSku.setQuantity(0);
                            orderSku.setPickQuantity(0);
                            orderSku.setSku(sku);
                        }
                        Integer returnPick = orderSku.getPickQuantity() == null ? 0: orderSku.getPickQuantity();
                        orderSku.setPickQuantity(returnPick + quantity);
                        orderSku.setAllocationNo(apvAllocation.getAllocationNo());
                        if (!skus.contains(sku)){
                            skus.add(sku);
                        }
                        orderSkuMap.put(sku, orderSku);
                    }
                }
            }
        }
    }


    /*************************************************************************** 海外仓调拨 ***********************************************************************/

    /**
     * @return com.whq.tool.json.ResponseJson
     * @Description 获取海外仓调拨列表（根据调拨单号查出调拨需求列表）
     * <AUTHOR>
     * @date 2020/11/30 10:02
     * @param: domain
     */
    @RequestMapping(value = "asnAllocationList", method = {RequestMethod.POST})
    public ResponseJson asnAllocationList(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android asnAllocationList ");
        return androidAllocationPickingService.asnAllocationList(domain);
    }

    /**
     * @return com.whq.tool.json.ResponseJson
     * @Description 海外仓调拨需求(调拨子任务)绑定播种箱号
     * <AUTHOR>
     * @date 2020/11/30 10:04
     * @param: domain
     */
    @RequestMapping(value = "asnAllocation", method = {RequestMethod.POST})
    public ResponseJson asnAllocation(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android asnAllocation ");
        return androidAllocationPickingService.asnAllocation(domain);
    }

    /**
     * @return com.whq.tool.json.ResponseJson
     * @Description 海外仓调拨播种
     * <AUTHOR>
     * @date 2020/11/30 10:04
     * @param: domain
     */
    @RequestMapping(value = "asnAllocationSow", method = {RequestMethod.POST})
    public ResponseJson asnAllocationSow(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android asnAllocationSow ");
        //兼容SKU编码和唯一码
        domain.setSku(CompatibleSkuUtils.getSkuForPda(domain.getSku()));
        return androidAllocationPickingService.doAsnAllocationSow(domain);
    }

    /**
     * @return com.whq.tool.json.ResponseJson
     * @Description 海外仓调拨播种完成检测
     * <AUTHOR>
     * @date 2020/11/30 10:04
     * @param: domain
     */
    @RequestMapping(value = "asnAllocationSowCheck", method = {RequestMethod.POST})
    public ResponseJson asnAllocationSowCheck(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android asnAllocationSowCheck ");
        return androidAllocationPickingService.asnAllocationSowCheck(domain);
    }

    /**
     * @return com.whq.tool.json.ResponseJson
     * @Description 海外仓调拨播种完成
     * <AUTHOR>
     * @date 2020/11/30 10:04
     * @param: domain
     */
    @RequestMapping(value = "asnAllocationSowFinish", method = {RequestMethod.POST})
    public ResponseJson asnAllocationSowFinish(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android asnAllocationSowFinish ");
        return androidAllocationPickingService.asnAllocationSowFinish(domain);
    }

    /**
     * @return com.whq.tool.json.ResponseJson
     * @Description 海外仓调拨打板
     * <AUTHOR>
     * @date 2020/11/30 10:04
     * @param: domain
     */
    @RequestMapping(value = "asnAllocationBeating", method = {RequestMethod.POST})
    public ResponseJson asnAllocationBeating(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android asnAllocationBeating ");
        return androidAllocationPickingService.asnAllocationBeating(domain);
    }


    /**
     * @return com.whq.tool.json.ResponseJson
     * @Description 海外仓调拨装车
     * <AUTHOR>
     * @date 2020/11/30 15:00
     * @param: domain
     */
    @RequestMapping(value = "asnAllocationLoading", method = {RequestMethod.POST})
    public ResponseJson asnAllocationLoading(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android asnAllocationLoading ");
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);

        String allocationBoardNo = domain.getAllocationBoardNo();
        String subTaskNo = domain.getSubTaskNo();
        if (StringUtils.isBlank(allocationBoardNo) && StringUtils.isBlank(subTaskNo)) {
            responseJson.setMessage("请输入卡板号或子任务号");
            return responseJson;
        }
        WhApvAllocationBoard allocationBoard = null;
        if (StringUtils.isNotBlank(allocationBoardNo)) {
            Pattern patternBOARD = Pattern.compile("([1-2]{1})([K-K]{1})([B-B]{1})([0-9]{4})([0-9]{0,})");
            Matcher matcherBOARD = patternBOARD.matcher(allocationBoardNo);
            if (!matcherBOARD.matches()) {
                responseJson.setMessage("卡板号无效！");
                return responseJson;
            }

            WhApvAllocationBoardQueryCondition queryBOARD = new WhApvAllocationBoardQueryCondition();
            queryBOARD.setBoardType(AllocationBoardTypeEnum.BOARD.intCode());
            queryBOARD.setBoardNo(domain.getAllocationBoardNo());
            WhApvAllocationBoard BOARD = whApvAllocationBoardService.queryWhApvAllocationBoard(queryBOARD);

            if (BOARD == null) {
                responseJson.setMessage("卡板号错误：请检查卡板号是否可以使用！");
                return responseJson;
            }
            allocationBoard = BOARD;
        }

        if (StringUtils.isNotBlank(subTaskNo)) {
            if (StringUtils.isBlank(domain.getSubTaskNo()) || !domain.getSubTaskNo().startsWith("BFBJH")) {
                responseJson.setMessage("海外仓调拨子任务号无效！");
                return responseJson;
            }

            WhApvAllocationDemandQueryCondition query = new WhApvAllocationDemandQueryCondition();
            query.setTaskNo(subTaskNo);
            WhApvAllocationDemand Demand = whApvAllocationDemandService.queryWhApvAllocationDemand(query);

            if (Demand == null) {
                responseJson.setMessage("子任务号不存在！");
                return responseJson;
            }

            if (!Demand.getTaskStatus().equals(AllocationDemandTaskStatus.FINISH.intCode())) {
                responseJson.setMessage("子任务号状态必须为已完成！");
                return responseJson;
            }
        }

        if (JedisUtils.exists(ApvTaskRedisLock.PDA_ORDER_ALLOCATION_LOADING.getName() + "-" + allocationBoardNo + "-" + subTaskNo)) {
            responseJson.setMessage("该调拨单正在装车，请稍后再试！");
            return responseJson;
        } else {
            JedisUtils.set(ApvTaskRedisLock.PDA_ORDER_ALLOCATION_LOADING.getName() + "-" + allocationBoardNo + "-" + subTaskNo, "lock", 600L);
        }

        try {
            responseJson = asnLoading(allocationBoardNo, subTaskNo, allocationBoard);
        } catch (Exception e) {
            responseJson.setMessage(e.getMessage());
            responseJson.setStatus(StatusCode.FAIL);
        } finally {
            JedisUtils.del(ApvTaskRedisLock.PDA_ORDER_ALLOCATION_LOADING.getName() + "-" + allocationBoardNo + "-" + subTaskNo);
        }
        return responseJson;
    }

    public ResponseJson asnLoading(String boardNo, String subTaskNo, WhApvAllocationBoard allocationBoard) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        WhApvAllocationQueryCondition aQuery = new WhApvAllocationQueryCondition();
        aQuery.setBoardNo(boardNo);
        aQuery.setSubTaskNo(subTaskNo);
        aQuery.setIsDiscard(AllocationStatusEnum.DISCARD.intCode());
        aQuery.setAllocationType(AllocationTypeEnum.ABROAD.intCode());
        List<WhApvAllocation> list = whApvAllocationDao.queryOrderAllocationDetailList(aQuery, null);

        if (CollectionUtils.isEmpty(list)) {
            response.setMessage("没有绑定该号码的调拨单");
            return response;
        }
        /** 调拨单号 */
        List<Integer> allocationIdList = new ArrayList<Integer>();
        List<String> allocationNoList = new ArrayList<>();
        List<String> skus = new ArrayList<>();
        Map<String, WhApvAllocationOrderSku> orderSkuMap = new HashMap<>();
        for (WhApvAllocation entity : list) {
            Integer status = entity.getAllocationStatus();
            if (!Integer.valueOf(AllocationOrderStatusEnum.WAIT_LOAD.intCode()).equals(status)) {
                response.setMessage("不可进行装车，调拨单：" + entity.getAllocationNo() + "状态：" + AllocationOrderStatusEnum.getNameByCode(status + ""));
                return response;
            }

            List<WhApvAllocationOrderItem> itemList = entity.getAllocationOrderItems();
            for (WhApvAllocationOrderItem item : itemList) {
                Integer loadStatus = item.getLoadStatus();
                if (Integer.valueOf(AllocationLoadStatusEnum.LOADING.intCode()).equals(loadStatus)) {
                    response.setMessage("该号码已装车，不可重复装车，调拨单号" + entity.getAllocationNo());
                    return response;
                }
            }

            allocationIdList.add(entity.getAllocationId());
            allocationNoList.add(entity.getAllocationNo());

            WhApvAllocationDemandQueryCondition queryCondition = new WhApvAllocationDemandQueryCondition();
            queryCondition.setAllocationNo(entity.getAllocationNo());
            queryCondition.setIsPick(1);
            List<WhApvAllocationDemandSku> demandSkuList = whApvAllocationDemandService.queryWhApvAllocationDemandSkuList(queryCondition);
            if (CollectionUtils.isNotEmpty(demandSkuList)) {
                for (WhApvAllocationDemandSku demandSku : demandSkuList) {
                    if (demandSku.getSowQuantity() > 0) {
                        WhApvAllocationOrderSku orderSku = orderSkuMap.get(demandSku.getSku());
                        if (orderSku == null) {
                            orderSku = new WhApvAllocationOrderSku();
                            orderSku.setQuantity(0);
                            orderSku.setSku(demandSku.getSku());
                        }
                        orderSku.setQuantity(orderSku.getQuantity() + demandSku.getSowQuantity());
                        orderSku.setAllocationNo(entity.getAllocationNo());
                        if (!skus.contains(demandSku.getSku())) {
                            skus.add(demandSku.getSku());
                        }
                        orderSkuMap.put(demandSku.getSku(), orderSku);
                    }
                }
            }
        }
        List<WhApvAllocationOrderItem> orderItems = whApvAllocationOrderService.queryWhApvAllocationOrderItems(allocationIdList);
        if (CollectionUtils.isEmpty(orderItems)) {
            response.setMessage("调拨单未匹配到订单调任务拨详情");
            return response;
        }
        int completeCount = 0;
        for (WhApvAllocationOrderItem orderItem : orderItems) {
            if (orderItem.getLoadStatus().equals(AllocationLoadStatusEnum.LOADING.intCode())) {
                completeCount++;
            }
        }
        try {
            if ((completeCount + 1) == orderItems.size()) {
                callReturnPickQuantity(list, skus, orderSkuMap);
            }
            if (CollectionUtils.isNotEmpty(skus) && (completeCount + 1) == orderItems.size()) {
                response = whAsnAllocationPickingService.updateAndCompleteLoading(skus, orderSkuMap, boardNo, subTaskNo, allocationBoard, allocationIdList);
            } else {
                response = whAsnAllocationPickingService.updateLoadingStatus(boardNo, subTaskNo, allocationBoard, allocationIdList);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            response.setMessage("装车失败：" + e.getMessage());
        }
        return response;
    }
}
