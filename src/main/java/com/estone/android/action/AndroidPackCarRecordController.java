package com.estone.android.action;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.apv.service.AccountNumberConfigurationService;
import com.estone.apv.service.WhApvService;
import com.estone.asn.bean.WhAsnExtra;
import com.estone.asn.enums.CollectMethodEnum;
import com.estone.common.enums.WarehousePropertyEnum;
import com.estone.common.util.*;
import com.estone.picking.bean.WhMergeApvQueryCondition;
import com.estone.scan.deliver.bean.WhWarehouseShipment;
import com.estone.scan.deliver.bean.WhWarehouseShipmentQueryCondition;
import com.estone.scan.deliver.service.WhWarehouseShipmentService;
import com.estone.scan.deliver.util.ScanShipmentUtils;
import com.estone.temu.bean.TemuPrepareOrder;
import com.estone.temu.bean.TemuPrepareOrderItem;
import com.estone.temu.bean.TemuPrepareOrderQueryCondition;
import com.estone.temu.enums.SourceFromEnum;
import com.estone.temu.enums.TemuPackageStatus;
import com.estone.temu.service.TemuPrepareOrderService;
import com.estone.temu.service.TemuShipmentOrderService;
import com.estone.transfer.bean.WarehouseLoadingSummary;
import com.estone.transfer.bean.WhFbaAllocation;
import com.estone.transfer.bean.WhFbaAllocationItem;
import com.estone.transfer.bean.WhFbaAllocationQueryCondition;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.estone.transfer.service.WhFbaAllocationHandleService;
import com.estone.transfer.service.WhFbaAllocationItemService;
import com.estone.transfer.service.WhFbaAllocationService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.estone.android.domain.AndroidProductDo;
import com.estone.checkin.enums.ShippingCompanyEnum;
import com.estone.common.SelectJson;
import com.estone.common.enums.LogModule;
import com.estone.scan.deliver.bean.*;
import com.estone.scan.deliver.enums.LoadTypeEnum;
import com.estone.scan.deliver.enums.ScanShipmentLogType;
import com.estone.scan.deliver.enums.ScanShipmentStatus;
import com.estone.scan.deliver.service.*;
import com.estone.system.param.bean.SystemParam;
import com.estone.system.rabbitmq.producer.RabbitmqProducerService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.json.ResponseJson;

import jodd.util.StringUtil;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description 装车发货
 * @date 2020/8/4 17:49
 */
@RestController
@RequestMapping(value = "android/packCarRecord")
public class AndroidPackCarRecordController {

    private Logger logger = LoggerFactory.getLogger(AndroidPackCarRecordController.class);

    final static SystemLogUtils SCANSHIPMENTLOG = SystemLogUtils.create(LogModule.SCANSHIPMENT.getCode());

    @Resource
    private WhPackCarRecordService whPackCarRecordService;

    @Resource
    private WhScanShipmentService whScanShipmentService;

    @Resource
    private WhShippingMethodService whShippingMethodService;

    @Resource
    private WhScanShipmentToApvService whScanShipmentToApvService;

    @Resource
    private RabbitmqProducerService rabbitmqProducerService;

    @Resource
    private DeliverOrderService deliverOrderService;

    @Resource
    private TemuShipmentOrderService temuShipmentOrderService;

    @Resource
    private TemuPrepareOrderService temuPrepareOrderService;

    @Resource
    private AccountNumberConfigurationService accountNumberConfigurationService;

    @Resource
    private WhApvService whApvService;

    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    @Resource
    private WhFbaAllocationItemService whFbaAllocationItemService;

    @Resource
    private WhWarehouseShipmentService whWarehouseShipmentService;

    @Resource
    private WhFbaAllocationHandleService whFbaAllocationHandleService;

    private final static ExecutorService executors = Executors.newFixedThreadPool(3);

    /**
     * @return com.whq.tool.json.ResponseJson
     * @Description 今日装车汇总
     * <AUTHOR>
     * @date 2020/8/4 18:15
     * @param: warehouseId
     */
    @RequestMapping(value = "todayIntoCarTotal", method = {RequestMethod.GET})
    public ResponseJson todayIntoCarTotal(@RequestParam("warehouseId") Integer warehouseId, @RequestParam(required = false) Integer isTransfer) {
        logger.info("android todayIntoCarTotal");
        logger.warn("今日汇总 warehouseId:" + warehouseId);
        ResponseJson responseJson = new ResponseJson();
        Date today = DateUtils.stringToDate(DateUtils.dateToString(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
        warehouseId = CacheUtils.getLocalWarehouseId();

        WhPackCarRecordQueryCondition query = new WhPackCarRecordQueryCondition();
        query.setLoadDate(new Timestamp(today.getTime()));
        query.setWarehouseId(warehouseId);
        query.setIsTransfer(isTransfer);
        List<WhPackCarRecord> packCarRecords = whPackCarRecordService.queryWhPackCarRecords(query, null);
        Map<String, TodayIntoCarTotalEntity> map = new HashMap<>();
        for (WhPackCarRecord packCarRecord : packCarRecords) {
            TodayIntoCarTotalEntity todayIntoCarTotalEntity = map.get(packCarRecord.getShippingCompanyCode());
            if (todayIntoCarTotalEntity == null) {
                todayIntoCarTotalEntity = new TodayIntoCarTotalEntity();
                todayIntoCarTotalEntity.setLogisticsCompany(packCarRecord.getShippingCompanyCode());
                todayIntoCarTotalEntity.setCount(packCarRecord.getTotalBagNum());
                todayIntoCarTotalEntity.setLogisticsCompanyName(packCarRecord.getShippingCompanyName());
                map.put(packCarRecord.getShippingCompanyCode(), todayIntoCarTotalEntity);
            } else {
                todayIntoCarTotalEntity.setCount(todayIntoCarTotalEntity.getCount() + packCarRecord.getTotalBagNum());
            }
        }
        responseJson.setStatus(StatusCode.SUCCESS);
        responseJson.setMessage(JSON.toJSONString(map.values()));
        return responseJson;
    }

    /**
     * @return com.whq.tool.json.ResponseJson
     * @Description 装车
     * <AUTHOR>
     * @date 2020/8/4 18:33
     * @param: domain
     */
    @RequestMapping(value = "load", method = {RequestMethod.POST})
    public ResponseJson load(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android load"+ JSONObject.toJSONString(domain));
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        String bagNo = domain.getBagNo();
        if (StringUtils.isBlank(bagNo)) {
            responseJson.setMessage("结袋卡号不能为空!");
            return responseJson;
        }
        // 0默认，1强制装车，-1不强制装车
        Integer isForce = domain.getIsForce();
        String shippingCompanyCode = domain.getShippingCompanyCode();
        Integer warehouseId = domain.getWarehouseId();
        logger.warn("结袋扫描 bagNo:" + bagNo + ", isForce:" + isForce + ", shippingCompanyCode:" + shippingCompanyCode + ", warehouseId:" + warehouseId
                + ", apvNo:" + domain.getApvNo());
        WhScanShipment scanShipment = whScanShipmentService.getWhScanShipmentByBagNo(bagNo);
        if (scanShipment == null) {
            responseJson.setMessage("该结袋卡号没有结袋信息！");
            return responseJson;
        }

        if (ScanShipmentUtils.caiNiaoZuBao() && ScanShipmentUtils.isCaiNiaoPlatform(scanShipment.getLogisticsCompanyCode()) && StringUtils.isBlank(scanShipment.getPickupOrderNo())) {
            responseJson.setMessage("该结袋卡号未成功创建揽收单，不能进行装车操作！");
            return  responseJson;
        }


        if (!(ScanShipmentStatus.WEIGHTED.intCode().equals(scanShipment.getStatus()) || ScanShipmentStatus.LOAD_FAILED.intCode().equals(scanShipment.getStatus()))) {
            SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败",
                    new String[][] {{ "原因", "该结袋卡不是可装车状态" }});
            responseJson.setMessage("该结袋卡不是可装车状态");
            return responseJson;
        }
        WhDeliveryCompany deliveryCompany = whShippingMethodService.getWhDeliveryCompanyDetailByDeliveryCompanyCode(scanShipment.getLogisticsCompanyCode());
        if (deliveryCompany == null) {
            SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败",
                    new String[][] {{ "原因", "物流方式或交运方式为空" }});
            responseJson.setMessage("物流方式或交运方式为空");
            return responseJson;
        }
        WhCollectCompany collectCompany = deliveryCompany.getWhCollectCompany();
        if (collectCompany == null) {
            SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败",
                    new String[][] {{ "原因", "物流渠道在管理物流公司中没有定义" }});
            responseJson.setMessage("物流渠道在管理物流公司中没有定义！");
            return responseJson;
        }
        if (StringUtils.isNotBlank(shippingCompanyCode) && !collectCompany.getCode().equals(shippingCompanyCode)) {
            SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败",
                    new String[][] {{ "原因", "物流渠道与当前物流公司不匹配" }});
            responseJson.setMessage("物流渠道与当前物流公司不匹配，装车失败！");
            return responseJson;
        }
        if (StringUtils.isBlank(collectCompany.getCode())) {
            logger.info("结袋卡装车: bagNo[" + scanShipment.getBagNo() + "], collectCompanyCode 为空");
            SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败",
                    new String[][] {{ "原因", "collectCompanyCode 为空" }});
            responseJson.setMessage("装车失败!,collectCompanyCode 为空");
            return responseJson;
        }
        WhScanShipmentToApvQueryCondition queryCondition = new WhScanShipmentToApvQueryCondition();
        queryCondition.setScanShipmentId(scanShipment.getId());
        List<WhScanShipmentToApv> apvs = whScanShipmentToApvService.queryWhScanShipmentToApvs(queryCondition,null);
        //int num = whScanShipmentToApvService.queryWhScanShipmentToApvCount(queryCondition);

        if (apvs.size() == 0) {
            SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败",
                    new String[][] {{ "原因", "没有发货订单" }});
            responseJson.setMessage("包装号" + bagNo + "下没有发货订单");
            return responseJson;
        }

        if (scanShipment.getActualTotalWeight() == null) {
            SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败",
                    new String[][] {{ "原因", "没有进行核重操作" }});
            responseJson.setMessage("包装号" + bagNo + "没有进行核重操作");
            return responseJson;
        }
        // 校验不同仓的是否混装车提交
        if (StringUtils.isNotBlank(domain.getApvNo())) {
            List<String> apvNoList = new ArrayList<>();
            apvNoList.add(domain.getApvNo());
            apvNoList.add(apvs.get(0).getApvNo());
            WhApvQueryCondition whApvQueryCondition = new WhApvQueryCondition();
            whApvQueryCondition.setApvNoList(apvNoList);
            WhMergeApvQueryCondition whMergeApvQueryCondition = new WhMergeApvQueryCondition();
            whMergeApvQueryCondition.setIsNanjingShop(true);
            accountNumberConfigurationService.getAccountNumberConfigurationList(whMergeApvQueryCondition);
            if (StringUtils.isNotBlank(whMergeApvQueryCondition.getSaleChannel())) {
                whApvQueryCondition.setSaleChannel(whMergeApvQueryCondition.getSaleChannel());
            }
            whApvQueryCondition.setShopList(whMergeApvQueryCondition.getShopList());
            whApvQueryCondition.setExcludeShopList(whMergeApvQueryCondition.getExcludeShopList());
            List<WhApv> whApvs = whApvService.queryWhApvAndItemList(whApvQueryCondition, null);
            if (CollectionUtils.isNotEmpty(whApvs) && whApvs.size() == 1) {
                SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败",
                        new String[][] {{ "原因", "不同仓的结袋卡不能一起装车" }});
                responseJson.setMessage("不同仓的结袋卡不能一起装车，装车失败！");
                return responseJson;
            }
        }
        // 重量差
        Double weightDiff = scanShipment.getWeightDiff();
        Double minScope = scanShipment.getMinScope();
        Double maxScope = scanShipment.getMaxScope();

        if (isForce == 0) {
            if (minScope != null && maxScope != null && (weightDiff < minScope || weightDiff > maxScope)) {
                responseJson.setMessage("confirm,包装号" + bagNo + "重量差超出规定重量差范围，是否强制装车？");
                return responseJson;
            }else{
                SCANSHIPMENTLOG.log(scanShipment.getId(), "PDA装车扫描结袋卡成功");
            }
        }
        else if (isForce == -1) {
            scanShipment.setStatus(ScanShipmentStatus.LOAD_FAILED.intCode());
            scanShipment.setLoadUser(DataContextHolder.getUserId());
            whScanShipmentService.updateWhScanShipment(scanShipment);
            SCANSHIPMENTLOG.log(scanShipment.getId(), ScanShipmentLogType.LOAD_FAILED.getName(),new String[][] {{ "原因", "没有强制装车" }});
            logger.info("结袋卡装车失败: bagNo[ " + scanShipment.getBagNo() + " ]");
            responseJson.setMessage("装车失败!");
            return responseJson;
        }else{
            SCANSHIPMENTLOG.log(scanShipment.getId(), "PDA装车扫描结袋卡成功(强制装车)");
        }

        if(StringUtils.isNotBlank(domain.getExpressCompany())){
            // PDA快递自发装车页面需填写快递方式信息
            if(!ShippingCompanyEnum.HUOLALA.getCode().equals(domain.getExpressCompany())
                    && StringUtils.isBlank(domain.getExpressOrderNo())){
                SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败",
                        new String[][] {{ "原因", "快递单号不能为空" }});
                responseJson.setMessage("快递单号不能为空");
                return responseJson;
            }
            scanShipment.setExpressCompany(domain.getExpressCompany());
            scanShipment.setExpressOrderNo(domain.getExpressOrderNo());
        }

        long startTimeMillis0 = System.currentTimeMillis();
        Map<String, String> body = new HashMap<String, String>();
        try {
            whPackCarRecordService.updateToload(scanShipment, "local");

            List<String> apvNos = Optional.ofNullable(apvs)
                    .orElse(new ArrayList<>())
                    .stream()
                    .map(WhScanShipmentToApv::getApvNo)
                    .collect(Collectors.toList());
            rabbitmqProducerService.pushScanShipmentToOMS(scanShipment,apvNos);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            responseJson.setMessage(e.getMessage());
            return responseJson;
        }

        logger.warn("other消耗时间：" + (System.currentTimeMillis() - startTimeMillis0));
        body.put("logisticsCompany", collectCompany.getCode());
        body.put("apvNo", apvs.get(0).getApvNo());
        body.put("logisticsCompanyName", collectCompany.getName());
        body.put("logisticsChannel", deliveryCompany.getCode());
        body.put("logisticsChannelName", deliveryCompany.getName());
        body.put("expressCompany", ShippingCompanyEnum.getNameByCode(domain.getExpressCompany()));
        body.put("expressOrderNo", domain.getExpressOrderNo());
        responseJson.setStatus(StatusCode.SUCCESS);
        responseJson.setMessage(JSON.toJSONString(body));
        return responseJson;
    }

    @RequestMapping(value = "generatePackCarRecord")
    public ResponseJson generatePackCarRecord(@RequestParam(value = "bagNos") List<String> bagNos) {
        logger.info("android generatePackCarRecord");
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(bagNos)) {
            responseJson.setMessage("结袋卡号不能为空!");
            return responseJson;
        }
        WhScanShipmentQueryCondition query = new WhScanShipmentQueryCondition();
        query.setBagNos(bagNos);
        List<WhScanShipment> whScanShipments = whScanShipmentService.queryWhScanShipments(query, null);
        if (CollectionUtils.isEmpty(whScanShipments)) {
            responseJson.setMessage("结袋卡记录为空!");
            return responseJson;
        }
        WhScanShipment scanShipment = whScanShipments.get(0);
        String logisticsCompanyCode = scanShipment.getLogisticsCompanyCode();
        if (StringUtils.isBlank(logisticsCompanyCode)) {
            responseJson.setMessage("该结袋卡物流代号为空！" + scanShipment.getBagNo());
            return responseJson;
        }
        for (WhScanShipment whScanShipment : whScanShipments) {
            if (!ScanShipmentStatus.LOADED.intCode().equals(whScanShipment.getStatus())) {
                responseJson.setMessage("该结袋卡不已装车状态" + whScanShipment.getBagNo());
                return responseJson;
            }
            if (whScanShipment.getPackCarId() != null) {
                responseJson.setMessage("该结袋卡已有装车记录" + whScanShipment.getBagNo());
                return responseJson;
            }
        }
        WhDeliveryCompany deliveryCompany = whShippingMethodService.getWhDeliveryCompanyDetailByDeliveryCompanyCode(logisticsCompanyCode);
        if (deliveryCompany == null) {
            responseJson.setMessage("物流方式或交运方式为空");
            return responseJson;
        }
        WhCollectCompany collectCompany = deliveryCompany.getWhCollectCompany();
        if (collectCompany == null) {
            responseJson.setMessage("物流渠道在管理物流公司中没有定义！");
            return responseJson;
        }
        try {
            whPackCarRecordService.doGeneratePackCarRecord(whScanShipments, collectCompany, LoadTypeEnum.LOCAL.getCode());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            responseJson.setMessage(e.getMessage());
            return responseJson;
        }
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    /**
     * JIT、拼多多装车
     */
    @RequestMapping(value = "loadTransfer", method = {RequestMethod.POST})
    public ResponseJson loadTransfer(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android loadTransfer bagNo：" + domain.getBagNo());
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        String bagNo = domain.getBagNo();
        if (StringUtils.isBlank(bagNo)) {
            responseJson.setMessage("结袋卡号不能为空!");
            return responseJson;
        }
        if (StringUtils.isBlank(domain.getType())) {
            responseJson.setMessage("装车类型不能为空!");
            return responseJson;
        }
        if ("temu".equals(domain.getType()) && !bagNo.startsWith("TEMU")){
            responseJson.setMessage("当前扫描的不是Temu包裹!");
            return responseJson;
        }
        if ("transfer".equals(domain.getType()) && !bagNo.startsWith("SMTJIT") && !bagNo.startsWith("SMTCF")){
            responseJson.setMessage("当前扫描的不是JIT或SMT仓发包裹!");
            return responseJson;
        }
        if ("temu".equals(domain.getType())
                && (StringUtils.isBlank(domain.getExpressCompany()) || StringUtils.isBlank(domain.getExpressOrderNo()))){
            responseJson.setMessage("Temu装车快递单号不能为空!");
            return responseJson;
        }
        String expressNo = domain.getExpressOrderNo();
        if (StringUtils.isNotBlank(expressNo) && (expressNo.startsWith("SMTJIT") || expressNo.startsWith("SHEIN") || expressNo.startsWith("TEMU"))) {
            responseJson.setMessage("快递单号不能为系统结袋卡号!");
            return responseJson;
        }
        // 0默认，1强制装车，-1不强制装车
        Integer isForce = domain.getIsForce();
        String shippingCompanyCode = domain.getShippingCompanyCode();
        Integer warehouseId = domain.getWarehouseId();
        logger.warn("中转仓结袋扫描 bagNo:" + bagNo + ", expressOrderNo:"+ expressNo +", isForce:" + isForce + ", shippingCompanyCode:" + shippingCompanyCode + ", warehouseId:" + warehouseId + ", type:" + domain.getType());
        WhScanShipment scanShipment = whScanShipmentService.getWhScanShipmentByBagNo(bagNo);
        if (scanShipment == null) {
            responseJson.setMessage("该结袋卡号没有结袋信息！");
            return responseJson;
        }
        if (!Boolean.TRUE.equals(scanShipment.getIsTransferHouse())){
            responseJson.setMessage("不是中转仓结袋卡！");
            return responseJson;
        }

        if (StringUtils.equalsIgnoreCase("SMTJIT", scanShipment.getPlatform())
                && StringUtils.isBlank(scanShipment.getPickupOrderNo())) {
            SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败", new String[][] { { "原因", "JIT单据结袋之后没有生成揽收单号不允许装车" } });
            responseJson.setMessage("JIT单据结袋之后没有生成揽收单号不允许装车");
            return responseJson;
        }
        if (StringUtils.equalsIgnoreCase("ASN", scanShipment.getPlatform())
                && bagNo.startsWith("SMTCF")
                && StringUtils.isBlank(scanShipment.getPickupOrderNo())) {
            SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败", new String[][] { { "原因", "SMT仓发单据结袋之后没有生成揽收单号不允许装车" } });
            responseJson.setMessage("SMT仓发结袋之后没有生成揽收单号不允许装车");
            return responseJson;
        }
        if (StringUtils.equalsIgnoreCase("ASN", scanShipment.getPlatform())
                && bagNo.startsWith("SMTCF")
                && "SMTZJ".equals(scanShipment.getLogisticsCompanyCode())) {
            SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败", new String[][] { { "原因", "SMT仓发自寄单请到自寄装车页面操作" } });
            responseJson.setMessage("SMT仓发自寄单请到自寄装车页面操作");
            return responseJson;
        }

        //原快递单号
        String oldExpressNo = scanShipment.getExpressOrderNo();
        if (ShippingCompanyEnum.JT_EXPRESS.getCode().equals(domain.getExpressCompany()) &&
                (!ShippingCompanyEnum.JT_EXPRESS.getCode().equals(scanShipment.getLogisticsCompanyCode()) ||  !expressNo.equals(scanShipment.getExpressOrderNo()))) {
            responseJson.setMessage("极兔快递，结袋卡号与快递单号或快递方式不一致!");
            return responseJson;
        }

        if (ShippingCompanyEnum.JYM.getCode().equals(domain.getExpressCompany())
                && !ShippingCompanyEnum.JYM.getCode().equals(scanShipment.getLogisticsCompanyCode())) {
            responseJson.setMessage("加运美物流，请选择加运美快递方式!");
            return responseJson;
        }
        if (ShippingCompanyEnum.KUAYUE.getCode().equals(scanShipment.getLogisticsCompanyCode())
                && !ShippingCompanyEnum.KUAYUE.getCode().equals(domain.getExpressCompany())) {
            responseJson.setMessage("跨越物流，请选择跨越快递方式!");
            return responseJson;
        }

        if (ShippingCompanyEnum.JYM.getCode().equals(domain.getExpressCompany())) {
            //校验加运美快递单号
            ResponseJson json = whScanShipmentService.jymExpressValidator(scanShipment, domain.getExpressOrderNo());
            if (!json.isSuccess()){
                responseJson.setMessage(json.getMessage());
                return json;
            }
        } else if (ShippingCompanyEnum.KUAYUE.getCode().equals(domain.getExpressCompany())) {
            //校验跨越快递单号
            if (StringUtils.contains(expressNo, "|")){
                expressNo = expressNo.substring(0, expressNo.indexOf("|"));
                domain.setExpressOrderNo(expressNo);
            }
            ResponseJson json = whScanShipmentService.kyeExpressValidator(scanShipment, domain.getExpressOrderNo());
            if (!json.isSuccess()){
                responseJson.setMessage(json.getMessage());
                return json;
            }
        }
        List<String> expressCompanyList = Arrays.asList(ShippingCompanyEnum.JYM.getCode(), ShippingCompanyEnum.JT_EXPRESS.getCode(), ShippingCompanyEnum.KUAYUE.getCode());
        if (!expressCompanyList.contains(domain.getExpressCompany()) && StringUtils.isNotBlank(scanShipment.getExpressOrderNo())) {
            responseJson.setMessage("该结袋卡已通过系统下单，请选择正确的物流方式");
            return responseJson;
        }

        if (!(ScanShipmentStatus.WEIGHTED.intCode().equals(scanShipment.getStatus()) || ScanShipmentStatus.LOAD_FAILED.intCode().equals(scanShipment.getStatus()))) {
            SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败",
                    new String[][] {{ "原因", "该结袋卡不是可装车状态" }});
            responseJson.setMessage("该结袋卡不是可装车状态");
            return responseJson;
        }
        if (StringUtils.isNotBlank(domain.getExpressCompany()) && StringUtils.isNotBlank(shippingCompanyCode) && !domain.getExpressCompany().equals(shippingCompanyCode)) {
            SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败",
                    new String[][] {{ "原因", "当前包裹与装车上一单物流信息不一致" }});
            responseJson.setMessage("当前包裹与装车上一单物流信息不一致，装车失败！");
            return responseJson;
        }
        WhScanShipmentToApvQueryCondition queryCondition = new WhScanShipmentToApvQueryCondition();
        queryCondition.setScanShipmentId(scanShipment.getId());
        int num = whScanShipmentToApvService.queryWhScanShipmentToApvCount(queryCondition);
        if (num == 0) {
            SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败",
                    new String[][] {{ "原因", "没有发货订单" }});
            responseJson.setMessage("包装号" + bagNo + "下没有发货订单");
            return responseJson;
        }
        if (scanShipment.getActualTotalWeight() == null) {
            SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败",
                    new String[][] {{ "原因", "没有进行核重操作" }});
            responseJson.setMessage("包装号" + bagNo + "没有进行核重操作");
            return responseJson;
        }
        // 重量差
        Double weightDiff = scanShipment.getWeightDiff();
        Double minScope = scanShipment.getMinScope();
        Double maxScope = scanShipment.getMaxScope();

        if (isForce == 0) {
            if (minScope != null && maxScope != null && (weightDiff < minScope || weightDiff > maxScope)) {
                responseJson.setMessage("confirm,包装号" + bagNo + "重量差超出规定重量差范围，是否强制装车？");
                return responseJson;
            }else{
                SCANSHIPMENTLOG.log(scanShipment.getId(), "PDA装车扫描结袋卡成功");
            }
        }
        else if (isForce == -1) {
            scanShipment.setStatus(ScanShipmentStatus.LOAD_FAILED.intCode());
            scanShipment.setLoadUser(DataContextHolder.getUserId());
            whScanShipmentService.updateWhScanShipment(scanShipment);
            SCANSHIPMENTLOG.log(scanShipment.getId(), ScanShipmentLogType.LOAD_FAILED.getName(),new String[][] {{ "原因", "没有强制装车" }});
            logger.info("结袋卡装车失败: bagNo[ " + scanShipment.getBagNo() + " ]");
            responseJson.setMessage("装车失败!");
            return responseJson;
        }else{
            SCANSHIPMENTLOG.log(scanShipment.getId(), "PDA装车扫描结袋卡成功(强制装车)");
        }

        if(StringUtils.isNotBlank(domain.getExpressCompany())){
            // PDA快递自发装车页面需填写快递方式信息
            if(StringUtils.isBlank(domain.getExpressOrderNo())){
                SCANSHIPMENTLOG.log(scanShipment.getId(), "装车失败",
                        new String[][] {{ "原因", "快递单号不能为空" }});
                responseJson.setMessage("快递单号不能为空");
                return responseJson;
            }
            scanShipment.setLogisticsCompanyCode(domain.getExpressCompany());
            scanShipment.setExpressCompany(ShippingCompanyEnum.getNameByCode(domain.getExpressCompany()));
            scanShipment.setExpressOrderNo(domain.getExpressOrderNo());
        }

        long startTimeMillis0 = System.currentTimeMillis();
        Map<String, String> body = new HashMap<String, String>();
        try {
            String redisBillCodeKey = scanShipment.getRedisBillCodeKey();
            whPackCarRecordService.updateToload(scanShipment, domain.getType());
            if (ShippingCompanyEnum.JYM.getCode().equals(domain.getExpressCompany())) {
                StringRedisUtils.set(RedisConstant.JYM_EXPRESS_NO_KEY + redisBillCodeKey, scanShipment.getRedisBillCode(), 5 * 24 * 60 * 60L);
                if (!scanShipment.getExpressOrderNo().equals(oldExpressNo)){
                    SCANSHIPMENTLOG.log(scanShipment.getId(), "结袋卡快递单号变更",
                            new String[][]{{"原单号:", oldExpressNo},
                                    {"更改单号:", scanShipment.getExpressOrderNo()}});
                }
            } else if (ShippingCompanyEnum.KUAYUE.getCode().equals(domain.getExpressCompany())) {
                StringRedisUtils.set(RedisConstant.KYE_EXPRESS_NO_KEY + redisBillCodeKey, scanShipment.getRedisBillCode(), 5 * 24 * 60 * 60L);
                if (!scanShipment.getExpressOrderNo().equals(oldExpressNo)){
                    SCANSHIPMENTLOG.log(scanShipment.getId(), "结袋卡快递单号变更",
                            new String[][]{{"原单号:", oldExpressNo},
                                    {"更改单号:", scanShipment.getExpressOrderNo()}});
                }
            }
        } catch (Exception e) {
            logger.error("loadTransfer error" + e.getMessage(), e);
            responseJson.setMessage(e.getMessage());
            return responseJson;
        }
        logger.warn("other消耗时间：" + (System.currentTimeMillis() - startTimeMillis0));
        String channelName = ShippingCompanyEnum.getNameByCode(domain.getExpressCompany());
        String companyName = ShippingCompanyEnum.getNameByCode(domain.getExpressCompany());
        if (StringUtils.isBlank(companyName)) {
            WhDeliveryCompany deliveryCompany = whShippingMethodService.getWhDeliveryCompanyDetailByDeliveryCompanyCode(scanShipment.getLogisticsCompanyCode());
            if (deliveryCompany != null) {
                channelName = deliveryCompany.getName();
                WhCollectCompany collectCompany2 = deliveryCompany.getWhCollectCompany();
                if (collectCompany2 != null) {
                    companyName = collectCompany2.getName();
                }
            }
        }
        if (StringUtils.isBlank(channelName)) {
            channelName = scanShipment.getLogisticsCompanyCode();
        }
        body.put("logisticsCompany", domain.getExpressCompany());
        body.put("logisticsCompanyName", companyName);
        body.put("logisticsChannel", domain.getExpressCompany());
        body.put("logisticsChannelName", channelName);
        body.put("expressCompany", channelName);
        body.put("expressOrderNo", domain.getExpressOrderNo());
        responseJson.setStatus(StatusCode.SUCCESS);
        responseJson.setMessage(JSON.toJSONString(body));
        return responseJson;
    }

    @RequestMapping(value = "generatePackCarRecordTransfer")
    public ResponseJson generatePackCarRecordTransfer(@RequestParam(value = "bagNos") List<String> bagNos) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(bagNos)) {
            responseJson.setMessage("结袋卡号不能为空!");
            return responseJson;
        }
        WhScanShipmentQueryCondition query = new WhScanShipmentQueryCondition();
        query.setBagNos(bagNos);
        List<WhScanShipment> whScanShipments = whScanShipmentService.queryWhScanShipments(query, null);
        if (CollectionUtils.isEmpty(whScanShipments)) {
            responseJson.setMessage("结袋卡记录为空!");
            return responseJson;
        }
        WhScanShipment scanShipment = whScanShipments.get(0);
        String logisticsCompanyCode = scanShipment.getLogisticsCompanyCode();
        if (StringUtils.isBlank(logisticsCompanyCode)) {
            responseJson.setMessage("该结袋卡物流代号为空！" + scanShipment.getBagNo());
            return responseJson;
        }
        for (WhScanShipment whScanShipment : whScanShipments) {
            if (!ScanShipmentStatus.LOADED.intCode().equals(whScanShipment.getStatus())) {
                responseJson.setMessage("该结袋卡不已装车状态" + whScanShipment.getBagNo());
                return responseJson;
            }
            if (whScanShipment.getPackCarId() != null) {
                responseJson.setMessage("该结袋卡已有装车记录" + whScanShipment.getBagNo());
                return responseJson;
            }
        }
        WhCollectCompany collectCompany = new WhCollectCompany();
        collectCompany.setCode(logisticsCompanyCode);
        collectCompany.setName(scanShipment.getExpressCompany());
        if (StringUtils.isBlank(collectCompany.getName())) {
            WhDeliveryCompany deliveryCompany = whShippingMethodService.getWhDeliveryCompanyDetailByDeliveryCompanyCode(logisticsCompanyCode);
            if (deliveryCompany != null) {
                WhCollectCompany collectCompany2 = deliveryCompany.getWhCollectCompany();
                if (collectCompany2 != null) {
                    collectCompany.setName(collectCompany2.getName());
                }
            }
            if (StringUtils.isBlank(collectCompany.getName())) {
                collectCompany.setName(ShippingCompanyEnum.getNameByCode(logisticsCompanyCode));
            }
            if (StringUtils.isBlank(collectCompany.getName())) {
                collectCompany.setName(logisticsCompanyCode);
            }
        }
        try {
            whPackCarRecordService.doGeneratePackCarRecord(whScanShipments, collectCompany, LoadTypeEnum.TRANSFER.getCode());
            this.sendMsgToOms(whScanShipments);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            responseJson.setMessage(e.getMessage());
            return responseJson;
        }
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    /**
     * 推送装车信息
     */
    public void sendMsgToOms(List<WhScanShipment> whScanShipments){
        executors.execute(() -> {
            for (WhScanShipment whScanShipment : whScanShipments) {
                if ("TEMU".equals(whScanShipment.getPlatform())) {
                    try {
                        WhScanShipmentToApvQueryCondition apvQuery = new WhScanShipmentToApvQueryCondition();
                        apvQuery.setScanShipmentId(whScanShipment.getId());
                        List<WhScanShipmentToApv> toApvs = whScanShipmentToApvService.queryWhScanShipmentToApvs(apvQuery, null);
                        if (CollectionUtils.isNotEmpty(toApvs)) {
                            String packageNos = toApvs.stream().map(WhScanShipmentToApv::getApvNo).collect(Collectors.joining(","));
                            TemuPrepareOrderQueryCondition temuQuery = new TemuPrepareOrderQueryCondition();
                            temuQuery.setPackageSn(packageNos);
                            List<TemuPrepareOrder> temuOrders = temuPrepareOrderService.queryTemuPrepareOrderAndItems(temuQuery, null);
                            if (CollectionUtils.isNotEmpty(temuOrders)) {
                                for (TemuPrepareOrder temuOrder : temuOrders) {
                                    List<TemuPrepareOrderItem> itemList = temuOrder.getItemList();
                                    if (CollectionUtils.isNotEmpty(itemList)
                                            && itemList.stream().anyMatch(c -> SourceFromEnum.ASN.intCode().equals(c.getSourceFrom()))) {
                                        // 推送订单系统
                                        temuOrder.setStatus(TemuPackageStatus.LOADED.intCode());
                                        temuOrder.setActualShippingNo(whScanShipment.getExpressOrderNo());
                                        temuOrder.setShippingCompany(whScanShipment.getExpressCompany());
                                        temuShipmentOrderService.sendAsnMsgToOms(temuOrder);
                                        // 推送物流系统
                                        temuShipmentOrderService.sendAsnMsgToTms(temuOrder);
                                    }
                                }
                            }
                        }
                    } catch (Exception e){
                        logger.error("temu packCarRecord sendMsg error:" + e.getMessage(), e);
                    }
                }
            }
        });
    }

    // 撤回装车
    @RequestMapping(value = "undoIntoCarTransfer", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson undoIntoCarTransfer(@RequestParam String bagNo) {
        logger.info("android undoIntoCarTransfer  bagNo:" + bagNo);
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        WhScanShipment scanShipment = whScanShipmentService.getWhScanShipmentByBagNo(bagNo);
        Map<String, String> jsonObject = new HashMap<String, String>();
        if (scanShipment == null || scanShipment.getActualTotalWeight() == null) {
            responseJson.setMessage("该结袋卡号没有核重记录");
            return responseJson;
        }
        if (!Boolean.TRUE.equals(scanShipment.getIsTransferHouse())){
            responseJson.setMessage("不是中转仓结袋卡！");
            return responseJson;
        }
        if (!ScanShipmentStatus.LOADED.intCode().equals(scanShipment.getStatus())) {
            responseJson.setMessage("该结袋卡号还未装车");
            return responseJson;
        }
        if (scanShipment.getPackCarId() == null) {
            responseJson.setMessage("该结袋卡没有装车记录");
            return responseJson;
        }
        WhPackCarRecord packCarRecord = whPackCarRecordService.getWhPackCarRecord(scanShipment.getPackCarId());
        if (packCarRecord == null) {
            responseJson.setMessage("该结袋卡号没有装车记录");
            return responseJson;
        }
        SCANSHIPMENTLOG.log(scanShipment.getId(), "撤回装车扫描结袋卡");

        if (packCarRecord.getTotalBagNum() > 1) {
            packCarRecord.setTotalBagNum(packCarRecord.getTotalBagNum() - 1);
            whPackCarRecordService.updateWhPackCarRecord(packCarRecord);
        } else {
            whPackCarRecordService.deleteWhPackCarRecord(packCarRecord.getId());
        }
        whScanShipmentService.undoIntoCar(scanShipment.getId());
        logger.info("结袋卡撤回装车: scanShipmentId[" + scanShipment.getId() + "]");

        // 修改回状态
        String apvStep = "Shipped Products";
        if(scanShipment.getBagNo().startsWith("TEMU")) {
            deliverOrderService.updateAllocationProductVoucherStepByScanShipmentIdTemu(scanShipment.getId(), apvStep);
        } else {
            deliverOrderService.updateAllocationProductVoucherStepByScanShipmentIdTransfer(scanShipment.getId(), apvStep);
        }
        jsonObject.put("logisticsChannel", scanShipment.getLogisticsCompanyCode());
        jsonObject.put("logisticsChannelName", scanShipment.getLogisticsCompanyCode());
        responseJson.setStatus(StatusCode.SUCCESS);
        responseJson.setMessage(JSON.toJSONString(jsonObject));
        return responseJson;
    }

    @GetMapping("queryNoPackCodeTransfer")
    public ResponseJson queryNoPackCodeTransfer(@RequestParam(required = false) Boolean isSmtSelfDelivery) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        Integer userId = DataContextHolder.getUserId();
        if (userId == null){
            responseJson.setMessage("请添加userId请求头");
            return responseJson;
        }
        Timestamp timestamp = new Timestamp(System.currentTimeMillis() - 1000 * 60 * 60 * 48);//当前时间前48小时
        WhScanShipmentQueryCondition queryCondition = new WhScanShipmentQueryCondition();
        queryCondition.setLoadUser(userId);
        queryCondition.setFromLoadDate(DateUtils.formatDate(timestamp, DateUtils.STANDARD_DATE_PATTERN));
        queryCondition.setStatus(ScanShipmentStatus.LOADED.intCode());
        queryCondition.setQueryNullPackCarId(true);
        List<WhScanShipment> whScanShipments = whScanShipmentService.queryWhScanShipments(queryCondition, null);
        if (CollectionUtils.isNotEmpty(whScanShipments)) {
            if (Boolean.TRUE.equals(isSmtSelfDelivery)) {
                whScanShipments = whScanShipments.stream().filter(c -> "SMTZJ".equals(c.getLogisticsCompanyCode())).collect(toList());
            } else {
                whScanShipments = whScanShipments.stream().filter(c -> !"SMTZJ".equals(c.getLogisticsCompanyCode())).collect(toList());
            }
        }
        List<Map<String, String>> maps = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(whScanShipments)) {
            String tempCode = whScanShipments.get(0).getLogisticsCompanyCode();
            for (int i = whScanShipments.size() - 1; i >= 0; i--) {
                WhScanShipment whScanShipment = whScanShipments.get(i);
                String logisticsCompany = whScanShipment.getLogisticsCompanyCode();
                if (StringUtil.equals(tempCode, logisticsCompany)) {
                    HashMap<String, String> map = new HashMap<>(8);
                    map.put("bagNo", whScanShipment.getBagNo());
                    String companyName = ShippingCompanyEnum.getNameByCode(logisticsCompany);
                    String channelName = ShippingCompanyEnum.getNameByCode(logisticsCompany);
                    if (StringUtils.isBlank(companyName)) {
                        companyName = logisticsCompany;
                        channelName = logisticsCompany;
                        WhDeliveryCompany deliveryCompany = whShippingMethodService.getWhDeliveryCompanyDetailByDeliveryCompanyCode(logisticsCompany);
                        if (deliveryCompany != null) {
                            channelName = deliveryCompany.getName();
                            WhCollectCompany collectCompany2 = deliveryCompany.getWhCollectCompany();
                            if (collectCompany2 != null) {
                                companyName = collectCompany2.getName();
                            }
                        }
                    }
                    map.put("logisticsChannelName", channelName);
                    map.put("logisticsCompanyName", companyName);
                    maps.add(map);
                }
            }
            responseJson.setStatus(StatusCode.SUCCESS);
            responseJson.setMessage(JSON.toJSONString(maps));
            return responseJson;
        } else {
            responseJson.setStatus(StatusCode.SUCCESS);
            return responseJson;
        }
    }

    @PostMapping("queryNoPackBagNos")
    public ResponseJson queryNoPackBagNos() {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        Integer userId = DataContextHolder.getUserId();
        if (userId == null){
            responseJson.setMessage("请添加userId请求头");
            return responseJson;
        }

        Timestamp timestamp = new Timestamp(System.currentTimeMillis() - 1000 * 60 * 60 * 48);//当前时间前48小时
        WhScanShipmentQueryCondition queryCondition = new WhScanShipmentQueryCondition();
        queryCondition.setLoadUser(userId);
        queryCondition.setLoadDate(timestamp);
        List<Map<String, Object>> maps = whScanShipmentService.queryNoPackBagNos(queryCondition);
        if (CollectionUtils.isNotEmpty(maps)) {
            String tempCode = null;
            for (int i = maps.size() - 1; i >= 0; i--) {
                Map<String, Object> map = maps.get(i);
                String logisticsCompany = (String) map.get("logisticsCompany");
                if (i == maps.size() - 1) {
                    tempCode = logisticsCompany;
                } else if (!StringUtil.equals(tempCode, logisticsCompany)) {
                    maps.remove(i);
                }
            }
            responseJson.setStatus(StatusCode.SUCCESS);
            responseJson.setMessage(JSON.toJSONString(maps));
            return responseJson;
        } else {
            responseJson.setStatus(StatusCode.SUCCESS);
            return responseJson;
        }
    }

    @GetMapping("getExpressCompany")
    public ResponseJson getExpressCompany(){
        ResponseJson responseJson = new ResponseJson(StatusCode.SUCCESS);
        SystemParam systemParam = CacheUtils.SystemParamGet("SPECIFIED_DESC.EXPRESS_COMPANY");
        if (systemParam != null && StringUtil.isNotBlank(systemParam.getParamValue())) {
            responseJson.setMessage(systemParam.getParamValue());
        }
        else {
            String list = SelectJson.getList(ShippingCompanyEnum.values());
            responseJson.setMessage(list);
        }
        return responseJson;
    }


    //获取揽收公司接口
    @GetMapping("getCollectCompany")
    public ResponseJson getCollectCompany(){
        ResponseJson responseJson = new ResponseJson(StatusCode.SUCCESS);
        SystemParam systemParam = CacheUtils.SystemParamGet("SPECIFIED_DESC.EXPRESS_COMPANY");
        if (systemParam == null || StringUtil.isBlank(systemParam.getParamValue())) {
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage("配置递四方,跨越揽收公司,请先配置揽收公司!");
            return responseJson;
        }
        List<SelectJson> selectJsons = JSONObject.parseArray(systemParam.getParamValue(), SelectJson.class);
        List<String> list = Arrays.asList("递四方","跨越");
        selectJsons= selectJsons.stream().filter(selectJson -> list.contains(selectJson.getText())).collect(toList());

        if (CollectionUtils.isEmpty(selectJsons)) {
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage("未查询到递四方,跨越揽收公司,请先配置揽收公司!");
            return responseJson;
        }
        responseJson.setMessage(JSON.toJSONString(selectJsons));
        return responseJson;
    }

    //扫描仓发LBX号装车
    @RequestMapping(value = "scanLbxNoLoadSmt", method = {RequestMethod.POST})
    public ResponseJson scanLbxNoLoad(@ModelAttribute("domain") AndroidProductDo domain) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        String apvNo = domain.getApvNo();
        String expressCompany = domain.getExpressCompany();
        if (StringUtils.isBlank(apvNo) || StringUtils.isBlank(expressCompany)){
            responseJson.setMessage("揽收公司或LBX单号为空！");
            return responseJson;
        }
        String lbxNoStr= StringRedisUtils.get(RedisConstant.WAREHOUSE_LOADING_SUMMARY + DataContextHolder.getUserId());
        if (StringUtils.isNotBlank(lbxNoStr)) {
            List<String> lbxNoList = CommonUtils.splitList(lbxNoStr, ",");
            if (!lbxNoList.contains(apvNo)){
                responseJson.setMessage("请继续扫描"+lbxNoStr+"如果找不到包裹，请先点击撤销操作");
                return responseJson;
            }
        }

        WhFbaAllocationQueryCondition query=new WhFbaAllocationQueryCondition();
        query.setLoadBy(DataContextHolder.getUserId());
        query.setLoaded(true);
        List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isNotEmpty(whFbaAllocations)) {
            String companyName = whFbaAllocations
                    .stream()
                    .map(WhFbaAllocation::getItems)
                    .flatMap(Collection::stream)
                    .map(WhFbaAllocationItem::getCompanyName)
                    .filter(StringUtils::isNotBlank)
                    .findFirst()
                    .orElse(null);
            if (StringUtils.isNotBlank(companyName) && !Objects.equals(ShippingCompanyEnum.getCodeByName(companyName), expressCompany)) {
                responseJson.setMessage("揽收公司必须与上一个单号的揽收公司一致");
                return responseJson;
            }
        }
        query = new WhFbaAllocationQueryCondition();
        query.setIsTransferOrder(true);
        query.setQueryWhAsnExtra(true);
        query.setLbxNo(apvNo);
        List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(whFbaAllocationList)) {
            responseJson.setMessage(String.format("LBX单号:%s不存在！", apvNo));
            return responseJson;
        }
        WhFbaAllocation whFbaAllocation = whFbaAllocationList.get(0);
        if (!AsnPrepareStatus.DELIVER.intCode().equals(whFbaAllocation.getStatus())) {
            responseJson.setMessage("该订单状态不是已交运，不能进行装车操作！");
            return responseJson;
        }
        WhAsnExtra whAsnExtra = whFbaAllocation.getWhAsnExtra();
        if (whAsnExtra != null && whAsnExtra.getCollectMethod() != null && !CollectMethodEnum.COLLECT.equals(whAsnExtra.getCollectMethod())) {
            responseJson.setMessage("该订单不是仓发上门揽单据，不能在上门揽装车页面操作！");
            return responseJson;
        }
        if (CollectionUtils.isEmpty(whFbaAllocation.getItems())) {
            responseJson.setMessage("仓发单明细数据不存在！");
            return responseJson;
        }
        boolean allMatch = whFbaAllocation.getItems()
                .stream()
                .filter(item -> item.getLoadBy() != null)
                .allMatch(item -> item.getLoadBy().equals(DataContextHolder.getUserId()));
        if (!allMatch) {
            responseJson.setMessage("该订单已被其它用户扫描装车！");
            return responseJson;
        }

        List<WhFbaAllocationItem> whFbaAllocationItemList = whFbaAllocation.getItems()
                .stream()
                .filter(item -> (item.getLoadBy() == null || item.getLoadTime() == null) && apvNo.equals(item.getTemuTagUrl()))
                .collect(toList());
        if (CollectionUtils.isEmpty(whFbaAllocationItemList)) {
            responseJson.setMessage(String.format("LBX单号:%s不存在或已装车！", apvNo));
            return responseJson;
        }
        try {
            List<String> lbxNoList = whFbaAllocationService.doUndoIntoCar(whFbaAllocation, whFbaAllocationItemList, apvNo, expressCompany);
            responseJson.setStatus(StatusCode.SUCCESS);
            if (CollectionUtils.isNotEmpty(lbxNoList)) {
                StringRedisUtils.set(RedisConstant.WAREHOUSE_LOADING_SUMMARY + DataContextHolder.getUserId(),StringUtils.join(lbxNoList, ","));
            }
            if (CollectionUtils.isEmpty(lbxNoList) && StringUtils.isNotBlank(lbxNoStr)) {
                StringRedisUtils.del(RedisConstant.WAREHOUSE_LOADING_SUMMARY + DataContextHolder.getUserId());
            }
            responseJson.setMessage(CollectionUtils.isEmpty(lbxNoList)?"装车成功":"该包裹对应发货单有多个LBX，请继续扫描"+StringUtils.join(lbxNoList, ",")+"如果找不到包裹，请点击撤销操作");
        } catch (Exception e) {
            logger.error("装车失败！"+e.getMessage(), e);
            responseJson.setMessage("装车失败！"+e.getMessage());
        }
        return responseJson;
    }

    //查询PDA仓发装车汇总列表
    @RequestMapping(value = "lbxTodayIntoCarTotal", method = {RequestMethod.GET})
    public ResponseJson todayIntoCarTotal(@RequestParam("loadBy") Integer loadBy) {
        ResponseJson responseJson = new ResponseJson(StatusCode.SUCCESS);
        List<WarehouseLoadingSummary> list = whFbaAllocationService.queryTodayIntoCarTotal(loadBy);
        responseJson.setMessage(JSON.toJSONString(list));
        return responseJson;
    }

    //撤销仓发装车订单
    @RequestMapping(value = "lbxNoUndoIntoCar", method = {RequestMethod.GET})
    public ResponseJson lbxNoUndoIntoCar(@RequestParam String apvNo) {
        logger.info("撤销装车订单，lbxNo:{}", apvNo);
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (StringUtils.isBlank(apvNo) ){
            responseJson.setMessage("LBX单号为空！");
            return responseJson;
        }
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setIsTransferOrder(true);
        query.setQueryWhAsnExtra(true);
        query.setLbxNo(apvNo);
        List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(whFbaAllocationList)) {
            responseJson.setMessage(String.format("LBX单号:%s不存在！", apvNo));
            return responseJson;
        }
        List<WhFbaAllocationItem> items = whFbaAllocationList.get(0).getItems();
        if (CollectionUtils.isEmpty(items)) {
            responseJson.setMessage("该订单没有明细，不能进行撤销装车操作！");
            return responseJson;
        }
        List<Integer> itemIdList = items.stream().filter(item -> apvNo.equals(item.getTemuTagUrl())).map(WhFbaAllocationItem::getId).distinct().collect(toList());
        if (CollectionUtils.isEmpty(itemIdList)) {
            responseJson.setMessage("该订单没有对应的明细，不能进行撤销装车操作！");
            return responseJson;
        }
        boolean allMatch = items
                .stream()
                .filter(f -> !apvNo.equals(f.getTemuTagUrl()))
                .allMatch(a->StringUtils.isBlank(a.getCompanyName()));
        try{
            whFbaAllocationItemService.undoLbx(itemIdList,AsnPrepareStatus.LOADED.intCode());
            String lbxNoStr= StringRedisUtils.get(RedisConstant.WAREHOUSE_LOADING_SUMMARY + DataContextHolder.getUserId());
            if (StringUtils.isNotBlank(lbxNoStr)) {
                List<String> lbxNoList = CommonUtils.splitList(lbxNoStr, ",");
                if (!lbxNoList.contains(apvNo)) {
                    lbxNoList.add(apvNo);
                    StringRedisUtils.set(RedisConstant.WAREHOUSE_LOADING_SUMMARY + DataContextHolder.getUserId(),StringUtils.join(lbxNoList, ","));
                }
                if(allMatch){
                    StringRedisUtils.del(RedisConstant.WAREHOUSE_LOADING_SUMMARY + DataContextHolder.getUserId());
                }
            }
        }catch (Exception e){
            logger.error("撤销装车订单失败，lbxNo:{}", apvNo, e);
            responseJson.setMessage("撤销装车失败！");
            return responseJson;
        }
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }

    //提交装车
    @RequestMapping(value = "warehouseSubmissionCarLoad", method = {RequestMethod.GET})
    public ResponseJson warehouseSubmissionCarLoad(@RequestParam("loadBy") Integer loadBy) {
        ResponseJson responseJson=new ResponseJson(StatusCode.FAIL);
        WhFbaAllocationQueryCondition query=new WhFbaAllocationQueryCondition();
        query.setLoadBy(loadBy);
        query.setLoaded(true);
        List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(whFbaAllocationList)) {
            responseJson.setMessage("无装车数据！");
            return responseJson;
        }
        String lbxNoStr= StringRedisUtils.get(RedisConstant.WAREHOUSE_LOADING_SUMMARY + loadBy);
        if (StringUtils.isNotBlank(lbxNoStr)) {
            responseJson.setMessage("请继续扫描"+lbxNoStr+"如果找不到包裹，请先点击撤销操作");
            return responseJson;
        }
        try {
            whPackCarRecordService.warehouseSubmissionCarLoad(whFbaAllocationList);
        } catch (Exception e) {
            responseJson.setMessage("提交仓发装车数据失败！"+e.getMessage());
            return responseJson;
        }
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;

    }

    /**
     * 仓发自寄装车
     * @param domain 装车请求参数
     * @return 装车结果
     */
    @RequestMapping(value = "loadSelfDelivery", method = {RequestMethod.POST})
    public ResponseJson loadSelfDelivery(@ModelAttribute("domain") AndroidProductDo domain) {
        logger.info("android loadSelfDelivery bagNo：" + domain.getBagNo());
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        
        String bagNo = domain.getBagNo();
        String driver = domain.getDriver();
        String vehicleNo = domain.getVehicleNo();
        String deliveryDate = domain.getDeliveryDate();
        // 0默认，1强制装车，-1不强制装车
        Integer isForce = domain.getIsForce();
        
        // 基础参数校验
        if (StringUtils.isBlank(bagNo)) {
            responseJson.setMessage("结袋卡号不能为空!");
            return responseJson;
        }
        if (StringUtils.isBlank(driver)) {
            responseJson.setMessage("司机不能为空!");
            return responseJson;
        }
        if (StringUtils.isBlank(vehicleNo)) {
            responseJson.setMessage("车牌号不能为空!");
            return responseJson;
        }
        if (deliveryDate == null) {
            responseJson.setMessage("送仓日期不能为空!");
            return responseJson;
        }
        logger.warn("仓发自寄装车扫描 bagNo:" + bagNo + ", driver:" + driver + ", vehicleNo:" + vehicleNo + ", deliveryDate:" + deliveryDate);
        
        // 1.1 获取结袋卡信息并校验状态
        WhScanShipment scanShipment = whScanShipmentService.getWhScanShipmentByBagNo(bagNo);
        if (scanShipment == null) {
            responseJson.setMessage("该结袋卡号没有结袋信息！");
            return responseJson;
        }
        // 校验结袋卡状态 - 必须是已核重状态
        if (!(ScanShipmentStatus.WEIGHTED.intCode().equals(scanShipment.getStatus()) || ScanShipmentStatus.LOAD_FAILED.intCode().equals(scanShipment.getStatus()))) {
            responseJson.setMessage("该结袋卡不是可装车状态");
            return responseJson;
        }
        // 校验是否已核重
        if (scanShipment.getActualTotalWeight() == null) {
            responseJson.setMessage("包装号" + bagNo + "没有进行核重操作");
            return responseJson;
        }
        // 1.2 判断是否是中转仓结袋卡
        if (!Boolean.TRUE.equals(scanShipment.getIsTransferHouse())){
            responseJson.setMessage("不是中转仓结袋卡！");
            return responseJson;
        }
        // 1.3 校验结袋卡物流方式是否是SMTZJ
        if (!"SMTZJ".equals(scanShipment.getLogisticsCompanyCode())) {
            responseJson.setMessage("该结袋卡不是仓发自寄！");
            return responseJson;
        }
        // 重量差
        Double weightDiff = scanShipment.getWeightDiff();
        Double minScope = scanShipment.getMinScope();
        Double maxScope = scanShipment.getMaxScope();

        // 1.4 校验送仓日期是否在可预约送仓日期内
        // 校验不能早于今天
        LocalDate inputDate = LocalDate.parse(deliveryDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        if (inputDate.isBefore(LocalDate.now())) {
            responseJson.setMessage("送仓日期不能早于今天！");
            return responseJson;
        }
        // 根据bagNo查询该结袋卡的可预约送仓日期
        WhWarehouseShipmentQueryCondition availableDateQuery = new WhWarehouseShipmentQueryCondition();
        availableDateQuery.setBagNo(bagNo);
        List<WhWarehouseShipment> warehouseShipments = whWarehouseShipmentService.queryWhWarehouseShipments(availableDateQuery, null);
        if(CollectionUtils.isEmpty(warehouseShipments) || StringUtils.isBlank(warehouseShipments.get(0).getAvailableDeliveryDate())){
            responseJson.setMessage("该结袋卡没有仓发自寄记录和可预约送仓日期，请在中转仓结袋卡页面重试获取！");
            return responseJson;
        }
        WhWarehouseShipment warehouseShipment = warehouseShipments.get(0);
        if (!StringUtils.contains(warehouseShipment.getAvailableDeliveryDate(), deliveryDate)){
            responseJson.setMessage("送仓日期不在可预约送仓日期范围内！");
            return responseJson;
        }
        
        // 校验是否有发货订单
        WhScanShipmentToApvQueryCondition queryCondition = new WhScanShipmentToApvQueryCondition();
        queryCondition.setScanShipmentId(scanShipment.getId());
        List<WhScanShipmentToApv> toApvs = whScanShipmentToApvService.queryWhScanShipmentToApvs(queryCondition, null);
        if (CollectionUtils.isEmpty(toApvs)) {
            SCANSHIPMENTLOG.log(scanShipment.getId(), "仓发自寄装车失败",
                    new String[][] {{ "原因", "没有发货订单" }});
            responseJson.setMessage("包装号" + bagNo + "下没有发货订单");
            return responseJson;
        }
        String lbxNos = toApvs.stream().map(WhScanShipmentToApv::getApvNo).collect(Collectors.joining(","));
        WhFbaAllocationQueryCondition fbaQuery = new WhFbaAllocationQueryCondition();
        fbaQuery.setQueryWhAsnExtra(true);
        fbaQuery.setLbxNo(lbxNos);
        List<WhFbaAllocation> fbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(fbaQuery, null);
        if (CollectionUtils.isEmpty(fbaAllocations)) {
            responseJson.setMessage("未找到结袋明细对应的海外仓发货单信息");
            return responseJson;
        }

        if (isForce == 0) {
            if (minScope != null && maxScope != null && (weightDiff < minScope || weightDiff > maxScope)) {
                responseJson.setMessage("confirm,包装号" + bagNo + "重量差超出规定重量差范围，是否强制装车？");
                return responseJson;
            }else{
                SCANSHIPMENTLOG.log(scanShipment.getId(), "PDA装车扫描结袋卡成功");
            }
        } else if (isForce == -1) {
            scanShipment.setStatus(ScanShipmentStatus.LOAD_FAILED.intCode());
            scanShipment.setLoadUser(DataContextHolder.getUserId());
            whScanShipmentService.updateWhScanShipment(scanShipment);
            SCANSHIPMENTLOG.log(scanShipment.getId(), ScanShipmentLogType.LOAD_FAILED.getName(),new String[][] {{ "原因", "没有强制装车" }});
            logger.info("结袋卡装车失败: bagNo[ " + scanShipment.getBagNo() + " ]");
            responseJson.setMessage("装车失败!");
            return responseJson;
        }else{
            SCANSHIPMENTLOG.log(scanShipment.getId(), "PDA装车扫描结袋卡成功(强制装车)");
        }

        try {
            warehouseShipment.setDeliveryman(driver);
            warehouseShipment.setLicensePlateNumber(vehicleNo);
            warehouseShipment.setActualDeliveryDate(new Timestamp(DateUtils.stringToDate(deliveryDate, "yyyy-MM-dd").getTime()));
            String licensePlateNo = whFbaAllocationHandleService.genSelfDeliveryOrder(fbaAllocations, warehouseShipment, 0);
            if (StringUtils.isBlank(licensePlateNo)) {
                responseJson.setMessage("生成自寄单失败!");
                return responseJson;
            }
            // 2.1 更新仓发自寄记录
            whWarehouseShipmentService.updateWhWarehouseShipment(warehouseShipment);

            // 2.2 修改结袋卡状态为已装车
            whPackCarRecordService.updateToload(scanShipment, "transfer");


            // 记录日志
            SCANSHIPMENTLOG.log(scanShipment.getId(), "仓发自寄装车成功",
                    new String[][] {
                        { "送仓日期", deliveryDate },
                        { "司机", driver },
                        { "车牌号", vehicleNo }
                    });

            // 构建返回结果
            Map<String, String> body = new HashMap<>();
            body.put("bagNo", bagNo);
            body.put("logisticsCompany", scanShipment.getLogisticsCompanyCode());
            body.put("logisticsCompanyName", ShippingCompanyEnum.getNameByCode(scanShipment.getLogisticsCompanyCode()));
            responseJson.setStatus(StatusCode.SUCCESS);
            responseJson.setMessage(JSON.toJSONString(body));
        } catch (Exception e) {
            logger.error("仓发自寄装车失败:{} ", e.getMessage(), e);
            responseJson.setMessage("装车失败：" + e.getMessage());
            return responseJson;
        }
        return responseJson;
    }


}
