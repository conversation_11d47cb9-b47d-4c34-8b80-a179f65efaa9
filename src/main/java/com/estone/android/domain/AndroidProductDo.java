package com.estone.android.domain;

import com.estone.allocation.bean.WhApvAllocation;
import com.estone.picking.bean.WhAllocationPickTask;
import com.estone.warehouse.bean.WhReturn;
import lombok.Data;

import java.util.List;

/**
 * 
 * @Description:
 * 
 * @ClassName: AndroidProductDo
 * @Author: qinyangkai
 * @Date: 2018年8月17日
 * @Version: 0.0.1
 */
@Data
public class AndroidProductDo {

    private Integer id; // 入库异常单ID

    private String locationNumber; // 入库异常单对应的库位

    private String lessGridNoStr;// 播种差异格子号码（播种<已拣）
    private String lessPickNoStr;// 少拣的格子
    private String cancelGridNoStr;// 取消的格子

    private String boxCayi;// 播种差异周转筐

    private String boxNo; // 移动库位号

    private String sku;

    private String spu;

    private Integer ceId;

    private Integer status; // 状态

    private Integer inId;// 入库单号

    private Integer quantity;// 上架数量

    private Integer checkInType;// 入库单类型

    private String location;// 货位

    private Integer receivePerson;// 领取拣货任务人

    private String receivePersonType;// 拣货人类型 新仓 老仓

    private Integer taskId;// 拣货任务ID

    private Integer taskType;// 拣货任务类型

    private Integer taskSkuId;// 拣货任务SKUId

    private Integer taskItemId;// 拣货任务详情Id

    private Integer needQuantity;// 需拣数量

    private Integer pickQuantity;// 已拣数量

    private Integer pickStatus;// 拣货状态

    private String relationNo;// 关联编号

    private Integer presentUser;// 当前使用人

    private Integer warehouseType;// 仓库类型

    private String nextLocation;// 下一个货位

    private String nextSku;// 下一个Sku

    private Integer returnId;// 返架ID

    private Integer returnItemId;// 返架条目ID

    private Integer abroadReturnId;// 海外退件上架ID

    private Integer abroadReturnItemId;// 海外退件上架条目ID

    private String bzycBoxNo;// 播种异常周转框

    private Integer pickingTaskType;// 拣货任务标识

    private String taskNo;// 拣货任务号

    // 扫描装车专用*****************************
    private String bagNo;// 结袋卡号
    private Integer isForce;// 0默认，1强制装车，-1不强制装车
    private String shippingCompanyCode;// 物流公司Code
    private Integer warehouseId;// 仓库
    private String expressCompany; // 快递方式
    private String expressOrderNo; // 快递单号
    private String type; // 类型 transfer：中转仓装车 temu：拼多多装车
    // 仓发自寄装车专用
    private String driver; // 司机
    private String vehicleNo; // 车牌号
    private String deliveryDate; // 送仓日期 yyyy-MM-dd
    // *****************************

    // PDA 移库专用****************
    private String oldLocation;
    private String newLocation;
    // ************************

    // 调拨拣货任务专用*************
    private String allocationNo;
    private Integer allocationId;
    private String allocationBoxNo;// 调拨箱号
    private Integer boxNoQuantity;// 调拨箱号数量
    /**
     * 调拨拣货任务
     */
    private WhAllocationPickTask whAllocationPickTask;

    /**
     * 调拨单
     */
    private WhApvAllocation whApvAllocation;
    // **************************
    private String allocationBoardNo;// 卡板号
    private String sowBoxNo;// 播种箱号
    private String subTaskNo;// 子任务号

    //扫描收货绑定快递单号
    private String expressNo;//快递单号
    private Integer bindNewBoxNo;//绑定新周转框(默认：0：不绑定，1：绑定)

    private String uuid;// 唯一码

    private List<String> uuidList;// 唯一码集合

    /**
     * 中转仓
     */
    private String site;

    private String fnSku;

    //类型字段，正常采购单类型为：1：本仓，海外仓采购单类型为: 2：中转仓, 3：优选仓
    private Integer exceptionType;

    private boolean queryBarCode = false;//是否查询SKU编码

    /** 库内返架 **/
    private WhReturn whReturn;

    private Integer realPickNum;

    private String uniqueSku;

    // 拣货 当前拣货的对应的库存id
    private List<Integer> stockIds;

    private boolean preStoreUp = false;

    private Boolean commit;

    private Integer isAsn;

    private String apvNo;

    private Double width;
    private Double height;
    private Double length;

    private String startPickDate;

    private boolean skuWeightFlag;

    /** 新增装车相关字段 **/
    private Integer deliveryMethod; // 送货方式
    private String logisticsNo;    // 物流单号
}
