package com.estone.transfer.service;

import com.estone.transfer.bean.FbaUpMonitor;
import com.estone.transfer.bean.FbaUpMonitorQueryCondition;
import com.whq.tool.component.Pager;
import java.util.List;
import java.util.Map;

public interface FbaUpMonitorService {
    List<FbaUpMonitor> queryAllFbaUpMonitors();

    List<FbaUpMonitor> queryFbaUpMonitors(FbaUpMonitorQueryCondition query, Pager pager);

    /**
     * 查询FBA货件监控数据数量
     * @param query 查询条件
     * @return 数量
     */
    int queryFbaUpMonitorCount(FbaUpMonitorQueryCondition query);

    /**
     * 获取各状态的统计数据
     * @param query 基础查询条件
     * @return 状态统计数据 Map<状态代码, 数量>
     */
    Map<String, Integer> getStatusStatistics(FbaUpMonitorQueryCondition query);

    FbaUpMonitor getFbaUpMonitor(Integer id);

    FbaUpMonitor getFbaUpMonitorDetail(Integer id);

    FbaUpMonitor queryFbaUpMonitor(FbaUpMonitorQueryCondition query);

    void createFbaUpMonitor(FbaUpMonitor fbaUpMonitor);

    void batchCreateFbaUpMonitor(List<FbaUpMonitor> entityList);

    void deleteFbaUpMonitor(Integer id);

    void updateFbaUpMonitor(FbaUpMonitor fbaUpMonitor);

    void batchUpdateFbaUpMonitor(List<FbaUpMonitor> entityList);

    /**
     * 根据FBA货件编号生成FbaUpMonitor对象并保存到数据库
     * 为每个SKU生成一条FbaUpMonitor记录
     * 
     * @param fbaNo FBA货件编号
     */
    void generateFbaUpMonitorFromFbaAllocation(String fbaNo);

    /**
     * 根据变化月份集合重新计算预警状态
     * 查询变化月份内已签收和部分上架状态的FbaUpMonitor记录，重新计算预警状态
     *
     * @param changedMonths 变化的月份集合（1-12）
     */
    void recalculateAlertStatusByChangedMonths(List<Integer> changedMonths);


    void pushDingTalkNotifications(List<FbaUpMonitor> fbaUpMonitors);
}