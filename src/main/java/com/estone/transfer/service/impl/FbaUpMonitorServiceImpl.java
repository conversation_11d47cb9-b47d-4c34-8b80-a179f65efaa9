package com.estone.transfer.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.exquisite.service.BaseService;
import com.estone.system.param.bean.SystemParam;
import com.estone.transfer.bean.*;
import com.estone.transfer.dao.FbaUpMonitorDao;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.estone.transfer.enums.FbaAlertStatus;
import com.estone.transfer.enums.FbaStatus;
import com.estone.transfer.enums.SeasonType;
import com.estone.transfer.service.FbaUpMonitorService;
import com.estone.transfer.service.WhFbaAllocationService;
import com.estone.transfer.bean.SeasonAlertSetting;
import com.estone.transfer.utils.SeasonAlertSettingUtil;
import com.estone.warehouse.enums.MaterialPurchaseEnums;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

@Service("fbaUpMonitorService")
@Slf4j
public class FbaUpMonitorServiceImpl extends BaseService<FbaUpMonitorQueryCondition, FbaUpMonitor> implements FbaUpMonitorService {

    private final static ThreadPoolExecutor executors = ExecutorUtils.newFixedThreadPool(10);

    @Resource
    private FbaUpMonitorDao fbaUpMonitorDao;

    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    @Override
    public List<FbaUpMonitor> list(FbaUpMonitorQueryCondition search, Pager pager) {
        return queryFbaUpMonitors(search, pager);
    }

    @Override
    public FbaUpMonitor getFbaUpMonitor(Integer id) {
        FbaUpMonitor fbaUpMonitor = fbaUpMonitorDao.queryFbaUpMonitor(id);
        return fbaUpMonitor;
    }

    @Override
    public FbaUpMonitor getFbaUpMonitorDetail(Integer id) {
        FbaUpMonitor fbaUpMonitor = fbaUpMonitorDao.queryFbaUpMonitor(id);
        // 关联查询
        return fbaUpMonitor;
    }

    @Override
    public FbaUpMonitor queryFbaUpMonitor(FbaUpMonitorQueryCondition query) {
        Assert.notNull(query, "query is null!");
        FbaUpMonitor fbaUpMonitor = fbaUpMonitorDao.queryFbaUpMonitor(query);
        return fbaUpMonitor;
    }

    @Override
    public List<FbaUpMonitor> queryAllFbaUpMonitors() {
        return fbaUpMonitorDao.queryFbaUpMonitorList();
    }

    @Override
    public List<FbaUpMonitor> queryFbaUpMonitors(FbaUpMonitorQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = fbaUpMonitorDao.queryFbaUpMonitorCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<FbaUpMonitor>();
            }
        }
        List<FbaUpMonitor> fbaUpMonitors = fbaUpMonitorDao.queryFbaUpMonitorList(query, pager);
        return fbaUpMonitors;
    }

    @Override
    public int queryFbaUpMonitorCount(FbaUpMonitorQueryCondition query) {
        Assert.notNull(query, "query is null!");
        return fbaUpMonitorDao.queryFbaUpMonitorCount(query);
    }

    @Override
    public Map<String, Integer> getStatusStatistics(FbaUpMonitorQueryCondition query) {
        Assert.notNull(query, "query is null!");
        
        long startTime = System.currentTimeMillis();
        Map<String, Integer> statistics = new HashMap<>();
        
        // 创建基础查询条件副本
        FbaUpMonitorQueryCondition baseQuery = copyBaseQuery(query);
        
        // 定义各种状态查询映射
        Map<String, String> statusQueries = new HashMap<>();
        statusQueries.put("not_received", FbaStatus.NOT_RECEIVED.getCode());
        statusQueries.put("received", FbaStatus.RECEIVED.getCode());
        statusQueries.put("partial", FbaStatus.PARTIAL.getCode());
        statusQueries.put("complete", FbaStatus.COMPLETE.getCode());
        statusQueries.put("overtime", FbaAlertStatus.OVERTIME.getCode());
        
        try {
            log.debug("开始并发查询FBA状态统计，查询任务数: {}", statusQueries.size() + 1);
            
            // 创建并发查询任务列表
            List<Future<StatusQueryResult>> futureList = new ArrayList<>();
            
            // 提交各状态查询任务
            for (Map.Entry<String, String> entry : statusQueries.entrySet()) {
                String statusKey = entry.getKey();
                String statusValue = entry.getValue();
                
                Future<StatusQueryResult> future = executors.submit(() -> {
                    try {
                        FbaUpMonitorQueryCondition statusQuery = copyBaseQuery(baseQuery);
                        int count;
                        
                        if ("overtime".equals(statusKey)) {
                            // 超期预警按预警状态查询
                            List<String> alertStatusList = new ArrayList<>();
                            alertStatusList.add(FbaAlertStatus.SEASON_ALERT.getCode());
                            alertStatusList.add(FbaAlertStatus.PEAK_ALERT.getCode());
                            alertStatusList.add(FbaAlertStatus.OVERTIME.getCode());
                            statusQuery.setAlertStatusList(alertStatusList);
                            count = fbaUpMonitorDao.queryFbaUpMonitorCount(statusQuery);
                        } else {
                            // 其他按货件状态查询
                            statusQuery.setStatus(statusValue);
                            count = fbaUpMonitorDao.queryFbaUpMonitorCount(statusQuery);
                        }
                        
                        log.debug("状态查询任务完成: {} = {}", statusKey, count);
                        return new StatusQueryResult(statusKey, count);
                        
                    } catch (Exception e) {
                        log.error("查询状态统计失败: statusKey={}, statusValue={}", statusKey, statusValue, e);
                        throw new RuntimeException("查询状态统计失败: " + statusKey, e);
                    }
                });
                futureList.add(future);
            }
            
            // 提交总数查询任务
            Future<StatusQueryResult> totalFuture = executors.submit(() -> {
                try {
                    FbaUpMonitorQueryCondition totalQuery = copyBaseQuery(baseQuery);
                    int totalCount = fbaUpMonitorDao.queryFbaUpMonitorCount(totalQuery);
                    log.debug("总数查询任务完成: total = {}", totalCount);
                    return new StatusQueryResult("total", totalCount);
                } catch (Exception e) {
                    log.error("查询总数统计失败", e);
                    throw new RuntimeException("查询总数统计失败", e);
                }
            });
            futureList.add(totalFuture);
            
            log.debug("所有查询任务已提交，等待结果返回...");
            
            // 等待所有查询完成并收集结果
            for (Future<StatusQueryResult> future : futureList) {
                StatusQueryResult result = future.get();
                statistics.put(result.getStatusKey(), result.getCount());
                log.debug("状态查询完成: {} = {}", result.getStatusKey(), result.getCount());
            }
            
            long endTime = System.currentTimeMillis();
            log.info("并发查询FBA状态统计完成，耗时: {}ms，查询结果: {}", endTime - startTime, statistics);
            
        } catch (Exception e) {
            log.error("并发查询状态统计出错", e);
            // 如果并发查询失败，降级为同步查询
            log.warn("降级为同步查询模式");
            return getStatusStatisticsSync(baseQuery, statusQueries);
        }
        
        return statistics;
    }

    /**
     * 同步查询状态统计（降级方案）
     */
    private Map<String, Integer> getStatusStatisticsSync(FbaUpMonitorQueryCondition baseQuery, Map<String, String> statusQueries) {
        long startTime = System.currentTimeMillis();
        Map<String, Integer> statistics = new HashMap<>();
        
        log.warn("使用同步查询模式进行状态统计");
        
        // 批量查询各状态数量
        for (Map.Entry<String, String> entry : statusQueries.entrySet()) {
            FbaUpMonitorQueryCondition statusQuery = copyBaseQuery(baseQuery);
            
            if ("overtime".equals(entry.getKey())) {
                // 超期预警按预警状态查询
                statusQuery.setAlertStatus(entry.getValue());
            } else {
                // 其他按货件状态查询
                statusQuery.setStatus(entry.getValue());
            }
            
            int count = fbaUpMonitorDao.queryFbaUpMonitorCount(statusQuery);
            statistics.put(entry.getKey(), count);
            log.debug("同步查询完成: {} = {}", entry.getKey(), count);
        }
        
        // 查询总数
        int totalCount = fbaUpMonitorDao.queryFbaUpMonitorCount(baseQuery);
        statistics.put("total", totalCount);
        log.debug("同步查询完成: total = {}", totalCount);
        
        long endTime = System.currentTimeMillis();
        log.info("同步查询FBA状态统计完成，耗时: {}ms，查询结果: {}", endTime - startTime, statistics);
        
        return statistics;
    }



    /**
     * 状态查询结果
     */
    private static class StatusQueryResult {
        private final String statusKey;
        private final Integer count;

        public StatusQueryResult(String statusKey, Integer count) {
            this.statusKey = statusKey;
            this.count = count;
        }

        public String getStatusKey() {
            return statusKey;
        }

        public Integer getCount() {
            return count;
        }
    }

    /**
     * 复制基础查询条件，避免查询条件相互污染
     */
    private FbaUpMonitorQueryCondition copyBaseQuery(FbaUpMonitorQueryCondition original) {
        FbaUpMonitorQueryCondition copy = new FbaUpMonitorQueryCondition();
        
        // 复制基础字段
        copy.setShipmentId(original.getShipmentId());
        copy.setFbaNo(original.getFbaNo());
        copy.setDestinationWarehouseId(original.getDestinationWarehouseId());
        copy.setSalesPerson(original.getSalesPerson());
        copy.setSkuCode(original.getSkuCode());
        copy.setFnsku(original.getFnsku());
        copy.setSellerSku(original.getSellerSku());
        
        // 复制扩展查询条件
        copy.setShipmentIdList(original.getShipmentIdList());
        copy.setSkuCodeList(original.getSkuCodeList());
        copy.setFnskuList(original.getFnskuList());
        copy.setSalesPersonList(original.getSalesPersonList());
        // 复制时间范围条件
        copy.setReceivedDateStart(original.getReceivedDateStart());
        copy.setReceivedDateEnd(original.getReceivedDateEnd());
        copy.setCreatedAtStart(original.getCreatedAtStart());
        copy.setCreatedAtEnd(original.getCreatedAtEnd());

        // 注意：不复制status和alertStatus，这些会在查询时单独设置
        
        return copy;
    }

    @Override
    public void createFbaUpMonitor(FbaUpMonitor fbaUpMonitor) {
        try {
            fbaUpMonitorDao.createFbaUpMonitor(fbaUpMonitor);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateFbaUpMonitor(List<FbaUpMonitor> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                fbaUpMonitorDao.batchCreateFbaUpMonitor(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteFbaUpMonitor(Integer id) {
        try {
            fbaUpMonitorDao.deleteFbaUpMonitor(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateFbaUpMonitor(FbaUpMonitor fbaUpMonitor) {
        try {
            fbaUpMonitorDao.updateFbaUpMonitor(fbaUpMonitor);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateFbaUpMonitor(List<FbaUpMonitor> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                fbaUpMonitorDao.batchUpdateFbaUpMonitor(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * 根据FBA货件编号生成FbaUpMonitor对象并保存到数据库
     * 为每个SKU生成一条FbaUpMonitor记录，支持去重处理
     * 
     * @param fbaNo FBA货件编号
     * @throws BusinessException 业务异常
     */
    @Override
    public void generateFbaUpMonitorFromFbaAllocation(String fbaNo) {
        try {
            if (StringUtils.isEmpty(fbaNo)) {
                return;
            }
            log.info("开始根据FBA货件编号生成FbaUpMonitor，fbaNo: {}", fbaNo);
            
            // 查询现有的FbaUpMonitor记录用于去重
            Set<String> existingSkuKeys = getExistingSkuKeys(fbaNo);
            
            // 构建查询条件
            WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
            query.setFbaNo(fbaNo);
            // 查询WhFbaAllocation对象
            List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
            
            if (CollectionUtils.isEmpty(whFbaAllocations)) {
                log.warn("未找到符合条件的WhFbaAllocation记录，fbaNo: {}", fbaNo);
                return;
            }
            
            // 获取第一条记录
            WhFbaAllocation whFbaAllocation = whFbaAllocations.get(0);
            log.info("找到WhFbaAllocation记录，id: {}, shipmentId: {}, salesPerson: {}, items数量: {}", 
                whFbaAllocation.getId(), whFbaAllocation.getShipmentId(), whFbaAllocation.getSalesperson(),
                CollectionUtils.isEmpty(whFbaAllocation.getItems()) ? 0 : whFbaAllocation.getItems().size());
            
            if (!whFbaAllocation.isFba()) return;
            
            // 检查是否有items
            if (CollectionUtils.isEmpty(whFbaAllocation.getItems())) {
                log.warn("WhFbaAllocation没有items数据，无法生成FbaUpMonitor记录，fbaNo: {}", fbaNo);
                return;
            }

            // 处理items并进行去重
            List<FbaUpMonitor> newFbaUpMonitors = processItemsWithDeduplication(whFbaAllocation, existingSkuKeys);
            
            if (CollectionUtils.isEmpty(newFbaUpMonitors)) {
                log.info("所有SKU已存在，无需新增FbaUpMonitor记录，fbaNo: {}", fbaNo);
                return;
            }
            
            // 批量保存到数据库
            batchCreateFbaUpMonitor(newFbaUpMonitors);
            log.info("批量保存FbaUpMonitor成功，新增{}条记录，fbaNo: {}", newFbaUpMonitors.size(), fbaNo);
            
        } catch (Exception e) {
            log.error("生成FbaUpMonitor失败，fbaNo: {}", fbaNo, e);
        }
    }

    /**
     * 获取现有的SKU组合键，用于去重判断
     * 
     * @param fbaNo FBA货件编号
     * @return 现有的SKU组合键集合
     */
    private Set<String> getExistingSkuKeys(String fbaNo) {
        try {
            FbaUpMonitorQueryCondition existingQuery = new FbaUpMonitorQueryCondition();
            existingQuery.setFbaNo(fbaNo);
            List<FbaUpMonitor> existingRecords = queryFbaUpMonitors(existingQuery, null);
            
            Set<String> existingSkuKeys = existingRecords.stream()
                .map(this::buildSkuKey)
                .collect(Collectors.toSet());
                
            log.info("查询到{}条现有FbaUpMonitor记录，fbaNo: {}", existingRecords.size(), fbaNo);
            return existingSkuKeys;
            
        } catch (Exception e) {
            log.warn("查询现有FbaUpMonitor记录失败，将跳过去重处理，fbaNo: {}", fbaNo, e);
            return new HashSet<>();
        }
    }

    /**
     * 处理items并进行去重，返回需要新增的FbaUpMonitor记录
     * 
     * @param whFbaAllocation WhFbaAllocation对象
     * @param existingSkuKeys 现有的SKU组合键集合
     * @return 需要新增的FbaUpMonitor记录列表
     */
    private List<FbaUpMonitor> processItemsWithDeduplication(WhFbaAllocation whFbaAllocation, Set<String> existingSkuKeys) {
        List<WhFbaAllocationItem> items = whFbaAllocation.getItems();
        
        // 根据sku、fnSku、sellerSku分组合并，累加数量字段，并过滤数量为0的记录
        Collection<WhFbaAllocationItem> groupedItems = items.stream()
            .collect(Collectors.groupingBy(item -> 
                String.join("|", 
                    StringUtils.isBlank(item.getProductSku()) ? "NULL" : item.getProductSku(),
                    StringUtils.isBlank(item.getFnSku()) ? "NULL" : item.getFnSku(),
                    StringUtils.isBlank(item.getSellSku()) ? "NULL" : item.getSellSku()
                )
            ))
            .values()
            .stream()
            .map(groupItems -> {
                // 取第一个item作为模板，累加数量字段
                WhFbaAllocationItem merged = new WhFbaAllocationItem();
                WhFbaAllocationItem first = groupItems.get(0);
                // 复制基础字段
                merged.setProductSku(first.getProductSku());
                merged.setFnSku(first.getFnSku());
                merged.setSellSku(first.getSellSku());
                // 累加数量字段
                merged.setLoadNum(groupItems.stream()
                    .mapToInt(item -> item.getLoadNum() != null ? item.getLoadNum() : 0)
                    .sum());
                return merged;
            })
            .filter(item -> item.getLoadNum() != null && item.getLoadNum() > 0)  // 过滤数量为0或null的记录
            .collect(Collectors.toList());
            
        log.info("原始items数量: {}, 分组合并后数量: {}", items.size(), groupedItems.size());
        if (CollectionUtils.isEmpty(groupedItems)) return null;
        // 转换为FbaUpMonitor并进行去重过滤
        List<FbaUpMonitor> newFbaUpMonitors = groupedItems.stream()
            .map(mergedItem -> convertToFbaUpMonitor(whFbaAllocation, mergedItem))
            .filter(Objects::nonNull)  // 过滤掉失败的记录
            .filter(fbaUpMonitor -> {
                String skuKey = buildSkuKey(fbaUpMonitor);
                boolean isNewRecord = !existingSkuKeys.contains(skuKey);
                if (!isNewRecord) {
                    log.debug("跳过重复SKU组合: {}", skuKey);
                }
                return isNewRecord;
            })
            .collect(Collectors.toList());
            
        log.info("去重后需要新增的记录数量: {}", newFbaUpMonitors.size());
        return newFbaUpMonitors;
    }

    /**
     * 构建SKU组合键，用于去重判断
     * 
     * @param fbaUpMonitor FbaUpMonitor对象
     * @return SKU组合键
     */
    private String buildSkuKey(FbaUpMonitor fbaUpMonitor) {
        return String.join("|",
            StringUtils.isBlank(fbaUpMonitor.getSkuCode()) ? "NULL" : fbaUpMonitor.getSkuCode(),
            StringUtils.isBlank(fbaUpMonitor.getFnsku()) ? "NULL" : fbaUpMonitor.getFnsku(),
            StringUtils.isBlank(fbaUpMonitor.getSellerSku()) ? "NULL" : fbaUpMonitor.getSellerSku()
        );
    }

    /**
     * 将WhFbaAllocation对象和具体的item转换为FbaUpMonitor对象
     * 为每个SKU单独生成一条记录
     * 
     * @param whFbaAllocation WhFbaAllocation对象
     * @param item 具体的WhFbaAllocationItem对象
     * @return FbaUpMonitor对象
     */
    private FbaUpMonitor convertToFbaUpMonitor(WhFbaAllocation whFbaAllocation, WhFbaAllocationItem item) {
        FbaUpMonitor fbaUpMonitor = new FbaUpMonitor();
        
        // 基础信息映射
        fbaUpMonitor.setShipmentId(whFbaAllocation.getShipmentId());
        fbaUpMonitor.setFbaNo(whFbaAllocation.getFbaNo());
        fbaUpMonitor.setSalesPerson(whFbaAllocation.getSalesperson());
        
        // 目的仓信息
        if (StringUtils.isNotBlank(whFbaAllocation.getPurposeHouse())) {
            // 这里可能需要根据实际业务逻辑来处理目的仓ID
            // 如果purpose_house存储的是ID，直接转换；如果是字符串，可能需要查询映射
            try {
                fbaUpMonitor.setDestinationWarehouseId(whFbaAllocation.getPurposeHouse());
            } catch (NumberFormatException e) {
                log.warn("目的仓不是数字格式，使用字符串: {}", whFbaAllocation.getPurposeHouse());
                // 如果不是数字，可以设置为null或者使用默认值
                fbaUpMonitor.setDestinationWarehouseId(null);
            }
        }

        int receivedQuantity = item.getLoadNum() != null ? item.getLoadNum() : 0;
        fbaUpMonitor.setTotalQuantity(receivedQuantity);
        fbaUpMonitor.setDifferenceQuantity(receivedQuantity);
        
        // SKU信息（使用当前item的SKU信息）
        fbaUpMonitor.setSkuCode(item.getProductSku());
        fbaUpMonitor.setFnsku(item.getFnSku());
        fbaUpMonitor.setSellerSku(item.getSellSku());
        
        // 时间信息
        fbaUpMonitor.setShippingDate(new Timestamp(System.currentTimeMillis())); // 使用创建时间作为发货时间
        // 状态信息（基于当前item的数量和签收状态判断）
        fbaUpMonitor.setStatus(FbaStatus.NOT_RECEIVED.getCode());
        
        // 等待天数计算
       /* if (fbaUpMonitor.getReceivedDate() != null) {
            long diffInMillies = System.currentTimeMillis() - fbaUpMonitor.getReceivedDate().getTime();
            int waitingDays = (int) (diffInMillies / (24 * 60 * 60 * 1000));
            fbaUpMonitor.setWaitingDays(waitingDays);
        } else {
            fbaUpMonitor.setWaitingDays(0);
        }*/
        
        log.debug("完成WhFbaAllocation和item到FbaUpMonitor的转换，shipmentId: {}, skuCode: {}, totalQuantity: {}, status: {}", 
            fbaUpMonitor.getShipmentId(), fbaUpMonitor.getSkuCode(), fbaUpMonitor.getTotalQuantity(), fbaUpMonitor.getStatus());
        
        return fbaUpMonitor;
    }

    /**
     * 根据变化月份集合重新计算预警状态
     * 查询变化月份内已签收和部分上架状态的FbaUpMonitor记录，重新计算预警状态
     * 使用线程池并发处理不同月份，提高处理效率
     * 
     * @param changedMonths 变化的月份集合（1-12）
     */
    @Override
    public void recalculateAlertStatusByChangedMonths(List<Integer> changedMonths) {
        if (CollectionUtils.isEmpty(changedMonths)) {
            log.info("变化月份集合为空，无需重新计算预警状态");
            return;
        }
        
        log.info("开始根据变化月份重新计算预警状态，变化月份: {}，使用多线程并发处理", changedMonths);
        
        try {
            // 获取季节预警配置
            SeasonAlertSetting seasonConfig = SeasonAlertSettingUtil.getSeasonAlertSetting();
            if (seasonConfig == null) {
                log.warn("未找到季节预警配置，跳过预警状态计算");
                return;
            }
            
            // 设置状态条件：已签收或部分上架
            List<String> statusList = new ArrayList<>();
            statusList.add(FbaStatus.RECEIVED.getCode());
            statusList.add(FbaStatus.PARTIAL.getCode());
            
            // 使用线程池异步处理每个月份，不等待执行完毕
            for (Integer month : changedMonths) {
                executors.submit(() -> {
                    processMonthRecordsAsync(month, statusList, seasonConfig);
                });
            }
            log.info("已提交{}个月份处理任务到线程池，异步执行中...", changedMonths.size());
        } catch (Exception e) {
            log.error("根据变化月份重新计算预警状态失败，变化月份: {}", changedMonths, e);
        }
    }

    /**
     * 异步处理单个月份的记录
     * 
     * @param month 月份（1-12）
     * @param statusList 状态列表
     * @param seasonConfig 季节预警配置
     */
    private void processMonthRecordsAsync(Integer month, List<String> statusList, SeasonAlertSetting seasonConfig) {
        try {
            log.info("开始处理{}月份的记录", month);
            
            // 查询该月份的记录（包括前一年和当前年）
            List<FbaUpMonitor> monthRecords = new ArrayList<>();
            
            // 获取当前年份
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            int currentYear = calendar.get(java.util.Calendar.YEAR);
            
            // 查询当前年份的指定月份
            List<FbaUpMonitor> currentYearRecords = queryRecordsByMonthAndYear(month, currentYear, statusList);
            monthRecords.addAll(currentYearRecords);
            log.debug("查询{}年{}月份记录：{}条", currentYear, month, currentYearRecords.size());
            
            // 查询前一年的指定月份
            int previousYear = currentYear - 1;
            List<FbaUpMonitor> previousYearRecords = queryRecordsByMonthAndYear(month, previousYear, statusList);
            monthRecords.addAll(previousYearRecords);
            log.debug("查询{}年{}月份记录：{}条", previousYear, month, previousYearRecords.size());
            
            log.info("{}月份查询到{}条记录（包括前一年）", month, monthRecords.size());
            
            if (!monthRecords.isEmpty()) {
                // 立即计算该月份的记录
                List<FbaUpMonitor> updatedRecords = recalculateAlertStatus(monthRecords, seasonConfig);
                
                // 立即更新该月份的记录
                if (CollectionUtils.isNotEmpty(updatedRecords)) {
                    batchUpdateFbaUpMonitor(updatedRecords);
                    log.info("{}月份完成计算并更新，更新了{}条记录", month, updatedRecords.size());
                } else {
                    log.info("{}月份无需更新记录", month);
                }
            } else {
                log.info("{}月份无记录需要处理", month);
            }
            
        } catch (Exception e) {
            log.error("处理{}月份记录失败", month, e);
        }
    }







    /**
     * 查询指定年份和月份的FbaUpMonitor记录
     * 
     * @param month 月份（1-12）
     * @param year 年份
     * @param statusList 状态列表
     * @return 该年份月份的记录列表
     */
    private List<FbaUpMonitor> queryRecordsByMonthAndYear(Integer month, Integer year, List<String> statusList) {
        FbaUpMonitorQueryCondition query = new FbaUpMonitorQueryCondition();
        
        // 设置状态条件
        query.setFbaStatusList(statusList);
        
        // 设置签收时间范围：指定年份的指定月份
        java.util.Calendar startCal = java.util.Calendar.getInstance();
        startCal.set(year, month - 1, 1, 0, 0, 0); // month-1 因为Calendar的月份从0开始
        startCal.set(java.util.Calendar.MILLISECOND, 0);
        
        java.util.Calendar endCal = java.util.Calendar.getInstance();
        endCal.set(year, month - 1, 1, 23, 59, 59);
        endCal.set(java.util.Calendar.DAY_OF_MONTH, endCal.getActualMaximum(java.util.Calendar.DAY_OF_MONTH));
        endCal.set(java.util.Calendar.MILLISECOND, 999);
        
        // 设置日期格式为字符串（YYYY-MM-DD）
        java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");
        query.setReceivedDateStart(dateFormat.format(startCal.getTime()));
        query.setReceivedDateEnd(dateFormat.format(endCal.getTime()));
        
        List<FbaUpMonitor> records = queryFbaUpMonitors(query, null);
        
        log.debug("查询{}年{}月份记录详情：开始时间={}, 结束时间={}, 状态={}, 记录数={}", 
            year, month, 
            startCal.getTime(), 
            endCal.getTime(), 
            statusList, 
            records.size());
        
        return records;
    }


    /**
     * 重新计算FbaUpMonitor记录的预警状态
     * 
     * @param records 需要计算的记录列表
     * @param seasonConfig 季节预警配置
     * @return 更新后的记录列表（只包含状态有变化的记录）
     */
    private List<FbaUpMonitor> recalculateAlertStatus(List<FbaUpMonitor> records, SeasonAlertSetting seasonConfig) {
        List<FbaUpMonitor> updatedRecords = new ArrayList<>();
        
        for (FbaUpMonitor record : records) {
            try {
                String oldAlertStatus = record.getAlertStatus();
                String newAlertStatus = record.calculateAlertStatus(seasonConfig);
                
                // 如果返回null，表示不参与计算，跳过该记录
                if (newAlertStatus == null) {
                    log.debug("记录{}不参与预警状态计算，跳过更新", record.getId());
                    continue;
                }
                
                // 只有状态发生变化的记录才需要更新
                if (!Objects.equals(oldAlertStatus, newAlertStatus)) {
                    FbaUpMonitor upMonitor = new FbaUpMonitor();
                    upMonitor.setId(record.getId());
                    upMonitor.setAlertStatus(newAlertStatus);
                    updatedRecords.add(upMonitor);
                    log.debug("记录{}的预警状态发生变化: {} -> {}", 
                        record.getId(), oldAlertStatus, newAlertStatus);
                    // 发送钉钉通知
                    if (FbaAlertStatus.OVERTIME.getCode().equals(newAlertStatus)) {
                        record.setAlertStatus(newAlertStatus);
                        sendDingTalkNotification(record);
                    }
                }
                
            } catch (Exception e) {
                log.error("计算记录{}的预警状态失败", record.getId(), e);
            }
        }
        log.info("预警状态计算完成，总记录数: {}, 状态变化记录数: {}", records.size(), updatedRecords.size());
        return updatedRecords;
    }



    private void sendDingTalkNotification(FbaUpMonitor monitor){
        String message = String.format("【紧急】货件%s已签收%s天,部分商品仍未上架,请立即处理！", monitor.getShipmentId(), monitor.getWaitingDays());
        //钉钉接口规则，推送钉钉消息,每个用户一天相同内容的消息只推送一次
        String ddMessage = String.format("【紧急】【%s】货件%s已签收%s天,部分商品仍未上架,请立即处理！3",FbaAlertStatus.getNameByCode(monitor.getAlertStatus()) ,monitor.getShipmentId(), monitor.getWaitingDays());
        try {
            String salesPerson = monitor.getSalesPerson();
            if (StringUtils.isNotBlank(salesPerson) && salesPerson.contains("-")) {
                salesPerson = salesPerson.split("-")[0];
            }
            if (StringUtils.isBlank(salesPerson)) {
                throw new RuntimeException(monitor.getFbaNo()+"单号未找到销售人员信息,无法发送钉钉消息！");
            }
            SystemParam dingMsgParam = CacheUtils.SystemParamGet("HR_PARAM.DING_MSG_URL");
            if (dingMsgParam == null || org.apache.commons.lang.StringUtils.isBlank(dingMsgParam.getParamValue())) {
                log.warn("未配置HR系统钉钉消息接口系统参数!");
                throw new RuntimeException("未配置HR系统钉钉消息接口系统参数!");
            }

            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("employeeNos", CommonUtils.splitList(salesPerson, ","));
            paramMap.put("conent", ddMessage);
            log.info("send hr ding msg param:" + JSON.toJSONString(paramMap));

            String shipmentId = StringRedisUtils.get(RedisConstant.FBA_UP_MONITOR_NOTIFICATION_JOB_HANDLER + monitor.getShipmentId()+ "_" + monitor.getSalesPerson());
            ApiResult<?> result =new ApiResult<>();
            if(StringUtils.isBlank(shipmentId) || !shipmentId.equals(ddMessage)){
                result = HttpUtils.post(dingMsgParam.getParamValue(), HttpUtils.ACCESS_TOKEN, paramMap, ApiResult.class);
                log.info("send hr ding msg result:" + JSON.toJSONString(result));
                String resultStr = result.getResult()==null?"发送失败":result.getResult().toString();
                if (result.isSuccess() && resultStr.contains("发送成功")) {
                    StringRedisUtils.set(RedisConstant.FBA_UP_MONITOR_NOTIFICATION_JOB_HANDLER +monitor.getShipmentId()+ "_" + monitor.getSalesPerson() , ddMessage,  8 * 60 * 60L);
                }
            }else{
                result =ApiResult.newSuccess("发送成功");
            }


            // 只有超期预警才记录日志
            if (FbaAlertStatus.OVERTIME.getCode().equals(monitor.getAlertStatus())) {
                String notificationStatus="发送成功";
                String errorMessage = null;
                String resultStr = result.getResult()==null?"发送失败":result.getResult().toString();
                if (result.isSuccess() && !resultStr.contains("发送成功")) {
                    notificationStatus="发送失败";
                    errorMessage=result.getResult().toString();
                }
                if (!result.isSuccess()) {
                    notificationStatus="发送失败";
                    errorMessage=result.getErrorMsg();
                }
                FbaUpMonitorNotificationLog notificationLog =new FbaUpMonitorNotificationLog(monitor.getShipmentId(),new Timestamp(System.currentTimeMillis()),FbaAlertStatus.OVERTIME.getName(),
                                                                    message, notificationStatus, errorMessage, monitor.getSalesPerson());
                SystemLogUtils.FBA_UP_MONITOR_NOTIFICATION_LOG.log(monitor.getId(),JSON.toJSONString(notificationLog));
            }
        }catch (Exception e){
            log.error("send hr ding msg error:" + e.getMessage(), e);
            // 只有超期预警才记录日志
            if (FbaAlertStatus.OVERTIME.getCode().equals(monitor.getAlertStatus())) {
                FbaUpMonitorNotificationLog notificationLog =new FbaUpMonitorNotificationLog(monitor.getShipmentId(),new Timestamp(System.currentTimeMillis()),FbaAlertStatus.OVERTIME.getName(),
                        message, "发送失败", e.getMessage(), monitor.getSalesPerson());
                SystemLogUtils.FBA_UP_MONITOR_NOTIFICATION_LOG.log(monitor.getId(),JSON.toJSONString(notificationLog));
            }
        }
    }

    @Override
    public void pushDingTalkNotifications(List<FbaUpMonitor> fbaUpMonitors) {
        // 获取季节预警配置
        SeasonAlertSetting seasonConfig = SeasonAlertSettingUtil.getSeasonAlertSetting();

        List<FbaUpMonitor> updatedRecords = new ArrayList<>();
        for (FbaUpMonitor fbaUpMonitor : fbaUpMonitors) {
            fbaUpMonitor.setWaitingDays(DateUtils.getDaysBetween(fbaUpMonitor.getReceivedDate(), new Date()));
            String newAlertStatus=fbaUpMonitor.calculateAlertStatus(seasonConfig);

            FbaUpMonitor record = new FbaUpMonitor();
            record.setId(fbaUpMonitor.getId());
            record.setAlertStatus(newAlertStatus);
            record.setWaitingDays(fbaUpMonitor.getWaitingDays());
            updatedRecords.add(record);

            fbaUpMonitor.setAlertStatus(newAlertStatus);
            sendDingTalkNotification(fbaUpMonitor);
        }
        if (CollectionUtils.isNotEmpty(updatedRecords)) {
            fbaUpMonitorDao.batchUpdateFbaUpMonitor(updatedRecords);
        }
    }


}