package com.estone.transfer.service.impl;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.estone.checkin.enums.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.estone.checkin.bean.WhCheckIn;
import com.estone.checkin.bean.WhCheckInItem;
import com.estone.checkin.service.WhCheckInItemService;
import com.estone.checkin.service.WhCheckInService;
import com.estone.common.SaleChannel;
import com.estone.elasticsearch.model.EsSaleAccount;
import com.estone.elasticsearch.service.EsSaleAccountService;
import com.estone.multiplelocation.enums.AllocatePhaseEnum;
import com.estone.multiplelocation.enums.HandleResultEnum;
import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuQueryCondition;
import com.estone.sku.service.WhSkuService;
import com.estone.statistics.enums.DrpTurnoverOderType;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.common.AmqMessageModuleName;
import com.estone.system.rabbitmq.model.OmsFbaSkuMessage;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.estone.transfer.bean.*;
import com.estone.transfer.enums.*;
import com.estone.transfer.service.*;
import com.estone.transfer.utils.TransferStockUtils;
import com.estone.warehouse.aspect.StockServicelock;
import com.estone.warehouse.bean.WhStock;
import com.estone.warehouse.bean.WhStockLog;
import com.estone.warehouse.bean.WhStockQueryCondition;
import com.estone.warehouse.enums.*;
import com.estone.warehouse.service.CheckInUpdateStockService;
import com.estone.warehouse.service.WhStockChangeRecordService;
import com.estone.warehouse.service.WhStockLogService;
import com.estone.warehouse.service.WhStockService;
import com.estone.warehouse.util.FrozenStockUtils;
import com.estone.warehouse.util.SyncInventoryPmsUtils;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;

import lombok.extern.slf4j.Slf4j;


/**
 * @Description:
 * @Author: Yimeil
 * @Date: 2021/7/13 9:51
 * @Version: 1.0.0
 */
@Slf4j
@Service
public class CheckInUpdateTransferStockServiceImpl implements CheckInUpdateTransferStockService {

    @Resource
    private TransferStockCountService transferStockCountService;
    
    @Resource
    private TransferStockService transferStockService;

    @Resource
    private EsSaleAccountService esSaleAccountService;

    @Resource
    private WhTransitStockLogService stockLogService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private WhFbaPurchaseDataService whFbaPurchaseDataService;

    @Resource
    private AmqMessageService amqMessageService;

    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    @Resource
    private WhStockService whStockService;

    @Resource
    private WhCheckInService whCheckInService;

    @Resource
    private CheckInUpdateStockService checkInUpdateStockService;

    @Resource
    private WhStockLogService whStockLogService;
    
    @Resource
    private TransitBatchHandleService transitBatchHandleService;
    @Resource
    private WhCheckInItemService whCheckInItemService;

    @Resource
    private WhStockChangeRecordService whStockChangeRecordService;

    @Override
    public boolean batchUpdateStockByCheckIn(WhCheckIn whCheckIn, WhStock whStock) {
        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null
                || StringUtils.isBlank(whCheckIn.getLocationNumber())
                || StringUtils.isBlank(whCheckIn.getWhCheckInItem().getSku())) {
            return false;
        }
        try {
            if (null != whStock) {
                WhStock updateStock = new WhStock();
                updateStock.setId(whStock.getId());
                updateStock.setLocationNumber(whCheckIn.getLocationNumber());
                whStockService.updateWhStock(updateStock);
            }
            else {// 入库时没有对应的sku库存
                whStock = new WhStock();
                whStock.setSku(whCheckIn.getWhCheckInItem().getSku());
                whStock.setLocationNumber(whCheckIn.getLocationNumber());
                whStockService.createWhStock(whStock);
            }
            //是否免检
            Boolean isFreeCheck = whCheckIn.getWhCheckInItem().getIsFreeCheck();

            // 增加sku库位
            WhSkuQueryCondition whSkuQueryCondition = new WhSkuQueryCondition();
            whSkuQueryCondition.setSku(whStock.getSku());
            WhSku whSku = whSkuService.queryWhSku(whSkuQueryCondition);
            if (whSku != null) {
                WhSku updateSku = new WhSku();
                updateSku.setId(whSku.getId());
                String skuLocation = whSku.addLocationNumber(whCheckIn.getLocationNumber());
                if (StringUtils.isNotEmpty(whStock.getLocationNumber())
                        && whSku.matchLocationNumber(whStock.getLocationNumber()))
                    skuLocation = whSku.replaceLocationNumber(whCheckIn.getLocationNumber(),
                            whStock.getLocationNumber());
                updateSku.setLocationNumber(skuLocation);
                whSkuService.updateWhSku(updateSku);
            }

            // 记录库存ID
            WhCheckInItem whCheckInItem = new WhCheckInItem();
            whCheckInItem.setItemId(whCheckIn.getWhCheckInItem().getItemId());
            whCheckInItem.setInId(whCheckIn.getInId());
            whCheckInItem.setSkuId(whStock.getId());
            whCheckInItemService.updateWhCheckInItem(whCheckInItem);

            TransferStockQueryCondition query = new TransferStockQueryCondition();
            query.setSku(whCheckIn.getWhCheckInItem().getSku());
            TransferStockCount stock = transferStockCountService.queryTransferStockCount(query);

            // 需要更新的库存
            List<TransferStockCount> updateList = new ArrayList<TransferStockCount>();

            List<WhTransitStockLog> whStockLogList = new ArrayList<WhTransitStockLog>();
            Integer skuQuantity = whCheckIn.getWhCheckInItem().getQuantity() == null ? 0
                    : whCheckIn.getWhCheckInItem().getQuantity();
            if (null != stock) {
                Integer originalWaitQcQuantity = stock.getWaitingQcQuantity() == null ? 0
                        : stock.getWaitingQcQuantity();

                //上架中库存
                Integer upQuantity = Optional.ofNullable(stock.getUpQuantity()).orElse(0);


                TransferStockCount updateStock = new TransferStockCount();
                updateStock.setId(stock.getId());
                updateStock.setSku(whCheckIn.getWhCheckInItem().getSku());
                updateStock.setLastUpdatedBy(DataContextHolder.getUserId());
                updateStock.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                if (isFreeCheck) {
                    updateStock.setUpQuantity(upQuantity + skuQuantity);
                    whStockLogList.add(new WhTransitStockLog(whCheckIn.getWhCheckInItem().getSku(), null, null,
                            TransferStockLogType.UPING, stock.getId(), stock.getLocation(),
                            StockLogStep.PURCHASE_CHECK_IN, skuQuantity, upQuantity, whCheckIn.getInId() + ""));
                }else{
                    updateStock.setWaitingQcQuantity(originalWaitQcQuantity + skuQuantity);
                    whStockLogList.add(new WhTransitStockLog(whCheckIn.getWhCheckInItem().getSku(), null, null,
                            TransferStockLogType.WAITING_QC, stock.getId(), stock.getLocation(),
                            StockLogStep.PURCHASE_CHECK_IN, skuQuantity, originalWaitQcQuantity, whCheckIn.getInId() + ""));
                }

                updateList.add(updateStock);


            }
            else {// 入库时没有对应的sku库存
                stock = new TransferStockCount();
                stock.setSku(whCheckIn.getWhCheckInItem().getSku());
                if (isFreeCheck) {
                    stock.setUpQuantity(skuQuantity);
                }else{
                    stock.setWaitingQcQuantity(skuQuantity);
                }
                stock.setLocation(whCheckIn.getLocationNumber());
                stock.setLastUpdatedBy(DataContextHolder.getUserId());
                stock.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                if (whSku != null) {
                    stock.setSkuName(whSku.getName());
                    stock.setImg(whSku.getImageUrl());
                    stock.setWarehouseId(whSku.getWarehouseId());
                }

                transferStockCountService.createTransferStockCount(stock);

                if (isFreeCheck) {
                    whStockLogList.add(new WhTransitStockLog(whCheckIn.getWhCheckInItem().getSku(), null, null,
                            TransferStockLogType.UPING, stock.getId(), stock.getLocation(),
                            StockLogStep.PURCHASE_CHECK_IN, skuQuantity, 0, whCheckIn.getInId() + ""));
                }else{
                    whStockLogList.add(new WhTransitStockLog(whCheckIn.getWhCheckInItem().getSku(), null, null,
                            TransferStockLogType.WAITING_QC, stock.getId(), stock.getLocation(),
                            StockLogStep.PURCHASE_CHECK_IN, skuQuantity, 0, whCheckIn.getInId() + ""));
                }
            }

            // 添加库存变动日志
            if (CollectionUtils.isNotEmpty(whStockLogList)) {
                stockLogService.batchAddWhStockLog(whStockLogList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                transferStockCountService.batchUpdateTransferStockCount(updateList);
            }
            return true;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public boolean batchUpdateStockByQc(WhCheckIn whCheckIn) {
        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null || StringUtils.isBlank(
                whCheckIn.getWhCheckInItem().getSku()) || StringUtils.isBlank(
                whCheckIn.getWhCheckInItem().getLocation())) {
            return false;
        }
        try {

            TransferStockQueryCondition query = new TransferStockQueryCondition();
            query.setSku(whCheckIn.getWhCheckInItem().getSku());
            TransferStockCount stock = transferStockCountService.queryTransferStockCount(query);

            if (stock == null) {
                // 入库时没有对应的sku库存
                return false;
            }

            // 需要更新的库存
            List<TransferStockCount> updateList = new ArrayList<TransferStockCount>();

            List<WhTransitStockLog> whStockLogList = new ArrayList<WhTransitStockLog>();
            Integer skuQuantity = whCheckIn.getWhCheckInItem().getQcQuantity() == null ? 0
                    : whCheckIn.getWhCheckInItem().getQcQuantity();
            Integer checkInQuantity = whCheckIn.getWhCheckInItem().getQuantity() == null ? 0
                    : whCheckIn.getWhCheckInItem().getQuantity();

            Integer originalWaitingUpQuantity = stock.getWaitingUpQuantity() == null ? 0
                    : stock.getWaitingUpQuantity();
            Integer originalUpingQuantity = stock.getUpQuantity() == null ? 0 : stock.getUpQuantity();
            Integer originalWaitingQcQuantity = stock.getWaitingQcQuantity() == null ? 0 : stock.getWaitingQcQuantity();

            if (originalWaitingQcQuantity - checkInQuantity < 0) {
                return false;
            }

            TransferStockCount updateStock = new TransferStockCount();
            updateStock.setId(stock.getId());
            updateStock.setSku(whCheckIn.getWhCheckInItem().getSku());
            if (skuQuantity > 0 && CheckInStatus.UPING.intCode().equals(whCheckIn.getStatus())) {
                updateStock.setUpQuantity(originalUpingQuantity + skuQuantity);
                whStockLogList.add(new WhTransitStockLog(stock.getSku(),null,null, TransferStockLogType.UPING, stock.getId(),stock.getLocation(),StockLogStep.PURCHASE_QC,
                        skuQuantity, originalUpingQuantity, whCheckIn.getInId() + ""));
            }
            else if (skuQuantity > 0 && CheckInStatus.WAITING_UP.intCode().equals(whCheckIn.getStatus())) {

                updateStock.setWaitingUpQuantity(originalWaitingUpQuantity + skuQuantity);
                whStockLogList.add(new WhTransitStockLog(stock.getSku(),null,null, TransferStockLogType.WAITING_UP,stock.getId(),stock.getLocation(), StockLogStep.PURCHASE_QC,
                        skuQuantity, originalWaitingUpQuantity, whCheckIn.getInId() + ""));
            }
            updateStock.setWaitingQcQuantity(originalWaitingQcQuantity - checkInQuantity);
            updateStock.setLastUpdatedBy(DataContextHolder.getUserId());
            updateStock.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            updateList.add(updateStock);

            whStockLogList.add(new WhTransitStockLog(stock.getSku(),null,null,TransferStockLogType.WAITING_QC, stock.getId(),stock.getLocation(),StockLogStep.PURCHASE_QC,
                    -checkInQuantity, originalWaitingQcQuantity, whCheckIn.getInId() + ""));

            if (CollectionUtils.isEmpty(updateList)) {
                return false;
            }
            // 添加库存变动日志
            if (CollectionUtils.isNotEmpty(whStockLogList)) {
                stockLogService.batchAddWhStockLog(whStockLogList);
            }

            transferStockCountService.batchUpdateTransferStockCount(updateList);

            return true;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public boolean batchUpdateStockByUping(WhCheckIn whCheckIn) {
        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null || StringUtils.isBlank(
                whCheckIn.getWhCheckInItem().getSku()) || StringUtils.isBlank(
                whCheckIn.getWhCheckInItem().getLocation())) {
            return false;
        }
        try {

            TransferStockQueryCondition query = new TransferStockQueryCondition();
            query.setSku(whCheckIn.getWhCheckInItem().getSku());
            TransferStockCount stock = transferStockCountService.queryTransferStockCount(query);

            if (stock == null) {
                // 入库时没有对应的sku库存
                return false;
            }

            // 需要更新的库存
            List<TransferStockCount> updateList = new ArrayList<TransferStockCount>();
            List<WhTransitStockLog> whStockLogList = new ArrayList<WhTransitStockLog>();

            Integer skuQuantity = whCheckIn.getWhCheckInItem().getQcQuantity() == null ? 0
                    : whCheckIn.getWhCheckInItem().getQcQuantity();
            Integer originalUpQuantity = stock.getUpQuantity() == null ? 0 : stock.getUpQuantity();
            Integer originalWaitingUpQuantity = stock.getWaitingUpQuantity() == null ? 0
                    : stock.getWaitingUpQuantity();
            if (originalWaitingUpQuantity - skuQuantity < 0) {
                return false;
            }
            TransferStockCount updateRecord = new TransferStockCount();
            updateRecord.setId(stock.getId());
            updateRecord.setSku(whCheckIn.getWhCheckInItem().getSku());
            updateRecord.setWaitingUpQuantity(originalWaitingUpQuantity - skuQuantity);
            updateRecord.setUpQuantity(originalUpQuantity + skuQuantity);
            updateRecord.setLastUpdatedBy(DataContextHolder.getUserId());
            updateRecord.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            updateList.add(updateRecord);

            whStockLogList.add(
                    new WhTransitStockLog(stock.getSku(), null, null, TransferStockLogType.WAITING_UP, stock.getId(),
                            stock.getLocation(), StockLogStep.PURCHASE_PICK_UP, -skuQuantity, originalWaitingUpQuantity,
                            whCheckIn.getInId() + ""));

            whStockLogList.add(
                    new WhTransitStockLog(stock.getSku(), null, null, TransferStockLogType.UPING, stock.getId(),
                            stock.getLocation(), StockLogStep.PURCHASE_PICK_UP, skuQuantity, originalUpQuantity,
                            whCheckIn.getInId() + ""));

            if (CollectionUtils.isEmpty(updateList)) {
                return false;
            }
            // 添加库存变动日志
            if (CollectionUtils.isNotEmpty(whStockLogList)) {
                stockLogService.batchAddWhStockLog(whStockLogList);
            }

            transferStockCountService.batchUpdateTransferStockCount(updateList);

            return true;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    // 新海外仓采购上架
    @Override
    public boolean batchUpdateStockByUp(WhCheckIn whCheckIn) {
        String sku = whCheckIn.getWhCheckInItem().getSku();
        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null
                || StringUtils.isBlank(sku)) {
            return false;
        }
        WhFbaPurchaseDataQueryCondition queryCondition = new WhFbaPurchaseDataQueryCondition();
        queryCondition.setPurchaseorderno(whCheckIn.getPurchaseOrderNo());
        queryCondition.setSku(sku);
        List<WhFbaPurchaseData> whFbaPurchaseDataList = whFbaPurchaseDataService.queryWhFbaPurchaseDatas(queryCondition,null);
        if (CollectionUtils.isEmpty(whFbaPurchaseDataList)){
            throw new BusinessException(String.format("中转仓入库单[%s]无上架明细信息!",whCheckIn.getInId()));
        }
        // 采购单对应sku的待上架
        Map<String,Integer> upMap = new HashMap<>();
        Map<String,String> shipmentIdMap=new HashMap<>();
        for(WhFbaPurchaseData whFbaPurchaseData:whFbaPurchaseDataList){
            if (StringUtils.isBlank(whFbaPurchaseData.getAccountNumber()))
                throw new BusinessException(String.format("中转仓入库单[%s]上架明细数据存在不确定店铺!",whCheckIn.getInId()));
            upMap.put(whFbaPurchaseData.getAccountNumber(),(whFbaPurchaseData.getOrderQuantity()==null?0:whFbaPurchaseData.getOrderQuantity()) -
                    (whFbaPurchaseData.getUpQuantity()==null?0:whFbaPurchaseData.getUpQuantity()));
            if (whFbaPurchaseData.getShipmentId()!=null) {
                shipmentIdMap.put(whFbaPurchaseData.getAccountNumber(),whFbaPurchaseData.getShipmentId());
            }
        }
        //1 本次上架数量大于待上架(采购数量 - 已上架)
        //本次入库上架数量
        Integer upQuantity = whCheckIn.getWhCheckInItem().getQuantity() == null ? 0
                : whCheckIn.getWhCheckInItem().getQuantity();
        // 获取待上架总数
        /*Integer waitUpQty = whFbaPurchaseDataList.stream().mapToInt(whFbaPurchaseData -> ((whFbaPurchaseData.getOrderQuantity()==null?0:whFbaPurchaseData.getOrderQuantity()) -
                (whFbaPurchaseData.getUpQuantity()==null?0:whFbaPurchaseData.getUpQuantity()))).sum();*/
        Integer waitUpQty = 0;
        for(String key:upMap.keySet()){
            waitUpQty += upMap.get(key);
        }
        if (upQuantity > waitUpQty){
            throw new BusinessException(String.format("中转仓入库单[%s]上架数量[%s]大于采购单对应店铺总待上架数量[%s]!",whCheckIn.getInId(),upQuantity,waitUpQty));
        }

        List<String> lockKeyList = new ArrayList<>();
        Map<String,Integer> upQtyMap = new HashMap<>();
        // 店铺实际上架数量
        Map<String,Integer> accountUpMap = new HashMap<>();
        for (String accountNumber : upMap.keySet()){
            boolean isAmazon = SaleChannel.saleChannels.stream()
                    .noneMatch(s -> StringUtils.equalsIgnoreCase(s, shipmentIdMap.get(accountNumber)));
            String accountSite = null;
            //平台
            String saleChannel = null;
            if (!isAmazon) {
                saleChannel=shipmentIdMap.get(accountNumber);
                accountSite = shipmentIdMap.get(accountNumber);
                EsSaleAccount esAccount = esSaleAccountService.getSaleAccountBySellerId(accountNumber, "accountNumber");
                if (StringUtils.isNotBlank(saleChannel)) {
                    esAccount = esSaleAccountService.getSaleAccountBySellerId( "accountNumber",accountNumber,saleChannel);
                }
                if (esAccount != null) {
                    accountSite = StringUtils.isNotBlank(esAccount.getAccountSite()) ? esAccount.getAccountSite()
                            : esAccount.getSaleChannel();
                }
  
            }else {
                saleChannel=SaleChannel.CHANNEL_AMAZON;
            }

            // 小于等于0
            if (!(upQuantity>0)) break;
            // 等待上架数量
            Integer waitQty = upMap.get(accountNumber);
            // 实际上架数
            Integer realQty = waitQty;
            String key = accountNumber+"~"+sku+"~"+(StringUtils.isNotBlank(accountSite)?accountSite:null)+"~"+(StringUtils.isNotBlank(saleChannel)?saleChannel:null);
            if (upQuantity >= waitQty) {
                upQuantity -= waitQty;
            }else{
                realQty = upQuantity;
                upQuantity = 0;
            }
            accountUpMap.put(accountNumber,realQty);
            upQtyMap.put(key, realQty);
            lockKeyList.add(key);
        }


        // 更新采购需求已上架数量
        List<WhFbaPurchaseData> updateWhFbaPurchaseData = new ArrayList<>();

        for(WhFbaPurchaseData whFbaPurchaseData:whFbaPurchaseDataList){
            Integer upQty = whFbaPurchaseData.getUpQuantity()==null?0:whFbaPurchaseData.getUpQuantity();
            Integer qty = accountUpMap.get(whFbaPurchaseData.getAccountNumber());
            if (qty != null) {
                WhFbaPurchaseData update = new WhFbaPurchaseData();
                update.setId(whFbaPurchaseData.getId());
                update.setUpQuantity(upQty+qty);
                updateWhFbaPurchaseData.add(update);
            }
        }
        if (CollectionUtils.isNotEmpty(updateWhFbaPurchaseData))
            whFbaPurchaseDataService.batchUpdateWhFbaPurchaseData(updateWhFbaPurchaseData);

        //记录中转仓上架数量
        whCheckIn.getWhCheckInItem().setPacUpNum(whCheckIn.getWhCheckInItem().getQuantity());
        WhStockQueryCondition query = new WhStockQueryCondition();
        query.setSku(whCheckIn.getWhCheckInItem().getSku());
        query.setLocationNumber(whCheckIn.getLocationNumber());
        WhStock whStock = whStockService.queryWhStock(query);
        return updateUpStock(lockKeyList, sku, upQtyMap, whCheckIn, true,whStock);
    }

    // 采购上架、调拨上架修改库存
    @StockServicelock
    public boolean updateUpStock(List<String> lockKeyList,String sku, Map<String,Integer> stockMap,
                                 WhCheckIn whCheckIn, boolean isFba, WhStock whStock)  {

        TransferStockQueryCondition queryCount = new TransferStockQueryCondition();
        queryCount.setSku(sku);
        TransferStockCount stockCount = transferStockCountService.queryTransferStockCount(queryCount);
        if (stockCount == null){
            stockCount = new TransferStockCount();
            stockCount.setSku(whCheckIn.getWhCheckInItem().getSku());
            stockCount.setLocation(whCheckIn.getLocationNumber());
            stockCount.setLastUpdatedBy(DataContextHolder.getUserId());
            stockCount.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            // 增加sku库位
            WhSkuQueryCondition whSkuQueryCondition = new WhSkuQueryCondition();
            whSkuQueryCondition.setSku(sku);
            WhSku whSku = whSkuService.queryWhSku(whSkuQueryCondition);
            if (whSku != null) {
                stockCount.setSkuName(whSku.getName());
                stockCount.setImg(whSku.getImageUrl());
                stockCount.setWarehouseId(whSku.getWarehouseId());
            }
            transferStockCountService.createTransferStockCount(stockCount);
            //throw new BusinessException(String.format("SKU[%s]无库存记录!",sku));
        }
        String locationNumber = whCheckIn.getLocationNumber();
        String content = whCheckIn.getInId() + "";
        LocationTagEnum locationTag = null;
        if (whCheckIn.isPreStoreUp())
            locationTag = LocationTagEnum.PRESTORE;


        if (whStock == null) {
            whStock = new WhStock();
            whStock.setSku(sku);
            whStock.setLocationNumber(locationNumber);
            if (locationTag != null)
                whStock.setLocationTag(locationTag.getCode());
            whStockService.createWhStock(whStock);
            // 增加sku库位
            WhSkuQueryCondition whSkuQueryCondition = new WhSkuQueryCondition();
            whSkuQueryCondition.setSku(sku);
            WhSku whSku = whSkuService.queryWhSku(whSkuQueryCondition);
            WhSku updateWhSku = new WhSku();
            updateWhSku.setId(whSku.getId());
            updateWhSku.setLocationNumber(whSku.addLocationNumber(locationNumber));
            whSkuService.updateWhSku(updateWhSku);
        }
        else if (locationTag != null && !whStock.getLocationTag().contains(locationTag.getCode())) {
            whStock.addLocationTag(locationTag);
            whStockService.updateWhStock(whStock);
        }

        //记录库存ID
        WhCheckInItem whCheckInItem = new WhCheckInItem();
        whCheckInItem.setItemId(whCheckIn.getWhCheckInItem().getItemId());
        whCheckInItem.setInId(whCheckIn.getInId());
        whCheckInItem.setSkuId(whStock.getId());
        whCheckInItemService.updateWhCheckInItem(whCheckInItem);

        // 需要更新的库存
        List<TransferStock> updateList = new ArrayList<>();
        List<WhTransitStockLog> whStockLogList = new ArrayList<WhTransitStockLog>();
        /** 库存变更推送 */
        List<AmqMessage> msgList = new ArrayList<>();
        // 上架总数
        Integer sumUpQty = 0;
        
        List<Integer> updateIdList = new ArrayList<>();
        
        for(String key:stockMap.keySet()){
            String[] argArr = StringUtils.split(key,"~");
            String store = argArr[0];
            String pSku = argArr[1];
            String site ="null".equals(argArr[2])?null:argArr[2];
            String remark ="null".equals(argArr[3])?null:argArr[3];
            TransferStockQueryCondition stockQuery = new TransferStockQueryCondition();
            stockQuery.setSku(pSku);
            stockQuery.setStore(store);
            stockQuery.setRemark(remark);
            stockQuery.setSite(site);
            TransferStock transferStock = null;
            List<TransferStock> transferStockList = transferStockService.queryTransferStocks(stockQuery, null);
            if (CollectionUtils.isNotEmpty(transferStockList) && transferStockList.size() == 1
                    && (transferStockList.get(0).getStockId() == null
                            || transferStockList.get(0).getStockId().equals(whStock.getId()))) {
                transferStock = transferStockList.get(0);
            }
            else {
                transferStock = Optional.ofNullable(transferStockList).orElse(new ArrayList<>()).stream()
                        .collect(
                                Collectors.toMap(s ->s.getSite()+ s.getSku() + s.getStore() + s.getStockId(), s -> s))
                        .get( site +pSku + store + whStock.getId());
            }
            Integer upQty = stockMap.get(key);
            sumUpQty+=upQty;
            Integer surplusQuantit = 0;
            if (null != transferStock) {
                transferStock.addLocationTag(locationTag);
                transferStock.setLocationNumber(locationNumber);
                transferStock.setStockId(whStock.getId());
                surplusQuantit = transferStock.getSurplusQuantity() == null ? 0
                        : transferStock.getSurplusQuantity();
                TransferStock updateStock = TransferStock.buildUpdateStock(transferStock);
                updateStock.setSurplusQuantity(surplusQuantit + upQty);
                updateList.add(updateStock);
                whStockChangeRecordService.generateStockChangeRecord(transferStock.getId(), upQty,
                        whCheckIn.getPurchaseOrderNo(), WhAllocateTypeEnum.FBA, OrderTypeEnum.CHECKIN, false);
            }
            else {// 入库时没有对应的sku库存
                transferStock = new TransferStock();
                transferStock.setSku(sku);
                transferStock.setSurplusQuantity(upQty);
                transferStock.setStore(store);
                transferStock.setLocationNumber(locationNumber);
                transferStock.setLocationTag(whStock.getLocationTag());
                if (locationTag != null)
                    transferStock.addLocationTag(locationTag);
                transferStock.setStockId(whStock.getId());
                transferStock.setLastUpdatedBy(DataContextHolder.getUserId());
                transferStock.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                transferStock.setSite(site);
                transferStock.setRemark(remark);
                if (isFba)
                    transferStock.setRemark(SaleChannel.CHANNEL_AMAZON);
                transferStockService.createTransferStock(transferStock);
                whStockChangeRecordService.generateStockChangeRecord(transferStock.getId(), upQty,
                        whCheckIn.getPurchaseOrderNo(), WhAllocateTypeEnum.FBA, OrderTypeEnum.CHECKIN, true);
            }
            updateIdList.add(transferStock.getId());
            whStockLogList.add(new WhTransitStockLog(sku, store, null, TransferStockLogType.USABLE_STOCK,
                    transferStock.getId(), transferStock.getLocationNumber(), StockLogStep.FBA_UP, upQty,
                    surplusQuantit, content + ""));

            // 售后结算数量大于0，记录售后结算明细
            if (whCheckIn.getAfterSaleQty() != null) {

                checkInUpdateStockService.createAfterSaleItem(whCheckIn, whStock, upQty, CheckInWhType.FBA.intCode(), transferStock);
            }

            // 推送fba库存变更到oms
            OmsFbaSkuMessage omsFbaSkuMessage = new OmsFbaSkuMessage(store,null,sku,upQty);
            msgList.add(AssembleMessageDataUtils.assembleooFbaStockData(omsFbaSkuMessage));
            // 推送中转仓库存变更到redis
            TransferStock finalTransferStock = transferStock;
            msgList.add(AssembleMessageDataUtils.assembleDataToFinance(AmqMessageModuleName.PUSH_TRANSFER_STOCK_INFO.getCode(),
                    amqMessage -> {
                        amqMessage.setRelevantParam(sku);
                        Map<String,Object> map = new HashMap<>();
                        map.put("sku",sku);
                        map.put("count_surplus",upQty);
                        map.put("saleChannel",(finalTransferStock.getRemark()));
                        // 消息体
                        String messageBody = JSON.toJSONString(map);
                        return messageBody;
                    }));
        }

        if (locationTag != null)
            whCheckInService.addLocationMatchReocrd(sku,
                    whCheckIn.getAfterSaleQty() != null,
                    StringUtils.contains(
                            whCheckIn.getWhCheckInItem().getFirstOrderType(),
                            CheckInFlags.SHELF_LIFE_STORAGE.getCode()),
                    whCheckIn.getLocationNumber(), whCheckIn.getInId()+"", AllocatePhaseEnum.UPLOAD,HandleResultEnum.STOCK);
        // 入库上架 需要扣减 上架中库存
        TransferStockCount updateRecord = new TransferStockCount();
        updateRecord.setId(stockCount.getId());
        updateRecord.setSku(sku);
        // FBA扣减上架中库存
        if (isFba) {
            Integer upQuantity = stockCount.getUpQuantity() == null ? 0 : stockCount.getUpQuantity();
            if (sumUpQty > upQuantity)
                throw new BusinessException(String.format("SKU[%s]本次入库上架数[%s]大于上架中[%s]数!", sku, sumUpQty, upQuantity));
            whStockLogList.add(new WhTransitStockLog(sku, null, null, TransferStockLogType.UPING, stockCount.getId(),
                    null, StockLogStep.FBA_UP, -sumUpQty, upQuantity, content + ""));
            updateRecord.setUpQuantity(upQuantity - sumUpQty);
        }
        updateRecord.setLastUpdatedBy(DataContextHolder.getUserId());
        updateRecord.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        transferStockCountService.updateTransferStockCount(updateRecord);

        // 添加库存变动日志
        if (CollectionUtils.isNotEmpty(whStockLogList)) {
            stockLogService.batchAddWhStockLog(whStockLogList);
            // 添加库存变动批次明细
            transitBatchHandleService.createTransitBatchDetail(whStockLogList, QuantityType.CHECK_IN,
                    TransitBatchOrderType.CHECK_IN, TransitBatchStockType.LOCAL_2_FBA, whCheckIn.getPurchaseOrderNo());
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            transferStockService.batchUpdateTransferStock(updateList);
        }
        // 记录最新上架时间
        if(!whCheckIn.isPreStoreUp()) {
            TransferStockUtils.updateTransferTime(updateIdList, DrpTurnoverOderType.CHECK_IN.intCode());
        }
        // 推送库存变更给oms
        if (CollectionUtils.isNotEmpty(msgList)){
            amqMessageService.batchCreateAmqMessage(msgList);
        }
        return true;
    }

    /**
     * 上架 加待确认库存
     *
     * @param whCheckIn
     * @return
     */
    @Override
    public boolean batchUpdateStockByUp(WhCheckIn whCheckIn, List<WhFbaPurchaseData> whFbaPurchaseDataList) {

        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null
                || StringUtils.isBlank(whCheckIn.getWhCheckInItem().getSku())
                || CollectionUtils.isEmpty(whFbaPurchaseDataList) || StringUtils.isEmpty(whCheckIn.getLocationNumber())
                        || whCheckIn.getWhCheckInItem().getSkuId() == null) {
            return false;
        }

        // 减本仓上架中
        WhStockQueryCondition query = new WhStockQueryCondition();
        query.setSku(whCheckIn.getWhCheckInItem().getSku());
        query.setId(whCheckIn.getWhCheckInItem().getSkuId());
        WhStock whStock = whStockService.queryWhStock(query);
        if (whStock == null) {
            // 入库时没有对应的sku库存
            throw new BusinessException(String.format("sku[%s]本地仓无库存记录!",whCheckIn.getWhCheckInItem().getSku()));
        }

        try {
            //本次上架数量
            Integer checkInQuantity = whCheckIn.getWhCheckInItem().getQuantity() == null ? 0
                    : whCheckIn.getWhCheckInItem().getQuantity();
            String sku = whCheckIn.getWhCheckInItem().getSku();
            List<String> accounts = whFbaPurchaseDataList.stream().map(i -> i.getAccountNumber()).collect(Collectors.toList());

            // 查下所有待分配中转仓单，保证有待分配单据的优先上架
            WhFbaAllocationQueryCondition whFbaAllocationQueryCondition = new WhFbaAllocationQueryCondition();
            whFbaAllocationQueryCondition.setStatus(AsnPrepareStatus.WAITING_ALLOT.intCode());
            whFbaAllocationQueryCondition.setSku(sku);
            whFbaAllocationQueryCondition.setAccountNumber(StringUtils.join(accounts,","));
            List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(whFbaAllocationQueryCondition, null);
            // 本地仓
            List<WhFbaPurchaseData> localList = whFbaPurchaseDataList.stream().filter(w -> StringUtils.equalsIgnoreCase("local", w.getShipmentId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(localList))
                whFbaPurchaseDataList.removeAll(localList);

            if (localList.size() > 1){
                throw new BusinessException(String.format("sku[%s]本地仓无库存记录!",sku));
            }

            // 中转仓
            List<WhFbaPurchaseData> sortList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(whFbaPurchaseDataList)) {
                for (WhFbaAllocation whFbaAllocation : whFbaAllocations) {
                    List<WhFbaPurchaseData> existList = whFbaPurchaseDataList.stream().filter(w -> StringUtils.equalsIgnoreCase(w.getAccountNumber(), whFbaAllocation.getAccountNumber())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(existList)) {
                        sortList.addAll(existList);
                        whFbaPurchaseDataList.removeAll(existList);
                    }
                }
                if (CollectionUtils.isNotEmpty(whFbaPurchaseDataList))
                    sortList.addAll(whFbaPurchaseDataList);
            }
            List<WhStockLog> whStockLogList = new ArrayList();
            //中转仓上架集合
            Map<String,Integer> upMap = new HashMap<>();
            Integer sumRealQty = 0;
            // 中转仓上架
            for(WhFbaPurchaseData whFbaPurchaseData:sortList){
                if (!(checkInQuantity>0)) break;

                // 小于等于0
                if (!(checkInQuantity>0)) break;

                String accountSite =null;
                String saleChannel=null;
                if (StringUtils.isNotBlank(whFbaPurchaseData.getShipmentId())) {
                    boolean isAmazon = SaleChannel.saleChannels.stream()
                            .noneMatch(s -> StringUtils.equalsIgnoreCase(s, whFbaPurchaseData.getShipmentId()));
                    saleChannel=whFbaPurchaseData.getShipmentId();
                    if (!isAmazon) {
                        accountSite = whFbaPurchaseData.getShipmentId();
                        EsSaleAccount esAccount = esSaleAccountService
                                .getSaleAccountBySellerId(whFbaPurchaseData.getAccountNumber(), "accountNumber");
                        if (StringUtils.isNotBlank(saleChannel)) {
                            esAccount = esSaleAccountService.getSaleAccountBySellerId( "accountNumber",whFbaPurchaseData.getAccountNumber(),saleChannel);
                        }
                        if (esAccount != null) {
                            accountSite = StringUtils.isNotBlank(esAccount.getAccountSite())
                                    ? esAccount.getAccountSite()
                                    : esAccount.getSaleChannel();
                        }
                    }else{
                        saleChannel= SaleChannel.CHANNEL_AMAZON;
                    }
                }

                // 等待上架数量
                Integer waitQty = whFbaPurchaseData.getOrderQuantity()==null?0:whFbaPurchaseData.getOrderQuantity() -
                        (whFbaPurchaseData.getUpQuantity()==null?0:whFbaPurchaseData.getUpQuantity());
                // 实际上架数
                Integer realQty = waitQty;
                String key = whFbaPurchaseData.getAccountNumber()+"~"+sku+"~"+(StringUtils.isNotBlank(accountSite)?accountSite:null)+"~"+(StringUtils.isNotBlank(saleChannel)?saleChannel:null);
                if (checkInQuantity >= waitQty) {
                    checkInQuantity -= waitQty;
                }else{
                    realQty = checkInQuantity;
                    checkInQuantity = 0;
                }
                Integer existRealQty = upMap.get(key) == null ? 0 : upMap.get(key);
                upMap.put(key,realQty+existRealQty);

                Integer upQty = whFbaPurchaseData.getUpQuantity()==null?0:whFbaPurchaseData.getUpQuantity();
                WhFbaPurchaseData update = new WhFbaPurchaseData();
                update.setId(whFbaPurchaseData.getId());
                update.setUpQuantity(upQty+realQty);
                whFbaPurchaseDataService.updateWhFbaPurchaseData(update);

                // 减本仓上架中
                Integer originalUpingQuantity = whStock.getUpQuantity() == null ? 0 : whStock.getUpQuantity();
                if (originalUpingQuantity - realQty < 0) {
                    throw new RuntimeException("本地仓上架中数量【"+originalUpingQuantity+"】小于本次上架数量【"+realQty+"】");
                }
                WhStock updateWhStock = whStock.buildUpdateStock(whStock);
                updateWhStock.setUpQuantity(originalUpingQuantity - realQty);
                whStockService.updateWhStock(updateWhStock);
                whStock.setUpQuantity(updateWhStock.getUpQuantity());
                whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.UPING,whStock.getId(),whStock.getLocationNumber(), StockLogStep.PURCHASE_UP,
                        -realQty, originalUpingQuantity, whCheckIn.getInId() + ""));

                sumRealQty+=realQty;
            }
            if (upMap.size() > 0) {
                WhStockQueryCondition query2 = new WhStockQueryCondition();
                query2.setSku(whCheckIn.getWhCheckInItem().getSku());
                query2.setLocationNumber(whCheckIn.getLocationNumber());
                WhStock upStock = whStockService.queryWhStock(query2);
                boolean isFba = StringUtils.indexOf(whCheckIn.getPurchaseOrderNo(),
                        PurchaseOrderType.NCGHW.getCode()) >= 0;
                if (CheckInType.MULTI_GOODS.intCode().equals(whCheckIn.getCheckInType())) {
                    isFba = false;
                }
                updateUpStock(new ArrayList(upMap.keySet()), sku, upMap, whCheckIn, isFba, upStock);
                // 记录入库单为中转仓数量
                whCheckIn.setExceptionType(CheckInWhType.FBA.intCode());
                whCheckIn.getWhCheckInItem().setPacUpNum(sumRealQty);
            }

            // 本地仓上架
            for(WhFbaPurchaseData whFbaPurchaseData:localList){
                if (!(checkInQuantity>0)) break;

                // 需要更新的库存
                List<WhStock> updateList = new ArrayList<WhStock>();

                //要修改的老库存表的可用库存
                Map<String, Integer> updateSurplusQuantityMap = new HashMap<String, Integer>();
                Map<String, String> updateIdMap = new HashMap<String, String>();

                Integer skuQuantity = checkInQuantity;
                Integer originalSurplusQuantity = whStock.getSurplusQuantity() == null ? 0 : whStock.getSurplusQuantity();
                Integer originalUpingQuantity = whStock.getUpQuantity() == null ? 0 : whStock.getUpQuantity();

                if (originalUpingQuantity - skuQuantity < 0) {
                    throw new RuntimeException("本地仓上架中数量【"+originalUpingQuantity+"】小于本次上架数量【"+skuQuantity+"】");
                }

                WhStockQueryCondition upQuery = new WhStockQueryCondition();
                upQuery.setSku(whCheckIn.getWhCheckInItem().getSku());
                upQuery.setLocationNumber(whCheckIn.getLocationNumber());
                WhStock upStock = whStockService.queryWhStock(upQuery);
                // 加可用库存
                whStockLogList.clear();
                if (whCheckIn.isPreStoreUp()) {
                    LocationTagEnum locationTag = LocationTagEnum.PRESTORE;
                    if (upStock == null) {
                        upStock = new WhStock();
                        upStock.setSku(whCheckIn.getWhCheckInItem().getSku());
                        upStock.setSurplusQuantity(skuQuantity);
                        upStock.setLocationNumber(whCheckIn.getLocationNumber());
                        if (locationTag != null)
                            upStock.setLocationTag(locationTag.getCode());
                        // 变更的可用库存差异数量
                        upStock.setUpdateSurplusQuantity(skuQuantity);
                        upStock.setLastUpdatedBy(DataContextHolder.getUserId());
                        upStock.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                        upStock.setLastSurplusDate(new Timestamp(System.currentTimeMillis()));
                        whStockService.createWhStock(upStock);
                        whStockLogList.add(new WhStockLog(upStock.getSku(), StockLogType.USABLE_STOCK,
                                upStock.getId(), upStock.getLocationNumber(), StockLogStep.PURCHASE_UP,
                                skuQuantity, 0, whCheckIn.getInId() + ""));
                        // 增加sku库位
                        WhSkuQueryCondition whSkuQueryCondition = new WhSkuQueryCondition();
                        whSkuQueryCondition.setSku(upStock.getSku());
                        WhSku whSku = whSkuService.queryWhSku(whSkuQueryCondition);
                        WhSku updateWhSku = new WhSku();
                        updateWhSku.setId(whSku.getId());
                        updateWhSku.setLocationNumber(whSku.addLocationNumber(upStock.getLocationNumber()));
                        whSkuService.updateWhSku(updateWhSku);
                        whStockChangeRecordService.generateStockChangeRecord(upStock.getId(), skuQuantity,
                                whCheckIn.getPurchaseOrderNo(), WhAllocateTypeEnum.LOCAL, OrderTypeEnum.CHECKIN, true);
                    }
                    else {
                        upStock.addLocationTag(locationTag);
                        WhStock updateStock = WhStock.buildUpdateStock(upStock);
                        updateStock.setSurplusQuantity(originalSurplusQuantity + skuQuantity);
                        // 变更的可用库存差异数量
                        updateStock.setUpdateSurplusQuantity(skuQuantity);
                        updateStock.setLastSurplusDate(new Timestamp(System.currentTimeMillis()));
                        updateList.add(updateStock);
                        whStockLogList.add(new WhStockLog(upStock.getSku(), StockLogType.USABLE_STOCK, upStock.getId(),
                                upStock.getLocationNumber(), StockLogStep.PURCHASE_UP, skuQuantity,
                                originalSurplusQuantity, whCheckIn.getInId() + ""));
                        whStockChangeRecordService.generateStockChangeRecord(upStock.getId(), skuQuantity,
                                whCheckIn.getPurchaseOrderNo(), WhAllocateTypeEnum.LOCAL, OrderTypeEnum.CHECKIN, false);
                    }
                    WhStock updateStock = WhStock.buildUpdateStock(whStock);
                    updateStock.setUpQuantity(originalUpingQuantity - skuQuantity);
                    updateList.add(updateStock);
                    whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.UPING, whStock.getId(),
                            whStock.getLocationNumber(), StockLogStep.PURCHASE_UP, -(skuQuantity + sumRealQty),
                            originalUpingQuantity + sumRealQty, whCheckIn.getInId() + ""));

                    if (locationTag != null)
                        whCheckInService.addLocationMatchReocrd(upStock.getSku(),
                                whCheckIn.getAfterSaleQty() != null,
                                org.apache.commons.lang.StringUtils.contains(
                                        whCheckIn.getWhCheckInItem().getFirstOrderType(),
                                        CheckInFlags.SHELF_LIFE_STORAGE.getCode()),
                                whCheckIn.getLocationNumber(), whCheckIn.getInId() + "",AllocatePhaseEnum.UPLOAD, HandleResultEnum.STOCK);


                    //记录库存ID
                    WhCheckInItem whCheckInItem = new WhCheckInItem();
                    whCheckInItem.setItemId(whCheckIn.getWhCheckInItem().getItemId());
                    whCheckInItem.setInId(whCheckIn.getInId());
                    whCheckInItem.setSkuId(whStock.getId());
                    whCheckInItemService.updateWhCheckInItem(whCheckInItem);
                }
                else {
                    WhStock updateStock = WhStock.buildUpdateStock(whStock);
                    updateStock.setSurplusQuantity(originalSurplusQuantity + skuQuantity);
                    //变更的可用库存差异数量
                    updateStock.setUpdateSurplusQuantity(skuQuantity);
                    updateStock.setUpQuantity(originalUpingQuantity - skuQuantity);
                    updateStock.setLastSurplusDate(new Timestamp(System.currentTimeMillis()));
                    updateList.add(updateStock);
                    whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.UPING, whStock.getId(),
                            whStock.getLocationNumber(), StockLogStep.PURCHASE_UP, -(skuQuantity + sumRealQty),
                            originalUpingQuantity + sumRealQty, whCheckIn.getInId() + ""));
                    whStockLogList.add(new WhStockLog(whStock.getSku(), StockLogType.USABLE_STOCK, whStock.getId(),
                            whStock.getLocationNumber(), StockLogStep.PURCHASE_UP, skuQuantity, originalSurplusQuantity,
                            whCheckIn.getInId() + ""));
                    whStockChangeRecordService.generateStockChangeRecord(whStock.getId(), skuQuantity,
                            whCheckIn.getPurchaseOrderNo(), WhAllocateTypeEnum.LOCAL, OrderTypeEnum.CHECKIN, false);
                }

                updateSurplusQuantityMap.put(whStock.getSku(), skuQuantity);
                updateIdMap.put(whStock.getSku(), whCheckIn.getInId() + "");

                // 更新上架数量
                WhFbaPurchaseData updateData = new WhFbaPurchaseData();
                updateData.setId(whFbaPurchaseData.getId());
                updateData.setUpQuantity(skuQuantity);
                whFbaPurchaseDataService.updateWhFbaPurchaseData(updateData);

                whStockService.batchUpdateWhStock(updateList);

                // 售后结算数量大于0，记录售后结算明细
                if (whCheckIn.getAfterSaleQty() != null)
                    checkInUpdateStockService.createAfterSaleItem(whCheckIn, upStock, skuQuantity,CheckInWhType.LOCAL.intCode(),null);
                Map<Integer,String> stockIdMap = new HashMap<>();
                stockIdMap.put(upStock.getId(), upStock.getSku());
                // 记录本地仓最新上架时间
                if (!whCheckIn.isPreStoreUp()) {
                    FrozenStockUtils.updateTimeAndUser(stockIdMap, false, true);
                }
                // TODO 推送可用库存
                SyncInventoryPmsUtils.syncStock(updateList);
                // local只有一条
                break;
            }
            // 添加库存变动日志
            if (CollectionUtils.isNotEmpty(whStockLogList)) {
                whStockLogService.batchAddWhStockLog(whStockLogList);
            }
            return true;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }

    }

    @Override
    public boolean batchUpdateStockByDiscardCheckIn(WhCheckIn whCheckIn) {
        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null
                || StringUtils.isBlank(whCheckIn.getWhCheckInItem().getSku())) {
            return false;
        }
        try {

            TransferStockQueryCondition query = new TransferStockQueryCondition();
            query.setSku(whCheckIn.getWhCheckInItem().getSku());
            TransferStockCount stock = transferStockCountService.queryTransferStockCount(query);

            if (stock == null) {
                // 入库时没有对应的sku库存
                return false;
            }

            // 需要更新的库存
            List<TransferStockCount> updateList = new ArrayList<TransferStockCount>();
            List<WhTransitStockLog> whStockLogList = new ArrayList<WhTransitStockLog>();

            Integer waitingUpQuantity = whCheckIn.getWhCheckInItem().getQcQuantity() == null ? 0
                    : whCheckIn.getWhCheckInItem().getQcQuantity();
            Integer upingQuantity = whCheckIn.getWhCheckInItem().getQcQuantity() == null ? 0
                    : whCheckIn.getWhCheckInItem().getQcQuantity();
            Integer waitingQcQuantity = whCheckIn.getWhCheckInItem().getQuantity() == null ? 0
                    : whCheckIn.getWhCheckInItem().getQuantity();

            Integer originalWaitingUpQuantity = stock.getWaitingUpQuantity() == null ? 0 : stock.getWaitingUpQuantity();
            Integer originalUpingQuantity = stock.getUpQuantity() == null ? 0 : stock.getUpQuantity();
            Integer originalWaitingQcQuantity = stock.getWaitingQcQuantity() == null ? 0 : stock.getWaitingQcQuantity();

            if (originalWaitingQcQuantity - waitingQcQuantity < 0
                    && CheckInStatus.WAITING_QC.intCode().equals(whCheckIn.getStatus())) {
                return false;
            }
            else if (originalWaitingUpQuantity - waitingUpQuantity < 0
                    && CheckInStatus.WAITING_UP.intCode().equals(whCheckIn.getStatus())) {
                return false;
            }
            else if (originalUpingQuantity - upingQuantity < 0
                    && (CheckInStatus.UPING.intCode().equals(whCheckIn.getStatus())
                            || CheckInStatus.UPERROR.intCode().equals(whCheckIn.getStatus()))) {
                return false;
            }

            TransferStockCount updateStock = new TransferStockCount();
            updateStock.setId(stock.getId());
            updateStock.setSku(whCheckIn.getWhCheckInItem().getSku());

            if (CheckInStatus.WAITING_QC.intCode().equals(whCheckIn.getStatus())) {
                updateStock.setWaitingQcQuantity(originalWaitingQcQuantity - waitingQcQuantity);
                whStockLogList.add(new WhTransitStockLog(stock.getSku(), null, null, TransferStockLogType.WAITING_QC,
                        stock.getId(), stock.getLocation(), StockLogStep.PURCHASE_DISCARD, -waitingQcQuantity,
                        originalWaitingQcQuantity, "" + whCheckIn.getInId()));
            }
            else if (CheckInStatus.WAITING_UP.intCode().equals(whCheckIn.getStatus())) {
                updateStock.setWaitingUpQuantity(originalWaitingUpQuantity - waitingUpQuantity);
                whStockLogList.add(new WhTransitStockLog(stock.getSku(), null, null, TransferStockLogType.WAITING_UP,
                        stock.getId(), stock.getLocation(), StockLogStep.PURCHASE_DISCARD, -waitingUpQuantity,
                        originalWaitingUpQuantity, "" + whCheckIn.getInId()));
            }
            else if (CheckInStatus.UPING.intCode().equals(whCheckIn.getStatus())
                    || CheckInStatus.UPERROR.intCode().equals(whCheckIn.getStatus())) {
                updateStock.setUpQuantity(originalUpingQuantity - upingQuantity);
                whStockLogList.add(new WhTransitStockLog(stock.getSku(), null, null, TransferStockLogType.UPING,
                        stock.getId(), stock.getLocation(), StockLogStep.PURCHASE_DISCARD, -upingQuantity,
                        originalUpingQuantity, "" + whCheckIn.getInId()));
            }
            updateStock.setLastUpdatedBy(DataContextHolder.getUserId());
            updateStock.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            updateList.add(updateStock);

            if (CollectionUtils.isEmpty(updateList)) {
                return false;
            }

            // 添加库存变动日志
            if (CollectionUtils.isNotEmpty(whStockLogList)) {
                stockLogService.batchAddWhStockLog(whStockLogList);
            }

            transferStockCountService.batchUpdateTransferStockCount(updateList);

            return true;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }
}
