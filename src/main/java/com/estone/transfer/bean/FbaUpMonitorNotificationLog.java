package com.estone.transfer.bean;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved.
 * Project Name:wms
 * Package Name:com.estone.transfer.bean
 * File Name:FbaUpMonitorNotificationLog.java
 * Description:FBA货件上架监控通知日志实体类
 * Author:Amoi
 * Date:2024-12-18
 * ---------------------------------------------------------------------------
 */
@Data
@Slf4j
public class FbaUpMonitorNotificationLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * FBA货件ID
     */
    private String shipmentId;

    /**
     * 通知时间
     */
    private Timestamp notificationTime;

    /**
     * 通知类型（超期预警）
     */
    private String notificationType;

    /**
     * 通知内容
     */
    private String notificationContent;

    /**
     * 通知状态（发送成功/发送失败）
     */
    private String notificationStatus;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 销售人员
     */
    private String salesPerson;

    public FbaUpMonitorNotificationLog( String shipmentId, Timestamp notificationTime, String notificationType, String notificationContent, String notificationStatus, String errorMessage, String salesPerson) {
        this.shipmentId = shipmentId;
        this.notificationTime = notificationTime;
        this.notificationType = notificationType;
        this.notificationContent = notificationContent;
        this.notificationStatus = notificationStatus;
        this.errorMessage = errorMessage;
        this.salesPerson = salesPerson;
    }
    public FbaUpMonitorNotificationLog() {}

} 