package com.estone.transfer.bean;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import com.estone.transfer.enums.FbaAlertStatus;
import com.estone.transfer.enums.FbaStatus;
import com.estone.transfer.enums.SeasonType;
import org.apache.commons.lang3.StringUtils;

@Data
@Slf4j
public class FbaUpMonitor implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column fba_up_monitor.id
     */
    private Integer id;

    /**
     * 货件编号(如FBA15DHPV9CK) database column fba_up_monitor.shipment_id
     */
    private String shipmentId;

    /**
     * 发货单号 database column fba_up_monitor.fba_no
     */
    private String fbaNo;

    /**
     * 目的仓ID database column fba_up_monitor.destination_warehouse_id
     */
    private String destinationWarehouseId;

    /**
     * 销售人员 database column fba_up_monitor.sales_person
     */
    private String salesPerson;

    /**
     * 发货总数量 database column fba_up_monitor.total_quantity
     */
    private Integer totalQuantity;

    /**
     * 已上架数量 database column fba_up_monitor.received_quantity
     */
    private Integer receivedQuantity;

    /**
     * 差异数量 database column fba_up_monitor.difference_quantity
     */
    private Integer differenceQuantity;

    /**
     * 发货日期 database column fba_up_monitor.shipping_date
     */
    private Timestamp shippingDate;

    /**
     * 签收时间 database column fba_up_monitor.received_date
     */
    private Timestamp receivedDate;

    /**
     * 预计上架时间 database column fba_up_monitor.estimated_shelf_date
     */
    private Timestamp estimatedShelfDate;

    /**
     * 实际上架时间 database column fba_up_monitor.actual_shelf_date
     */
    private Timestamp actualShelfDate;

    /**
     * 等待天数 database column fba_up_monitor.waiting_days
     */
    private Integer waitingDays;

    /**
     * 货件状态 database column fba_up_monitor.status
     */
    private String status;

    /**
     * 预警状态 database column fba_up_monitor.alert_status
     */
    private String alertStatus;

    /**
     * SKU编码(如4NB401334) database column fba_up_monitor.sku_code
     */
    private String skuCode;

    /**
     * FNSKU编码(如X001HLKNVF) database column fba_up_monitor.fnsku
     */
    private String fnsku;

    /**
     * SellerSku(如4NB401334_FRFBAMSJ) database column fba_up_monitor.seller_sku
     */
    private String sellerSku;

    /**
     * 创建时间 database column fba_up_monitor.created_at
     */
    private Timestamp createdAt;

    /**
     * 更新时间 database column fba_up_monitor.updated_at
     */
    private Timestamp updatedAt;

    //推送次数
    private Integer pushCount;


    /**
     * 计算预警状态
     * 根据签收时间月份、等待天数和季节配置计算预警状态
     * 
     * @param seasonConfig 季节预警配置
     * @return 预警状态代码，如果签收时间为空则返回null表示不参与计算
     */
    public String calculateAlertStatus(SeasonAlertSetting seasonConfig) {
        // 检查签收时间 - 为空时不参与计算
        if (this.receivedDate == null) {
            log.debug("记录{}签收时间为空，不参与预警状态计算", this.id);
            return alertStatus;
        }
        
        // 使用已有的等待天数字段值，不重新计算
        if (this.waitingDays == null || this.waitingDays < 0) {
            log.warn("记录{}的等待天数为空或无效，返回正常状态", this.id);
            return alertStatus;
        }
        
        // 获取签收月份
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        calendar.setTime(this.receivedDate);
        int receivedMonth = calendar.get(java.util.Calendar.MONTH) + 1; // Calendar月份从0开始
        
        // 获取该月份对应的季节类型
        Integer seasonCode = seasonConfig.getMonthSeasonMap().get(String.valueOf(receivedMonth));
        if (seasonCode == null) {
            log.warn("月份{}对应的季节配置不存在，默认为淡季", receivedMonth);
            seasonCode = SeasonType.OFF_SEASON.getCode();
        }
        
        log.debug("记录{}预警状态计算：签收月份={}, 季节类型={}, 等待天数={}", 
            this.id, receivedMonth, seasonCode, this.waitingDays);
        
        // 根据预警规则计算状态
        if (seasonCode.equals(SeasonType.OFF_SEASON.getCode())) {
            // 淡季规则：等待天数 > 淡季预警天数
            Integer offSeasonAlertDays = seasonConfig.getOffSeasonAlertDays();
            if (this.waitingDays > offSeasonAlertDays) {
                log.debug("记录{}符合淡季预警条件：等待{}天 > 淡季预警{}天", 
                    this.id, this.waitingDays, offSeasonAlertDays);
                return FbaAlertStatus.SEASON_ALERT.getCode();
            }
        } else if (seasonCode.equals(SeasonType.PEAK_SEASON.getCode())) {
            // 旺季规则：从配置中获取旺季预警开始天数和结束天数
            Integer peakStartDays = seasonConfig.getPeakStartDays();
            Integer peakEndDays = seasonConfig.getPeakEndDays();
            
            if (peakEndDays != null && this.waitingDays > peakEndDays) {
                log.debug("记录{}符合超期预警条件：等待{}天 > 旺季预警结束{}天", 
                    this.id, this.waitingDays, peakEndDays);
                return FbaAlertStatus.OVERTIME.getCode();
            } else if (peakStartDays != null && this.waitingDays > peakStartDays) {
                log.debug("记录{}符合旺季预警条件：等待{}天 > 旺季预警开始{}天", 
                    this.id, this.waitingDays, peakStartDays);
                return FbaAlertStatus.PEAK_ALERT.getCode();
            }
        }
        
        // 默认正常状态
        log.debug("记录{}保持正常状态：等待{}天未触发预警", this.id, this.waitingDays);
        return FbaAlertStatus.NORMAL.getCode();
    }

    /**
     * 获取货件状态的中文名称
     * 
     * @return 货件状态的中文名称，如果状态为空则返回空字符串
     */
    public String getStatusName() {
        return  StringUtils.isBlank(this.status) ? null : FbaStatus.getNameByCode(this.status);
    }

    /**
     * 获取预警状态的中文名称
     * 
     * @return 预警状态的中文名称，如果状态为空则返回空字符串
     */
    public String getAlertStatusName() {
        return  StringUtils.isBlank(this.alertStatus) ? null : FbaAlertStatus.getNameByCode(this.alertStatus);
    }
}