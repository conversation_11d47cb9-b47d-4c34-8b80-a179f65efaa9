package com.estone.transfer.bean;

import com.estone.apv.bean.WhApvGridItem;
import com.estone.apv.bean.WhApvOutStockChain;
import com.estone.apv.common.ApvGridStatus;
import com.estone.apv.enums.ApvTypeEnum;
import com.estone.asn.bean.WhAsnExtra;
import com.estone.asn.enums.AsnPackageMethodEnum;
import com.estone.asn.enums.AsnWarehouseStatus;
import com.estone.asn.util.YcUrlUtil;
import com.estone.common.SaleChannel;
import com.estone.common.util.CommonUtils;
import com.estone.picking.enums.PickTaskGridStatus;
import com.estone.temu.bean.TemuPrepareOrder;
import com.estone.temu.bean.TemuPrepareOrderItem;
import com.estone.temu.enums.SourceFromEnum;
import com.estone.temu.enums.TemuOrderStatus;
import com.estone.temu.enums.TemuPackageStatus;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.estone.transfer.request.CreatCoOrderRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Data
public class WhFbaAllocation implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id database column wh_fba_allocation.id
     */
    private Integer id;

    /**
     * 海外仓发货单号 database column wh_fba_allocation.fba_no
     */
    private String fbaNo;

    /**
     * 货架单号shipmentID database column wh_fba_allocation.shipment_id
     */
    private String shipmentId;

    /**
     * 备货销售账号 database column wh_fba_allocation.account_number
     */
    private String accountNumber;

    // 站点
    private String site;
    /**
     * 备货仓库 database column wh_fba_allocation.purpose_house
     */
    private String purposeHouse;

    /**
     * 状态，1待分配，3分配中 6待生成 8待调拨 10拣货中 12待装箱 14待审核 16待发货 17已交运 18已装车 19已废弃 database
     * column wh_fba_allocation.status
     */
    private Integer status;

    /**
     * 头程运输方式 database column wh_fba_allocation.sm_code
     */
    private String smCode;

    /**
     * 运输方式（自送时，需要填写到仓方式） database column wh_fba_allocation.shipping_method
     */
    private String shippingMethod;

    /**
     * tms推送的运输方式
     */
    private String shippingMethodByTms;

    /**
     * 物流商 database column wh_fba_allocation.shipping_company
     */
    private String shippingCompany;

    /**
     * 追踪号 database column wh_fba_allocation.tracking_number
     */
    private String trackingNumber;

    /**
     * tms系统推送的追踪号
     */
    private String trackingNumberByTms;

    /**
     * 物流单号 database column wh_fba_allocation.shipping_order_no
     * smr服务商侧运单号
     */
    private String shippingOrderNo;

    /**
     * 创建时间/推单时间：OMS推送单据到WMS的时间 database column wh_fba_allocation.push_time
     */
    private Timestamp pushTime;

    /**
     * 装箱推送人 database column wh_fba_allocation.box_push_by
     */
    private Integer boxPushBy;

    /**
     * 装箱推送时间：WMS推送装箱明细到OMS/TMS的时间 database column wh_fba_allocation.box_push_time
     */
    private Timestamp boxPushTime;

    /**
     * 确认时间：OMS返回确认出库的时间 database column wh_fba_allocation.confirm_time
     */
    private Timestamp confirmTime;

    /**
     * 交运人 database column wh_fba_allocation.deliver_by
     */
    private Integer deliverBy;

    /**
     * 交运时间：一体称/PDA交运扫描时间 database column wh_fba_allocation.deliver_time
     */
    private Timestamp deliverTime;

    /**
     * 取消时间：单据被取消的时间 database column wh_fba_allocation.cancel_time
     */
    private Timestamp cancelTime;

    /**
     * 起航时间：TMS推送（如果有的话） database column wh_fba_allocation.departure_time
     */
    private Timestamp departureTime;

    /**
     * 拣货任务号 database column wh_fba_allocation.task_no
     */
    private String taskNo;

    /**
     * 拣货任务号 database column wh_fba_allocation.pdf_url
     */
    private String pdfUrl;

    /**
     * 批次号 database column firstorder.batNo
     */
    private String batNo;

    /**
     * Amazon是否成功上传亚马逊后台
     * Smt是否拆包
     */
    private Integer transitType;

    /**
     * 发货单标签
     */
    private String tags;

    /**
     * 拣货缺货
     */
    private Boolean pickOut;

    /**
     * 复核人
     */
    private Integer checkBy;

    /**
     * 复核时间
     */
    private Timestamp checkTime;


    /**
     * 销售人员
     */
    private String salesperson;

    //海外仓上架时间
    private Timestamp overseasUpTime;

    /**
     * 销售驳回原因
     */
    private String rejectReason;

    // 是否重置物流信息
    private Boolean isResetShippingInfo;

    private List<WhFbaAllocationItem> items = new ArrayList<>();

    //发货地址信息
    private WhAsnExtra whAsnExtra;

    private AsnPickBox asnPickBox;

    private String remark;//货件备注

    //物流时效
    private String logisticsAging;

    private Boolean localAllot;//自发仓调拨

    private Boolean isAsn;

    /** 异常单据 */
    private Boolean exceptionOrder;

    /**
     * SKU 分组合并
     */
    private List<WhFbaAllocationItem> groupItems;

    private Map<String, Map<String, List<WhFbaAllocationItem>>> boxMap = new HashMap<>();

    private Integer boxTotal;

    private Map<String, List<WhApvGridItem>> apvGridItemMap = new HashMap<>();

    private List<Map<String, Object>> fmisItems;

    private List<WhApvOutStockChain> whApvOutStockChainList = new ArrayList<>();

    /**
     * 是否精品SKU
     */
    private boolean clothingBoutique;

    /**
     * 订单类型
     *
     * @see com.estone.apv.enums.ApvTypeEnum
     */
    private String apvType;

    /**
     * 接单时间：订单系统接收订单的时间
     */
    private Timestamp receiveTime;

    /**
     * 订单对应的结袋卡进行装车的时间
     */
    private Timestamp loadTime;

    /**
     * 合单时间
     */
    private Timestamp mergeTime;

    /**
     * 分拣状态
     */
    private Integer pickBoxStatus;

    /**
     * 分拣筐号
     */
    private Integer number;

    // 是否跨楼层
    private Boolean splitRegionFlag;

    private String warehouseCodeStr;

    /**
     * 包裹号
     */
    private String packageSn;

    //是否退仓
    private Boolean isReturn;

    private String amazonSite;
    //拣货完成时间
    private Timestamp pickTime;
    // 播种人
    private Integer sowUser;
    // 播种完成时间
    private Timestamp sowTime;
    // 包装人
    private Integer packUser;
    // 包装完成时间
    private Timestamp packTime;
    //装车id
    private Integer loadId;
    // 计划编号 fba新流程才推送 用于订单区分fba新流程
    private String planNo;
    // fba调拨单FNSKU汇总信息
    private WhFbaAllocationItem fbaPageItem;
    // FBA SKU信息
    private Map<String, List<WhFbaAllocationItem>>  skuInfoMap;
    // FBA装箱信息
    private Map<Integer, List<WhFbaAllocationItem>>  boxInfoMap;
    // 拣货任务类型
    private Integer taskType;

    /**
     * 计费金额 database column smt_aidc_settlement.settle_amount
     */     // 揽收分摊金额
    private BigDecimal settleAmount;

    /*//oms推送发货单号
    private String consignOrderNo;
    //oms推送LBX号
    private String logisticsNo;*/

    /**
     * 用于记录从该对象转化过来的fbaAllocation对象原先的对象
     */
    private WhFbaShipment whFbaShipment;

    //sku数量
    public Integer getSkuNum(){
        return Math.toIntExact(items.stream().map(WhFbaAllocationItem::getProductSku).distinct().count());
    };
    //sku件数
    public Integer getSkuQueryNum(){
        return Math.toIntExact(items.stream().filter(f->f.getBoxNo()==null || f.getBoxNo()==1).map(f->Optional.ofNullable(f.getQuantity()).orElse(0)).reduce(0, Integer::sum));
    };
    //sku已分配数量
    public Integer getSkuAllocatedNum(){
        return Math.toIntExact(items.stream().filter(f->f.getBoxNo()==null || f.getBoxNo()==1).map(f->Optional.ofNullable(f.getAllotQuantity()).orElse(0)).reduce(0, Integer::sum));
    };
    //sku已拣数量
    public Integer getSkuPickedNum(){
        return Math.toIntExact(items.stream().filter(f->f.getBoxNo()==null || f.getBoxNo()==1).map(f->Optional.ofNullable(f.getPickQuantity()).orElse(0)).reduce(0, Integer::sum));
    };
    //sku装箱数量
    public Integer getSkuBoxNum(){
        return Math.toIntExact(items.stream().map(f->Optional.ofNullable(f.getLoadNum()).orElse(0)).reduce(0, Integer::sum));
    };
    //sku上架数量
    public Integer getSkuPutawayNum(){
        return Math.toIntExact(items.stream().filter(f->f.getBoxNo()==null || f.getBoxNo()==1).map(f->Optional.ofNullable(f.getPutawayQuantity()).orElse(0)).reduce(0, Integer::sum));
    };



    public String getPickBoxStatusStr(){
        if (Objects.isNull(this.getPickBoxStatus())){
            return "";
        }
        if (Objects.equals(ApvGridStatus.COMPLETED.intCode(),this.getPickBoxStatus())){
            return "是";
        }
        return "否";
    }

    public void addItem(WhFbaAllocationItem item) {
        this.items.add(item);
    }

    /**
     * 用于将whFbaAllocation对象转化为temuPrepareOrder对象
     * @return
     */
    public TemuPrepareOrder buildTemuPrepareOrder() {
        TemuPrepareOrder order = new TemuPrepareOrder();
        // 用于设置构建字段对象
        order.setPrepareOrderNo(this.getFbaNo());
        order.setAccountNumber(this.getAccountNumber());
        order.setSeller(this.getSalesperson());
        Integer type = null;
        if (AsnPackageMethodEnum.URGENT_BACKUP.getCode().equals(this.getWhAsnExtra().getPackageMethod())) {
            type = 3;
        } else if (AsnPackageMethodEnum.NORMAL_BACKUP.getCode().equals(this.getWhAsnExtra().getPackageMethod())) {
            type = 1;
        }
        order.setType(type);
        String originAddress = this.getWhAsnExtra().getReceiptAddress();
        order.setReceiveHouse(originAddress);
        // 此处地址字段获取逗号分隔后的最后一个值，是为了预防有些地址的填写逗号分隔符前面有区号那些(这里中英文逗号都进行处理)
        if(originAddress.contains(",")) {
            List<String> addressList = CommonUtils.splitList(originAddress, ",");
            order.setReceiveHouse(addressList.get(addressList.size() - 1));
        }else if(originAddress.contains("，")){
            List<String> addressList = CommonUtils.splitList(originAddress, "，");
            order.setReceiveHouse(addressList.get(addressList.size() - 1));
        }

        order.setStatus(TemuOrderStatus.WAITING_ALLOT.intCode());
        order.setDeliverOrderNo(this.getShipmentId());
        order.setExpressDelivery(this.getTrackingNumber());
        order.setShippingCompany(this.getShippingMethod());
        order.setDriverPhone(this.getWhAsnExtra().getPhoneNumber());
        order.setCreationDate(new Timestamp(System.currentTimeMillis()));
        List<TemuPrepareOrderItem> items = this.items.stream().map(item -> {
            TemuPrepareOrderItem temuPrepareOrderItem = new TemuPrepareOrderItem();
            temuPrepareOrderItem.setSku(item.getProductSku());
            temuPrepareOrderItem.setPrepareQuantity(item.getQuantity());
            temuPrepareOrderItem.setRealQuantity(item.getQuantity());
            temuPrepareOrderItem.setPackageSn(this.getPackageSn());
            temuPrepareOrderItem.setPackageStatus(TemuPackageStatus.WAIT_MERGED.intCode());
            temuPrepareOrderItem.setSourceFrom(SourceFromEnum.ASN.intCode());
            temuPrepareOrderItem.setTemuCodeUrl(item.getTemuCodeUrl());
            temuPrepareOrderItem.setTemuTagUrl(item.getTemuTagUrl());
            return temuPrepareOrderItem;
        }).collect(Collectors.toList());
        order.setItemList(items);
        return order;
    }

    public Map<String, WhFbaAllocationItem> buildComparisonItems(Function<WhFbaAllocationItem, String> keyOperator, BinaryOperator<WhFbaAllocationItem> mergeOperator) {
        if (CollectionUtils.isEmpty(items)){
            return null;
        }
        return items.stream().collect(Collectors.toMap(keyOperator, Function.identity(),mergeOperator));
    }

    /**
     * @return java.util.List<com.estone.asn.bean.WhAsnItem>
     * @Description SKU分组合并
     * <AUTHOR>
     * @date 2020/11/27 11:42
     * @param:
     */
    public List<WhFbaAllocationItem> buildGroupItems() {
        if (CollectionUtils.isNotEmpty(items)) {
            Map<String, WhFbaAllocationItem> map = new HashMap<>();
            Set<String> existSet = new HashSet();
            Set<String> existBoxSet = new HashSet();
            for (WhFbaAllocationItem item : items) {
                WhFbaAllocationItem exist = map.get(item.getProductSku());
                if (exist == null) {
                    exist = new WhFbaAllocationItem();
                    exist.setProductSku(item.getProductSku());
                    exist.setSellSku(item.getSellSku());
                    exist.setFnSku(item.getFnSku());
                    exist.setSellSkuName(item.getSellSkuName());
                    exist.setStore(item.getStore());
                    exist.setSite(item.getSite());
                    exist.setSuitFlag(item.getSuitFlag());
                    exist.setSkuSuitNum(item.getSkuSuitNum());
                    if (getAsnFirst())
                        exist.setTemuCodeUrl(item.getTemuCodeUrl());
                    exist.setWhSku(item.getWhSku());
                }

                //装箱数量
                Integer loadingQuantityCount = exist.getLoadingQuantity() == null ? 0 : exist.getLoadingQuantity();
                // 数量
                Integer quantity = item.getQuantity() == null ? 0 : item.getQuantity();
                //海外上架数量
                Integer upQuantity = item.getPutawayQuantity() == null ? 0 : item.getPutawayQuantity();
                // 已分配数量
                Integer allotQuantity = item.getAllotQuantity() == null ? 0 : item.getAllotQuantity();
                //已捡数量
                Integer pickQuantity = item.getPickQuantity() == null ? 0 : item.getPickQuantity();
                //已播数量
                Integer gridQuantity = item.getGridQuantity() == null ? 0 : item.getGridQuantity();
                Integer loadingQuantity = item.getLoadingQuantity() == null ? 0 : item.getLoadingQuantity();
                String key = item.getFnSku() + "~" + item.getProductSku();
                if (isAsn != null && isAsn) {
                    key = item.getFnSku() + "~" + item.getProductSku() + "~" + item.getScItemId();
                }
                String boxKey = item.getFnSku() + "~" + item.getProductSku() + "~" + item.getBoxNo();

                boolean existContains = existSet.contains(key);
                if (item.getId() == null || !existContains || !isFba() && !(isAsn != null && isAsn)) {
                    exist.setSkuQuantity((item.getSkuQuantity() == null ? 0 : item.getSkuQuantity()) + (exist.getSkuQuantity() == null ? 0 : exist.getSkuQuantity()));
                    exist.setPickQuantity(pickQuantity + (exist.getPickQuantity() == null ? 0 : exist.getPickQuantity()));
                    exist.setGridQuantity(gridQuantity + (exist.getGridQuantity() == null ? 0 : exist.getGridQuantity()));
                    exist.setAllotQuantity(allotQuantity + (exist.getAllotQuantity() == null ? 0 : exist.getAllotQuantity()));
                    existSet.add(key);
                }

                if (!existBoxSet.contains(boxKey)) {
                    exist.setLoadingQuantity(loadingQuantityCount + loadingQuantity);
                    existBoxSet.add(boxKey);
                }
                exist.setQuantity(quantity);
                exist.setPutawayQuantity(upQuantity);
                exist.setPutawayDiff(upQuantity - exist.getLoadingQuantity());
                map.put(item.getProductSku(), exist);
            }
            groupItems = new ArrayList<>(map.values());
        }
        return groupItems;
    }

    /**
     * FBA调拨发货页面
     */
    public void buildFbaItem() {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        List<Integer> statuArr = List.of(17, 18);
        fbaPageItem = new WhFbaAllocationItem();
        Map<String, List<WhFbaAllocationItem>> fnskuGroup = items.stream().collect(Collectors.groupingBy(WhFbaAllocationItem::getFnSku));
        fbaPageItem.setFnskuNum(fnskuGroup.size());
        fnskuGroup.forEach((k, v) -> {
            WhFbaAllocationItem item = v.get(0);
            // FNSKU件数
            Integer fnskuQty = Optional.ofNullable(item.getQuantity()).orElse(0);
            Integer sumfnskuQty = Optional.ofNullable(fbaPageItem.getQuantity()).orElse(0);
            fbaPageItem.setQuantity(sumfnskuQty + fnskuQty);
            // FNSKU装箱数
            int loadNum = v.stream().collect(Collectors.groupingBy(c -> c.getBoxNo() == null ? "null" : c.getBoxNo().toString(),
                            Collectors.collectingAndThen(Collectors.toList(), c -> c.get(0))))
                    .values().stream().mapToInt(c -> c.getLoadNum() == null ? 0 : c.getLoadNum()).sum();
            Integer sumLoadNum = Optional.ofNullable(fbaPageItem.getLoadNum()).orElse(0);
            fbaPageItem.setLoadNum(sumLoadNum + loadNum);
            if (status != null && statuArr.contains(status)) {
                int loadingQty = v.stream().mapToInt(c -> c.getLoadingQuantity() == null ? 0 : c.getLoadingQuantity()).sum();
                Integer sumLoadingQty = Optional.ofNullable(fbaPageItem.getLoadingQuantity()).orElse(0);
                fbaPageItem.setLoadingQuantity(sumLoadingQty + loadingQty);
            }
            // 海外仓上架数量数
            Integer skuPutawayQty = Optional.ofNullable(item.getPutawayQuantity()).orElse(0);
            Integer sumSkuPutawayQty = Optional.ofNullable(fbaPageItem.getPutawayQuantity()).orElse(0);
            fbaPageItem.setPutawayQuantity(sumSkuPutawayQty + skuPutawayQty);
            // 海外仓上架差异
            Integer skuPutawayDiff = Optional.ofNullable(item.getPutawayDiff()).orElse(0);
            Integer sumSkuPutawayDiff = Optional.ofNullable(fbaPageItem.getPutawayDiff()).orElse(0);
            fbaPageItem.setPutawayDiff(sumSkuPutawayDiff - skuPutawayDiff);
        });
        Map<String, List<WhFbaAllocationItem>> skuGroup = items.stream()
                .collect(Collectors.groupingBy(f -> f.getFnSku() + "&" + f.getProductSku()));
        skuGroup.forEach((k, v) -> {
            WhFbaAllocationItem item = v.get(0);
            // SKU件数
            Integer skuQty = Optional.ofNullable(item.getSkuQuantity()).orElse(0);
            Integer sumSkuQty = Optional.ofNullable(fbaPageItem.getSkuQuantity()).orElse(0);
            fbaPageItem.setSkuQuantity(sumSkuQty + skuQty);
            // 拣货数
            Integer skuPickQty = Optional.ofNullable(item.getPickQuantity()).orElse(0);
            Integer sumSkuPickQty = Optional.ofNullable(fbaPageItem.getPickQuantity()).orElse(0);
            fbaPageItem.setPickQuantity(sumSkuPickQty + skuPickQty);
            // 播种数
            Integer skuGridQty = Optional.ofNullable(item.getGridQuantity()).orElse(0);
            Integer sumSkuGridQty = Optional.ofNullable(fbaPageItem.getGridQuantity()).orElse(0);
            fbaPageItem.setGridQuantity(sumSkuGridQty + skuGridQty);
        });
    }


    public void buildAsnDetail() {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        List<Integer> statuArr = List.of(17, 18);
        // 构建详情页SKU信息
        skuInfoMap = new HashMap<>();
        Map<String, List<WhFbaAllocationItem>> skuGroup = items.stream().collect(Collectors.groupingBy(WhFbaAllocationItem::getProductSku));
        List<WhFbaAllocationItem> list = new ArrayList<>();
        skuGroup.forEach((k, v) -> {
            WhFbaAllocationItem v2Item = v.get(0);
            WhFbaAllocationItem item = new WhFbaAllocationItem();
            BeanUtils.copyProperties(v2Item, item);
            int loadNumSum = v.stream().collect(Collectors.groupingBy(c -> c.getBoxNo() == null ? "null" : c.getBoxNo().toString(),
                            Collectors.collectingAndThen(Collectors.toList(), c -> c.get(0))))
                    .values().stream().mapToInt(c -> c.getLoadNum() == null ? 0 : c.getLoadNum()).sum();
            item.setLoadNum(loadNumSum);
            if (status != null && statuArr.contains(status)) {
                int loadingQtySum = v.stream().mapToInt(c -> c.getLoadingQuantity() == null ? 0 : c.getLoadingQuantity()).sum();
                item.setLoadingQuantity(loadingQtySum);
            } else {
                item.setLoadingQuantity(null);
            }
            list.add(item);
        });
        skuInfoMap=list.stream().collect(Collectors.groupingBy(WhFbaAllocationItem::getProductSku));

        // 构建详情页装箱信息
        boxMap = items.stream().filter(i -> i.getId() != null)
                .collect(Collectors.groupingBy(
                        item -> item.getBoxNo() == null ? "null" : item.getBoxNo().toString(), HashMap::new,
                        Collectors.groupingBy(WhFbaAllocationItem::getProductSku, HashMap::new,
                                Collectors.toList())));
    }


    /**
     * FBA调拨发货详情
     */
    public void buildFbaDetail() {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        List<Integer> statuArr = List.of(17, 18);
        // 构建详情页SKU信息
        skuInfoMap = new HashMap<>();
        Map<String, List<WhFbaAllocationItem>> fnskuGroup = items.stream().collect(Collectors.groupingBy(WhFbaAllocationItem::getFnSku));
        fnskuGroup.forEach((k, v) -> {
            List<WhFbaAllocationItem> list = new ArrayList<>();
            Map<String, List<WhFbaAllocationItem>> skuGroup = v.stream().collect(Collectors.groupingBy(WhFbaAllocationItem::getProductSku));
            skuGroup.forEach((k2, v2) -> {
                WhFbaAllocationItem v2Item = v2.get(0);
                WhFbaAllocationItem item = new WhFbaAllocationItem();
                BeanUtils.copyProperties(v2Item, item);
                int loadNumSum = v2.stream().collect(Collectors.groupingBy(c -> c.getBoxNo() == null ? "null" : c.getBoxNo().toString(),
                                Collectors.collectingAndThen(Collectors.toList(), c -> c.get(0))))
                        .values().stream().mapToInt(c -> c.getLoadNum() == null ? 0 : c.getLoadNum()).sum();
                item.setLoadNum(loadNumSum);
                if (status != null && statuArr.contains(status)) {
                    int loadingQtySum = v2.stream().mapToInt(c -> c.getLoadingQuantity() == null ? 0 : c.getLoadingQuantity()).sum();
                    item.setLoadingQuantity(loadingQtySum);
                } else {
                    item.setLoadingQuantity(null);
                }
                list.add(item);
            });
            skuInfoMap.put(k, list);
        });
        // 构建详情页装箱信息
        boxMap = items.stream().filter(i -> i.getId() != null)
                .collect(Collectors.groupingBy(
                        item -> item.getBoxNo() == null ? "null" : item.getBoxNo().toString(), HashMap::new,
                        Collectors.groupingBy(WhFbaAllocationItem::getFnSku, HashMap::new,
                                Collectors.toList())));
    }

    /**
     * @return void
     * @Description 匹配已分配和已拣数量
     * <AUTHOR>
     * @date 2020/11/27 11:41
     * @param: type：1已分配；2已拣
     */
    public void buildItemQuantity(Integer type, Map<String, Integer> updateItemQuantityMap) {
        if (updateItemQuantityMap != null) {

            for (WhFbaAllocationItem item : items) {
                if (updateItemQuantityMap.get(item.getProductSku()) == null) {
                    continue;
                }

                Integer updateQuantity = updateItemQuantityMap.get(item.getProductSku());
                Integer skuQuantity = item.getSkuQuantity() == null ? 0 : item.getSkuQuantity();
                Integer allotQuantity = item.getAllotQuantity() == null ? 0 : item.getAllotQuantity();
                Integer pickQuantity = item.getPickQuantity() == null ? 0 : item.getPickQuantity();
                if (type == 1) {
                    // 匹配分配数量,sku数量全部分配完
                    item.setAllotQuantity(allotQuantity + skuQuantity);
                } else if (type == 2) {
                    // 匹配已拣数量
                    item.setPickQuantity(pickQuantity + updateQuantity);
                }
            }
        }
    }

    // 组装
    public void combinReceivingCode() {
        if (AsnWarehouseStatus.THAILAND.getCode().equalsIgnoreCase(purposeHouse)) {
            this.fbaNo = this.getFbaNo() + "-" + YcUrlUtil.TH_SUFFIX;
        } else {
            this.fbaNo = this.getFbaNo() + "-" + YcUrlUtil.PH_SUFFIX;
        }
    }

    // 拆分 receivingCode
    public void splitReceivingCode() {
        if (AsnWarehouseStatus.THAILAND.getCode().equalsIgnoreCase(purposeHouse)) {
            this.fbaNo = this.getFbaNo().replaceAll("-" + YcUrlUtil.TH_SUFFIX, "");
        } else {
            this.fbaNo = this.getFbaNo().replaceAll("-" + YcUrlUtil.PH_SUFFIX, "");
        }
    }

    public Integer getBoxTotal() {
        if (CollectionUtils.isNotEmpty(items)) {
            boxTotal = items.stream().map(WhFbaAllocationItem::getBoxNo).distinct().collect(Collectors.toList()).size();
        }
        return boxTotal;
    }

    /**
     * 拣货数量
     */
    public Integer getPickNum() {
        if (CollectionUtils.isEmpty(items))
            return 0;
        Map<String, Integer> skuPickQuantityMap = items.stream()
                .collect(Collectors.toMap(WhFbaAllocationItem::getProductSku,
                        o -> o.getPickQuantity() == null ? 0 : o.getPickQuantity(), (o1, o2) -> o2));
        return skuPickQuantityMap.values().stream().mapToInt(q -> q).sum();
    }

    /**
     * 已分配数量
     */
    public Integer getAllotNum() {
        if (CollectionUtils.isEmpty(items))
            return 0;
        Map<String, Integer> skuPickQuantityMap = items.stream()
                .collect(Collectors.toMap(WhFbaAllocationItem::getProductSku,
                        o -> o.getAllotQuantity() == null ? 0 : o.getAllotQuantity(), (o1, o2) -> o2));
        return skuPickQuantityMap.values().stream().mapToInt(q -> q).sum();
    }

    /**
     * 播种数量
     *
     * @return
     */
    public Integer getGridNum() {
        if (CollectionUtils.isEmpty(items))
            return 0;
        Map<String, Integer> skuGridQuantityMap = items.stream()
                .collect(Collectors.toMap(WhFbaAllocationItem::getProductSku,
                        o -> o.getGridQuantity() == null ? 0 : o.getGridQuantity(), (o1, o2) -> o2));
        return skuGridQuantityMap.values().stream().mapToInt(q -> q).sum();
    }

    /**
     * 装车发货数量
     *
     * @return
     */
    public Integer getLoadingQuantitySum() {
        return Optional.ofNullable(items).orElse(new ArrayList<>()).stream()
                .mapToInt(i -> Optional.ofNullable(i.getLoadingQuantity()).orElse(0)).sum();
    }

    public boolean isAllGrid() {
        if (CollectionUtils.isEmpty(items))
            return false;
        return items.stream().allMatch(i -> i.getPickQuantity() == null || i.getPickQuantity() == 0
                || PickTaskGridStatus.COMPLETED.intCode().equals(i.getGridStatus()));
    }

    public Set<String> getLockKeys() {
        if (CollectionUtils.isEmpty(this.getItems())) return null;
        return this.buildGroupItems().stream().map(item -> {
            return accountNumber + "-" + item.getSite() + "-" + item.getProductSku();
        }).collect(Collectors.toSet());
    }

    /**
     * 功能描述: 添加标签
     *
     * @Author: fangxin
     **/
    public void addTag(String tag) {
        if (StringUtils.isBlank(tag)) return;
        if (StringUtils.isBlank(this.tags)) {
            this.setTags(tag);
        } else {
            List<String> asList = Arrays.asList(this.tags.split(","));
            if (!asList.contains(tag)) {
                asList.add(tag);
                this.setTags(StringUtils.join(asList, ","));
            }
        }
    }

    /**
     * 功能描述: 删除标签
     *
     * @Author: fangxin
     **/
    public void delTag(String tag) {
        if (StringUtils.isBlank(tag) || StringUtils.isBlank(tags)) return;
        List<String> asList = Arrays.asList(this.tags.split(","));
        if (asList.contains(tag)) {
            asList.remove(tag);
            this.setTags(StringUtils.join(asList, ","));
        }
    }

    // 获取重置物流信息sql
    public String getResetShippingInfoSql() {
        if (isResetShippingInfo == null || !isResetShippingInfo) return null;
        return "bat_no = null, tracking_number=null, shipping_order_no=null, pdf_url=null, shipping_method=null, shipping_company=null,";
    }

    // 是否amazon
    public boolean isFba() {
        return !SaleChannel.saleChannels.stream().anyMatch(s -> StringUtils.equalsIgnoreCase(s, purposeHouse));
    }

    /**
     * 判断是否shein平台
     *
     * @return
     */
    public boolean isShein() {
        return SaleChannel.CHANNEL_SHEIN.equalsIgnoreCase(this.getPurposeHouse());
    }

    /**
     * 判断temu平台
     * @return
     */
    public boolean isTemu() {
        return SaleChannel.CHANNEL_TEMU.equalsIgnoreCase(this.getPurposeHouse());
    }
    
    
    public boolean isJit() {
        WhAsnExtra extra = this.getWhAsnExtra();

        if (StringUtils.isBlank(this.getPurposeHouse()) || Objects.isNull(extra)
                || Objects.isNull(extra.getPackageMethod())) {
            return false;
        }
        return Objects.equals(SaleChannel.CHANNEL_SMT, this.getPurposeHouse())
                && (Objects.equals(AsnPackageMethodEnum.JIT_HALF.getCode(), extra.getPackageMethod())
                        || Objects.equals(AsnPackageMethodEnum.JIT.getCode(), extra.getPackageMethod()));
    }

    /**
     * 判断是否为转为拼多多备货单的temu单据
     * @return
     */
    public boolean isPrepareTemu(){
        return this.isTemu() && !Objects.equals(AsnPackageMethodEnum.TEMU_HALF.getCode(),this.getPackMethod());
    }

    /**
     * 判断是否是中转仓订单
     *
     * @return
     */
    public boolean isTransfer() {
        WhAsnExtra extra = this.getWhAsnExtra();

        if (StringUtils.isBlank(this.getPurposeHouse())
                || Objects.isNull(extra) || Objects.isNull(extra.getPackageMethod())) {
            return false;
        }

        boolean isSmtTransferOrder = Objects.equals(SaleChannel.CHANNEL_SMT, this.getPurposeHouse()) && (
                Objects.equals(AsnPackageMethodEnum.JIT_HALF.getCode(), extra.getPackageMethod())
                        || Objects.equals(AsnPackageMethodEnum.JIT.getCode(), extra.getPackageMethod()));
        boolean isSheinTransferOrder = Objects.equals(SaleChannel.CHANNEL_SHEIN, this.getPurposeHouse()) && (
                Objects.equals(AsnPackageMethodEnum.URGENT.getCode(), extra.getPackageMethod())
                        || Objects.equals(AsnPackageMethodEnum.BACKUP.getCode(), extra.getPackageMethod()));

        if (isSmtTransferOrder || isSheinTransferOrder) {
            return true;
        }

        return false;
    }

    public boolean getAsnFirst() {
        if (StringUtils.isBlank(this.getPurposeHouse())) {
            return false;
        }
        boolean anyMatch = SaleChannel.saleChannels.stream()
                .anyMatch(s -> StringUtils.equalsIgnoreCase(s, purposeHouse));
        boolean smt = SaleChannel.CHANNEL_SMT.equalsIgnoreCase(purposeHouse);

        if (anyMatch && !smt && !SaleChannel.CHANNEL_SHEIN.equalsIgnoreCase(purposeHouse))
            return true;
        return false;
    }

    /**
     * 根据订单明细判断订单类型
     *
     * @return
     */
    public String judgeApvTypeByItems() {
        if (CollectionUtils.isEmpty(this.getItems())) {
            return null;
        }

        List<WhFbaAllocationItem> items = this.getItems();
        Map<String, Integer> skuQuantityMap = items.stream()
                .filter(v -> Objects.nonNull(v.getSkuQuantity()) && Objects.nonNull(v.getProductSku()))
                .collect(Collectors.toMap(WhFbaAllocationItem::getProductSku, w-> w.getSkuQuantity() == null ? w.getQuantity() : w.getSkuQuantity(), (v1, v2) -> v1 + v2));
        if (Objects.isNull(skuQuantityMap)){
            skuQuantityMap = new HashMap<>();
        }

        if (skuQuantityMap.size() > 1) {
            return ApvTypeEnum.MM.getCode();
        }

        Integer allQuantity = 0;
        for (Integer quantity : skuQuantityMap.values()) {
            allQuantity += quantity;
        }
        if (skuQuantityMap.size() == 1 && allQuantity > 1) {
            return ApvTypeEnum.SM.getCode();
        }

        return ApvTypeEnum.SS.getCode();
    }

    /**
     * @return java.util.List<com.estone.asn.bean.WhAsnItem>
     * @Description SKU分组合并
     * 用于海外退件 前端页面
     */
    public List<WhFbaAllocationItem> getWhApvItems() {
        Map<String, WhFbaAllocationItem> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(items)) {
            for (WhFbaAllocationItem item : items) {
                WhFbaAllocationItem exist = map.get(item.getProductSku());
                if (exist == null) {
                    exist = new WhFbaAllocationItem();
                    exist.setProductSku(item.getProductSku());
                }
                //装箱数量
                Integer loadingQuantityCount = exist.getLoadingQuantity() == null ? 0 : exist.getLoadingQuantity();
                Integer loadingQuantity = item.getLoadingQuantity() == null ? 0 : item.getLoadingQuantity();
                exist.setLoadingQuantity(loadingQuantityCount + loadingQuantity);
                map.put(item.getProductSku(), exist);
            }
        }
        return new ArrayList<>(map.values());
    }

    /**
     * @return java.util.List<com.estone.asn.bean.WhAsnItem>
     * @Description SKU分组合并
     * 用于海外退件 前端页面 TEMU
     */
    public List<WhFbaAllocationItem> getTemuReturnItems() {
        if (CollectionUtils.isNotEmpty(items)) {
            Map<String, WhFbaAllocationItem> map = new HashMap<>();
            for (WhFbaAllocationItem item : items) {
                WhFbaAllocationItem exist = map.get(item.getFbaNo()+item.getProductSku());
                if (exist == null) {
                    exist = new WhFbaAllocationItem();
                    exist.setProductSku(item.getProductSku());
                    exist.setFbaNo(item.getFbaNo());
                }
                //装箱数量
                Integer loadingQuantityCount = exist.getLoadingQuantity() == null ? 0 : exist.getLoadingQuantity();
                Integer loadingQuantity = item.getLoadingQuantity() == null ? 0 : item.getLoadingQuantity();
                exist.setLoadingQuantity(loadingQuantityCount + loadingQuantity);
                map.put(item.getFbaNo()+item.getProductSku(), exist);
            }
            ArrayList<WhFbaAllocationItem> list = new ArrayList<>(map.values());
            Collections.sort(list, (o1, o2) -> ObjectUtils.compare(o1.getFbaNo(), o2.getFbaNo()));
            return list;
        }
        return new ArrayList<>();
    }

    // 获取去前缀的店铺
    public String getUnprefixedAccountNumber() {
        if (StringUtils.isBlank(accountNumber))
            return null;
        // 非amazon，非精品
        if (!isFba() && !clothingBoutique)
            return accountNumber;

        if (StringUtils.substring(accountNumber, 2, 3).equals("-")) {
            return StringUtils.substring(accountNumber, 3);
        }
        return accountNumber;
    }

    public Integer getPackMethod() {
        if (this.getWhAsnExtra() == null) {
            return null;
        }
        return this.getWhAsnExtra().getPackageMethod();
    }

    public String getWarehouseCode() {
        if (this.getWhAsnExtra() == null) {
            return null;
        }
        return this.getWhAsnExtra().getWarehouseCode();
    }

    // 增加取消后缀
    public void addCancelSuffix(WhFbaAllocation update) {
        if (Objects.equals(SaleChannel.CHANNEL_SMT, purposeHouse)
                || Objects.equals(SaleChannel.CHANNEL_SHEIN, purposeHouse)){
            String format = new SimpleDateFormat("mmSS").format(new Timestamp(System.currentTimeMillis()));
            if (org.apache.commons.lang3.StringUtils.isNotBlank(trackingNumber))
                update.setTrackingNumber(trackingNumber+"_"+format);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(shipmentId))
                update.setShipmentId(shipmentId+"_"+format);
        }
    }

    public boolean orderGrid() {
        if (CollectionUtils.isEmpty(items))
            return false;
        return items.stream()
                .allMatch(i -> PickTaskGridStatus.COMPLETED.intCode().equals(i.getGridStatus())
                        || i.getAllotQuantity() == null || i.getAllotQuantity() == 0);
    }

    /**
     * 包含套装
     *
     * @return
     */
    public boolean containSuitSku() {
        if (CollectionUtils.isEmpty(items))
            return false;
        return items.stream().anyMatch(i -> i.getSuitFlag() != null && i.getSuitFlag().equals(1));
    }

    public static final Integer SURVEILLANCE_TIME = 36;

    /**
     * 用于获取截止时间,yyyy-MM-dd HH:mm:ss
     */
    public String getStopTime() {
        List<Integer> packageMethods = Arrays.asList(AsnPackageMethodEnum.JIT.getCode(), AsnPackageMethodEnum.JIT_HALF.getCode());
        if (Objects.nonNull(this.getReceiveTime())
                && Objects.nonNull(this.getWhAsnExtra()) && packageMethods.contains(this.getWhAsnExtra().getPackageMethod())
                && Objects.nonNull(this.getPurposeHouse()) && Objects.equals(SaleChannel.CHANNEL_SMT.toLowerCase(), this.getPurposeHouse().toLowerCase())) {
            Date endTime = DateUtils.addHours(this.getReceiveTime(), SURVEILLANCE_TIME);
            return com.estone.common.util.DateUtils.dateToString(endTime, com.estone.common.util.DateUtils.STANDARD_DATE_PATTERN);
        }
        return null;
    }

    /**
     * 用于获取是否需要进行倒计时的判断
     * @return false-不用进行倒计时，true-要进行倒计时操作
     */
    public Boolean getCountDownFlag(){
        List<Integer> completedStatus = Arrays.asList(AsnPrepareStatus.LOADED.intCode(), AsnPrepareStatus.CANCEL.intCode());
        List<Integer> packageMethods = Arrays.asList(AsnPackageMethodEnum.JIT.getCode(), AsnPackageMethodEnum.JIT_HALF.getCode());
        return !completedStatus.contains(this.getStatus())
                && Objects.nonNull(this.getPurposeHouse())
                && Objects.equals(SaleChannel.CHANNEL_SMT.toLowerCase(), this.getPurposeHouse().toLowerCase())
                && Objects.nonNull(this.getWhAsnExtra())
                && packageMethods.contains(this.getWhAsnExtra().getPackageMethod());
    }


    /**
     * 获取截止时间和完成时间（已取消状态的是取消时间，已装车状态的是装车时间）之间的时间间隔，
     * 用HH:mm:ss表示，其值可能在最前面有个负号
     * @return
     */
    public String getIntervalTime(){
        List<Integer> completedStatus = Arrays.asList(AsnPrepareStatus.LOADED.intCode(), AsnPrepareStatus.CANCEL.intCode());
        if (completedStatus.contains(this.getStatus())){
            Timestamp completedTime = null;
            if (Objects.equals(AsnPrepareStatus.LOADED.intCode(), this.getStatus())){
                completedTime = this.getLoadTime();
            }else if(Objects.equals(AsnPrepareStatus.CANCEL.intCode(), this.getStatus())){
                completedTime = this.getCancelTime();
            }

            String stopTime = this.getStopTime();
            if(Objects.nonNull(completedTime) && StringUtils.isNotBlank(stopTime)){
                try {
                    Date stopDate = DateUtils.parseDate(stopTime, new String[]{com.estone.common.util.DateUtils.STANDARD_DATE_PATTERN});
                    long remainMilliseconds = stopDate.getTime() - completedTime.getTime();
                    String minusFlag = remainMilliseconds < 0? "<span style='color:red;'>已超出 </span>" : "";
                    remainMilliseconds = Math.abs(remainMilliseconds);
                    long seconds = remainMilliseconds / 1000 ;
                    long hours = seconds / 3600;
                    long minutes = (seconds % 3600) / 60;
                    long second = seconds % 60;
                    DecimalFormat df = new DecimalFormat("00");
                    return minusFlag + df.format(hours)+"小时"+df.format(minutes)+"分"+df.format(second)+"秒";
                }catch (Exception e){
                    log.error(e.getMessage());
                }
            }
        }
        return null;
    }
    public CreatCoOrderRequest buildJitCoOrderRequest() {
        if (Objects.isNull(whAsnExtra) || StringUtils.isBlank(whAsnExtra.getPurchaseOrderNo()) || CollectionUtils.isEmpty(items)) {
            throw new RuntimeException("创建发货单参数whAsnExtra||JIT采购单号 || 发货单明细为空 为空！");
        }
        CreatCoOrderRequest request = new CreatCoOrderRequest();
        request.setPurchase_order_no(whAsnExtra.getPurchaseOrderNo());


        List<CreatCoOrderRequest.JitPurchaseOrderItem>  purchaseOrderItemList = new ArrayList<>();
        Integer boxNum = 1;
        for (WhFbaAllocationItem item : items) {
            if (StringUtils.isBlank(item.getProductBarcode())) {
                continue;
            }
            CreatCoOrderRequest.JitPurchaseOrderItem purchaseOrderItem= new CreatCoOrderRequest.JitPurchaseOrderItem();
            purchaseOrderItem.setPo_line_id(item.getPoLineId());
            Integer sentQty;
            if (transitType != null && transitType == 1) {
                if (item.getLoadingQuantity() == null || item.getLoadingQuantity() == 0) continue;
                sentQty = item.getLoadingQuantity();
                purchaseOrderItem.setSent_qty(sentQty);
                purchaseOrderItem.setDelivery_finish(false);
            } else {
                sentQty = item.getAllotQuantity();
                purchaseOrderItem.setSent_qty(sentQty);
            }
            purchaseOrderItem.setSc_item_id(Long.valueOf(item.getProductBarcode()));
            purchaseOrderItemList.add(purchaseOrderItem);

            CreatCoOrderRequest.JitPurchaseOrderItem.JitPackage jitPackage = new CreatCoOrderRequest.JitPurchaseOrderItem.JitPackage();
            jitPackage.setBox_qty(sentQty);
            jitPackage.setBox_sequence(boxNum.toString());
            purchaseOrderItem.setPackage_list(Arrays.asList(jitPackage));
            boxNum++;
        }

        request.setPurchase_order_item_list(purchaseOrderItemList);
        return request;
    }

    //支持套装
    public CreatCoOrderRequest buildJitCoOrderRequestv2() {
        if (Objects.isNull(whAsnExtra) || StringUtils.isBlank(whAsnExtra.getPurchaseOrderNo()) || CollectionUtils.isEmpty(items)) {
            throw new RuntimeException("创建发货单参数whAsnExtra||JIT采购单号 || 发货单明细为空 为空！");
        }
        CreatCoOrderRequest request = new CreatCoOrderRequest();
        request.setPurchase_order_no(whAsnExtra.getPurchaseOrderNo());


        List<CreatCoOrderRequest.JitPurchaseOrderItem>  purchaseOrderItemList = new ArrayList<>();
        Integer boxNum = 1;
        for (WhFbaAllocationItem item : items) {
            if (item.getScItemId() == null) {
                continue;
            }
            if (purchaseOrderItemList.stream().anyMatch(i -> i.getSc_item_id().equals(item.getScItemId()))) continue;
            CreatCoOrderRequest.JitPurchaseOrderItem purchaseOrderItem= new CreatCoOrderRequest.JitPurchaseOrderItem();
            purchaseOrderItem.setPo_line_id(item.getPoLineId());
            Integer sentQty;
            if (transitType != null && transitType == 1) {
                sentQty = item.getLoadNum();
                if (item.getLoadNum() == null || item.getLoadNum() == 0) continue;
               /* sentQty = item.getLoadingQuantity();
                if (item.getSuitFlag() == 1) {
                    sentQty = sentQty%item.getSkuSuitNum();
                    if (sentQty==0) continue;
                }*/
                purchaseOrderItem.setSent_qty(item.getLoadNum());
                purchaseOrderItem.setDelivery_finish(false);
            } else {
                sentQty = item.getAllotQuantity();
                if (item.getSuitFlag() == 1) {
                    sentQty = sentQty/item.getSkuSuitNum();
                    if (sentQty==0) continue;
                }
                purchaseOrderItem.setSent_qty(sentQty);
            }
            purchaseOrderItem.setSc_item_id(item.getScItemId());
            purchaseOrderItemList.add(purchaseOrderItem);

            CreatCoOrderRequest.JitPurchaseOrderItem.JitPackage jitPackage = new CreatCoOrderRequest.JitPurchaseOrderItem.JitPackage();
            jitPackage.setBox_qty(sentQty);
            jitPackage.setBox_sequence(boxNum.toString());
            purchaseOrderItem.setPackage_list(Arrays.asList(jitPackage));
            boxNum++;
        }

        request.setPurchase_order_item_list(purchaseOrderItemList);
        return request;
    }

    public List<WhFbaAllocationItem> getBoxNumList() {
        if (CollectionUtils.isEmpty(items)){
            return new ArrayList<>();
        }
        Map<Integer, WhFbaAllocationItem> map = new HashMap<>();
        for (WhFbaAllocationItem item : items) {
            if (item.getBoxNo()==null) {
                continue;
            }
            WhFbaAllocationItem whFbaAllocationItem = map.get(item.getBoxNo());
            if (whFbaAllocationItem!=null) {
                continue;
            }
            item.setFbaNo(fbaNo);
            map.put(item.getBoxNo(), item);
        }
        return new ArrayList<>(map.values());
    }
}