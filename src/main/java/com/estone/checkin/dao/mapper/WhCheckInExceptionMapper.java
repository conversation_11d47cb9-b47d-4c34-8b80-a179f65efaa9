package com.estone.checkin.dao.mapper;

import com.estone.checkin.bean.WhCheckInException;
import com.estone.checkin.bean.WhCheckInExceptionQueryCondition;
import com.estone.checkin.enums.PurchaseOrderFlags;
import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class WhCheckInExceptionMapper implements RowMapper<WhCheckInException> {

    private WhCheckInExceptionQueryCondition query;

    public WhCheckInExceptionMapper(){

    }

    public WhCheckInExceptionMapper(WhCheckInExceptionQueryCondition query){
        this.query = query;
    }

    public WhCheckInException mapRow(ResultSet rs, int rowNum) throws SQLException {
        if (query != null && query.getIsNew() != null && query.getIsNew()){
            return getNewMapRow(rs,"wce.","wcb.");
        }else{
            return getMapRow(rs,rowNum);
        }
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public WhCheckInException getMapRow(ResultSet rs, int rowNum) throws SQLException {
        WhCheckInException entity = new WhCheckInException();
        entity.setId(rs.getObject(WhCheckInExceptionDBField.ID) == null ? null : rs.getInt(WhCheckInExceptionDBField.ID));
        entity.setInId(rs.getObject(WhCheckInExceptionDBField.IN_ID) == null ? null : rs.getInt(WhCheckInExceptionDBField.IN_ID));
        entity.setPurchaseOrderNo(rs.getString(WhCheckInExceptionDBField.PURCHASE_ORDER_NO));
        entity.setNewPurchaseOrderNo(rs.getString(WhCheckInExceptionDBField.NEW_PURCHASE_ORDER_NO));
        entity.setTrackingNumber(rs.getString(WhCheckInExceptionDBField.TRACKING_NUMBER));
        entity.setCreatedBy(rs.getObject(WhCheckInExceptionDBField.CREATED_BY) == null ? null : rs.getInt(WhCheckInExceptionDBField.CREATED_BY));
        entity.setCreationDate(rs.getTimestamp(WhCheckInExceptionDBField.CREATION_DATE));
        entity.setExceptionType(rs.getString(WhCheckInExceptionDBField.EXCEPTION_TYPE));
        entity.setStatus(rs.getObject(WhCheckInExceptionDBField.STATUS) == null ? null : rs.getInt(WhCheckInExceptionDBField.STATUS));
        entity.setHandleWay(rs.getObject(WhCheckInExceptionDBField.HANDLE_WAY) == null ? null : rs.getInt(WhCheckInExceptionDBField.HANDLE_WAY));
        entity.setExceptionUser(rs.getObject(WhCheckInExceptionDBField.EXCEPTION_USER) == null ? null : rs.getInt(WhCheckInExceptionDBField.EXCEPTION_USER));
        entity.setBoxNo(rs.getString(WhCheckInExceptionDBField.BOX_NO));
        entity.setSku(rs.getString(WhCheckInExceptionDBField.SKU));
        entity.setImage(rs.getString(WhCheckInExceptionDBField.IMAGE));
        entity.setQuantity(rs.getObject(WhCheckInExceptionDBField.QUANTITY) == null ? null : rs.getInt(WhCheckInExceptionDBField.QUANTITY));
        entity.setPurchaseUser(rs.getObject(WhCheckInExceptionDBField.PURCHASE_USER) == null ? null : rs.getInt(WhCheckInExceptionDBField.PURCHASE_USER));
        entity.setConfirmQuantity(rs.getObject(WhCheckInExceptionDBField.CONFIRM_QUANTITY) == null ? null : rs.getInt(WhCheckInExceptionDBField.CONFIRM_QUANTITY));
        entity.setHandledQuantity(rs.getObject(WhCheckInExceptionDBField.HANDLED_QUANTITY) == null ? null : rs.getInt(WhCheckInExceptionDBField.HANDLED_QUANTITY));
        entity.setExceptionComment(rs.getString(WhCheckInExceptionDBField.EXCEPTION_COMMENT));
        entity.setLastUpdateUser(rs.getObject(WhCheckInExceptionDBField.LAST_UPDATE_USER) == null ? null : rs.getInt(WhCheckInExceptionDBField.LAST_UPDATE_USER));
        entity.setLastUpdateDate(rs.getTimestamp(WhCheckInExceptionDBField.LAST_UPDATE_DATE));
        entity.setWarehouseId(rs.getObject(WhCheckInExceptionDBField.WAREHOUSE_ID) == null ? null : rs.getInt(WhCheckInExceptionDBField.WAREHOUSE_ID));
        entity.setExceptionFrom(rs.getObject(WhCheckInExceptionDBField.EXCEPTION_FORM) == null ? null : rs.getInt(WhCheckInExceptionDBField.EXCEPTION_FORM));
        entity.setIsCarryProduct(rs.getObject(WhCheckInExceptionDBField.IS_CARRY_PRODUCT) == null ? null : rs.getBoolean(WhCheckInExceptionDBField.IS_CARRY_PRODUCT));
        entity.setCarryQuantity(rs.getObject(WhCheckInExceptionDBField.CARRY_QUANTITY) == null ? null : rs.getInt(WhCheckInExceptionDBField.CARRY_QUANTITY));
        entity.setReturnInformationJson(rs.getString(WhCheckInExceptionDBField.RETURN_INFORMATION_JSON));
        entity.setFinishDate(rs.getTimestamp(WhCheckInExceptionDBField.FINISH_DATE));
        entity.setDiscardedDate(rs.getTimestamp(WhCheckInExceptionDBField.DISCARDED_DATE));
        entity.setFinishUser(rs.getInt(WhCheckInExceptionDBField.FINISH_USER));
        entity.setDiscardedUser(rs.getInt(WhCheckInExceptionDBField.DISCARDED_USER));
        entity.setExceptionHandleDate(rs.getTimestamp(WhCheckInExceptionDBField.EXCEPTION_HANDLE_DATE));
        entity.setPurchaseHandleDate(rs.getTimestamp(WhCheckInExceptionDBField.PURCHASE_HANDLE_DATE));
        entity.setReceiveBoxNo(rs.getString(WhCheckInExceptionDBField.RECEIVE_BOX_NO));
        entity.setCheckInUser(rs.getObject(WhCheckInExceptionDBField.CHECK_IN_USER) == null ? null : rs.getInt(WhCheckInExceptionDBField.CHECK_IN_USER));
        entity.setFirstOrderType(rs.getObject(WhCheckInExceptionDBField.FIRST_ORDER_TYPE) == null ? 0 : rs.getInt(WhCheckInExceptionDBField.FIRST_ORDER_TYPE));
        entity.setExTimes(rs.getObject(WhCheckInExceptionDBField.EX_TIMES) == null ? null : rs.getInt(WhCheckInExceptionDBField.EX_TIMES));
        entity.setLocationNumber(rs.getString(WhCheckInExceptionDBField.LOCATION_NUMBER));
        entity.setWaitCheckInDate(rs.getTimestamp(WhCheckInExceptionDBField.WAIT_CHECK_IN_DATE));
        entity.setDoingCheckInDate(rs.getTimestamp(WhCheckInExceptionDBField.DOING_CHECK_IN_DATE));
        entity.setExceptionHandled(rs.getBoolean(WhCheckInExceptionDBField.EXCEPTION_HANDLED));
        entity.setTags(rs.getString(WhCheckInExceptionDBField.TAGS));
        entity.setNextGenerationExceptionIds(rs.getString(WhCheckInExceptionDBField.NEXT_GENERATION_EXCEPTION_IDS));
        entity.setWmsHandleTimes(rs.getObject("wmsHandleTimes") == null ? null : rs.getInt("wmsHandleTimes"));
        entity.setOrderFlag(rs.getString("flags"));
        entity.setMarkReason(rs.getString(WhCheckInExceptionDBField.MARK_REASON));
        entity.setMarkTime(rs.getTimestamp(WhCheckInExceptionDBField.MARK_TIME));
        entity.setMarkUserId(rs.getObject(WhCheckInExceptionDBField.MARK_USER_ID) == null ? null : rs.getInt(WhCheckInExceptionDBField.MARK_USER_ID));
        return entity;
    }

    public WhCheckInException getNewMapRow(ResultSet rs, String prefix,String prefix2) throws SQLException {
        WhCheckInException entity = new WhCheckInException();
        entity.setId(rs.getObject(prefix+WhCheckInExceptionDBField.ID) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.ID));
        entity.setInId(rs.getObject(prefix+WhCheckInExceptionDBField.IN_ID) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.IN_ID));
        entity.setPurchaseOrderNo(rs.getString(prefix+WhCheckInExceptionDBField.PURCHASE_ORDER_NO));
        entity.setNewPurchaseOrderNo(rs.getString(prefix+WhCheckInExceptionDBField.NEW_PURCHASE_ORDER_NO));
        entity.setTrackingNumber(rs.getString(prefix+WhCheckInExceptionDBField.TRACKING_NUMBER));
        entity.setCreatedBy(rs.getObject(prefix+WhCheckInExceptionDBField.CREATED_BY) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.CREATED_BY));
        entity.setCreationDate(rs.getTimestamp(prefix+WhCheckInExceptionDBField.CREATION_DATE));
        entity.setExceptionType(rs.getString(prefix+WhCheckInExceptionDBField.EXCEPTION_TYPE));
        entity.setStatus(rs.getObject(prefix+WhCheckInExceptionDBField.STATUS) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.STATUS));
        entity.setHandleWay(rs.getObject(prefix+WhCheckInExceptionDBField.HANDLE_WAY) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.HANDLE_WAY));
        entity.setExceptionUser(rs.getObject(prefix+WhCheckInExceptionDBField.EXCEPTION_USER) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.EXCEPTION_USER));
        entity.setBoxNo(rs.getString(prefix+WhCheckInExceptionDBField.BOX_NO));
        entity.setSku(rs.getString(prefix+WhCheckInExceptionDBField.SKU));
        entity.setImage(rs.getString(prefix+WhCheckInExceptionDBField.IMAGE));
        entity.setQuantity(rs.getObject(prefix+WhCheckInExceptionDBField.QUANTITY) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.QUANTITY));
        entity.setPurchaseUser(rs.getObject(prefix+WhCheckInExceptionDBField.PURCHASE_USER) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.PURCHASE_USER));
        entity.setConfirmQuantity(rs.getObject(prefix+WhCheckInExceptionDBField.CONFIRM_QUANTITY) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.CONFIRM_QUANTITY));
        entity.setHandledQuantity(rs.getObject(prefix+WhCheckInExceptionDBField.HANDLED_QUANTITY) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.HANDLED_QUANTITY));
        entity.setExceptionComment(rs.getString(prefix+WhCheckInExceptionDBField.EXCEPTION_COMMENT));
        entity.setLastUpdateUser(rs.getObject(prefix+WhCheckInExceptionDBField.LAST_UPDATE_USER) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.LAST_UPDATE_USER));
        entity.setLastUpdateDate(rs.getTimestamp(prefix+WhCheckInExceptionDBField.LAST_UPDATE_DATE));
        entity.setWarehouseId(rs.getObject(prefix+WhCheckInExceptionDBField.WAREHOUSE_ID) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.WAREHOUSE_ID));
        entity.setExceptionFrom(rs.getObject(prefix+WhCheckInExceptionDBField.EXCEPTION_FORM) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.EXCEPTION_FORM));
        entity.setIsCarryProduct(rs.getObject(prefix+WhCheckInExceptionDBField.IS_CARRY_PRODUCT) == null ? null : rs.getBoolean(prefix+WhCheckInExceptionDBField.IS_CARRY_PRODUCT));
        entity.setCarryQuantity(rs.getObject(prefix+WhCheckInExceptionDBField.CARRY_QUANTITY) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.CARRY_QUANTITY));
        entity.setReturnInformationJson(rs.getString(prefix+WhCheckInExceptionDBField.RETURN_INFORMATION_JSON));
        entity.setFinishDate(rs.getTimestamp(prefix+WhCheckInExceptionDBField.FINISH_DATE));
        entity.setDiscardedDate(rs.getTimestamp(prefix+WhCheckInExceptionDBField.DISCARDED_DATE));
        entity.setFinishUser(rs.getInt(prefix+WhCheckInExceptionDBField.FINISH_USER));
        entity.setDiscardedUser(rs.getInt(prefix+WhCheckInExceptionDBField.DISCARDED_USER));
        entity.setExceptionHandleDate(rs.getTimestamp(prefix+WhCheckInExceptionDBField.EXCEPTION_HANDLE_DATE));
        entity.setPurchaseHandleDate(rs.getTimestamp(prefix+WhCheckInExceptionDBField.PURCHASE_HANDLE_DATE));
        entity.setReceiveBoxNo(rs.getString(prefix+WhCheckInExceptionDBField.RECEIVE_BOX_NO));
        entity.setCheckInUser(rs.getObject(prefix+WhCheckInExceptionDBField.CHECK_IN_USER) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.CHECK_IN_USER));
        entity.setFirstOrderType(rs.getObject(prefix+WhCheckInExceptionDBField.FIRST_ORDER_TYPE) == null ? 0 : rs.getInt(prefix+WhCheckInExceptionDBField.FIRST_ORDER_TYPE));
        entity.setExTimes(rs.getObject(prefix+WhCheckInExceptionDBField.EX_TIMES) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.EX_TIMES));
        entity.setBatchNo(rs.getString(prefix2+WhCheckInExceBatchDBField.BATCH_NO));
        entity.setLocationNumber(rs.getString(WhCheckInExceptionDBField.LOCATION_NUMBER));
        entity.setWaitCheckInDate(rs.getTimestamp(WhCheckInExceptionDBField.WAIT_CHECK_IN_DATE));
        entity.setDoingCheckInDate(rs.getTimestamp(prefix+WhCheckInExceptionDBField.DOING_CHECK_IN_DATE));
        entity.setWmsHandleTimes(rs.getObject("wmsHandleTimes") == null ? null : rs.getInt("wmsHandleTimes"));
        entity.setExceptionHandled(rs.getBoolean(prefix+WhCheckInExceptionDBField.EXCEPTION_HANDLED));
        entity.setTags(rs.getString(WhCheckInExceptionDBField.TAGS));
        entity.setOrderFlag(rs.getString("flags"));

        if (PurchaseOrderFlags.ORDINARY.getCode().equals(query.getOrderFlag())) {
            String orderFlag = StringUtils.isBlank(entity.getOrderFlag())?"2":entity.getOrderFlag();
            entity.setOrderFlag(orderFlag);
        }

        entity.setNextGenerationExceptionIds(rs.getString(prefix+WhCheckInExceptionDBField.NEXT_GENERATION_EXCEPTION_IDS));
        if (query != null && query.getQueryLocationStock() != null && query.getQueryLocationStock()) {
            Integer locationQty = rs.getObject("locationQty") == null ? 0 : rs.getInt("locationQty");
            Integer tfLocationQty = rs.getObject("tfLocationQty") == null ? 0 : rs.getInt("tfLocationQty");
            entity.setSkuLocationQty(locationQty + tfLocationQty);
        }
        entity.setMarkReason(rs.getString(prefix+WhCheckInExceptionDBField.MARK_REASON));
        entity.setMarkTime(rs.getTimestamp(prefix+WhCheckInExceptionDBField.MARK_TIME));
        entity.setMarkUserId(rs.getObject(prefix+WhCheckInExceptionDBField.MARK_USER_ID) == null ? null : rs.getInt(prefix+WhCheckInExceptionDBField.MARK_USER_ID));
        return entity;
    }
}