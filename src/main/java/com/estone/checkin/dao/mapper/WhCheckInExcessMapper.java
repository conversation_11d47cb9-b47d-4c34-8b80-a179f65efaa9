package com.estone.checkin.dao.mapper;

import com.estone.checkin.bean.WhCheckInExcess;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.springframework.jdbc.core.RowMapper;

public class WhCheckInExcessMapper implements Row<PERSON>apper<WhCheckInExcess> {

    public WhCheckInExcess mapRow(ResultSet rs, int rowNum) throws SQLException {
        WhCheckInExcess entity = new WhCheckInExcess();
        entity.setId(rs.getObject(WhCheckInExcessDBField.ID) == null ? null : rs.getInt(WhCheckInExcessDBField.ID));
        entity.setInId(rs.getObject(WhCheckInExcessDBField.IN_ID) == null ? null : rs.getInt(WhCheckInExcessDBField.IN_ID));
        entity.setSubId(rs.getString(WhCheckInExcessDBField.SUB_ID));
        entity.setSku(rs.getString(WhCheckInExcessDBField.SKU));
        entity.setExcessQuantity(rs.getObject(WhCheckInExcessDBField.EXCESS_QUANTITY) == null ? null : rs.getInt(WhCheckInExcessDBField.EXCESS_QUANTITY));
        entity.setPurchaseOrderNo(rs.getString(WhCheckInExcessDBField.PURCHASE_ORDER_NO));
        entity.setPurchaseUser(rs.getString(WhCheckInExcessDBField.PURCHASE_USER));
        entity.setPurchaseQuantity(rs.getObject(WhCheckInExcessDBField.PURCHASE_QUANTITY) == null ? null : rs.getInt(WhCheckInExcessDBField.PURCHASE_QUANTITY));
        entity.setMatchedQuantity(rs.getObject(WhCheckInExcessDBField.MATCHED_QUANTITY) == null ? null : rs.getInt(WhCheckInExcessDBField.MATCHED_QUANTITY));
        entity.setDifferenceQuantity(rs.getObject(WhCheckInExcessDBField.DIFFERENCE_QUANTITY) == null ? null : rs.getInt(WhCheckInExcessDBField.DIFFERENCE_QUANTITY));
        entity.setProcessingMethod(rs.getString(WhCheckInExcessDBField.PROCESSING_METHOD));
        entity.setOperation(rs.getString(WhCheckInExcessDBField.OPERATION));
        entity.setCreateTime(rs.getTimestamp(WhCheckInExcessDBField.CREATE_TIME));
        entity.setUpdateTime(rs.getTimestamp(WhCheckInExcessDBField.UPDATE_TIME));
        entity.setQcQuantity(rs.getObject(WhCheckInExcessDBField.QC_QUANTITY) == null ? null : rs.getInt(WhCheckInExcessDBField.QC_QUANTITY));
        entity.setUpQuantity(rs.getObject(WhCheckInExcessDBField.UP_QUANTITY) == null ? null : rs.getInt(WhCheckInExcessDBField.UP_QUANTITY));
        entity.setTransitionUpQuantity(rs.getObject(WhCheckInExcessDBField.TRANSITION_UP_QUANTITY) == null ? null : rs.getInt(WhCheckInExcessDBField.TRANSITION_UP_QUANTITY));
        entity.setWeight(rs.getObject(WhCheckInExcessDBField.WEIGHT) == null ? null : rs.getDouble(WhCheckInExcessDBField.WEIGHT));
        entity.setShippingCost(rs.getObject(WhCheckInExcessDBField.SHIPPING_COST) == null ? null : rs.getBigDecimal(WhCheckInExcessDBField.SHIPPING_COST));
        entity.setTrackingNumber(rs.getString(WhCheckInExcessDBField.TRACKING_NUMBER));
        entity.setSupplierId(rs.getString(WhCheckInExcessDBField.SUPPLIER_ID));
        entity.setQcNum(rs.getObject(WhCheckInExcessDBField.QC_NUM) == null ? null : rs.getInt(WhCheckInExcessDBField.QC_NUM));
        entity.setVendorName(rs.getString(WhCheckInExcessDBField.VENDOR_NAME));
        return entity;
    }
}