package com.estone.checkin.dao.mapper;

public interface WhCheckInExceptionDBField {
    String ID = "id";

    String IN_ID = "in_id";

    String PURCHASE_ORDER_NO = "purchase_order_no";

    String NEW_PURCHASE_ORDER_NO = "new_purchase_order_no";

    String TRACKING_NUMBER = "tracking_number";

    String CREATED_BY = "created_by";

    String CREATION_DATE = "creation_date";

    String EXCEPTION_TYPE = "exception_type";

    String STATUS = "status";

    String HANDLE_WAY = "handle_way";

    String EXCEPTION_USER = "exception_user";

    String BOX_NO = "box_no";

    String SKU = "sku";

    String IMAGE = "image";

    String QUANTITY = "quantity";

    String PURCHASE_USER = "purchase_user";

    String CONFIRM_QUANTITY = "confirm_quantity";

    String HANDLED_QUANTITY = "handled_quantity";

    String EXCEPTION_COMMENT = "exception_comment";

    String LAST_UPDATE_USER = "last_update_user";

    String LAST_UPDATE_DATE = "last_update_date";

    String WAREHOUSE_ID = "warehouse_id";

    String EXCEPTION_FORM = "exception_form";

    String IS_CARRY_PRODUCT = "is_carry_product";

    String CARRY_QUANTITY = "carry_quantity";

    String RETURN_INFORMATION_JSON = "return_information_json";

    String FINISH_DATE = "finish_date";

    String DISCARDED_DATE = "discarded_date";

    String DISCARDED_USER = "discarded_user";

    String EXCEPTION_HANDLE_DATE = "exception_handle_date";

    String PURCHASE_HANDLE_DATE = "purchase_handle_date";

    String START_QC_HANDLE_DATE = "start_qc_handle_date";

    String COMPLETED_QC_HANDLE_DATE = "completed_qc_handle_date";

    String FINISH_USER = "finish_user";

    String RECEIVE_BOX_NO = "receive_box_no";

    String CHECK_IN_USER = "check_in_user";

    String FIRST_ORDER_TYPE = "first_order_type";

    String EX_TIMES = "ex_times";

    String LOCATION_NUMBER = "location_number";

    String WAIT_CHECK_IN_DATE = "wait_check_in_date";

    String DOING_CHECK_IN_DATE = "doing_check_in_date";

    String EXCEPTION_HANDLED = "exception_handled";

    String TAGS = "tags";

    String ABANDON_REASON = "abandon_reason";

    String NEXT_GENERATION_EXCEPTION_IDS = "next_generation_exception_ids";

    String MARK_REASON = "mark_reason";

    String MARK_TIME = "mark_time";

    String MARK_USER_ID = "mark_user_id";

}