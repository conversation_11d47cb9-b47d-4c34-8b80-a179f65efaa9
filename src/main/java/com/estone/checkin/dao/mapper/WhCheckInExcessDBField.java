package com.estone.checkin.dao.mapper;

public interface WhCheckInExcessDBField {
    String ID = "id";

    String IN_ID = "in_id";

    String SUB_ID = "sub_id";

    String SKU = "sku";

    String EXCESS_QUANTITY = "excess_quantity";

    String PURCHASE_ORDER_NO = "purchase_order_no";

    String PURCHASE_USER = "purchase_user";

    String PURCHASE_QUANTITY = "purchase_quantity";

    String MATCHED_QUANTITY = "matched_quantity";

    String PROCESSING_METHOD = "processing_method";

    String OPERATION = "operation";

    String CREATE_TIME = "create_time";

    String UPDATE_TIME = "update_time";

    String QC_QUANTITY = "qc_quantity";
    
    String UP_QUANTITY = "up_quantity";
    
    String TRANSITION_UP_QUANTITY = "transition_up_quantity";
    
    String WEIGHT = "weight";
    
    String SHIPPING_COST = "shipping_cost";

    String TRACKING_NUMBER = "tracking_number";
    
    String SUPPLIER_ID = "supplier_id";

    String QC_NUM = "qc_num";
    
    String VENDOR_NAME = "vendor_name";
    
    String DIFFERENCE_QUANTITY = "difference_quantity";
}