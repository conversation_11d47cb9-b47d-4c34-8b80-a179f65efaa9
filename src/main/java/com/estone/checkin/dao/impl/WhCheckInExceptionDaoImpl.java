package com.estone.checkin.dao.impl;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.checkin.bean.HistoryCheckInExceptionCount;
import com.estone.checkin.bean.WhCheckInException;
import com.estone.checkin.bean.WhCheckInExceptionQueryCondition;
import com.estone.checkin.dao.WhCheckInExceptionDao;
import com.estone.checkin.dao.mapper.HistoryCheckInExceptionCountMapper;
import com.estone.checkin.dao.mapper.WhCheckInExceptionDBField;
import com.estone.checkin.dao.mapper.WhCheckInExceptionMapper;
import com.estone.checkin.enums.ExceptionStatus;
import com.estone.checkin.enums.PurchaseOrderFlags;
import com.estone.common.util.CommonUtils;
import com.estone.common.util.SqlerTemplate;
import com.estone.sku.enums.SpecialTypeEnums;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("whCheckInExceptionDao")
public class WhCheckInExceptionDaoImpl implements WhCheckInExceptionDao {

    private void setQueryCondition(SqlerRequest request, WhCheckInExceptionQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        request.addDataParam(WhCheckInExceptionDBField.ID, DataType.INT, query.getId());
        request.addDataParam(WhCheckInExceptionDBField.IN_ID, DataType.INT, query.getInId());
        request.addDataParam(WhCheckInExceptionDBField.STATUS, DataType.INT, query.getStatus());

        request.addDataParam(WhCheckInExceptionDBField.PURCHASE_ORDER_NO, DataType.STRING, query.getPurchaseOrderNo());
        request.addDataParam(WhCheckInExceptionDBField.NEW_PURCHASE_ORDER_NO, DataType.STRING,
                query.getNewPurchaseOrderNo());
        request.addDataParam(WhCheckInExceptionDBField.HANDLE_WAY, DataType.INT, query.getHandleWay());
        request.addDataParam(WhCheckInExceptionDBField.TRACKING_NUMBER, DataType.STRING, query.getTrackingNumber());
        request.addDataParam(WhCheckInExceptionDBField.CREATED_BY, DataType.INT, query.getCreatedBy());
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_TYPE, DataType.STRING, query.getExceptionType());
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_USER, DataType.INT, query.getExceptionUser());
        request.addDataParam(WhCheckInExceptionDBField.DISCARDED_USER, DataType.INT, query.getDiscardedUser());
        request.addDataParam(WhCheckInExceptionDBField.FINISH_USER, DataType.INT, query.getFinishUser());
        request.addDataParam(WhCheckInExceptionDBField.BOX_NO, DataType.STRING, query.getBoxNo());
        request.addDataParam(WhCheckInExceptionDBField.SKU, DataType.STRING, query.getSku());
        request.addDataParam(WhCheckInExceptionDBField.LOCATION_NUMBER, DataType.STRING, query.getLocationNumber());
        request.addDataParam(WhCheckInExceptionDBField.PURCHASE_USER, DataType.INT, query.getPurchaseUser());
        request.addDataParam(WhCheckInExceptionDBField.CONFIRM_QUANTITY, DataType.INT, query.getConfirmQuantity());
        request.addDataParam(WhCheckInExceptionDBField.WAREHOUSE_ID, DataType.INT, query.getWarehouseId());
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_FORM, DataType.INT, query.getExceptionFrom());
        request.addDataParam(WhCheckInExceptionDBField.IS_CARRY_PRODUCT, DataType.BOOLEAN, query.getIsCarryProduct());
        request.addDataParam(WhCheckInExceptionDBField.CARRY_QUANTITY, DataType.INT, query.getCarryQuantity());
        request.addDataParam(WhCheckInExceptionDBField.FIRST_ORDER_TYPE, DataType.INT, query.getFirstOrderType());
        request.addDataParam(WhCheckInExceptionDBField.EX_TIMES, DataType.INT, query.getExTimes());
        request.addDataParam(WhCheckInExceptionDBField.RECEIVE_BOX_NO, DataType.STRING, query.getReceiveBoxNo());
        // 入库时间查询
        request.addDataParam("from_create_date", DataType.STRING, query.getFromCreateDate());
        request.addDataParam("to_create_date", DataType.STRING, query.getToCreateDate());

        // 异常处理时间查询
        request.addDataParam("from_exception_handle_date", DataType.STRING, query.getFromExceptionHandleDate());
        request.addDataParam("to_exception_handle_date", DataType.STRING, query.getToExceptionHandleDate());

        // 采购处理时间查询
        request.addDataParam("from_purchase_handle_date", DataType.STRING, query.getFromPurchaseHandleDate());
        request.addDataParam("to_purchase_handle_date", DataType.STRING, query.getToPurchaseHandleDate());

        // 完成时间查询
        request.addDataParam("from_finish_date", DataType.STRING, query.getFromFinishDate());
        request.addDataParam("to_finish_date", DataType.STRING, query.getToFinishDate());

        // 待入库时间查询
        request.addDataParam("from_wait_check_in_date", DataType.STRING, query.getFromWaitCheckInDate());
        request.addDataParam("to_wait_check_in_date", DataType.STRING, query.getToWaitCheckInDate());

        // 入库中时间查询
        request.addDataParam("from_doing_check_in_date", DataType.STRING, query.getFromDoingCheckInDate());
        request.addDataParam("to_doing_check_in_date", DataType.STRING, query.getToDoingCheckInDate());

        // 开始待质控处理时间查询
        request.addDataParam("from_start_qc_handle_date", DataType.STRING, query.getFromStartQCHandleDate());
        request.addDataParam("to_start_qc_handle_date", DataType.STRING, query.getToStartQCHandleDate());

        // 完成待质控处理时间查询
        request.addDataParam("from_completed_qc_handle_date", DataType.STRING, query.getFromCompletedQCHandleDate());
        request.addDataParam("to_completed_qc_handle_date", DataType.STRING, query.getToCompletedQCHandleDate());

        // 是否经历过待开发处理或者待质控处理的异常单
        if (Objects.nonNull(query.getExceptionHandled())) {
            request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_HANDLED, DataType.BOOLEAN, query.getExceptionHandled());
        }
        // 批量条件查询
        List<String> purchaseOrderNos = query.getPurchaseOrderNos();
        if (CollectionUtils.isNotEmpty(purchaseOrderNos)) {
            request.addDataParam("purchaseOrderNos", DataType.STRING, purchaseOrderNos);
        }
        if (CollectionUtils.isNotEmpty(query.getTrackingNos())) {
            request.addDataParam("trackingNos", DataType.STRING, query.getTrackingNos());
        }

        // 批量条件查询
        List<String> exceptionTypes = query.getExceptionTypes();
        if (CollectionUtils.isNotEmpty(exceptionTypes)) {
            request.addDataParam("exceptionTypes", DataType.STRING, exceptionTypes);
        }

        List<Integer> exceptionFroms = query.getExceptionFroms();
        if (CollectionUtils.isNotEmpty(exceptionFroms)) {
            request.addDataParam("exceptionFroms", DataType.INT, exceptionFroms);
        }
        List<Integer> statusList = query.getStatusList();
        if (CollectionUtils.isNotEmpty(statusList)) {
            request.addDataParam("statusList", DataType.INT, statusList);
            if (statusList.size() == 1 && ExceptionStatus.getWarehousePendingCode().contains(statusList.get(0))) {
                query.setOrderBy("ORDER BY purchase_handle_date ASC");
            }
        }
        List<Integer> handleWays = query.getHandleWays();
        if (CollectionUtils.isNotEmpty(handleWays)) {
            request.addDataParam("handleWays", DataType.INT, handleWays);
        }
        if (CollectionUtils.isNotEmpty(query.getIds())) {
            request.addDataParam("ids", DataType.INT, query.getIds());
        }
        if (CollectionUtils.isNotEmpty(query.getInIds())) {
            request.addDataParam("inIds", DataType.INT, query.getInIds());
        }
        if (CollectionUtils.isNotEmpty(query.getPurchaseUserList())) {
            request.addDataParam("purchaseUserList", DataType.INT, query.getPurchaseUserList());
        }
        if (StringUtils.isNotBlank(query.getBatchNo())) {
            request.addDataParam("batchNo", DataType.STRING, query.getBatchNo());
        }

        if (CollectionUtils.isNotEmpty(query.getSkuList())) {
            request.addDataParam("skuList", DataType.STRING, query.getSkuList());
        }
        if (CollectionUtils.isNotEmpty(query.getExclusiveFirstOrderTypes())) {
            request.addDataParam("exclusiveFirstOrderTypeList", DataType.INT, query.getExclusiveFirstOrderTypes());
        }

        if (StringUtils.isNotBlank(query.getTags())) {
            request.addSqlDataParam("QUERY_TAGS_COLUMN", ",CONCAT_WS(',',wce.tags,sg.special_type) as tags");
            String JOIN_TYPE = "LEFT ";
            if (!StringUtils.equalsIgnoreCase(SpecialTypeEnums.FZ.getCode(),query.getTags())
                    && !StringUtils.equalsIgnoreCase(query.getTags(),"all")) {
                request.addSqlDataParam("TAG_FILTER_SQL", "AND INSTR(tags , '" + query.getTags() + "' ) > 0");
            } else if (StringUtils.equalsIgnoreCase(SpecialTypeEnums.FZ.getCode(),query.getTags())) {
                 JOIN_TYPE = "INNER ";
            }
            String special_type_condition = " AND sg.special_type = " + SpecialTypeEnums.FZ.getCode();
            request.addSqlDataParam("joinSpecialType", JOIN_TYPE+" JOIN wh_sku_special_goods sg ON wce.sku = sg.special_sku "+special_type_condition);
            //request.addSqlDataParam("joinSpecialTypeSub", "LEFT JOIN wh_sku_special_goods sg ON wce2.sku = sg.special_sku " + special_type_condition);
        } else {
            request.addSqlDataParam("QUERY_TAGS_COLUMN", ", wce.tags as tags");
        }
        if (StringUtils.isNotBlank(query.getSpu())) {
            if (StringUtils.contains(query.getSpu(), ",")) {
                List<String> list = CommonUtils.splitList(query.getSpu(), ",");
                request.addDataParam("spuList", DataType.STRING, list);
            } else {
                request.addDataParam("spu", DataType.STRING, query.getSpu());
            }
        }

        if (StringUtils.isNotBlank(query.getOrderFlag())) {
            if (PurchaseOrderFlags.ORDINARY.getCode().equals(query.getOrderFlag())) {
                request.addSqlDataParam("ORDER_FLAG_SQL", "AND (FIND_IN_SET('2',p.flags) or p.flags IS NULL OR p.flags='' )");
            }else{
                request.addDataParam("orderFlag", DataType.STRING, query.getOrderFlag());
            }
        }

        // 标记查询条件
        if (StringUtils.isNotBlank(query.getMarkStatus())) {
            if ("marked".equals(query.getMarkStatus())) {
                request.addSqlDataParam("MARK_STATUS_FILTER_SQL", "AND wce.mark_reason IS NOT NULL AND wce.mark_reason != ''");
            } else if ("unmarked".equals(query.getMarkStatus())) {
                request.addSqlDataParam("MARK_STATUS_FILTER_SQL", "AND (wce.mark_reason IS NULL OR wce.mark_reason = '')");
            }
        }

        if (StringUtils.isNotBlank(query.getMarkReason())) {
            request.addDataParam(WhCheckInExceptionDBField.MARK_REASON, DataType.STRING, query.getMarkReason());
        }

        // 标记时间查询
        request.addDataParam("from_mark_time", DataType.STRING, query.getFromMarkTime());
        request.addDataParam("to_mark_time", DataType.STRING, query.getToMarkTime());

        if (query.getMarkUserId() != null) {
            request.addDataParam(WhCheckInExceptionDBField.MARK_USER_ID, DataType.INT, query.getMarkUserId());
        }

        request.addSqlDataParam("orderBy", query.getOrderBy());
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public int queryWhCheckInExceptionCount(WhCheckInExceptionQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhCheckInExceptionCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public int queryNewWhCheckInExceptionCount(WhCheckInExceptionQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryNewWhCheckInExceptionCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public List<WhCheckInException> queryWhCheckInExceptionList() {
        SqlerRequest request = new SqlerRequest("queryWhCheckInExceptionList");
        return SqlerTemplate.query(request, new WhCheckInExceptionMapper());
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public List<WhCheckInException> queryWhCheckInExceptionList(WhCheckInExceptionQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhCheckInExceptionList");
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhCheckInExceptionMapper());
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public List<WhCheckInException> queryNewWhCheckInExceptionList(WhCheckInExceptionQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryNewWhCheckInExceptionList");
        setQueryCondition(request, query);
        if (pager != null || query.getPdaQueryLocationStock()!=null && query.getPdaQueryLocationStock()) {
            String sql = "";
            if (query.getQueryLocationStock() != null && query.getQueryLocationStock()) {
                sql = ", (SELECT SUM(IFNULL(surplus_quantity,0))+SUM(IFNULL(allot_quantity,0)) FROM wh_stock WHERE sku = wce.sku) AS locationQty"
                        + ", (SELECT SUM(IFNULL(surplus_quantity,0))+SUM(IFNULL(allot_quantity,0)) FROM wh_transfer_stock WHERE sku = wce.sku) AS tfLocationQty";
            }
            request.addSqlDataParam("QUERY_LOCATION_STOCK", sql);
            if (pager!=null){
                request.addFetch(pager.getPageNo(), pager.getPageSize());
//                request.addSqlDataParam("groupByAndLimit", "group by wcb2.batch_no ORDER BY wce2.creation_date DESC LIMIT "
//                        + ((pager.getPageNo() - 1) * pager.getPageSize()) + ", " + pager.getPageSize());
            }

        } else {
//            request.addSqlDataParam("groupByAndLimit", "group by wcb2.batch_no ORDER BY wce2.creation_date DESC ");
        }
        return SqlerTemplate.query(request, new WhCheckInExceptionMapper(query));
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public WhCheckInException queryWhCheckInException(Integer primaryKey) {
        Assert.notNull(primaryKey);
        SqlerRequest request = new SqlerRequest("queryWhCheckInExceptionByPrimaryKey");
        request.addDataParam(WhCheckInExceptionDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhCheckInExceptionMapper());
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public WhCheckInException queryWhCheckInException(WhCheckInExceptionQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhCheckInException");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhCheckInExceptionMapper());
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public void createWhCheckInException(WhCheckInException entity) {
        SqlerRequest request = new SqlerRequest("createWhCheckInException");
        request.addDataParam(WhCheckInExceptionDBField.IN_ID, DataType.INT, entity.getInId());
        request.addDataParam(WhCheckInExceptionDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
        request.addDataParam(WhCheckInExceptionDBField.NEW_PURCHASE_ORDER_NO, DataType.STRING,
                entity.getNewPurchaseOrderNo());
        request.addDataParam(WhCheckInExceptionDBField.TRACKING_NUMBER, DataType.STRING, entity.getTrackingNumber());
        request.addDataParam(WhCheckInExceptionDBField.CREATED_BY, DataType.INT,
                entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
        request.addDataParam(WhCheckInExceptionDBField.CREATION_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_TYPE, DataType.STRING, entity.getExceptionType());
        request.addDataParam(WhCheckInExceptionDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(WhCheckInExceptionDBField.HANDLE_WAY, DataType.INT, entity.getHandleWay());
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_USER, DataType.INT, entity.getExceptionUser());
        request.addDataParam(WhCheckInExceptionDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
        request.addDataParam(WhCheckInExceptionDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(WhCheckInExceptionDBField.LOCATION_NUMBER, DataType.STRING, entity.getLocationNumber());
        request.addDataParam(WhCheckInExceptionDBField.IMAGE, DataType.STRING, entity.getImage());
        request.addDataParam(WhCheckInExceptionDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(WhCheckInExceptionDBField.PURCHASE_USER, DataType.INT, entity.getPurchaseUser());
        request.addDataParam(WhCheckInExceptionDBField.CONFIRM_QUANTITY, DataType.INT, entity.getConfirmQuantity());
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_COMMENT, DataType.STRING,
                entity.getExceptionComment());
        request.addDataParam(WhCheckInExceptionDBField.LAST_UPDATE_USER, DataType.INT, entity.getLastUpdateUser());
        request.addDataParam(WhCheckInExceptionDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhCheckInExceptionDBField.WAREHOUSE_ID, DataType.INT, entity.getWarehouseId());
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_FORM, DataType.INT, entity.getExceptionFrom());
        request.addDataParam(WhCheckInExceptionDBField.IS_CARRY_PRODUCT, DataType.BOOLEAN, entity.getIsCarryProduct());
        request.addDataParam(WhCheckInExceptionDBField.CARRY_QUANTITY, DataType.INT, entity.getCarryQuantity());
        request.addDataParam(WhCheckInExceptionDBField.RETURN_INFORMATION_JSON, DataType.STRING,
                entity.getReturnInformationJson());
        request.addDataParam(WhCheckInExceptionDBField.FINISH_DATE, DataType.TIMESTAMP, entity.getFinishDate());
        request.addDataParam(WhCheckInExceptionDBField.DISCARDED_DATE, DataType.TIMESTAMP, entity.getDiscardedDate());
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_HANDLE_DATE, DataType.TIMESTAMP,
                entity.getExceptionHandleDate());
        request.addDataParam(WhCheckInExceptionDBField.PURCHASE_HANDLE_DATE, DataType.TIMESTAMP,
                entity.getPurchaseHandleDate());
        request.addDataParam(WhCheckInExceptionDBField.FINISH_USER, DataType.INT, entity.getFinishUser());
        request.addDataParam(WhCheckInExceptionDBField.DISCARDED_USER, DataType.INT, entity.getDiscardedUser());
        request.addDataParam(WhCheckInExceptionDBField.RECEIVE_BOX_NO, DataType.STRING, entity.getReceiveBoxNo());
        request.addDataParam(WhCheckInExceptionDBField.CHECK_IN_USER, DataType.INT, entity.getCheckInUser());
        //默认0：普通
        request.addDataParam(WhCheckInExceptionDBField.FIRST_ORDER_TYPE, DataType.INT, entity.getFirstOrderType() == null ? 0 : entity.getFirstOrderType());
        request.addDataParam(WhCheckInExceptionDBField.EX_TIMES, DataType.INT, entity.getExTimes() == null ? 1 : entity.getExTimes());
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_HANDLED, DataType.BOOLEAN, entity.judgeExceptionHandledByStatus());
        request.addDataParam(WhCheckInExceptionDBField.TAGS, DataType.STRING, entity.getTags());
        request.addDataParam(WhCheckInExceptionDBField.NEXT_GENERATION_EXCEPTION_IDS, DataType.STRING, entity.getNextGenerationExceptionIds());
        request.addDataParam(WhCheckInExceptionDBField.MARK_REASON, DataType.STRING, entity.getMarkReason());
        request.addDataParam(WhCheckInExceptionDBField.MARK_TIME, DataType.TIMESTAMP, entity.getMarkTime());
        request.addDataParam(WhCheckInExceptionDBField.MARK_USER_ID, DataType.INT, entity.getMarkUserId());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public void updateWhCheckInException(WhCheckInException entity) {
        SqlerRequest request = new SqlerRequest("updateWhCheckInExceptionByPrimaryKey");
        request.addDataParam(WhCheckInExceptionDBField.ID, DataType.INT, entity.getId());

        request.addDataParam(WhCheckInExceptionDBField.IN_ID, DataType.INT, entity.getInId());
        request.addDataParam(WhCheckInExceptionDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
        request.addDataParam(WhCheckInExceptionDBField.NEW_PURCHASE_ORDER_NO, DataType.STRING,
                entity.getNewPurchaseOrderNo());
        request.addDataParam(WhCheckInExceptionDBField.TRACKING_NUMBER, DataType.STRING, entity.getTrackingNumber());

        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_TYPE, DataType.STRING, entity.getExceptionType());
        request.addDataParam(WhCheckInExceptionDBField.STATUS, DataType.INT, entity.getStatus());
        request.addDataParam(WhCheckInExceptionDBField.HANDLE_WAY, DataType.INT, entity.getHandleWay());
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_USER, DataType.INT, entity.getExceptionUser());
        request.addDataParam(WhCheckInExceptionDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
        request.addDataParam(WhCheckInExceptionDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(WhCheckInExceptionDBField.LOCATION_NUMBER, DataType.STRING, entity.getLocationNumber());
        request.addDataParam(WhCheckInExceptionDBField.IMAGE, DataType.STRING, entity.getImage());
        request.addDataParam(WhCheckInExceptionDBField.QUANTITY, DataType.INT, entity.getQuantity());
        request.addDataParam(WhCheckInExceptionDBField.PURCHASE_USER, DataType.INT, entity.getPurchaseUser());
        request.addDataParam(WhCheckInExceptionDBField.CONFIRM_QUANTITY, DataType.INT, entity.getConfirmQuantity());
        request.addDataParam(WhCheckInExceptionDBField.HANDLED_QUANTITY, DataType.INT, entity.getHandledQuantity());
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_COMMENT, DataType.STRING,
                entity.getExceptionComment());
        request.addDataParam(WhCheckInExceptionDBField.LAST_UPDATE_USER, DataType.INT,
                entity.getLastUpdateUser() == null ? DataContextHolder.getUserId() : entity.getLastUpdateUser());
        request.addDataParam(WhCheckInExceptionDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhCheckInExceptionDBField.WAREHOUSE_ID, DataType.INT, entity.getWarehouseId());
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_FORM, DataType.INT, entity.getExceptionFrom());
        request.addDataParam(WhCheckInExceptionDBField.IS_CARRY_PRODUCT, DataType.BOOLEAN, entity.getIsCarryProduct());
        request.addDataParam(WhCheckInExceptionDBField.CARRY_QUANTITY, DataType.INT, entity.getCarryQuantity());
        request.addDataParam(WhCheckInExceptionDBField.RETURN_INFORMATION_JSON, DataType.STRING,
                entity.getReturnInformationJson());
        request.addDataParam(WhCheckInExceptionDBField.FINISH_DATE, DataType.TIMESTAMP, entity.getFinishDate());
        request.addDataParam(WhCheckInExceptionDBField.DISCARDED_DATE, DataType.TIMESTAMP, entity.getDiscardedDate());
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_HANDLE_DATE, DataType.TIMESTAMP,
                entity.getExceptionHandleDate());
        request.addDataParam(WhCheckInExceptionDBField.PURCHASE_HANDLE_DATE, DataType.TIMESTAMP,
                entity.getPurchaseHandleDate());
        request.addDataParam(WhCheckInExceptionDBField.START_QC_HANDLE_DATE, DataType.TIMESTAMP,
                entity.getStartQCHandleDate());
        request.addDataParam(WhCheckInExceptionDBField.COMPLETED_QC_HANDLE_DATE, DataType.TIMESTAMP,
                entity.getCompletedQCHandleDate());
        request.addDataParam(WhCheckInExceptionDBField.FINISH_USER, DataType.INT, entity.getFinishUser());
        request.addDataParam(WhCheckInExceptionDBField.DISCARDED_USER, DataType.INT, entity.getDiscardedUser());
        request.addDataParam(WhCheckInExceptionDBField.FIRST_ORDER_TYPE, DataType.INT, entity.getFirstOrderType());
        request.addDataParam(WhCheckInExceptionDBField.EX_TIMES, DataType.INT, entity.getExTimes());
        request.addDataParam(WhCheckInExceptionDBField.RECEIVE_BOX_NO, DataType.STRING, entity.getReceiveBoxNo());
        request.addDataParam(WhCheckInExceptionDBField.WAIT_CHECK_IN_DATE, DataType.TIMESTAMP, entity.getWaitCheckInDate());
        request.addDataParam(WhCheckInExceptionDBField.DOING_CHECK_IN_DATE, DataType.TIMESTAMP, entity.getDoingCheckInDate());
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_HANDLED, DataType.BOOLEAN, entity.judgeExceptionHandledByStatus());
        request.addDataParam(WhCheckInExceptionDBField.TAGS, DataType.STRING, entity.getTags());
        request.addDataParam(WhCheckInExceptionDBField.ABANDON_REASON, DataType.STRING, entity.getAbandonReason());
        request.addDataParam(WhCheckInExceptionDBField.NEXT_GENERATION_EXCEPTION_IDS, DataType.STRING, entity.getNextGenerationExceptionIds());
        request.addDataParam(WhCheckInExceptionDBField.MARK_REASON, DataType.STRING, entity.getMarkReason());
        request.addDataParam(WhCheckInExceptionDBField.MARK_TIME, DataType.TIMESTAMP, entity.getMarkTime());
        request.addDataParam(WhCheckInExceptionDBField.MARK_USER_ID, DataType.INT, entity.getMarkUserId());
        SqlerTemplate.execute(request);
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public void batchCreateWhCheckInException(List<WhCheckInException> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhCheckInException");
            for (WhCheckInException entity : entityList) {
                request.addBatchDataParam(WhCheckInExceptionDBField.IN_ID, DataType.INT, entity.getInId());
                request.addBatchDataParam(WhCheckInExceptionDBField.PURCHASE_ORDER_NO, DataType.STRING,
                        entity.getPurchaseOrderNo());
                request.addBatchDataParam(WhCheckInExceptionDBField.NEW_PURCHASE_ORDER_NO, DataType.STRING,
                        entity.getNewPurchaseOrderNo());
                request.addBatchDataParam(WhCheckInExceptionDBField.TRACKING_NUMBER, DataType.STRING,
                        entity.getTrackingNumber());
                request.addBatchDataParam(WhCheckInExceptionDBField.CREATED_BY, DataType.INT,
                        entity.getCreatedBy() == null ? DataContextHolder.getUserId() : entity.getCreatedBy());
                request.addBatchDataParam(WhCheckInExceptionDBField.CREATION_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhCheckInExceptionDBField.EXCEPTION_TYPE, DataType.STRING,
                        entity.getExceptionType());
                request.addBatchDataParam(WhCheckInExceptionDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(WhCheckInExceptionDBField.HANDLE_WAY, DataType.INT, entity.getHandleWay());
                request.addBatchDataParam(WhCheckInExceptionDBField.EXCEPTION_USER, DataType.INT,
                        entity.getExceptionUser());
                request.addBatchDataParam(WhCheckInExceptionDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
                request.addBatchDataParam(WhCheckInExceptionDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(WhCheckInExceptionDBField.LOCATION_NUMBER, DataType.STRING, entity.getLocationNumber());
                request.addBatchDataParam(WhCheckInExceptionDBField.IMAGE, DataType.STRING, entity.getImage());
                request.addBatchDataParam(WhCheckInExceptionDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(WhCheckInExceptionDBField.PURCHASE_USER, DataType.INT,
                        entity.getPurchaseUser());
                request.addBatchDataParam(WhCheckInExceptionDBField.CONFIRM_QUANTITY, DataType.INT,
                        entity.getConfirmQuantity());
                request.addBatchDataParam(WhCheckInExceptionDBField.EXCEPTION_COMMENT, DataType.STRING,
                        entity.getExceptionComment());
                request.addBatchDataParam(WhCheckInExceptionDBField.LAST_UPDATE_USER, DataType.INT,
                        entity.getLastUpdateUser());
                request.addBatchDataParam(WhCheckInExceptionDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhCheckInExceptionDBField.WAREHOUSE_ID, DataType.INT,
                        entity.getWarehouseId());
                request.addBatchDataParam(WhCheckInExceptionDBField.EXCEPTION_FORM, DataType.INT,
                        entity.getExceptionFrom());
                request.addBatchDataParam(WhCheckInExceptionDBField.IS_CARRY_PRODUCT, DataType.BOOLEAN,
                        entity.getIsCarryProduct());
                request.addBatchDataParam(WhCheckInExceptionDBField.CARRY_QUANTITY, DataType.INT,
                        entity.getCarryQuantity());
                request.addBatchDataParam(WhCheckInExceptionDBField.RETURN_INFORMATION_JSON, DataType.STRING,
                        entity.getReturnInformationJson());
                request.addBatchDataParam(WhCheckInExceptionDBField.FINISH_DATE, DataType.TIMESTAMP,
                        entity.getFinishDate());
                request.addBatchDataParam(WhCheckInExceptionDBField.DISCARDED_DATE, DataType.TIMESTAMP,
                        entity.getDiscardedDate());
                request.addBatchDataParam(WhCheckInExceptionDBField.EXCEPTION_HANDLE_DATE, DataType.TIMESTAMP,
                        entity.getExceptionHandleDate());
                request.addBatchDataParam(WhCheckInExceptionDBField.PURCHASE_HANDLE_DATE, DataType.TIMESTAMP,
                        entity.getPurchaseHandleDate());
                request.addBatchDataParam(WhCheckInExceptionDBField.FINISH_USER, DataType.INT, entity.getFinishUser());
                request.addBatchDataParam(WhCheckInExceptionDBField.RECEIVE_BOX_NO, DataType.STRING,
                        entity.getReceiveBoxNo());
                request.addBatchDataParam(WhCheckInExceptionDBField.DISCARDED_USER, DataType.INT,
                        entity.getDiscardedUser());
                request.addBatchDataParam(WhCheckInExceptionDBField.CHECK_IN_USER, DataType.INT, entity.getCheckInUser());
                //默认0：普通
                request.addBatchDataParam(WhCheckInExceptionDBField.FIRST_ORDER_TYPE, DataType.INT, entity.getFirstOrderType() == null ? 0 : entity.getFirstOrderType());
                request.addBatchDataParam(WhCheckInExceptionDBField.EX_TIMES, DataType.INT, entity.getExTimes() == null ? 1 : entity.getExTimes());
                request.addBatchDataParam(WhCheckInExceptionDBField.EXCEPTION_HANDLED, DataType.BOOLEAN, entity.judgeExceptionHandledByStatus());
                request.addBatchDataParam(WhCheckInExceptionDBField.TAGS, DataType.STRING, entity.getTags());
                request.addBatchDataParam(WhCheckInExceptionDBField.NEXT_GENERATION_EXCEPTION_IDS, DataType.STRING, entity.getNextGenerationExceptionIds());
                request.addBatchDataParam(WhCheckInExceptionDBField.MARK_REASON, DataType.STRING, entity.getMarkReason());
                request.addBatchDataParam(WhCheckInExceptionDBField.MARK_TIME, DataType.TIMESTAMP, entity.getMarkTime());
                request.addBatchDataParam(WhCheckInExceptionDBField.MARK_USER_ID, DataType.INT, entity.getMarkUserId());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public void batchUpdateWhCheckInException(List<WhCheckInException> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhCheckInExceptionByPrimaryKey");
            for (WhCheckInException entity : entityList) {
                request.addBatchDataParam(WhCheckInExceptionDBField.ID, DataType.INT, entity.getId());

                request.addBatchDataParam(WhCheckInExceptionDBField.IN_ID, DataType.INT, entity.getInId());
                request.addBatchDataParam(WhCheckInExceptionDBField.PURCHASE_ORDER_NO, DataType.STRING,
                        entity.getPurchaseOrderNo());
                request.addBatchDataParam(WhCheckInExceptionDBField.NEW_PURCHASE_ORDER_NO, DataType.STRING,
                        entity.getNewPurchaseOrderNo());
                request.addBatchDataParam(WhCheckInExceptionDBField.TRACKING_NUMBER, DataType.STRING,
                        entity.getTrackingNumber());

                request.addBatchDataParam(WhCheckInExceptionDBField.EXCEPTION_TYPE, DataType.STRING,
                        entity.getExceptionType());
                request.addBatchDataParam(WhCheckInExceptionDBField.STATUS, DataType.INT, entity.getStatus());
                request.addBatchDataParam(WhCheckInExceptionDBField.HANDLE_WAY, DataType.INT, entity.getHandleWay());
                request.addBatchDataParam(WhCheckInExceptionDBField.EXCEPTION_USER, DataType.INT,
                        entity.getExceptionUser());
                request.addBatchDataParam(WhCheckInExceptionDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
                request.addBatchDataParam(WhCheckInExceptionDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(WhCheckInExceptionDBField.LOCATION_NUMBER, DataType.STRING, entity.getLocationNumber());
                request.addBatchDataParam(WhCheckInExceptionDBField.IMAGE, DataType.STRING, entity.getImage());
                request.addBatchDataParam(WhCheckInExceptionDBField.QUANTITY, DataType.INT, entity.getQuantity());
                request.addBatchDataParam(WhCheckInExceptionDBField.PURCHASE_USER, DataType.INT,
                        entity.getPurchaseUser());
                request.addBatchDataParam(WhCheckInExceptionDBField.CONFIRM_QUANTITY, DataType.INT,
                        entity.getConfirmQuantity());
                request.addBatchDataParam(WhCheckInExceptionDBField.EXCEPTION_COMMENT, DataType.STRING,
                        entity.getExceptionComment());
                request.addBatchDataParam(WhCheckInExceptionDBField.LAST_UPDATE_USER, DataType.INT,
                        entity.getLastUpdateUser() == null ? DataContextHolder.getUserId()
                                : entity.getLastUpdateUser());
                request.addBatchDataParam(WhCheckInExceptionDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhCheckInExceptionDBField.WAREHOUSE_ID, DataType.INT,
                        entity.getWarehouseId());
                request.addBatchDataParam(WhCheckInExceptionDBField.EXCEPTION_FORM, DataType.INT,
                        entity.getExceptionFrom());
                request.addBatchDataParam(WhCheckInExceptionDBField.IS_CARRY_PRODUCT, DataType.BOOLEAN,
                        entity.getIsCarryProduct());
                request.addBatchDataParam(WhCheckInExceptionDBField.CARRY_QUANTITY, DataType.INT,
                        entity.getCarryQuantity());
                request.addBatchDataParam(WhCheckInExceptionDBField.RETURN_INFORMATION_JSON, DataType.STRING,
                        entity.getReturnInformationJson());
                request.addBatchDataParam(WhCheckInExceptionDBField.FINISH_DATE, DataType.TIMESTAMP,
                        entity.getFinishDate());
                request.addBatchDataParam(WhCheckInExceptionDBField.DISCARDED_DATE, DataType.TIMESTAMP,
                        entity.getDiscardedDate());
                request.addBatchDataParam(WhCheckInExceptionDBField.EXCEPTION_HANDLE_DATE, DataType.TIMESTAMP,
                        entity.getExceptionHandleDate());
                request.addBatchDataParam(WhCheckInExceptionDBField.PURCHASE_HANDLE_DATE, DataType.TIMESTAMP,
                        entity.getPurchaseHandleDate());
                request.addBatchDataParam(WhCheckInExceptionDBField.FINISH_USER, DataType.INT, entity.getFinishUser());
                request.addBatchDataParam(WhCheckInExceptionDBField.FIRST_ORDER_TYPE, DataType.INT, entity.getFirstOrderType());
                request.addBatchDataParam(WhCheckInExceptionDBField.EX_TIMES, DataType.INT, entity.getExTimes());
                request.addBatchDataParam(WhCheckInExceptionDBField.RECEIVE_BOX_NO, DataType.STRING,
                        entity.getReceiveBoxNo());
                request.addBatchDataParam(WhCheckInExceptionDBField.DISCARDED_USER, DataType.INT,
                        entity.getDiscardedUser());
                request.addBatchDataParam(WhCheckInExceptionDBField.WAIT_CHECK_IN_DATE, DataType.TIMESTAMP, entity.getWaitCheckInDate());
                request.addBatchDataParam(WhCheckInExceptionDBField.EXCEPTION_HANDLED, DataType.BOOLEAN, entity.judgeExceptionHandledByStatus());
                request.addBatchDataParam(WhCheckInExceptionDBField.TAGS, DataType.STRING, entity.getTags());
                request.addBatchDataParam(WhCheckInExceptionDBField.ABANDON_REASON, DataType.STRING, entity.getAbandonReason());
                request.addBatchDataParam(WhCheckInExceptionDBField.NEXT_GENERATION_EXCEPTION_IDS, DataType.STRING, entity.getNextGenerationExceptionIds());
                request.addBatchDataParam(WhCheckInExceptionDBField.MARK_REASON, DataType.STRING, entity.getMarkReason());
                request.addBatchDataParam(WhCheckInExceptionDBField.MARK_TIME, DataType.TIMESTAMP, entity.getMarkTime());
                request.addBatchDataParam(WhCheckInExceptionDBField.MARK_USER_ID, DataType.INT, entity.getMarkUserId());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public void deleteWhCheckInException(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhCheckInExceptionByPrimaryKey");
        request.addDataParam(WhCheckInExceptionDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public List<HistoryCheckInExceptionCount> queryAllHistoryCheckInExceptionCountList() {
        SqlerRequest request = new SqlerRequest("queryAllHistoryCheckInExceptionCountList");
        request.setReadOnly(true);
        return SqlerTemplate.query(request, new HistoryCheckInExceptionCountMapper(true));
    }

    @Override
    public List<WhCheckInException> queryRecentThreeExceptionsByExceptionType(WhCheckInExceptionQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryRecentThreeExceptionsByExceptionType");
        setQueryCondition(request, query);
        return getReturnRow(request);
    }

    @Override
    public List<WhCheckInException> queryRecentFinishedExceptionsByExceptionType(String exceptionType, String sku) {
        Assert.notNull(exceptionType);
        SqlerRequest request = new SqlerRequest("queryRecentFinishedExceptionsByExceptionType");
        request.addDataParam(WhCheckInExceptionDBField.EXCEPTION_TYPE, DataType.STRING, exceptionType);
        request.addDataParam(WhCheckInExceptionDBField.SKU, DataType.STRING, sku);
        String unionSql = "";
        String baseSql = " SELECT e.id,e.creation_date,e.exception_type,e.`status`,e.handle_way,e.image"
                + ",(SELECT h.handle_comment FROM wh_check_in_exception_handle h WHERE h.exception_id = e.id AND h.`status` = 5 ORDER BY h.creation_date ASC LIMIT 1) AS firstEditHandleComment"
                + ",(SELECT h.handle_comment FROM wh_check_in_exception_handle h WHERE h.exception_id = e.id AND h.`status` = 7 ORDER BY h.creation_date DESC LIMIT 1) AS lastPurchaseHandleComment"
                + " FROM wh_check_in_exception e WHERE 1=1 AND e.`status` = 9";
        if (StringUtils.isNotBlank(sku)) {
            baseSql += " AND sku like '" + sku + "%'";
        }
        if (exceptionType.contains(",")) {
            String[] splitType = StringUtils.split(exceptionType, ",");
            for (String type : splitType) {
                unionSql += " UNION (" + baseSql + " AND FIND_IN_SET('" + type + "',e.exception_type) ORDER BY e.creation_date DESC LIMIT 3 )";
            }
            request.addSqlDataParam("UNION_SQL", unionSql);
        }
        return getReturnRow(request);
    }

    public List<WhCheckInException> getReturnRow(SqlerRequest request) {
        return SqlerTemplate.query(request, new RowMapper<WhCheckInException>() {
            @Override
            public WhCheckInException mapRow(ResultSet rs, int rowNum) throws SQLException {
                WhCheckInException entity = new WhCheckInException();
                entity.setId(rs.getObject(WhCheckInExceptionDBField.ID) == null ? null
                        : rs.getInt(WhCheckInExceptionDBField.ID));
                entity.setCreationDate(rs.getTimestamp(WhCheckInExceptionDBField.CREATION_DATE));
                entity.setExceptionType(rs.getString(WhCheckInExceptionDBField.EXCEPTION_TYPE));
                entity.setStatus(rs.getObject(WhCheckInExceptionDBField.STATUS) == null ? null
                        : rs.getInt(WhCheckInExceptionDBField.STATUS));
                entity.setImage(rs.getString(WhCheckInExceptionDBField.IMAGE));
                entity.setHandleWay(rs.getObject(WhCheckInExceptionDBField.HANDLE_WAY) == null ? null
                        : rs.getInt(WhCheckInExceptionDBField.HANDLE_WAY));
                entity.setFirstEditHandleComment(rs.getString("firstEditHandleComment"));
                entity.setLastPurchaseHandleComment(rs.getString("lastPurchaseHandleComment"));
                return entity;
            }
        });
    }

    @Override
    public void batchUpdateLocation(List<Integer> ids, String location) {
        if (CollectionUtils.isEmpty(ids))
            return;
        SqlerRequest request = new SqlerRequest("batchUpdateLocation");
        request.addDataParam("idList", DataType.INT, ids);
        request.addDataParam(WhCheckInExceptionDBField.LOCATION_NUMBER, DataType.STRING, location);
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCancelMarkWhCheckInException(List<Integer> ids, Integer lastUpdateUser) {
        if (CollectionUtils.isEmpty(ids))
            return;
        SqlerRequest request = new SqlerRequest("batchCancelMarkWhCheckInException");
        request.addDataParam("idList", DataType.INT, ids);
        request.addDataParam(WhCheckInExceptionDBField.LAST_UPDATE_USER, DataType.INT, lastUpdateUser);
        request.addDataParam(WhCheckInExceptionDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        SqlerTemplate.execute(request);
    }

    @Override
    public List<String> queryDistinctMarkReasons() {
        SqlerRequest request = new SqlerRequest("queryDistinctMarkReasons");
        return SqlerTemplate.query(request, new RowMapper<String>() {
            @Override
            public String mapRow(ResultSet resultSet, int i) throws SQLException {
                return resultSet.getString("mark_reason");
            }
        });
    }
}