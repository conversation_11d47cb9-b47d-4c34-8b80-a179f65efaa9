package com.estone.checkin.dao.impl;

import com.estone.checkin.bean.WhCheckInExcess;
import com.estone.checkin.bean.WhCheckInExcessQueryCondition;
import com.estone.checkin.dao.WhCheckInExcessDao;
import com.estone.checkin.dao.mapper.WhCheckInExcessDBField;
import com.estone.checkin.dao.mapper.WhCheckInExcessMapper;
import com.estone.common.util.SqlerTemplate;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import java.sql.Timestamp;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

@Repository("whCheckInExcessDao")
public class WhCheckInExcessDaoImpl implements WhCheckInExcessDao {

    private void setQueryCondition(SqlerRequest request, WhCheckInExcessQueryCondition query) {
        if (query == null) {
            return;
        }
        // 添加只读权限
        if (query.getReadOnly() != null && query.getReadOnly())
        {
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);
        
        request.addDataParam(WhCheckInExcessDBField.ID, DataType.INT, query.getId());
        request.addDataParam(WhCheckInExcessDBField.SKU, DataType.STRING, query.getSku());
        request.addDataParam(WhCheckInExcessDBField.IN_ID, DataType.INT, query.getInitId());
        request.addDataParam(WhCheckInExcessDBField.PURCHASE_ORDER_NO, DataType.STRING, query.getPurchaseOrderNo());
        request.addDataParam(WhCheckInExcessDBField.QC_QUANTITY, DataType.INT, query.getQcQuantity());
        request.addDataParam(WhCheckInExcessDBField.UP_QUANTITY, DataType.INT, query.getUpQuantity());
        request.addDataParam(WhCheckInExcessDBField.TRANSITION_UP_QUANTITY, DataType.INT, query.getTransitionUpQuantity());
        request.addDataParam(WhCheckInExcessDBField.WEIGHT, DataType.DOUBLE, query.getWeight());
        request.addDataParam(WhCheckInExcessDBField.SHIPPING_COST, DataType.BIGDECIMAL, query.getShippingCost());
        request.addDataParam(WhCheckInExcessDBField.TRACKING_NUMBER, DataType.STRING, query.getTrackingNumber());
        request.addDataParam(WhCheckInExcessDBField.SUPPLIER_ID, DataType.STRING, query.getSupplierId());
        request.addDataParam(WhCheckInExcessDBField.QC_NUM, DataType.INT, query.getQcNum());
        request.addDataParam(WhCheckInExcessDBField.VENDOR_NAME, DataType.STRING, query.getVendorName());
        request.addDataParam(WhCheckInExcessDBField.DIFFERENCE_QUANTITY, DataType.INT, query.getDifferenceQuantity());

        if (CollectionUtils.isNotEmpty(query.getInIdList())) {
            request.addDataParam("inIdList", DataType.INT, query.getInIdList());
        }
    }

    @Override
    public int queryWhCheckInExcessCount(WhCheckInExcessQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhCheckInExcessCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhCheckInExcess> queryWhCheckInExcessList() {
        SqlerRequest request = new SqlerRequest("queryWhCheckInExcessList");
        return SqlerTemplate.query(request, new WhCheckInExcessMapper());
    }

    @Override
    public List<WhCheckInExcess> queryWhCheckInExcessList(WhCheckInExcessQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryWhCheckInExcessList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhCheckInExcessMapper());
    }

    @Override
    public WhCheckInExcess queryWhCheckInExcess(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryWhCheckInExcessByPrimaryKey");
        request.addDataParam(WhCheckInExcessDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhCheckInExcessMapper());
    }

    @Override
    public WhCheckInExcess queryWhCheckInExcess(WhCheckInExcessQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhCheckInExcess");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhCheckInExcessMapper());
    }

    @Override
    public void createWhCheckInExcess(WhCheckInExcess entity) {
        SqlerRequest request = new SqlerRequest("createWhCheckInExcess");
        request.addDataParam(WhCheckInExcessDBField.IN_ID, DataType.INT, entity.getInId());
        request.addDataParam(WhCheckInExcessDBField.SUB_ID, DataType.STRING, entity.getSubId());
        request.addDataParam(WhCheckInExcessDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(WhCheckInExcessDBField.EXCESS_QUANTITY, DataType.INT, entity.getExcessQuantity());
        request.addDataParam(WhCheckInExcessDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
        request.addDataParam(WhCheckInExcessDBField.PURCHASE_USER, DataType.STRING, entity.getPurchaseUser());
        request.addDataParam(WhCheckInExcessDBField.PURCHASE_QUANTITY, DataType.INT, entity.getPurchaseQuantity());
        request.addDataParam(WhCheckInExcessDBField.MATCHED_QUANTITY, DataType.INT, entity.getMatchedQuantity());
        request.addDataParam(WhCheckInExcessDBField.PROCESSING_METHOD, DataType.STRING, entity.getProcessingMethod());
        request.addDataParam(WhCheckInExcessDBField.OPERATION, DataType.STRING, entity.getOperation());
        request.addDataParam(WhCheckInExcessDBField.CREATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhCheckInExcessDBField.UPDATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhCheckInExcessDBField.QC_QUANTITY, DataType.INT, entity.getQcQuantity());
        request.addDataParam(WhCheckInExcessDBField.UP_QUANTITY, DataType.INT, entity.getUpQuantity());
        request.addDataParam(WhCheckInExcessDBField.TRANSITION_UP_QUANTITY, DataType.INT, entity.getTransitionUpQuantity());
        request.addDataParam(WhCheckInExcessDBField.WEIGHT, DataType.DOUBLE, entity.getWeight());
        request.addDataParam(WhCheckInExcessDBField.SHIPPING_COST, DataType.BIGDECIMAL, entity.getShippingCost());
        request.addDataParam(WhCheckInExcessDBField.TRACKING_NUMBER, DataType.STRING, entity.getTrackingNumber());
        request.addDataParam(WhCheckInExcessDBField.SUPPLIER_ID, DataType.STRING, entity.getSupplierId());
        request.addDataParam(WhCheckInExcessDBField.QC_NUM, DataType.INT, entity.getQcNum());
        request.addDataParam(WhCheckInExcessDBField.VENDOR_NAME, DataType.STRING, entity.getVendorName());
        request.addDataParam(WhCheckInExcessDBField.DIFFERENCE_QUANTITY, DataType.INT, entity.getDifferenceQuantity());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateWhCheckInExcess(WhCheckInExcess entity) {
        SqlerRequest request = new SqlerRequest("updateWhCheckInExcessByPrimaryKey");
        request.addDataParam(WhCheckInExcessDBField.ID, DataType.INT, entity.getId());
        
        request.addDataParam(WhCheckInExcessDBField.IN_ID, DataType.INT, entity.getInId());
        request.addDataParam(WhCheckInExcessDBField.SUB_ID, DataType.STRING, entity.getSubId());
        request.addDataParam(WhCheckInExcessDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(WhCheckInExcessDBField.EXCESS_QUANTITY, DataType.INT, entity.getExcessQuantity());
        request.addDataParam(WhCheckInExcessDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
        request.addDataParam(WhCheckInExcessDBField.PURCHASE_USER, DataType.STRING, entity.getPurchaseUser());
        request.addDataParam(WhCheckInExcessDBField.PURCHASE_QUANTITY, DataType.INT, entity.getPurchaseQuantity());
        request.addDataParam(WhCheckInExcessDBField.MATCHED_QUANTITY, DataType.INT, entity.getMatchedQuantity());
        request.addDataParam(WhCheckInExcessDBField.PROCESSING_METHOD, DataType.STRING, entity.getProcessingMethod());
        request.addDataParam(WhCheckInExcessDBField.OPERATION, DataType.STRING, entity.getOperation());
        request.addDataParam(WhCheckInExcessDBField.CREATE_TIME, DataType.TIMESTAMP, entity.getCreateTime());
        request.addDataParam(WhCheckInExcessDBField.UPDATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhCheckInExcessDBField.QC_QUANTITY, DataType.INT, entity.getQcQuantity());
        request.addDataParam(WhCheckInExcessDBField.UP_QUANTITY, DataType.INT, entity.getUpQuantity());
        request.addDataParam(WhCheckInExcessDBField.TRANSITION_UP_QUANTITY, DataType.INT, entity.getTransitionUpQuantity());
        request.addDataParam(WhCheckInExcessDBField.WEIGHT, DataType.DOUBLE, entity.getWeight());
        request.addDataParam(WhCheckInExcessDBField.SHIPPING_COST, DataType.BIGDECIMAL, entity.getShippingCost());
        request.addDataParam(WhCheckInExcessDBField.TRACKING_NUMBER, DataType.STRING, entity.getTrackingNumber());
        request.addDataParam(WhCheckInExcessDBField.SUPPLIER_ID, DataType.STRING, entity.getSupplierId());
        request.addDataParam(WhCheckInExcessDBField.QC_NUM, DataType.INT, entity.getQcNum());
        request.addDataParam(WhCheckInExcessDBField.VENDOR_NAME, DataType.STRING, entity.getVendorName());
        request.addDataParam(WhCheckInExcessDBField.DIFFERENCE_QUANTITY, DataType.INT, entity.getDifferenceQuantity());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateWhCheckInExcess(List<WhCheckInExcess> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createWhCheckInExcess");
            for (WhCheckInExcess entity : entityList) {
                request.addBatchDataParam(WhCheckInExcessDBField.IN_ID, DataType.INT, entity.getInId());
                request.addBatchDataParam(WhCheckInExcessDBField.SUB_ID, DataType.STRING, entity.getSubId());
                request.addBatchDataParam(WhCheckInExcessDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(WhCheckInExcessDBField.EXCESS_QUANTITY, DataType.INT, entity.getExcessQuantity());
                request.addBatchDataParam(WhCheckInExcessDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
                request.addBatchDataParam(WhCheckInExcessDBField.PURCHASE_USER, DataType.STRING, entity.getPurchaseUser());
                request.addBatchDataParam(WhCheckInExcessDBField.PURCHASE_QUANTITY, DataType.INT, entity.getPurchaseQuantity());
                request.addBatchDataParam(WhCheckInExcessDBField.MATCHED_QUANTITY, DataType.INT, entity.getMatchedQuantity());
                request.addBatchDataParam(WhCheckInExcessDBField.PROCESSING_METHOD, DataType.STRING, entity.getProcessingMethod());
                request.addBatchDataParam(WhCheckInExcessDBField.OPERATION, DataType.STRING, entity.getOperation());
                request.addBatchDataParam(WhCheckInExcessDBField.CREATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhCheckInExcessDBField.UPDATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhCheckInExcessDBField.QC_QUANTITY, DataType.INT, entity.getQcQuantity());
                request.addBatchDataParam(WhCheckInExcessDBField.UP_QUANTITY, DataType.INT, entity.getUpQuantity());
                request.addBatchDataParam(WhCheckInExcessDBField.TRANSITION_UP_QUANTITY, DataType.INT, entity.getTransitionUpQuantity());
                request.addBatchDataParam(WhCheckInExcessDBField.WEIGHT, DataType.DOUBLE, entity.getWeight());
                request.addBatchDataParam(WhCheckInExcessDBField.SHIPPING_COST, DataType.BIGDECIMAL, entity.getShippingCost());
                request.addBatchDataParam(WhCheckInExcessDBField.TRACKING_NUMBER, DataType.STRING, entity.getTrackingNumber());
                request.addBatchDataParam(WhCheckInExcessDBField.SUPPLIER_ID, DataType.STRING, entity.getSupplierId());
                request.addBatchDataParam(WhCheckInExcessDBField.QC_NUM, DataType.INT, entity.getQcNum());
                request.addBatchDataParam(WhCheckInExcessDBField.VENDOR_NAME, DataType.STRING, entity.getVendorName());
                request.addBatchDataParam(WhCheckInExcessDBField.DIFFERENCE_QUANTITY, DataType.INT, entity.getDifferenceQuantity());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateWhCheckInExcess(List<WhCheckInExcess> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateWhCheckInExcessByPrimaryKey");
            for (WhCheckInExcess entity : entityList) {
                request.addBatchDataParam(WhCheckInExcessDBField.ID, DataType.INT, entity.getId());
                
                request.addBatchDataParam(WhCheckInExcessDBField.IN_ID, DataType.INT, entity.getInId());
                request.addBatchDataParam(WhCheckInExcessDBField.SUB_ID, DataType.STRING, entity.getSubId());
                request.addBatchDataParam(WhCheckInExcessDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(WhCheckInExcessDBField.EXCESS_QUANTITY, DataType.INT, entity.getExcessQuantity());
                request.addBatchDataParam(WhCheckInExcessDBField.PURCHASE_ORDER_NO, DataType.STRING, entity.getPurchaseOrderNo());
                request.addBatchDataParam(WhCheckInExcessDBField.PURCHASE_USER, DataType.STRING, entity.getPurchaseUser());
                request.addBatchDataParam(WhCheckInExcessDBField.PURCHASE_QUANTITY, DataType.INT, entity.getPurchaseQuantity());
                request.addBatchDataParam(WhCheckInExcessDBField.MATCHED_QUANTITY, DataType.INT, entity.getMatchedQuantity());
                request.addBatchDataParam(WhCheckInExcessDBField.PROCESSING_METHOD, DataType.STRING, entity.getProcessingMethod());
                request.addBatchDataParam(WhCheckInExcessDBField.OPERATION, DataType.STRING, entity.getOperation());
                request.addBatchDataParam(WhCheckInExcessDBField.CREATE_TIME, DataType.TIMESTAMP, entity.getCreateTime());
                request.addBatchDataParam(WhCheckInExcessDBField.UPDATE_TIME, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhCheckInExcessDBField.QC_QUANTITY, DataType.INT, entity.getQcQuantity());
                request.addBatchDataParam(WhCheckInExcessDBField.UP_QUANTITY, DataType.INT, entity.getUpQuantity());
                request.addBatchDataParam(WhCheckInExcessDBField.TRANSITION_UP_QUANTITY, DataType.INT, entity.getTransitionUpQuantity());
                request.addBatchDataParam(WhCheckInExcessDBField.WEIGHT, DataType.DOUBLE, entity.getWeight());
                request.addBatchDataParam(WhCheckInExcessDBField.SHIPPING_COST, DataType.BIGDECIMAL, entity.getShippingCost());
                request.addBatchDataParam(WhCheckInExcessDBField.TRACKING_NUMBER, DataType.STRING, entity.getTrackingNumber());
                request.addBatchDataParam(WhCheckInExcessDBField.SUPPLIER_ID, DataType.STRING, entity.getSupplierId());
                request.addBatchDataParam(WhCheckInExcessDBField.QC_NUM, DataType.INT, entity.getQcNum());
                request.addBatchDataParam(WhCheckInExcessDBField.VENDOR_NAME, DataType.STRING, entity.getVendorName());
                request.addBatchDataParam(WhCheckInExcessDBField.DIFFERENCE_QUANTITY, DataType.INT, entity.getDifferenceQuantity());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteWhCheckInExcess(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteWhCheckInExcessByPrimaryKey");
        request.addDataParam(WhCheckInExcessDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }
}