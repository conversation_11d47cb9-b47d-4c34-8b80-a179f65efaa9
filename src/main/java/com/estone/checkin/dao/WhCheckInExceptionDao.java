package com.estone.checkin.dao;

import com.estone.checkin.bean.HistoryCheckInExceptionCount;
import com.estone.checkin.bean.WhCheckInException;
import com.estone.checkin.bean.WhCheckInExceptionQueryCondition;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

import java.util.List;

public interface WhCheckInExceptionDao {
    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    int queryWhCheckInExceptionCount(WhCheckInExceptionQueryCondition query);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    List<WhCheckInException> queryWhCheckInExceptionList();

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    List<WhCheckInException> queryWhCheckInExceptionList(WhCheckInExceptionQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    WhCheckInException queryWhCheckInException(Integer primaryKey);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    WhCheckInException queryWhCheckInException(WhCheckInExceptionQueryCondition query);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    void createWhCheckInException(WhCheckInException entity);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    void batchCreateWhCheckInException(List<WhCheckInException> entityList);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    void batchUpdateWhCheckInException(List<WhCheckInException> entityList);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    void deleteWhCheckInException(Integer primaryKey);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    void updateWhCheckInException(WhCheckInException entity);

    List<HistoryCheckInExceptionCount> queryAllHistoryCheckInExceptionCountList();

    List<WhCheckInException> queryRecentThreeExceptionsByExceptionType(WhCheckInExceptionQueryCondition query);

    List<WhCheckInException> queryRecentFinishedExceptionsByExceptionType(String exceptionType, String sku);

    int queryNewWhCheckInExceptionCount(WhCheckInExceptionQueryCondition query);

    List<WhCheckInException> queryNewWhCheckInExceptionList(WhCheckInExceptionQueryCondition query, Pager pager);

    /**
     * 修改库位
     *
     * @param ids
     * @param location
     * @return
     */
    void batchUpdateLocation(List<Integer> ids, String location);

    /**
     * 批量取消标记
     *
     * @param ids ID列表
     * @param lastUpdateUser 更新用户
     */
    void batchCancelMarkWhCheckInException(List<Integer> ids, Integer lastUpdateUser);

    /**
     * 查询数据库中已使用的不重复标记原因
     *
     * @return 标记原因列表
     */
    List<String> queryDistinctMarkReasons();
}