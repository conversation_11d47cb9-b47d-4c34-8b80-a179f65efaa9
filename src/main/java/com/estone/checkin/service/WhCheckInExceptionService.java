package com.estone.checkin.service;

import java.util.List;

import com.estone.checkin.bean.HistoryCheckInExceptionCount;
import com.estone.checkin.bean.WhCheckInException;
import com.estone.checkin.bean.WhCheckInExceptionHandle;
import com.estone.checkin.bean.WhCheckInExceptionQueryCondition;
import com.estone.checkin.domain.WhCheckInExceptionDo;
import com.estone.common.util.model.ApiResult;
import com.whq.tool.component.Pager;
import com.whq.tool.json.ResponseJson;

public interface WhCheckInExceptionService {
    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    List<WhCheckInException> queryAllWhCheckInExceptions();

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    List<WhCheckInException> queryWhCheckInExceptions(WhCheckInExceptionQueryCondition query, Pager pager);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    WhCheckInException getWhCheckInException(Integer id);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    WhCheckInException getWhCheckInExceptionDetail(Integer id);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    WhCheckInException queryWhCheckInException(WhCheckInExceptionQueryCondition query);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    //void createWhCheckInException(WhCheckInException whCheckInException);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    void createWhCheckInException(WhCheckInException whCheckInException,String type);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    void batchCreateWhCheckInException(List<WhCheckInException> entityList);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    void deleteWhCheckInException(Integer id);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    void updateWhCheckInException(WhCheckInException whCheckInException);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    void updateWhCheckInException(WhCheckInException whCheckInException, String type);

    void updateLocationAuto(WhCheckInException whCheckInException, String oldLocation);

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    List<Integer> batchUpdateExceptionToFinished(List<Integer> ids, List<Integer> ignoreHandleWays);

    void updateExceptionToFinished(WhCheckInException whCheckInException);

    int getExceptionCount(WhCheckInExceptionQueryCondition query);

    List<HistoryCheckInExceptionCount> queryAllHistoryCheckInExceptionCountList();

    List<WhCheckInException> queryRecentThreeExceptionsByExceptionType(WhCheckInExceptionQueryCondition query);

    List<WhCheckInException> queryRecentFinishedExceptionsByExceptionType(String exceptionType, String sku);

    ResponseJson batchUpdateExceptionToPurchasePending(List<Integer> ids);

    List<WhCheckInException> queryNewWhCheckInExceptions(WhCheckInExceptionQueryCondition query, Pager page);

    /**
     * 标记待入库
     * @param ids
     * @return
     */
    List<Integer> batchUpdateToWaitCheckIn(List<Integer> ids);

    void waitCheckInException(WhCheckInException whCheckInException, String comment);


    ResponseJson saveWhCheckException( WhCheckInExceptionDo domain,String type,String exceptionBoxNo);

    ApiResult pushCheckInExceptionToProduct(WhCheckInException whCheckInException);

    ResponseJson saveNewWhCheckException(WhCheckInExceptionDo domain, String type, String abandonReason);

    void saveHandle(WhCheckInExceptionHandle handle, WhCheckInException whCheckInException);

    /**
     * 修改库位
     * 
     * @param ids
     * @param location
     * @return
     */
    ResponseJson batchUpdateLocation(List<Integer> ids, String location);

    /**
     * 校验库位
     * @param location
     * @return
     */
    ResponseJson checkUpdateLocation(List<Integer> ids, String location);

    /**
     * 全检生成少货异常
     *
     * @param inId
     * @param qcNum
     */
    void createLossSkuException(Integer inId, Integer qcNum);

    /**
     * 批量废弃
     * 
     * @param ids
     * @param reason
     * @param remark 草稿状态下的订单的废弃备注
     * @return
     */
    List<Integer> batchUpdateToDiscarded(List<Integer> ids,String remark, String reason);
    
    /**
     * 批量标记异常单
     * 
     * @param ids 异常单ID列表
     * @param markReason 标记原因
     * @param markRemark 标记备注
     * @return ApiResult
     */
    ApiResult<?> updateMarkExceptions(List<Integer> ids, String markReason, String markRemark);
    
    /**
     * 取消标记异常单
     * 
     * @param id 异常单ID
     * @return ApiResult
     */
    ApiResult<?> updateCancelMark(Integer id);

    /**
     * 查询数据库中已使用的不重复标记原因
     *
     * @return 标记原因列表
     */
    List<String> queryDistinctMarkReasons();
}