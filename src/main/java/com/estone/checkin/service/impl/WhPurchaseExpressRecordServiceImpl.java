package com.estone.checkin.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.allocation.util.WarehouseProperties;
import com.estone.android.domain.AndroidProductDo;
import com.estone.checkin.bean.*;
import com.estone.checkin.dao.WhPurchaseExpressRecordDao;
import com.estone.checkin.domain.WhPurchaseExpressRecordDo;
import com.estone.checkin.enums.*;
import com.estone.checkin.service.*;
import com.estone.checkin.utils.TranslateWhPurchaseOrderToPurchaseOrderUtils;
import com.estone.common.enums.LogModule;
import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.sku.bean.WhSkuSpecialGoods;
import com.estone.sku.service.WhSkuService;
import com.estone.system.param.bean.SystemParam;
import com.estone.system.rabbitmq.bean.AmqMessage;
import com.estone.system.rabbitmq.model.PushCheckInException;
import com.estone.system.rabbitmq.model.PushCheckinMessage;
import com.estone.system.rabbitmq.producer.RabbitmqProducerService;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.estone.warehouse.bean.WhBox;
import com.estone.warehouse.bean.WhBoxItem;
import com.estone.warehouse.bean.WhBoxItemQueryCondition;
import com.estone.warehouse.bean.WhBoxQueryCondition;
import com.estone.warehouse.enums.BoxType;
import com.estone.warehouse.enums.ItemOrderTypeEnum;
import com.estone.warehouse.service.WhBoxItemService;
import com.estone.warehouse.service.WhBoxService;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service("whPurchaseExpressRecordService")
@Slf4j
public class WhPurchaseExpressRecordServiceImpl implements WhPurchaseExpressRecordService {

    private static Logger logger = LoggerFactory.getLogger(WhPurchaseExpressRecordServiceImpl.class);

    final static SystemLogUtils EXPRESSRECORDLOG = SystemLogUtils.create(LogModule.EXPRESSRECORD.getCode());
    final static SystemLogUtils PURCHASEORDERLOG = SystemLogUtils.create(LogModule.PURCHASE_ORDER.getCode());

    ExecutorService executor = ExecutorUtils.newFixedThreadPool(5,10, 5*60*1000L);

    @Resource
    private WhPurchaseExpressRecordDao whPurchaseExpressRecordDao;

    @Resource
    private WhBoxItemService whBoxItemService;

    @Resource
    private WhBoxService whBoxService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private WhCheckInExceptionService whCheckInExceptionService;

    @Resource
    private WhCheckInExceptionHandleService whCheckInExceptionHandleService;

    @Resource
    private WhPurchaseOrderService whPurchaseOrderService;

    @Resource
    private WhPurchaseToExpressService whPurchaseToExpressService;

    @Resource
    private WhPurchaseItemService whPurchaseItemService;

    @Resource
    private OutStockMatchHandelService outStockMatchHandelService;

    @Resource
    private PurchaseApvOutStockMatchService outStockMatchService;

    @Resource
    private AmqMessageService amqMessageService;

    @Resource
    private RabbitmqProducerService rabbitmqProducerService;

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public WhPurchaseExpressRecord getWhPurchaseExpressRecord(Integer id) {
        WhPurchaseExpressRecord whPurchaseExpressRecord = whPurchaseExpressRecordDao.queryWhPurchaseExpressRecord(id);
        return whPurchaseExpressRecord;
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public WhPurchaseExpressRecord getWhPurchaseExpressRecordDetail(Integer id) {
        WhPurchaseExpressRecord whPurchaseExpressRecord = whPurchaseExpressRecordDao.queryWhPurchaseExpressRecord(id);
        // 关联查询
        return whPurchaseExpressRecord;
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public WhPurchaseExpressRecord queryWhPurchaseExpressRecord(WhPurchaseExpressRecordQueryCondition query) {
        Assert.notNull(query);
        WhPurchaseExpressRecord whPurchaseExpressRecord = whPurchaseExpressRecordDao
                .queryWhPurchaseExpressRecord(query);
        return whPurchaseExpressRecord;
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public List<WhPurchaseExpressRecord> queryAllWhPurchaseExpressRecords() {
        return whPurchaseExpressRecordDao.queryWhPurchaseExpressRecordList();
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public List<WhPurchaseExpressRecord> queryWhPurchaseExpressRecords(WhPurchaseExpressRecordQueryCondition query,
            Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whPurchaseExpressRecordDao.queryWhPurchaseExpressRecordCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhPurchaseExpressRecord>();
            }
        }
        List<WhPurchaseExpressRecord> whPurchaseExpressRecords = whPurchaseExpressRecordDao
                .queryWhPurchaseExpressRecordList(query, pager);
        return whPurchaseExpressRecords;
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public void createWhPurchaseExpressRecord(WhPurchaseExpressRecord whPurchaseExpressRecord) {
        try {
            whPurchaseExpressRecordDao.createWhPurchaseExpressRecord(whPurchaseExpressRecord);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public void batchCreateWhPurchaseExpressRecord(List<WhPurchaseExpressRecord> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whPurchaseExpressRecordDao.batchCreateWhPurchaseExpressRecord(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public void deleteWhPurchaseExpressRecord(Integer id) {
        try {
            whPurchaseExpressRecordDao.deleteWhPurchaseExpressRecord(id);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public void updateWhPurchaseExpressRecord(WhPurchaseExpressRecord whPurchaseExpressRecord) {
        try {
            whPurchaseExpressRecordDao.updateWhPurchaseExpressRecord(whPurchaseExpressRecord);
        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_purchase_express_record
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    public void batchUpdateWhPurchaseExpressRecord(List<WhPurchaseExpressRecord> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whPurchaseExpressRecordDao.batchUpdateWhPurchaseExpressRecord(entityList);
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void updatePurchaseExpressRecordByTrackingNumber(WhPurchaseExpressRecord entity) {
        String trackingNumber = entity.getTrackingNumber();

        if (StringUtils.isBlank(trackingNumber)) {
            return;
        }
        List<WhPurchaseExpressRecord> purchaseExpressRecordList = null;
        WhPurchaseExpressRecordQueryCondition query = new WhPurchaseExpressRecordQueryCondition();

        // 采购单单号
        if (StringUtils.startsWith(trackingNumber, "CG") || StringUtils.startsWith(trackingNumber, "HWCG")
                || StringUtils.startsWith(trackingNumber, "NCG")) {
            query.setPurchaseOrderNo(trackingNumber);
            purchaseExpressRecordList = whPurchaseExpressRecordDao.queryWhPurchaseExpressRecordList(query, null);
        }
        else {
            query.setTrackingNumber(trackingNumber);
            purchaseExpressRecordList = whPurchaseExpressRecordDao.queryWhPurchaseExpressRecordList(query, null);
        }

        try {
            if (CollectionUtils.isNotEmpty(purchaseExpressRecordList)) {
                for (WhPurchaseExpressRecord purchaseExpressRecord : purchaseExpressRecordList) {
                    entity.setWarehouseId(purchaseExpressRecord.getWarehouseId());// 携带仓库ID到前端
                    WhPurchaseExpressRecord updatePurchaseExpressRecord = new WhPurchaseExpressRecord();
                    updatePurchaseExpressRecord.setReceiveDate(purchaseExpressRecord.getReceiveDate());
                    updatePurchaseExpressRecord.setReceiveUser(purchaseExpressRecord.getReceiveUser());
                    updatePurchaseExpressRecord.setBoxNo(purchaseExpressRecord.getBoxNo());
                    // 只更新第一次
                    // 扫描人
                    if (purchaseExpressRecord.getCheckInScanner() == null
                            || purchaseExpressRecord.getCheckInScanner() == 0) {
                        // 入库扫描记录
                        updatePurchaseExpressRecord.setId(purchaseExpressRecord.getId());
                        updatePurchaseExpressRecord.setCheckInScanTime(entity.getCheckInScanTime());
                        updatePurchaseExpressRecord.setCheckInScanner(entity.getCheckInScanner());
                        updatePurchaseExpressRecord.setCheckInScanStatus(entity.getCheckInScanStatus());
                        updatePurchaseExpressRecord.setStatus(entity.getStatus());
                        whPurchaseExpressRecordDao.updateWhPurchaseExpressRecord(updatePurchaseExpressRecord);
                        if (entity.getCheckInScanner() != null && entity.getCheckInScanner() != 0) {
                            logger.info("更新收货记录插入扫描人和扫描时间 : trackingNumber[" + trackingNumber + "], id["
                                    + purchaseExpressRecord.getId() + "], checkInScanner[" + entity.getCheckInScanner()
                                    + "], checkInScanTime[" + entity.getCheckInScanTime() + "]");
                            EXPRESSRECORDLOG.log(purchaseExpressRecord.getId(), CheckInLogType.CHECK_IN_SCAN.getName(),
                                    new String[][] { { "扫描人",
                                            TaglibUtils.getEmployeeNameByUserId(entity.getCheckInScanner()) } });
                        }
                    }
                    // 未扫描的不跟新拆分人
                    else if ((purchaseExpressRecord.getSplitUser() == null || purchaseExpressRecord.getSplitUser() == 0)
                            && (purchaseExpressRecord.getCheckInScanner() != null
                                    && purchaseExpressRecord.getCheckInScanner() != 0)) {
                        updatePurchaseExpressRecord.setId(purchaseExpressRecord.getId());
                        updatePurchaseExpressRecord.setSplitUser(entity.getSplitUser());
                        updatePurchaseExpressRecord.setSplitDate(entity.getSplitDate());
                        whPurchaseExpressRecordDao.updateWhPurchaseExpressRecord(updatePurchaseExpressRecord);

                        if (entity.getSplitUser() != null && entity.getSplitUser() != 0) {
                            logger.info("更新收货记录插入拆分人和拆分时间 : trackingNumber[" + trackingNumber + "], id["
                                    + purchaseExpressRecord.getId() + "], splitUser[" + entity.getSplitUser()
                                    + "], splitDate[" + entity.getSplitDate() + "]");
                            EXPRESSRECORDLOG.log(purchaseExpressRecord.getId(), CheckInLogType.SPLIT_EXPRESS.getName(),
                                    new String[][] { { "拆分人", entity.getSplitUser().toString() } });
                        }
                    }
                }
            }

        }
        catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }

    }

    /**
     * 刷新签收记录
     *
     * <p>
     * 根据快递单后判断之前是否已经存在，如果已经存在就更新
     *
     * @param entity
     *
     */
    @Override
    public void refreshWhPurchaseExpressRecord(WhPurchaseExpressRecord entity) {
        // 更新未绑定的为绑定
        if (StringUtils.isNotBlank(entity.getTrackingNumber())) {
            try {
                // 之前如果存在直接更新
                WhPurchaseExpressRecordQueryCondition query = new WhPurchaseExpressRecordQueryCondition();
                query.setTrackingNumber(entity.getTrackingNumber());
                List<WhPurchaseExpressRecord> expressRecordList = whPurchaseExpressRecordDao
                        .queryWhPurchaseExpressRecordList(query, null);

                if (CollectionUtils.isNotEmpty(expressRecordList)) {
                    for (WhPurchaseExpressRecord expressRecord : expressRecordList) {
                        WhPurchaseExpressRecord updateExpressRecord = new WhPurchaseExpressRecord();
                        updateExpressRecord.setId(expressRecord.getId());
                        // 添加采购单号
                        updateExpressRecord.setPurchaseOrderNo(entity.getPurchaseOrderNo());
                        whPurchaseExpressRecordDao.updateWhPurchaseExpressRecord(updateExpressRecord);
                        logger.info("update WhPurchaseExpressRecord: " + updateExpressRecord);
                    }
                }
                else {
                    whPurchaseExpressRecordDao.createWhPurchaseExpressRecord(entity);
                    logger.info("create WhPurchaseExpressRecord: " + entity);
                }
            }
            catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void updatePurchaseOrderNo(List<PurchaseOrder> purchaseOrders, String expressId) {
        // TODO 批量更新采购单号，快递单为空时全部更新 空的采购单 为一个采购单的问题
        if (StringUtils.isBlank(expressId)) {
            return;
        }
        // 统计重量
        Double totalWeight = 0d;
        List<String> purchaseOrderNos = new ArrayList<>();
        for (PurchaseOrder purchaseOrder : purchaseOrders) {
            if (!purchaseOrderNos.contains(purchaseOrder.getPurchaseOrderNo())) {
                totalWeight += purchaseOrder.getTotalWeight() == null ? 0 : purchaseOrder.getTotalWeight();
                purchaseOrderNos.add(purchaseOrder.getPurchaseOrderNo());
            }
        }
        totalWeight = totalWeight / 1000;// 转换成KG
        String purchaseOrderNoArr = StringUtils.join(purchaseOrderNos, ",");

        // 更新采购单号
        WhPurchaseExpressRecordQueryCondition query = new WhPurchaseExpressRecordQueryCondition();
        query.setTrackingNumber(expressId);
        List<WhPurchaseExpressRecord> expressRecordList = whPurchaseExpressRecordDao
                .queryWhPurchaseExpressRecordList(query, null);
        if (CollectionUtils.isNotEmpty(expressRecordList)) {
            for (WhPurchaseExpressRecord expressRecord : expressRecordList) {
                if (StringUtils.isBlank(expressRecord.getPurchaseOrderNo())) {
                    WhPurchaseExpressRecord updateExpressRecord = new WhPurchaseExpressRecord();
                    updateExpressRecord.setId(expressRecord.getId());
                    updateExpressRecord.setPurchaseOrderNo(purchaseOrderNoArr);
                    updateExpressRecord.setBoxNo(expressRecord.getBoxNo());
                    updateExpressRecord.setReceiveUser(expressRecord.getReceiveUser());
                    updateExpressRecord.setReceiveDate(expressRecord.getReceiveDate());
                    updateExpressRecord.setTotalWeight(totalWeight > 0 ? totalWeight : null);
                    whPurchaseExpressRecordDao.updateWhPurchaseExpressRecord(updateExpressRecord);
                    EXPRESSRECORDLOG.log(expressRecord.getId(), CheckInLogType.PURCHASE_RECEIVED.getName(),
                            new String[][] { { "快递单号", expressId }, { "采购单号", purchaseOrderNoArr } });
                    logger.info("update WhPurchaseExpressRecord: " + updateExpressRecord);
                }
            }

            // 反向查询采购单列表，修改采购单，快递单状态
            WhPurchaseOrderQueryCondition purchaseOrderQuery = new WhPurchaseOrderQueryCondition();
            purchaseOrderQuery.setPurchaseOrderNoStr(purchaseOrderNoArr);
            purchaseOrderQuery.setQueryExpress(true);
            List<WhPurchaseOrder> whPurchaseOrderList = whPurchaseOrderService
                    .queryWhPurchaseExpressOrderAndItemList(purchaseOrderQuery, null);
            if (CollectionUtils.isNotEmpty(whPurchaseOrderList)) {
                for (WhPurchaseOrder purchaseOrder : whPurchaseOrderList) {
                    if (purchaseOrder.getPurchaseStatus() == null
                            || WmsPurchaseOrderStatus.WAIT_STOCK_IN.intCode() > purchaseOrder.getPurchaseStatus()) {
                        // 修改采购单状态,待入库
                        updatePurchaseOrderPurchaseStatus(purchaseOrder.getPurchaseOrderNo(),
                                WmsPurchaseOrderStatus.WAIT_STOCK_IN.intCode());
                    }

                    for (WhPurchaseToExpress purchaseToExpress : purchaseOrder.getPurchaseToExpress()) {
                        String remark = "";
                        for (WhPurchaseExpressRecord expressRecord : expressRecordList) {
                            if (expressRecord.getTrackingNumber().equals(purchaseToExpress.getExpressId())) {
                                remark = expressRecord.getComment();
                                break;
                            }
                        }
                        if (expressId.equals(purchaseToExpress.getExpressId()) && StringUtils.isNotBlank(remark)) {
                            // 修改快递单状态,有备注时改为已处理
                            updatePurchaseExpressStatus(purchaseOrder.getPurchaseOrderNo(), expressId,
                                    PurchaseExpressStatus.PROCESSED.intCode());
                        }
                        else if (expressId.equals(purchaseToExpress.getExpressId())
                                && (purchaseToExpress.getExpressStatus() == null
                                        || PurchaseExpressStatus.TO_BE_PROCESSED.intCode() > purchaseToExpress
                                                .getExpressStatus())) {
                            // 修改快递单状态,待处理
                            updatePurchaseExpressStatus(purchaseOrder.getPurchaseOrderNo(), expressId,
                                    PurchaseExpressStatus.TO_BE_PROCESSED.intCode());
                        }

                    }

                }
            }
        }
    }

    @Override
    public int queryWhPurchaseExpressRecordCount(WhPurchaseExpressRecordQueryCondition query) {
        return whPurchaseExpressRecordDao.queryWhPurchaseExpressRecordCount(query);
    }

    @Override
    public ResponseJson pdaBindExpressNo(AndroidProductDo domain) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.FAIL);
        WhBoxQueryCondition query = new WhBoxQueryCondition();
        query.setBoxNo(domain.getBoxNo());
        WhBox whBox = whBoxService.queryWhBox(query);
        if (null == whBox) {
            responseJson.setExceptionCode("0");
            responseJson.setMessage("当前周转框不存在！");
            return responseJson;
        }
        String expressId = domain.getExpressNo();
        // 兼容入库异常框，如果扫描的是异常周转码，记录日志
        if (StringUtils.isNotBlank(expressId) && expressId.contains("RKYC")) {
            try {
                if (updateExceptionAndSaveHandleLog(expressId, domain.getBoxNo(),whBox.getType())) {
                    responseJson.setStatus(StatusCode.SUCCESS);
                }
                else {
                    responseJson.setMessage("找不到待入库、入库中对应的异常单！");
                }
            }catch (Exception e){
                responseJson.setMessage("绑定失败！"+e.getMessage());

            }
        }
        else if (!expressId.contains("RKYC")) {

            // 如果快递单存在未匹配采购单类型的异常单，且异常单状态为草稿或待采购处理的，
            // 提示：快递单关联的“未匹配采购单”类型不为待仓库处理，请先处理入库异常单。
            WhCheckInExceptionQueryCondition exceptionQuery = new WhCheckInExceptionQueryCondition();
            exceptionQuery.setExceptionType(ExceptionType.NO_PURCHASE_ORDER_MATCH.intCode().toString());
            exceptionQuery.setTrackingNumber(expressId);
            List<Integer> statusList = new ArrayList<Integer>();
            if (ExceptionStatus.values().length > 0) {
                for (int i = 0; i < ExceptionStatus.values().length; i++) {
                    if (ExceptionStatus.PURCHASE_PENDING.intCode().equals(ExceptionStatus.values()[i].intCode())
                            || ExceptionStatus.DEVELOP_CONFIRM.intCode().equals(ExceptionStatus.values()[i].intCode())
                            || ExceptionStatus.UNCONFIRM.intCode().equals(ExceptionStatus.values()[i].intCode())) {
                        statusList.add(ExceptionStatus.values()[i].intCode());
                    }
                }
            }
            exceptionQuery.setStatusList(statusList);
            List<WhCheckInException> whCheckInExceptionList = whCheckInExceptionService
                    .queryWhCheckInExceptions(exceptionQuery, null);
            if (CollectionUtils.isNotEmpty(whCheckInExceptionList)) {
                responseJson.setMessage("快递单关联的“未匹配采购单”类型不为待仓库处理，请先处理入库异常单！");
                return responseJson;
            }

            WhPurchaseExpressRecordQueryCondition queryCondition = new WhPurchaseExpressRecordQueryCondition();
            queryCondition.setTrackingNumber(expressId);
            WhPurchaseExpressRecord whPurchaseExpressRecord = queryWhPurchaseExpressRecord(queryCondition);
            if (whPurchaseExpressRecord != null
                    && StringUtils.isNotBlank(whPurchaseExpressRecord.getPurchaseOrderNo())) {

                // 判断是否是物流收货
                boolean isExpress = Objects.nonNull(whPurchaseExpressRecord.getSerialNumber());

                // 根据快递单号查询是否绑定周转框
                WhBoxItemQueryCondition itemQuery = new WhBoxItemQueryCondition();
                itemQuery.setRelationNo(whPurchaseExpressRecord.getId().toString());
                itemQuery.setOrderType(ItemOrderTypeEnum.EXPRESS_RECEIPT.intCode());
                WhBoxItem whBoxItem = whBoxItemService.queryWhBoxItem(itemQuery);
                if (Objects.isNull(whBoxItem)){
                    itemQuery.setRelationNo(whPurchaseExpressRecord.getId().toString());
                    itemQuery.setOrderType(ItemOrderTypeEnum.WL_RECEIPT.intCode());
                    whBoxItem = whBoxItemService.queryWhBoxItem(itemQuery);
                }

                if (PurchaseExpressRecordStatus.REJECT.intCode().equals(whPurchaseExpressRecord.getStatus())) {
                    responseJson.setMessage("拒收单！");
                    return responseJson;
                }

                if (whBoxItem != null && domain.getBindNewBoxNo() != null && domain.getBindNewBoxNo() != 1
                        && whBoxItem.getBoxNo().equals(domain.getBoxNo())) {
                    responseJson.setMessage("快递单:" + domain.getExpressNo() + "与周转框：" + domain.getBoxNo() + "已存在绑定关系");
                    responseJson.setExceptionCode("0");
                    return responseJson;
                }

                WhPurchaseOrderQueryCondition purchaseOrderQuery = new WhPurchaseOrderQueryCondition();
                purchaseOrderQuery.setTrackingNumber(expressId);
                List<WhPurchaseOrder> orders = whPurchaseOrderService.queryWhPurchaseOrderAndItems(purchaseOrderQuery, null);
                List<PurchaseOrder> purchaseOrders = TranslateWhPurchaseOrderToPurchaseOrderUtils
                        .translateWhPurchaseOrderToPurchaseOrder(orders);

                int existTj = Optional.ofNullable(purchaseOrders).orElse(new ArrayList<>()).stream()
                        .filter(p -> p.getFlagsName().indexOf("特急") >= 0).collect(Collectors.toList()).size();
                if (existTj > 0 && !domain.getBoxNo().substring(0,3).equalsIgnoreCase(BoxType.RECEIVE_TJ.getShortCode()) && !isExpress){
                    responseJson.setMessage("特急包裹，请绑定特急收货周转筐!");
                    return responseJson;
                }

                if (existTj == 0 && domain.getBoxNo().substring(0,3).equalsIgnoreCase(BoxType.RECEIVE_TJ.getShortCode()) && !isExpress){
                    responseJson.setMessage("普通包裹，请绑定普通收货周转筐收货!");
                    return responseJson;
                }


                if (whBoxItem != null && domain.getBindNewBoxNo() != null && domain.getBindNewBoxNo() != 1
                        && !whBoxItem.getBoxNo().equals(domain.getBoxNo())) {
                    responseJson.setMessage("该件绑定收货周转框：" + whBoxItem.getBoxNo() + "，确定绑定新的周转框");
                    responseJson.setExceptionCode("1");
                    return responseJson;
                }

                int existNoLabel = (int) Optional.ofNullable(purchaseOrders).orElse(new ArrayList<>()).stream()
                        .filter(p -> StringUtils.contains(p.getFlagsName(), PurchaseOrderFlags.NO_LABEL.getName())).count();


                if (existNoLabel > 0 && !BoxType.PF_ORDER.intCode().equals(whBox.getType()) && !isExpress){
                    responseJson.setMessage("不贴标入库包裹，请绑定批发订单周转筐!");
                    return responseJson;
                }

                if (whBoxItem == null
                        || (whBoxItem != null && domain.getBindNewBoxNo() != null && domain.getBindNewBoxNo() == 1)) {
                    int orderType = ItemOrderTypeEnum.EXPRESS_RECEIPT.intCode();
                    if (isExpress){
                        orderType = ItemOrderTypeEnum.WL_RECEIPT.intCode();
                    }
                    // 如果该快递单有绑定的周转框，先解绑原来的周转框
                    if (whBoxItem != null) {
                        whBoxService.unbindItem(whBoxItem.getBoxNo(), whPurchaseExpressRecord.getId().toString(), orderType, false);
                    }
                    // 绑定新的周转框
                    whBoxItem = new WhBoxItem();
                    whBoxItem.setBoxNo(domain.getBoxNo());
                    whBoxItem.setRelationNo(whPurchaseExpressRecord.getId().toString());
                    whBoxItem.setOrderType(orderType);
                    whBoxItemService.createWhBoxItem(whBoxItem);
                    // 修改周转框使用状态
                    whBoxService.updateStatus(domain.getBoxNo(), true);
                    // 收货单添加领取人和领取时间
                    WhPurchaseExpressRecord expressRecord = new WhPurchaseExpressRecord();
                    expressRecord.setId(whPurchaseExpressRecord.getId());
                    expressRecord.setBoxNo(domain.getBoxNo());
                    expressRecord.setReceiveUser(DataContextHolder.getUserId());
                    expressRecord.setReceiveDate(new Timestamp(System.currentTimeMillis()));
                    updateWhPurchaseExpressRecord(expressRecord);

                    String tips = "快递单号";
                    if (isExpress){
                        tips = "物流单号";
                    }

                    EXPRESSRECORDLOG.log(whPurchaseExpressRecord.getId(),
                            CheckInLogType.PDA_BIND_RECEIVE_BOX_NO.getName(),
                            new String[][] { { tips, domain.getExpressNo() }, { "周转框", domain.getBoxNo() } });

                    if (whBox != null) {
                        SystemLogUtils.BOXLOG.log(whBox.getId(), CheckInLogType.PDA_BIND_RECEIVE_BOX_NO.getName(),
                                new String[][] { { tips, domain.getExpressNo() } });
                    }
                    responseJson.setStatus(StatusCode.SUCCESS);
                }
            }
            else {
                responseJson.setExceptionCode("0");
                responseJson.setMessage("快递单号无效");
            }
        }

        return responseJson;
    }

    /**
     * 异常单绑定周转框时增加异常单日志
     *
     * @param expressId
     * @param boxNo
     */
    @Override
    public boolean updateExceptionAndSaveHandleLog(String expressId, String boxNo,Integer type) {
        WhBox exceptionWhBox = whBoxService.queryWhBoxByBoxNo(expressId);
        if (exceptionWhBox != null && StringUtils.isNotBlank(exceptionWhBox.getRelationNo())) {
            String relationNo = exceptionWhBox.getRelationNo();
            WhCheckInException exception = whCheckInExceptionService.getWhCheckInException(Integer.valueOf(relationNo));

            if (exception==null) {
                return false;
            }

            if (type != null) {
                if (StringUtils.isNotBlank(exception.getFlagName()) && StringUtils.contains(exception.getFlagName(), "不贴标入库") && !BoxType.PF_ORDER.intCode().equals(type)) {
                    throw new BusinessException("不贴标入库异常单,请绑定批发收货周转筐!");
                }
                if (StringUtils.isNotBlank(exception.getFlagName()) && StringUtils.contains(exception.getFlagName(), "特急") && !BoxType.RECEIVE_TJ.intCode().equals(type)) {
                    throw new BusinessException("特急异常单,请绑定特急收货周转筐!");
                }
                if (BoxType.PF_ORDER.intCode().equals(type) && (StringUtils.isBlank(exception.getFlagName()) || !StringUtils.contains(exception.getFlagName(), "不贴标入库"))) {
                    throw new BusinessException("批发收货周转筐，请绑定不贴标入库异常单!");
                }
                if (BoxType.RECEIVE_TJ.intCode().equals(type) && (StringUtils.isBlank(exception.getFlagName()) || !StringUtils.contains(exception.getFlagName(), "特急"))) {
                    throw new BusinessException("特急收货周转筐，请绑定特急异常单!");
                }
            }
            if (ExceptionStatus.WAIT_CHECK_IN.intCode().equals(exception.getStatus())
                                    || ExceptionStatus.STOCK_IN_ING.intCode().equals(exception.getStatus())) {

                WhCheckInException updateException = new WhCheckInException();
                updateException.setId(exception.getId());
                updateException.setReceiveBoxNo(boxNo);
                updateException.setStatus(ExceptionStatus.STOCK_IN_ING.intCode());
                updateException.setDoingCheckInDate(new Timestamp(System.currentTimeMillis()));
                whCheckInExceptionService.updateWhCheckInException(updateException);

                WhCheckInExceptionHandle whCheckInExceptionHandle = new WhCheckInExceptionHandle();
                if (StringUtils.isBlank(boxNo)) {
                    whCheckInExceptionHandle.setHandleComment(
                            CheckInLogType.UNBIND_RECEIVE_BOX_NO.getName() + "  ,周转框：" + exception.getReceiveBoxNo());
                }
                else {
                    whCheckInExceptionHandle
                            .setHandleComment(CheckInLogType.PDA_BIND_RECEIVE_BOX_NO.getName() + "  ,周转框：" + boxNo);
                    whCheckInExceptionHandle.setStatus(ExceptionStatus.STOCK_IN_ING.intCode());
                    // 异常入库单ID绑定收货周转筐
                    WhBoxItemQueryCondition itemQuery = new WhBoxItemQueryCondition();
                    itemQuery.setRelationNo(relationNo);
                    itemQuery.setOrderType(ItemOrderTypeEnum.WH_CHECK_IN_EXCEPTION.intCode());
                    WhBoxItem whBoxItem = whBoxItemService.queryWhBoxItem(itemQuery);
                    // 如果该快递单有绑定的周转框，先解绑原来的周转框
                    if (Objects.nonNull(whBoxItem)){
                        whBoxService.unbindItem(whBoxItem.getBoxNo(), relationNo,ItemOrderTypeEnum.WH_CHECK_IN_EXCEPTION.intCode(), false);
                    }
                    whBoxItem = new WhBoxItem();
                    whBoxItem.setBoxNo(boxNo);
                    whBoxItem.setRelationNo(relationNo);
                    whBoxItem.setOrderType(ItemOrderTypeEnum.WH_CHECK_IN_EXCEPTION.intCode());
                    whBoxItemService.createWhBoxItem(whBoxItem);
                    // 修改周转框使用状态
                    whBoxService.updateStatus(boxNo, true);
                }
                whCheckInExceptionHandle.setExceptionId(exception.getId());
                whCheckInExceptionHandle.setCreatedBy(DataContextHolder.getUserId());
                whCheckInExceptionHandle.setCreationDate(new Timestamp(System.currentTimeMillis()));
                whCheckInExceptionHandleService.createWhCheckInExceptionHandle(whCheckInExceptionHandle);
                if(StringUtils.isNotBlank(boxNo)){
                    // 推送至采购
                    exception.setReceiveBoxNo(boxNo);
                    exception.setStatus(ExceptionStatus.STOCK_IN_ING.intCode());
                    logger.info("start to send saveWhCheckException message to pms ====exception:"
                            + exception.toString() + "===exceptionHandle:" + whCheckInExceptionHandle);
                    rabbitmqProducerService.pushCheckInExceptionMsgToPms(exception, whCheckInExceptionHandle, new PushCheckInException());
                    logger.info("send saveWhCheckException message to pms end ");
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 批量拒收
     *
     * @param ids
     * @param rejectComment
     * @return
     */
    @Override
    public ResponseJson batchRejectExpressRecord(List<Integer> ids, String rejectComment) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        WhPurchaseExpressRecordQueryCondition query = new WhPurchaseExpressRecordQueryCondition();
        query.setIds(ids);
        List<WhPurchaseExpressRecord> whPurchaseExpressRecordList = queryWhPurchaseExpressRecords(query, null);
        if (CollectionUtils.isEmpty(whPurchaseExpressRecordList)) {
            response.setMessage("没有找到相应的收货单！");
            return response;
        }
        List<WhPurchaseExpressRecord> updateList = new ArrayList<WhPurchaseExpressRecord>();
        for (WhPurchaseExpressRecord record : whPurchaseExpressRecordList) {
            if (record.getCheckInScanStatus() != null && record.getCheckInScanStatus()) {
                response.setMessage("已扫描的收货单不能拒收！");
                return response;
            }
            if (PurchaseExpressRecordStatus.REJECT.intCode().equals(record.getStatus())) {
                response.setMessage("已拒收的收货单不能重复拒收！");
                return response;
            }
            WhPurchaseExpressRecord updateRecord = new WhPurchaseExpressRecord();
            updateRecord.setId(record.getId());
            updateRecord.setComment(rejectComment);
            updateRecord.setStatus(PurchaseExpressRecordStatus.REJECT.intCode());
            updateRecord.setBoxNo(record.getBoxNo());
            updateRecord.setReceiveUser(record.getReceiveUser());
            updateRecord.setReceiveDate(record.getReceiveDate());
            updateList.add(updateRecord);
            String[] purchaseOrderNoArr = StringUtils.split(record.getPurchaseOrderNo(), ",");
            for (int i = 0; i < purchaseOrderNoArr.length; i++) {
                // 修改快递单状态,已处理
                updatePurchaseExpressStatus(purchaseOrderNoArr[i], record.getTrackingNumber(),
                        PurchaseExpressStatus.PROCESSED.intCode());
                // 修改采购单状态,待入库
                updatePurchaseOrderPurchaseStatus(purchaseOrderNoArr[i],
                        WmsPurchaseOrderStatus.WAIT_STOCK_IN.intCode());
            }
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            // 批量更新收货单状态
            batchUpdateWhPurchaseExpressRecord(updateList);
            updateList.forEach(record -> {
                EXPRESSRECORDLOG.log(record.getId(), PurchaseExpressRecordStatus.REJECT.getName(),
                        new String[][] { { "拒收人", DataContextHolder.getUsername() } });
                // 解绑收货框
                if (StringUtils.isNotBlank(record.getBoxNo())) {
                    whBoxService.unbindItem(record.getBoxNo(), record.getId().toString(),ItemOrderTypeEnum.EXPRESS_RECEIPT.intCode(), true);
                }
            });
            response.setStatus(StatusCode.SUCCESS);
        }
        return response;
    }

    @Override
    public String receiveExpressRecord(WhPurchaseExpressRecordDo domain, String expressId, Integer warehouseId,
            Double weight, Integer quantity, String boxNo) {
        // 先校验是否有签收记录
        WhPurchaseExpressRecordQueryCondition query = new WhPurchaseExpressRecordQueryCondition();
        query.setTrackingNumber(expressId);
        List<WhPurchaseExpressRecord> records = queryWhPurchaseExpressRecords(query, null);
        if (CollectionUtils.isNotEmpty(records)) {
            domain.setHasExpressRecord(true);
            domain.setWhPurchaseExpressRecord(records.get(0));
            logger.info("hasExpressRecord 已经有收件记录: expressId[" + expressId + "]");
            return "checkin/receive_express_scan";// 有签收记录不能重复签收
        }

         WhPurchaseOrderQueryCondition queryCondition = new WhPurchaseOrderQueryCondition();
         queryCondition.setTrackingNumber(expressId);
         List<WhPurchaseOrder> orders = whPurchaseOrderService.queryWhPurchaseOrderAndItems(queryCondition, null);
         List<PurchaseOrder> purchaseOrders = TranslateWhPurchaseOrderToPurchaseOrderUtils
                 .translateWhPurchaseOrderToPurchaseOrder(orders);

        // List<PurchaseOrder> purchaseOrders = pmsCheckInService.queryPurchaseOrdersByExpressIdOrPurchaseOrderNoToCheckIn(expressId);// 签收接口

        if (CollectionUtils.isNotEmpty(purchaseOrders) && purchaseOrders.get(0).getIsTransportOrder() != null && purchaseOrders.get(0).getIsTransportOrder()) {
            Map<String, Object> validateMap = new HashMap<>();
            validateMap.put("isError", true);
            validateMap.put("errorMsg", "请在物流收货签收!");
            domain.setValidateMap(validateMap);
            return "checkin/receive_express_scan";
        }

        if (CollectionUtils.isNotEmpty(purchaseOrders) && StringUtils
                .equalsIgnoreCase(PurchaseOrderType.NCGHC.getCode(), purchaseOrders.get(0).getPurchaseOrderType())) {
            Map<String, Object> validateMap = new HashMap<>();
            validateMap.put("isError", true);
            validateMap.put("errorMsg", "耗材订单直接在耗材入库页面入库，不用收货!");
            domain.setValidateMap(validateMap);
            return "checkin/receive_express_scan";
        }
        
        int existTj = Optional.ofNullable(purchaseOrders).orElse(new ArrayList<>()).stream()
                .filter(p -> p.getFlagsName().indexOf("特急") >= 0).collect(Collectors.toList()).size();
        if (existTj > 0 && !boxNo.substring(0,3).equalsIgnoreCase(BoxType.RECEIVE_TJ.getShortCode())){
            Map<String, Object> validateMap = new HashMap<>();
            validateMap.put("isError", true);
            validateMap.put("errorMsg", "特急包裹，请使用特急收货周转筐收货!");
            domain.setValidateMap(validateMap);
            return "checkin/receive_express_scan";
        }

        domain.setPurchaseOrders(purchaseOrders);
        if (CollectionUtils.isEmpty(purchaseOrders)) {
            // 没有采购单，不更新
            List<WhPurchaseOrder> otherPurchases = getOtherWarehousePurchaseOrders(queryCondition.getTrackingNumber());
            if (CollectionUtils.isNotEmpty(otherPurchases)) {
                // 非本仓包裹 保存扫描记录
                WhPurchaseExpressRecord expressRecord = new WhPurchaseExpressRecord();
                expressRecord.setTrackingNumber(expressId);
                expressRecord.setWarehouseId(warehouseId);
                expressRecord.setWeight(weight);
                expressRecord.setQuantity(quantity);
                if (domain.getQuery() != null && StringUtils.isNotEmpty(domain.getQuery().getShippingCpn()))
                    expressRecord.setShippingCpn(domain.getQuery().getShippingCpn());
                expressRecord.setScanMark(1);
                createWhPurchaseExpressRecord(expressRecord);
                domain.setValidateMap(Map.of("isError", true, "errorMsg", "非本仓包裹，请拿出单独处理"));
                return "checkin/receive_express_scan";
            }
            return "checkin/receive_express_scan";
        }
        // 判断sku是否含有 shopify 标签

        if (CollectionUtils.isNotEmpty(purchaseOrders)) {
            //匹配缺货订单
            boolean matchedOrder = false;
            Future<Boolean> resultFuture = outStockMatchHandelService.matchApv(orders, null);
            try {
                List<String> purchaseOrderNos = orders.stream().map(WhPurchaseOrder::getPurchaseOrderNo).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(purchaseOrderNos) && resultFuture.get()){
                    Set<String> skuSet = orders.stream()
                            .map(WhPurchaseOrder::getItems)
                            .flatMap(Collection::stream)
                            .map(WhPurchaseItem::getSku)
                            .collect(Collectors.toSet());
                    PurchaseApvOutStockMatchQueryCondition matchedQuery = new PurchaseApvOutStockMatchQueryCondition();
                    matchedQuery.setSkuList(new ArrayList<>(skuSet));
                    matchedQuery.setStatus(OutStockMatchStatus.MATCHED.intCode());
                    matchedQuery.setPurchaseOrderNoList(purchaseOrderNos);
                    List<PurchaseApvOutStockMatch> matchedOrderList = outStockMatchService.queryPurchaseApvOutStockMatchs(matchedQuery, null);
                    if (CollectionUtils.isNotEmpty(matchedOrderList)){
                        matchedOrder = true;
                        if(!boxNo.substring(0,3).equalsIgnoreCase(BoxType.RECEIVE_TJ.getShortCode())) {
                            Map<String, Object> validateMap = new HashMap<>();
                            validateMap.put("isError", true);
                            validateMap.put("errorMsg", "包含不入库直发SKU，请使用特急收货周转筐收货!");
                            domain.setValidateMap(validateMap);
                            return "checkin/receive_express_scan";
                        }
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            if (existTj == 0 && !matchedOrder && boxNo.substring(0,3).equalsIgnoreCase(BoxType.RECEIVE_TJ.getShortCode())){
                Map<String, Object> validateMap = new HashMap<>();
                validateMap.put("isError", true);
                validateMap.put("errorMsg", "普通包裹，请使用普通收货周转筐收货!");
                domain.setValidateMap(validateMap);
                return "checkin/receive_express_scan";
            }

            // 先插入数据再调接口,防止并发生成多条数据
            WhPurchaseExpressRecord expressRecord = new WhPurchaseExpressRecord();
            expressRecord.setTrackingNumber(expressId);
            expressRecord.setWarehouseId(warehouseId);
            expressRecord.setWeight(weight);
            expressRecord.setQuantity(quantity);
            if (domain.getQuery() != null && StringUtils.isNotEmpty(domain.getQuery().getShippingCpn()))
                expressRecord.setShippingCpn(domain.getQuery().getShippingCpn());
            createWhPurchaseExpressRecord(expressRecord);
            logger.info("create WhPurchaseExpressRecord: " + expressRecord);

            // 统计重量
            Double totalWeight = 0d;
            List<String> purchaseOrderNos = new ArrayList<>();
            String flagsName = "";
            for (PurchaseOrder purchaseOrder : purchaseOrders) {
                if (!purchaseOrderNos.contains(purchaseOrder.getPurchaseOrderNo())) {
                    totalWeight += purchaseOrder.getTotalWeight() == null ? 0 : purchaseOrder.getTotalWeight();
                    purchaseOrderNos.add(purchaseOrder.getPurchaseOrderNo());
                    // 修改快递单状态,待处理
                    updatePurchaseExpressStatus(purchaseOrder.getPurchaseOrderNo(), expressId,
                            PurchaseExpressStatus.TO_BE_PROCESSED.intCode());
                    // 修改采购单状态,待入库
                    updatePurchaseOrderPurchaseStatus(purchaseOrder.getPurchaseOrderNo(),
                            WmsPurchaseOrderStatus.WAIT_STOCK_IN.intCode());
                }
                // 特急情况下，标记sku是否s包含shopify
                try {
                    if (StringUtils.isNotBlank(purchaseOrder.getFlagsName())
                            && purchaseOrder.getFlagsName().indexOf("特急")>0
                                && CollectionUtils.isNotEmpty(purchaseOrder.getPurchaseOrderItems())){
                        boolean bool = purchaseOrder.getPurchaseOrderItems().stream().anyMatch(item -> StringUtils.isNotBlank(item.getPurchaseSkuSpecialGoods())
                                && StringUtils.contains(item.getPurchaseSkuSpecialGoods(), WhSkuSpecialGoods.SPECIAL_TYPE_CODE));
                        if (bool && !domain.isHasShopifyTJSku()){
                            domain.setHasShopifyTJSku(bool);
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                if (StringUtils.isNotBlank(purchaseOrder.getFlagsName()))
                    flagsName = flagsName + "," + purchaseOrder.getFlagsName();
            }
            totalWeight = totalWeight / 1000;// 转换成KG
            String purchaseOrderNoArr = StringUtils.join(purchaseOrderNos, ",");

            boolean noLabel = StringUtils.isNotBlank(flagsName) && StringUtils.contains(flagsName, PurchaseOrderFlags.NO_LABEL.getName());
            // 关联采购信息
            if (!noLabel){
                expressRecord.setBoxNo(boxNo);
            }
            expressRecord.setReceiveUser(DataContextHolder.getUserId());
            expressRecord.setReceiveDate(new Timestamp(System.currentTimeMillis()));
            expressRecord.setPurchaseOrderNo(purchaseOrderNoArr);
            expressRecord.setTotalWeight(totalWeight > 0 ? totalWeight : null);
            updateWhPurchaseExpressRecord(expressRecord);

            //不贴标需要绑定批发收货周转筐
            // 关联采购信息后才绑定周转框
            if (!noLabel) {
                WhBoxItem entity = new WhBoxItem();
                entity.setBoxNo(boxNo);
                entity.setRelationNo(expressRecord.getId().toString());
                entity.setOrderType(ItemOrderTypeEnum.EXPRESS_RECEIPT.intCode());
                whBoxItemService.createWhBoxItem(entity);
                if (expressRecord.getId() != null) {
                    EXPRESSRECORDLOG.log(expressRecord.getId(), CheckInLogType.RECEIVE_EXPRESS.getName(),
                            new String[][] { { "快递单号", expressId }, { "绑定周转框", boxNo } });
                }
            }

            EXPRESSRECORDLOG.log(expressRecord.getId(), CheckInLogType.PURCHASE_RECEIVED.getName(),
                    new String[][] { { "快递单号", expressId }, { "采购单号", purchaseOrderNoArr } });
            logger.info("update WhPurchaseExpressRecord: " + expressRecord);
            domain.setWhPurchaseExpressRecord(expressRecord);

            if (CollectionUtils.isNotEmpty(purchaseOrderNos)) {
                expressRecord.setCreatedBy(DataContextHolder.getUserId());
                expressRecord.setCreationDate(new Timestamp(System.currentTimeMillis()));
                purchaseOrderNos.forEach(purchaseOrderNo -> this.createPurchaseExpressAmqMessage(expressRecord, purchaseOrderNo));
            }

        }
        return "checkin/receive_express_scan";
    }

    @Override
    public String receiveExpressRecordLog(WhPurchaseExpressRecordDo domain, String expressId, Double weight, Integer quantity, String boxNo) {

        // 先插入数据再调接口,防止并发生成多条数据
        WhPurchaseExpressRecord expressRecord = new WhPurchaseExpressRecord();
        expressRecord.setTrackingNumber(expressId);
        expressRecord.setSerialNumber(1);
        expressRecord.setWarehouseId(CacheUtils.getLocalWarehouseId());
        expressRecord.setWeight(weight);
        expressRecord.setQuantity(quantity);
        if (domain.getQuery() != null && StringUtils.isNotEmpty(domain.getQuery().getShippingCpn()))
            expressRecord.setShippingCpn(domain.getQuery().getShippingCpn());
        createWhPurchaseExpressRecord(expressRecord);
        logger.info("create WhPurchaseExpressRecord: " + expressRecord);

        // 扫描之后重新刷新缓存
        String data = StringRedisUtils.get(StringRedisUtils.CHECK_SCAN_LOG_KEY + DataContextHolder.getOperationId());
        if (StringUtils.isNotBlank(data)) {
            List<String> expressIds = JSON.parseArray(data, String.class);
            if (CollectionUtils.isNotEmpty(expressIds)) {
                expressIds.remove(expressId);
                StringRedisUtils.set(StringRedisUtils.CHECK_SCAN_LOG_KEY
                        + DataContextHolder.getOperationId(), JSON.toJSONString(expressIds));
            } else {
                StringRedisUtils.del(StringRedisUtils.CHECK_SCAN_LOG_KEY + DataContextHolder.getOperationId());
            }
        }

        String oldExpressId = null;
        if (expressId.indexOf("=") > 0) {
            oldExpressId = expressId.split("=")[0];
        }

        WhPurchaseOrderQueryCondition queryCondition = new WhPurchaseOrderQueryCondition();
        queryCondition.setTrackingNumber(expressId);
        if (StringUtils.isNotBlank(oldExpressId)) {
            // 物流子单号截取
            queryCondition.setTrackingNumber(oldExpressId);
        }
        List<WhPurchaseOrder> orders = whPurchaseOrderService.queryWhPurchaseOrderAndItems(queryCondition, null);
        List<PurchaseOrder> purchaseOrders = TranslateWhPurchaseOrderToPurchaseOrderUtils
                .translateWhPurchaseOrderToPurchaseOrder(orders);

        domain.setPurchaseOrders(purchaseOrders);
        if (CollectionUtils.isEmpty(purchaseOrders)) {
            // 没有采购单 查询其它仓库的采购单
            List<WhPurchaseOrder> otherPurchases = getOtherWarehousePurchaseOrders(queryCondition.getTrackingNumber());
            if (CollectionUtils.isNotEmpty(otherPurchases)) {
                expressRecord.setScanMark(1);
                updateWhPurchaseExpressRecord(expressRecord);
                domain.setValidateMap(Map.of("isError", true, "errorMsg", "非本仓包裹，请拿出单独处理"));
                return "checkin/receive_express_scan";
            }
            return "checkin/receive_express_scan";
        }

        if (CollectionUtils.isNotEmpty(purchaseOrders)) {
            // 统计重量
            Double totalWeight = 0d;
            List<String> purchaseOrderNos = new ArrayList<>();
            for (PurchaseOrder purchaseOrder : purchaseOrders) {
                if (!purchaseOrderNos.contains(purchaseOrder.getPurchaseOrderNo())) {
                    totalWeight += purchaseOrder.getTotalWeight() == null ? 0 : purchaseOrder.getTotalWeight();
                    purchaseOrderNos.add(purchaseOrder.getPurchaseOrderNo());
                    // 修改快递单状态,待处理
                    updatePurchaseExpressStatus(purchaseOrder.getPurchaseOrderNo(), expressId,
                            PurchaseExpressStatus.TO_BE_PROCESSED.intCode());
                    // 修改采购单状态,待入库
                    updatePurchaseOrderPurchaseStatus(purchaseOrder.getPurchaseOrderNo(),
                            WmsPurchaseOrderStatus.WAIT_STOCK_IN.intCode());
                }
                // 特急情况下，标记sku是否s包含shopify
                if (StringUtils.isNotBlank(purchaseOrder.getFlagsName())
                        && purchaseOrder.getFlagsName().indexOf("特急")>0
                        && CollectionUtils.isNotEmpty(purchaseOrder.getPurchaseOrderItems())){
                    boolean bool = purchaseOrder.getPurchaseOrderItems().stream().anyMatch(item -> StringUtils.isNotBlank(item.getPurchaseSkuSpecialGoods())
                            && StringUtils.contains(item.getPurchaseSkuSpecialGoods(), WhSkuSpecialGoods.SPECIAL_TYPE_CODE));
                    if (bool){
                        domain.setHasShopifyTJSku(bool);
                    }
                }
            }
            totalWeight = totalWeight / 1000;// 转换成KG
            String purchaseOrderNoArr = StringUtils.join(purchaseOrderNos, ",");

            // 关联采购信息
            expressRecord.setReceiveUser(DataContextHolder.getUserId());
            expressRecord.setReceiveDate(new Timestamp(System.currentTimeMillis()));
            expressRecord.setPurchaseOrderNo(purchaseOrderNoArr);
            expressRecord.setTotalWeight(totalWeight > 0 ? totalWeight : null);
            expressRecord.setBoxNo(boxNo);
            updateWhPurchaseExpressRecord(expressRecord);

            // 如果提供了周转筐号，处理绑定关系
            if (StringUtils.isNotBlank(boxNo)) {
                // 绑定物流单号到周转筐
                bindLogisticsToBox(expressRecord.getId(), boxNo, quantity);
                // 设置周转筐为已使用状态（第一次绑定时）
                whBoxService.updateStatus(boxNo, true);
            }

            EXPRESSRECORDLOG.log(expressRecord.getId(), CheckInLogType.PURCHASE_RECEIVED.getName(),
                    new String[][] { { "快递单号", expressId }, { "采购单号", purchaseOrderNoArr } });
            logger.info("update WhPurchaseExpressRecord: " + expressRecord);
            domain.setWhPurchaseExpressRecord(expressRecord);

            if (CollectionUtils.isNotEmpty(purchaseOrderNos) && StringUtils.isBlank(oldExpressId)) {
                expressRecord.setCreatedBy(DataContextHolder.getUserId());
                expressRecord.setCreationDate(new Timestamp(System.currentTimeMillis()));
                purchaseOrderNos.forEach(purchaseOrderNo -> this.createPurchaseExpressAmqMessage(expressRecord, purchaseOrderNo));
            }
            //匹配缺货订单
            outStockMatchHandelService.matchApv(orders,null);
        }
        return "checkin/receive_express_scan";
    }

    /**
     * 绑定物流单号到周转筐
     * @param expressId 物流单号
     * @param boxNo 周转筐号
     * @param quantity 件数
     */
    public void bindLogisticsToBox(Integer expressId, String boxNo, Integer quantity) {
        try {
            // 检查是否已经绑定过
            WhBoxItemQueryCondition query = new WhBoxItemQueryCondition();
            query.setBoxNo(boxNo);
            query.setRelationNo(String.valueOf(expressId));
            query.setOrderType(ItemOrderTypeEnum.WL_RECEIPT.intCode());
            
            List<WhBoxItem> existingItems = whBoxItemService.queryWhBoxItems(query, null);
            if (CollectionUtils.isNotEmpty(existingItems)) {
                // 已经绑定过，不重复绑定
                logger.info("Logistics tracking number [{}] already bound to box [{}]", expressId, boxNo);
                return;
            }
            
            // 创建新的绑定关系
            WhBoxItem boxItem = new WhBoxItem();
            boxItem.setBoxNo(boxNo);
            boxItem.setRelationNo(String.valueOf(expressId));
            boxItem.setOrderType(ItemOrderTypeEnum.WL_RECEIPT.intCode());
            boxItem.setQuantity(quantity);
            boxItem.setIsCheckIn(false);
            
            whBoxItemService.createWhBoxItem(boxItem);
            logger.info("Successfully bound logistics tracking id [{}] to box [{}]", expressId, boxNo);
            
        } catch (Exception e) {
            logger.error("Failed to bind logistics tracking id [{}] to box [{}]: {}", expressId, boxNo, e.getMessage(), e);
            throw new RuntimeException("绑定物流单号到周转筐失败: " + e.getMessage());
        }
    }

    /**
     * 修改采购单状态
     *
     * @param purchaseOrderNo
     */
    @Override
    public void updatePurchaseOrderPurchaseStatus(String purchaseOrderNo, Integer purchaseStatus) {
        if (StringUtils.isBlank(purchaseOrderNo) || purchaseStatus == null) {
            return;
        }
        WhPurchaseOrderQueryCondition purchaseOrderQueryCondition = new WhPurchaseOrderQueryCondition();
        purchaseOrderQueryCondition.setPurchaseOrderNo(purchaseOrderNo);
        WhPurchaseOrder dbWhPurchaseOrder = whPurchaseOrderService.queryWhPurchaseOrder(purchaseOrderQueryCondition);
        if (dbWhPurchaseOrder == null)
            return;
        if (dbWhPurchaseOrder.getPurchaseStatus() != null
                && dbWhPurchaseOrder.getPurchaseStatus() >= WmsPurchaseOrderStatus.CANCELED.intCode()) {
            return;
        }
        WhPurchaseOrder updatePurchaseOrder = new WhPurchaseOrder();
        updatePurchaseOrder.setPurchaseStatus(purchaseStatus);
        updatePurchaseOrder.setId(dbWhPurchaseOrder.getId());
        whPurchaseOrderService.updateWhPurchaseOrder(updatePurchaseOrder);
        if (!purchaseStatus.equals(dbWhPurchaseOrder.getPurchaseStatus())) {
            PURCHASEORDERLOG.log(dbWhPurchaseOrder.getId(), "修改采购单状态：",
                    new String[][] {
                            { "原状态", dbWhPurchaseOrder.getPurchaseStatus() == null ? "null"
                                    : WmsPurchaseOrderStatus
                                            .getNameByCode(dbWhPurchaseOrder.getPurchaseStatus().toString()) },
                            { "新状态", WmsPurchaseOrderStatus.getNameByCode(purchaseStatus.toString()) } });
        }
    }

    /**
     * 修改采购快递单状态
     *
     * @param purchaseOrderNo
     * @param expressId
     */
    @Override
    public void updatePurchaseExpressStatus(String purchaseOrderNo, String expressId, Integer expressStatus) {
        log.info("updatePurchaseExpressStatus {},{},{}", purchaseOrderNo, expressId, expressStatus);
        if (StringUtils.isBlank(purchaseOrderNo) || StringUtils.isBlank(expressId) || expressStatus == null) {
            return;
        }
        WhPurchaseToExpressQueryCondition query = new WhPurchaseToExpressQueryCondition();
        query.setExpressId(expressId);
        query.setPurchaseOrderNo(purchaseOrderNo);
        WhPurchaseToExpress dbExpress = whPurchaseToExpressService.queryWhPurchaseToExpress(query);
        if (dbExpress != null) {
            if (dbExpress.getExpressStatus() != null && dbExpress.getExpressStatus() > expressStatus) {
                return;
            }
            WhPurchaseToExpress updateExpress = new WhPurchaseToExpress();
            updateExpress.setId(dbExpress.getId());
            updateExpress.setExpressStatus(expressStatus);
            whPurchaseToExpressService.updateWhPurchaseToExpress(updateExpress);

            WhPurchaseOrderQueryCondition orderQuery = new WhPurchaseOrderQueryCondition();
            orderQuery.setPurchaseOrderNo(purchaseOrderNo);
            WhPurchaseOrder whPurchaseOrder = whPurchaseOrderService.queryWhPurchaseOrder(orderQuery);
            if (whPurchaseOrder != null && !expressStatus.equals(dbExpress.getExpressStatus())) {
                PURCHASEORDERLOG.log(whPurchaseOrder.getId(), "修改快递单状态：" + dbExpress.getExpressId(),
                        new String[][] {
                                { "原状态", dbExpress.getExpressStatus() == null ? "null"
                                        : PurchaseExpressStatus
                                                .getNameByCode(dbExpress.getExpressStatus().toString()) },
                                { "新状态", PurchaseExpressStatus.getNameByCode(expressStatus.toString()) } });
            }
        }
    }

    @Override
    public void updatePurchaseSkuStatus(String purchaseOrderNo, String sku, Integer skuStatus, Integer upQuantity) {
        if (StringUtils.isBlank(purchaseOrderNo) || StringUtils.isBlank(sku) || skuStatus == null) {
            return;
        }
        WhPurchaseOrderQueryCondition orderQuery = new WhPurchaseOrderQueryCondition();
        orderQuery.setPurchaseOrderNo(purchaseOrderNo);
        WhPurchaseOrder whPurchaseOrder = whPurchaseOrderService.queryWhPurchaseOrder(orderQuery);
        if (whPurchaseOrder == null || whPurchaseOrder.getId() == null) {
            return;
        }

        WhPurchaseItemQueryCondition itemQuery = new WhPurchaseItemQueryCondition();
        itemQuery.setpId(whPurchaseOrder.getId());
        itemQuery.setSku(sku);
        WhPurchaseItem dbItem = whPurchaseItemService.queryWhPurchaseItem(itemQuery);
        if (dbItem == null || dbItem.getId() == null) {
            return;
        }
        if (dbItem.getSkuStatus() != null && !PurchaseSkuStatus.ARRIVE_EXCEPTION.intCode().equals(skuStatus)
                && !PurchaseSkuStatus.UN_ARRIVED.intCode().equals(dbItem.getSkuStatus())
                && !PurchaseSkuStatus.CANCELED.intCode().equals(dbItem.getSkuStatus())
                && !(PurchaseSkuStatus.CONFIRMED.intCode().equals(dbItem.getSkuStatus())
                        && PurchaseSkuStatus.PART_CONFIRMED.intCode().equals(skuStatus))
                && dbItem.getSkuStatus() > skuStatus) {
            return;
        }
        WhPurchaseItem updateItem = new WhPurchaseItem();
        updateItem.setId(dbItem.getId());
        updateItem.setSkuStatus(skuStatus);
        whPurchaseItemService.updateWhPurchaseItem(updateItem);
        if (!skuStatus.equals(dbItem.getSkuStatus())) {
            PURCHASEORDERLOG.log(whPurchaseOrder.getId(), "修改sku状态：" + dbItem.getSku(),
                    new String[][] {
                            { "原状态", dbItem.getSkuStatus() == null ? "null"
                                    : PurchaseSkuStatus.getNameByCode(dbItem.getSkuStatus().toString()) },
                            { "新状态", PurchaseSkuStatus.getNameByCode(skuStatus.toString()) } });
        }
        if (ArrayUtils.contains(new Integer[]{PurchaseSkuStatus.CONFIRMED.intCode(),PurchaseSkuStatus.PART_CONFIRMED.intCode()}, skuStatus)
                && Objects.equals(PurchaseOrderType.NCGOYP.getCode(), whPurchaseOrder.getPurchaseOrderType())) {
            executor.execute(() -> {
                ExecutorUtils.setThreadSuffix("push_purchase_item_to_product");
                String errorMsg = this.pushPurchaseItemToProduct(purchaseOrderNo, dbItem, upQuantity);
                PURCHASEORDERLOG.log(whPurchaseOrder.getId(), "推送产品系统sku-" + dbItem.getSku()+"上架信息："+(StringUtils.isBlank(errorMsg)?"成功":"失败,"+errorMsg),
                        new String[][] {{ "上架数量",  String.valueOf(upQuantity)}});
            });
        }
    }

    /**
     * 用于推送上架的sku信息到产品系统
     * @param purchaseOrderNo 采购单号
     * @param upQuantity 上架数量
     */
    private String pushPurchaseItemToProduct(String purchaseOrderNo,WhPurchaseItem whPurchaseItem, Integer upQuantity){
        if (Objects.isNull(whPurchaseItem)){
            return "采购单明细信息对象为空!";
        }

        // 调用产品http接口推送数据
        SystemParam systemParam = CacheUtils.SystemParamGet("product_system.push_purchase_item");

        if (null == systemParam) {
            logger.error("systemParam product_system.push_purchase_item is null");
            return "systemParam product_system.push_purchase_item is null";
        }

        String url = systemParam.getParamValue();
        if (StringUtils.isBlank(url)) {
            logger.error("systemParam paramValue is null");
            return "systemParam paramValue is null";
        }

        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", HttpUtils.ACCESS_TOKEN);
            headers.put("Content-Type", "application/json; charset=utf-8");
            Map<String,Object> body = new HashMap<>();
            body.put("purchaseOrderNo", purchaseOrderNo);
            body.put("sku", whPurchaseItem.getSku());
            body.put("upQuantity", upQuantity);
            body.put("upTime", new Date(System.currentTimeMillis()));
            log.info("推送产品系统采购sku信息，调用接口参数："+JSON.toJSONString(body));
            ApiResult apiResult = HttpUtils.post(url, headers, JSON.toJSONString(body), ApiResult.class);
            log.info("推送产品系统采购sku信息，接口返回结果："+JSON.toJSONString(apiResult));
            if (Objects.isNull(apiResult) || !apiResult.isSuccess()) {
                return "推送产品系统采购sku信息，调用接口失败!"+(Objects.nonNull(apiResult)?apiResult.getErrorMsg():"");
            }
        }
        catch (Exception e) {
            logger.error("推送产品系统采购sku信息异常！", e);
            return "推送产品系统采购sku信息异常！";
        }
        return null;
    }

    @Override
    public void createPurchaseExpressAmqMessage(WhPurchaseExpressRecord expressRecord, String purchaseOrderNo) {
        //发消息到采购
        PushCheckinMessage pushCheckinMessage = new PushCheckinMessage();
        pushCheckinMessage.setExpressId(expressRecord.getTrackingNumber());
        pushCheckinMessage.setPurchaseOrderNo(purchaseOrderNo);
        pushCheckinMessage.setQuantity(expressRecord.getQuantity());
        pushCheckinMessage.setWeight(expressRecord.getWeight());
        pushCheckinMessage.setReceivingUser(expressRecord.getCreatedBy() == null ? null : TaglibUtils.getEmployeeNameByUserId(expressRecord.getCreatedBy()));
        pushCheckinMessage.setReceivingTime(expressRecord.getCreationDate());
        AmqMessage amqMessage = AssembleMessageDataUtils.assembleCheckInData(pushCheckinMessage);
        amqMessageService.createAmqMessage(amqMessage);
    }

    @Override
    public List<WhPurchaseExpressRecord> getNotFinishExpressRecord(Integer userId) {
        List<WhPurchaseExpressRecord> whPurchaseExpressRecords = whPurchaseExpressRecordDao
                .getNotFinishExpressRecord(userId);
        return whPurchaseExpressRecords;
    }

    @Override
    public void updateCheckInFinish(PurchaseOrder purchaseOrder, Map<String, Timestamp> map, Map<String, Integer> boxItemMap) {
        WhPurchaseOrderQueryCondition queryCondition = new WhPurchaseOrderQueryCondition();
        queryCondition.setPurchaseOrderNo(purchaseOrder.getPurchaseOrderNo());
        WhPurchaseOrder newPurchaseOrder = whPurchaseOrderService.queryWhPurchaseOrder(queryCondition);
        if (newPurchaseOrder == null){
            return;
        }
        String expressIdStr = newPurchaseOrder.getExpressId();
        if (StringUtils.isBlank(expressIdStr))
            return;
        String[] strArr = expressIdStr.split(",");
        List<String> strList = new ArrayList<>();
        for (int i = 0; i < strArr.length; i++) {
            strList.add(strArr[i]);
        }
        if (StringUtils.isNotBlank(purchaseOrder.getExpressId())) {
            String[] expressArr = purchaseOrder.getExpressId().split(",");
            for (int i = 0; i < expressArr.length; i++) {
                if (!strList.contains(expressArr[i]))
                    strList.add(expressArr[i]);
            }
        }
        for (String trackingNumber :strList) {
            WhPurchaseExpressRecordQueryCondition query = new WhPurchaseExpressRecordQueryCondition();
            query.setTrackingNumber(trackingNumber);
            query.setPurchaseOrderNo(purchaseOrder.getPurchaseOrderNo());
            WhPurchaseExpressRecord record = whPurchaseExpressRecordDao.queryWhPurchaseExpressRecord(query);
            if (record != null && record.getStatus() != null
                    && record.getStatus().equals(PurchaseExpressRecordStatus.SCANNER.intCode())) {
                WhPurchaseExpressRecord updateRecord = new WhPurchaseExpressRecord();
                updateRecord.setId(record.getId());
                updateRecord.setStatus(PurchaseExpressRecordStatus.FINISH.intCode());
                whPurchaseExpressRecordDao.updateWhPurchaseExpressRecord(updateRecord);
                map.put(record.getId().toString(), record.getCheckInScanTime());
                boxItemMap.put(record.getId().toString(), ItemOrderTypeEnum.EXPRESS_RECEIPT.intCode());
                // 为物流单时，设置为物流类型
                if (Objects.equals(1, record.getSerialNumber())){
                    boxItemMap.put(record.getId().toString(), ItemOrderTypeEnum.WL_RECEIPT.intCode());
                }
            }
        }
    }

    @Override
    public void setWhPurchaseExpressRecordFlags(List<WhPurchaseExpressRecord> whPurchaseExpressRecords) {
        // 获取采购单信息
        try {
            List<String> whPurchaseOrderNos = whPurchaseExpressRecords.stream().filter(w -> StringUtils.isNotBlank(w.getPurchaseOrderNo())).
                    map(w2 -> w2.getPurchaseOrderNo()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(whPurchaseOrderNos)){
                WhPurchaseOrderQueryCondition queryCondition = new WhPurchaseOrderQueryCondition();
                queryCondition.setPurchaseOrderNoStr(StringUtils.join(whPurchaseOrderNos,","));
                List<WhPurchaseOrder> whPurchaseOrderList = whPurchaseOrderService.queryWhPurchaseOrders(queryCondition,null);
                Map<String,String> map = whPurchaseOrderList.stream().collect(Collectors.toMap(WhPurchaseOrder::getPurchaseOrderNo, WhPurchaseOrder::getFlags, (key1 , key2)-> key2 ));
                whPurchaseExpressRecords.stream().map(w -> {
                    if (StringUtils.isNotBlank(w.getPurchaseOrderNo())){
                        String[] pOrderArr = StringUtils.split(w.getPurchaseOrderNo(),",");
                        for(int i=0; i<pOrderArr.length; i++){
                            if (StringUtils.isNotBlank(map.get(pOrderArr[i])) && map.get(pOrderArr[i]).contains("TJ")){
                                w.setFlags(map.get(pOrderArr[i]));
                            }
                        }
                    }
                    return w;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void unbindExpressRecord(Integer id) throws Exception {
       /* WhPurchaseExpressRecordQueryCondition queryCondition = new WhPurchaseExpressRecordQueryCondition();
        queryCondition.setId(id);
        WhPurchaseExpressRecord whPurchaseExpressRecord = queryWhPurchaseExpressRecord(queryCondition);
        if(whPurchaseExpressRecord.getCheckInScanStatus() == null || !whPurchaseExpressRecord.getCheckInScanStatus()){
            throw new Exception("快递单收货单还未扫描");
        }
        if (whPurchaseExpressRecord.getSerialNumber() == null || whPurchaseExpressRecord.getSerialNumber() != 1){
            throw new Exception("非 物流单 据，请通过周转筐明细解绑");
        }*/

       /* WhPurchaseOrderQueryCondition queryCondition1 = new WhPurchaseOrderQueryCondition();
        queryCondition1.setTrackingNumber(whPurchaseExpressRecord.getTrackingNumber());
        List<PurchaseOrder> purchaseOrders = TranslateWhPurchaseOrderToPurchaseOrderUtils.translateWhPurchaseOrderToPurchaseOrder(
                whPurchaseOrderService.queryWhPurchaseOrderAndItems(queryCondition1, null));*/

        // 判断sku仓库和本仓
        //Integer warehouseId = WarehouseProperties.getWarehouseProperties().getLocalWarehouseId();
        //boolean existSpanSku = false;

        /*for (PurchaseOrder purchaseOrder : purchaseOrders) {
            if (CollectionUtils.isEmpty(purchaseOrder.getPurchaseOrderItems())) {
                throw new Exception("快递单对应采购明细数据为空");
            }

            for (PurchaseOrderItem item : purchaseOrder.getPurchaseOrderItems()) {
                if (StringUtils.isBlank(item.getSku())) {
                    throw new Exception("快递单对应采购明细数据sku为空");
                }
                WhSkuQueryCondition whSkuQueryCondition = new WhSkuQueryCondition();
                whSkuQueryCondition.setSku(item.getSku());
                WhSku whSku = whSkuService.queryWhSku(whSkuQueryCondition);
                if (whSku == null) {
                    throw new Exception("快递单对应采购明细数据sku" + item.getSku() + "不存在");
                } else if (whSku.getWarehouseId() == null) {
                    throw new Exception("快递单对应采购明细数据sku" + item.getSku() + "仓库属性为空");
                } else if (!whSku.getWarehouseId().equals(warehouseId)) {
                    existSpanSku = true;
                }
            }
        }
        if (!existSpanSku){
            throw new Exception("不存在跨仓sku，不能手动解绑");
        }*/

        WhPurchaseExpressRecord updateRecord = new WhPurchaseExpressRecord();
        updateRecord.setId(id);
        updateRecord.setStatus(3);
        updateWhPurchaseExpressRecord(updateRecord);
        EXPRESSRECORDLOG.log(id, "手动解绑快递单!");
    }

    @Override
    public void doBatchUpdateCommentByIds(String ids, String comment) {

        /*
            1、修改备注
            2、修改关联的快递单状态为已处理
            3、修改关联的采购单为待入库
            4、记录日志 快递单在方法里面已经记录了，单独记录采购日志
         */

        //修改备注
        whPurchaseExpressRecordDao.batchUpdateCommentByIds(ids, comment);

        //修改关联的快递单状态为已处理
        if (StringUtils.isNotBlank(ids)) {
            List<Integer> idList = Stream.of(ids.split(",")).map(Integer::parseInt).collect(Collectors.toList());

            //根据ids查询所有收货记录
            WhPurchaseExpressRecordQueryCondition queryCondition = new WhPurchaseExpressRecordQueryCondition();
            queryCondition.setIds(idList);
            List<WhPurchaseExpressRecord> whPurchaseExpressRecords = whPurchaseExpressRecordDao.queryWhPurchaseExpressRecordList(queryCondition, null);

            //修改关联数据的状态（复用之前的方法）
            Optional.ofNullable(whPurchaseExpressRecords).orElse(new ArrayList<>()).forEach(item -> {
                if (StringUtils.isNotBlank(item.getPurchaseOrderNo())) {
                    String[] purchaseOrderNoArr = StringUtils.split(item.getPurchaseOrderNo(), ",");
                    for (String purchaseOrderNo : purchaseOrderNoArr) {
                        // 修改快递单状态,已处理
                        this.updatePurchaseExpressStatus(purchaseOrderNo, item.getTrackingNumber(), PurchaseExpressStatus.PROCESSED.intCode());

                        // 修改采购单状态,待入库
                        this.updatePurchaseOrderPurchaseStatus(purchaseOrderNo, WmsPurchaseOrderStatus.WAIT_STOCK_IN.intCode());
                    }
                }

                //记录日志
                String[][] log ;
                if (StringUtils.isNotBlank(item.getPurchaseOrderNo()) && !StringUtils.equals(item.getPurchaseOrderNo(), item.getOldPurchaseOrderNo())) {
                    log = new String[3][2];
                    log[0] = new String[]{"原采购单号", item.getOldPurchaseOrderNo()};
                    log[1] = new String[]{"现采购单号", item.getPurchaseOrderNo()};
                } else {
                    log = new String[1][2];
                }

                log[log.length - 1] = new String[]{"原因", "批量修改快递单备注：" + comment};
                EXPRESSRECORDLOG.log(item.getId(), "编辑采购单", log);
            });
        }
    }

    @Override
    public List<WhPurchaseOrder> getOtherWarehousePurchaseOrders(String expressNo) {
        if (StringUtils.isBlank(expressNo)) {
            return List.of();
        }
        try {
            WarehouseProperties warehouseProperties = WarehouseProperties.getWarehouseProperties();
            String url =warehouseProperties.getWarehouseIpUrl().entrySet().stream()
                    .filter(entry -> entry.getKey() != warehouseProperties.getLocalWarehouseId())
                    .map(Map.Entry::getValue)
                    .findFirst().get();
            url += "/checkin/whPurchaseOrder/getWhPurchaseOrders?expressNo="+expressNo;
            log.info("getOtherWarehousePurchaseOrders url: {}", url);
            ApiResult apiResult = HttpUtils.get(url, HttpUtils.ACCESS_TOKEN, ApiResult.class);
            if (apiResult == null || !apiResult.isSuccess()) {
                return List.of();
            }
            List<WhPurchaseOrder> whPurchaseOrders = JSON.parseArray(JSON.toJSONString(apiResult.getResult()), WhPurchaseOrder.class);
            return whPurchaseOrders;
        } catch (Exception e) {
            log.error("getOtherWarehousePurchaseOrders error:{}", e.getMessage(), e);
        }
        return List.of();
    }

}