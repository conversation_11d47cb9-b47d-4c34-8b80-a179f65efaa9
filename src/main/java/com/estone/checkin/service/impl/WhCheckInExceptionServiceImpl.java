package com.estone.checkin.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.checkin.bean.*;
import com.estone.checkin.dao.WhCheckInExceptionDao;
import com.estone.checkin.domain.WhCheckInExceptionDo;
import com.estone.checkin.enums.*;
import com.estone.checkin.service.*;
import com.estone.checkin.utils.GetUserNameOrEmployeeNameUtil;
import com.estone.common.ProductConfigProperties;
import com.estone.common.util.CacheUtils;
import com.estone.common.util.HttpUtils;
import com.estone.common.util.model.ApiResult;
import com.estone.rule.common.HandleTypeModal;
import com.estone.sku.bean.WhSkuExtend;
import com.estone.sku.bean.WhSkuExtendQueryCondition;
import com.estone.sku.bean.WhUniqueSku;
import com.estone.sku.bean.WhUniqueSkuQueryCondition;
import com.estone.sku.enums.SkuBusinessType;
import com.estone.sku.service.WhSkuExtendService;
import com.estone.sku.service.WhUniqueSkuService;
import com.estone.system.rabbitmq.model.PushCheckInException;
import com.estone.system.rabbitmq.producer.RabbitmqProducerService;
import com.estone.system.user.bean.SaleUser;
import com.estone.system.user.bean.SaleUserQueryCondition;
import com.estone.system.user.service.SaleUserService;
import com.estone.warehouse.bean.WhExLocation;
import com.estone.warehouse.bean.WhExLocationQueryCondition;
import com.estone.warehouse.enums.LocationStatus;
import com.estone.warehouse.service.WhBoxService;
import com.estone.warehouse.service.WhExLocationService;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service("whCheckInExceptionService")
public class WhCheckInExceptionServiceImpl implements WhCheckInExceptionService {
    private static Logger logger = LoggerFactory.getLogger(WhCheckInExceptionServiceImpl.class);

    @Resource
    private WhCheckInExceptionDao whCheckInExceptionDao;
    @Resource
    private WhBoxService whBoxService;
    @Resource
    private WhCheckInExceptionHandleService whCheckInExceptionHandleService;

    @Resource
    private WhCheckInExceBatchService whCheckInExceBatchService;
    @Resource
    private WhPurchaseOrderService whPurchaseOrderService;

    @Resource
    private RabbitmqProducerService rabbitmqProducerService;

    @Resource
    private HandleExceptionDateService handleExceptionDateService;

    @Resource
    private WhCheckInService whCheckInService;

    @Resource
    private WhPurchaseExpressRecordService whPurchaseExpressRecordService;

    @Resource
    private WhExLocationService whExLocationService;

    @Resource
    private WhSkuExtendService whSkuExtendService;

    @Resource
    private SaleUserService saleUserService;

    @Resource
    private ProductConfigProperties productConfigProperties;

    @Resource
    private WhUniqueSkuService whUniqueSkuService;

    @Resource
    private SkuScanExceptionService skuScanExceptionService;

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public WhCheckInException getWhCheckInException(Integer id) {
        WhCheckInException whCheckInException = whCheckInExceptionDao.queryWhCheckInException(id);
        return whCheckInException;
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public WhCheckInException getWhCheckInExceptionDetail(Integer id) {
        WhCheckInException whCheckInException = whCheckInExceptionDao.queryWhCheckInException(id);
        // 关联查询
        return whCheckInException;
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public WhCheckInException queryWhCheckInException(WhCheckInExceptionQueryCondition query) {
        Assert.notNull(query);
        WhCheckInException whCheckInException = whCheckInExceptionDao.queryWhCheckInException(query);
        return whCheckInException;
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public List<WhCheckInException> queryAllWhCheckInExceptions() {
        return whCheckInExceptionDao.queryWhCheckInExceptionList();
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public List<WhCheckInException> queryWhCheckInExceptions(WhCheckInExceptionQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whCheckInExceptionDao.queryWhCheckInExceptionCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhCheckInException>();
            }
        }
        List<WhCheckInException> whCheckInExceptions = whCheckInExceptionDao.queryWhCheckInExceptionList(query, pager);
        return whCheckInExceptions;
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public List<WhCheckInException> queryNewWhCheckInExceptions(WhCheckInExceptionQueryCondition query, Pager pager) {
        Assert.notNull(query);
        if (pager != null && pager.isQueryCount()) {
            int count = whCheckInExceptionDao.queryNewWhCheckInExceptionCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhCheckInException>();
            }
        }
        query.setIsNew(true);
        List<WhCheckInException> whCheckInExceptions = whCheckInExceptionDao.queryNewWhCheckInExceptionList(query, pager);
        return whCheckInExceptions;
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public void createWhCheckInException(WhCheckInException whCheckInException) {
        try {
            if (whCheckInException != null) {
                //去空格
                replaceAllTrim(whCheckInException);
                whCheckInExceptionDao.createWhCheckInException(whCheckInException);
                logger.info("create whCheckInException :" + whCheckInException);

                String boxNo = whCheckInException.getBoxNo();
                if (StringUtils.isNotBlank(boxNo)) {
                    String[][] logs = new String[][]{{"生成入库异常单", ""},
                            {"relationNo", whCheckInException.getId().toString()}};
                    int updated = whBoxService.updateWhBoxOfBinding(boxNo, whCheckInException.getId().toString(), logs);
                    if (updated >= 1) {
                        logger.info("绑定周转码: relationNo[" + whCheckInException.getId() + "], boxNo["
                                + whCheckInException.getBoxNo() + "], boxUpdated[" + updated + "]");
                    }
                }
            }
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * 去除sku，采购单号，物流单号的空格
     *
     * @param whCheckInException
     */
    private void replaceAllTrim(WhCheckInException whCheckInException) {
        // 把空格去掉
        if (StringUtils.isNotEmpty(whCheckInException.getSku())) {
            String sku = whCheckInException.getSku().replaceAll(" ", "");
            whCheckInException.setSku(StringUtils.trim(sku));
        }
        if (StringUtils.isNotEmpty(whCheckInException.getPurchaseOrderNo())) {
            String purchaseOrderNo = whCheckInException.getPurchaseOrderNo().replaceAll(" ", "");
            whCheckInException.setPurchaseOrderNo(StringUtils.trim(purchaseOrderNo));
        }

        if (StringUtils.isNotEmpty(whCheckInException.getTrackingNumber())) {
            String trackingNumber = whCheckInException.getTrackingNumber().replaceAll(" ", "");
            whCheckInException.setTrackingNumber(StringUtils.trim(trackingNumber));
        }
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public void createWhCheckInException(WhCheckInException whCheckInException, String type) {
        try {
            if (whCheckInException != null) {
                //去空格
                replaceAllTrim(whCheckInException);
                //设置新品首单
                WhPurchaseOrderQueryCondition query = new WhPurchaseOrderQueryCondition();
                query.setPurchaseOrderNo(whCheckInException.getPurchaseOrderNo());
                query.setTrackingNumber(whCheckInException.getTrackingNumber());
                query.setSku(whCheckInException.getSku());
                List<WhPurchaseOrder> purchaseOrderList = whPurchaseOrderService.queryWhPurchaseOrderAndItems(query, null);
                if (CollectionUtils.isNotEmpty(purchaseOrderList)
                        && StringUtils.isNotEmpty(purchaseOrderList.get(0).getJsonData())) {
                    if (CollectionUtils.isNotEmpty(purchaseOrderList.get(0).init().getItems()))
                        whCheckInException.setFirstOrderType(
                                purchaseOrderList.get(0).init().getItems().get(0).getFirstOrderType());

                }
                //异常次数的计算方法：同一个采购单的同一个SKU出现异常的次数，生成第一条为1，第二条为2，依次类推
                WhCheckInExceptionQueryCondition exceptionQuery = new WhCheckInExceptionQueryCondition();
                exceptionQuery.setPurchaseOrderNo(whCheckInException.getPurchaseOrderNo());
                exceptionQuery.setSku(whCheckInException.getSku());
                int exTimes = whCheckInExceptionDao.queryWhCheckInExceptionCount(exceptionQuery);
                whCheckInException.setExTimes(exTimes + 1);
                whCheckInExceptionDao.createWhCheckInException(whCheckInException);
                logger.info("create whCheckInException :" + whCheckInException);

                // 创建异常批次
                whCheckInExceBatchService.createWhCheckInExceptionBatch(whCheckInException, type);

                String boxNo = whCheckInException.getBoxNo();
                if (StringUtils.isNotBlank(boxNo)) {
                    String[][] logs = new String[][]{{"生成入库异常单", ""},
                            {"relationNo", whCheckInException.getId().toString()}};
                    int updated = whBoxService.updateWhBoxOfBinding(boxNo, whCheckInException.getId().toString(), logs);
                    if (updated >= 1) {
                        logger.info("绑定周转码: relationNo[" + whCheckInException.getId() + "], boxNo["
                                + whCheckInException.getBoxNo() + "], boxUpdated[" + updated + "]");
                    }
                }
            }
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public void batchCreateWhCheckInException(List<WhCheckInException> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whCheckInExceptionDao.batchCreateWhCheckInException(entityList);
            } catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public void deleteWhCheckInException(Integer id) {
        try {
            whCheckInExceptionDao.deleteWhCheckInException(id);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public void updateWhCheckInException(WhCheckInException whCheckInException) {
        try {
            whCheckInExceptionDao.updateWhCheckInException(whCheckInException);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public void updateWhCheckInException(WhCheckInException whCheckInException, String type) {
        try {
            whCheckInExceptionDao.updateWhCheckInException(whCheckInException);
            // 创建异常批次
            whCheckInExceBatchService.createWhCheckInExceptionBatch(whCheckInException, type);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * 自动下架修改库位
     *
     * @param whCheckInException
     */
    @Override
    public void updateLocationAuto(WhCheckInException whCheckInException, String oldLocation) {
        if (StringUtils.isEmpty(oldLocation))
            return;
        // 异常单状态变更为待入库/完成/已废弃/已退货时,从库位下架
        List<Integer> statusList = Arrays.asList(ExceptionStatus.WAIT_CHECK_IN.intCode(), ExceptionStatus.STOCK_IN_ING.intCode(),
                ExceptionStatus.COMPLETE.intCode(), ExceptionStatus.DISCARDED.intCode(),
                ExceptionStatus.RETURNED.intCode());
        if (whCheckInException.getStatus() != null && statusList.contains(whCheckInException.getStatus())) {
            WhCheckInExceptionHandle handle = new WhCheckInExceptionHandle();
            handle.setExceptionId(whCheckInException.getId());
            handle.setCreatedBy(DataContextHolder.getUserId());
            handle.setQuantity(whCheckInException.getConfirmQuantity());
            handle.setStatus(whCheckInException.getStatus());
            handle.setHandleComment("自动下架修改库位->原库位：" + oldLocation + " 新库位：无");
            // 创建入库异常单处理详情
            whCheckInExceptionHandleService.createWhCheckInExceptionHandle(handle);
        }
    }


    /**
     * This method corresponds to the database table wh_check_in_exception
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    public List<Integer> batchUpdateExceptionToFinished(List<Integer> ids, List<Integer> ignoreHandleWays) {
        List<Integer> successIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            logger.info("start mark exception status to finished ，param listIds===" + ids + "===");
            for (Integer id : ids) {
                WhCheckInException whCheckInException = whCheckInExceptionDao.queryWhCheckInException(id);
                // 需忽略的处理方式
                if (CollectionUtils.isNotEmpty(ignoreHandleWays)) {
                    if (ignoreHandleWays.contains(whCheckInException.getHandleWay())) {
                        continue;
                    }
                }
                // 必须是待仓库处理才能标记完成
                if (ExceptionStatus.getWarehousePendingCode().contains(whCheckInException.getStatus())
                        || ExceptionStatus.WAIT_CHECK_IN.intCode().equals(whCheckInException.getStatus())
                        || ExceptionStatus.STOCK_IN_ING.intCode().equals(whCheckInException.getStatus())) {
                    updateExceptionToFinished(whCheckInException);
                    successIdList.add(id);
                }
            }
            logger.info("end mark exception status to finished ，param listIds===" + ids + "===");
        }
        return successIdList;
    }

    @Override
    public void updateExceptionToFinished(WhCheckInException whCheckInException) {
        if (whCheckInException == null || whCheckInException.getId() == null) {
            return;
        }
        WhCheckInException updateException = new WhCheckInException();
        updateException.setId(whCheckInException.getId());
        updateException.setStatus(ExceptionStatus.COMPLETE.intCode());// 状态标记为已完成
        updateException.setFinishDate(new Timestamp(System.currentTimeMillis()));// 完成时间（仓库处理时间）
        updateException.setFinishUser(DataContextHolder.getUserId());// 完成员（仓库处理员）
        updateException.setLocationNumber("");
        updateWhCheckInException(updateException);
        //下架库位记录日志
        updateLocationAuto(updateException, whCheckInException.getLocationNumber());

        // 组装入库异常单处理详情数据
        WhCheckInExceptionHandle whCheckInExceptionHandle = new WhCheckInExceptionHandle();
        whCheckInExceptionHandle.setCreatedBy(DataContextHolder.getUserId());
        whCheckInExceptionHandle.setExceptionId(whCheckInException.getId());
        whCheckInExceptionHandle.setQuantity(whCheckInException.getConfirmQuantity());
        whCheckInExceptionHandle.setStatus(ExceptionStatus.COMPLETE.intCode());
        //自动完成时填的备注
        whCheckInExceptionHandle.setHandleComment(whCheckInException.getLastPurchaseHandleComment());
        // 创建入库异常单处理详情
        whCheckInExceptionHandleService.createWhCheckInExceptionHandle(whCheckInExceptionHandle);

        // 记录关联表完成时间
        handleExceptionDateService.doSaveExceptionDate(updateException, whCheckInException.getStatus());

        // 解绑周转筐
        String boxNo = whCheckInException.getBoxNo();
        if (StringUtils.isNotBlank(boxNo)) {
            String[][] logs = new String[][]{{"入库异常单完成", ""},
                    {"relationNo", whCheckInException.getId().toString()}};
            int updated = whBoxService.updateWhBoxOfUnbinding(boxNo, logs);
            if (updated >= 1) {
                logger.info("入库异常单完成后释放周转码: boxNo[" + boxNo + "],  boxUpdated[" + updated + "]");
            }
        }
    }

    @Override
    public int getExceptionCount(WhCheckInExceptionQueryCondition query) {
        return whCheckInExceptionDao.queryWhCheckInExceptionCount(query);
    }

    @Override
    public List<HistoryCheckInExceptionCount> queryAllHistoryCheckInExceptionCountList() {
        return whCheckInExceptionDao.queryAllHistoryCheckInExceptionCountList();
    }

    @Override
    public List<WhCheckInException> queryRecentThreeExceptionsByExceptionType(WhCheckInExceptionQueryCondition query) {
        return whCheckInExceptionDao.queryRecentThreeExceptionsByExceptionType(query);
    }

    @Override
    public List<WhCheckInException> queryRecentFinishedExceptionsByExceptionType(String exceptionType, String sku) {
        return whCheckInExceptionDao.queryRecentFinishedExceptionsByExceptionType(exceptionType, sku);
    }

    /**
     * 批量提交
     *
     * @param ids
     */
    @Override
    public ResponseJson batchUpdateExceptionToPurchasePending(List<Integer> ids) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(ids)) {
            response.setMessage("没有要提交的异常单！");
            return response;
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            logger.info("start mark exception status to purchase pending ，param listIds===" + ids + "===");
            for (Integer id : ids) {
                WhCheckInException whCheckInException = whCheckInExceptionDao.queryWhCheckInException(id);

                if (!(ExceptionFrom.AUTO_CHECK_IN_EXCEPTION.intCode().equals(whCheckInException.getExceptionFrom())
                        && (ExceptionStatus.UNCONFIRM.intCode().equals(whCheckInException.getStatus()))
                        || ExceptionStatus.WAIT_PUSH.intCode().equals(whCheckInException.getStatus()))) {
                    response.setMessage("只能提交自动生成少货的草稿异常单！");
                    return response;
                }
                Integer status = ExceptionStatus.PURCHASE_PENDING.intCode();
                if (whCheckInException.isPurchaseUserVocationContainDev()){
                    status = ExceptionStatus.DEVELOP_CONFIRM.intCode();
                }
                WhCheckInException updateException = new WhCheckInException();
                updateException.setId(whCheckInException.getId());
                updateException.setStatus(status);// 状态标记为待采购处理
                updateException.setConfirmQuantity(whCheckInException.getQuantity());
                updateWhCheckInException(updateException);
                // 记录关联表完成时间
                handleExceptionDateService.doSaveExceptionDate(updateException, whCheckInException.getStatus());

                // 组装入库异常单处理详情数据
                WhCheckInExceptionHandle whCheckInExceptionHandle = new WhCheckInExceptionHandle();
                whCheckInExceptionHandle.setCreatedBy(DataContextHolder.getUserId());
                whCheckInExceptionHandle.setCreationDate(new Timestamp(System.currentTimeMillis()));
                whCheckInExceptionHandle.setExceptionId(id);
                whCheckInExceptionHandle.setQuantity(whCheckInException.getQuantity());
                whCheckInExceptionHandle.setHandleComment("批量提交自动生成的【少货/少SKU】异常");
                whCheckInExceptionHandle.setStatus(status);
                // 创建入库异常单处理详情
                whCheckInExceptionHandleService.createWhCheckInExceptionHandle(whCheckInExceptionHandle);
                //推送到采购
                whCheckInException.setStatus(status);
                whCheckInException.setConfirmQuantity(whCheckInException.getQuantity());
                rabbitmqProducerService.pushCheckInExceptionMsgToPms(whCheckInException, whCheckInExceptionHandle, new PushCheckInException());
            }
            logger.info("end mark exception status to purchase pending ，param listIds===" + ids + "===");
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @Override
    public List<Integer> batchUpdateToWaitCheckIn(List<Integer> ids) {
        List<Integer> errorIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            logger.info("start mark exception status to finished ，param listIds===" + ids + "===");
            for (Integer id : ids) {
                WhCheckInException whCheckInException = whCheckInExceptionDao.queryWhCheckInException(id);

                // 状态为待仓库处理，且，处理方式为：入库、带样换图/加图、改描述、建单入库其中之一的异常单
                if (ExceptionStatus.getWarehousePendingCode().contains(whCheckInException.getStatus())
                        && (whCheckInException.getHandleWay() != null
                        && (ExceptionHandleWay.CHECK_IN.intCode().equals(whCheckInException.getHandleWay())
                        || ExceptionHandleWay.UPDATE_QC_REMARK.intCode().equals(whCheckInException.getHandleWay())
                        || ExceptionHandleWay.UPDATE_IMAGE.intCode().equals(whCheckInException.getHandleWay())
                        || ExceptionHandleWay.UPDATE_DESCRIPTION.intCode().equals(whCheckInException.getHandleWay())
                        || ExceptionHandleWay.REISSUE.intCode().equals(whCheckInException.getHandleWay())
                        || ExceptionHandleWay.UPDATE_IMG_SIZE.intCode().equals(whCheckInException.getHandleWay())
                        || ExceptionHandleWay.CHANG_SIZE.intCode().equals(whCheckInException.getHandleWay())
                        || ExceptionHandleWay.CREATE_NEW_ORDER_TO_CHECK_IN.intCode().equals(whCheckInException.getHandleWay())))) {
                    String comment = "标记待入库";
                    this.waitCheckInException(whCheckInException, comment);

                } else {
                    errorIdList.add(id);
                }

            }
            logger.info("end mark exception status to finished ，param listIds===" + ids + "===");
        }
        return errorIdList;
    }

    @Override
    public void waitCheckInException(WhCheckInException whCheckInException, String comment){
        WhCheckInException updateOrder = new WhCheckInException();
        updateOrder.setId(whCheckInException.getId());
        updateOrder.setStatus(ExceptionStatus.WAIT_CHECK_IN.intCode());
        updateOrder.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        updateOrder.setLastUpdateUser(DataContextHolder.getUserId());
        updateOrder.setLocationNumber("");
        updateOrder.setWaitCheckInDate(new Timestamp(System.currentTimeMillis()));
        if (Objects.equals(whCheckInException.getStatus(),ExceptionStatus.QC_PENDING.intCode())){
            updateOrder.setCompletedQCHandleDate(new Timestamp(System.currentTimeMillis()));
        }
        updateWhCheckInException(updateOrder);
        //下架库位记录日志
        updateLocationAuto(updateOrder, whCheckInException.getLocationNumber());
        // 记录关联表完成时间
        handleExceptionDateService.doSaveExceptionDate(updateOrder, whCheckInException.getStatus());

        // 组装入库异常单处理详情数据
        WhCheckInExceptionHandle whCheckInExceptionHandle = new WhCheckInExceptionHandle();
        whCheckInExceptionHandle.setCreatedBy(DataContextHolder.getUserId());
        whCheckInExceptionHandle.setExceptionId(whCheckInException.getId());
        whCheckInExceptionHandle.setQuantity(whCheckInException.getConfirmQuantity());
        whCheckInExceptionHandle.setStatus(updateOrder.getStatus());
        whCheckInExceptionHandle.setHandleComment(comment);
        // 创建入库异常单处理详情
        whCheckInExceptionHandleService.createWhCheckInExceptionHandle(whCheckInExceptionHandle);

        updateOrder.setPurchaseUserNameToPms(whCheckInException.getPurchaseUserNameToPms());
        logger.info("start to send exception waitCheckIn status message to pms ====exception:"
                + updateOrder.toString() + "===exceptionHandle:" + whCheckInExceptionHandle);
        rabbitmqProducerService.pushCheckInExceptionMsgToPms(updateOrder, whCheckInExceptionHandle,
                new PushCheckInException());
        logger.info("send exception waitCheckIn status message to pms end ");
    }

    @Override
    public ResponseJson saveWhCheckException(WhCheckInExceptionDo domain, String type,String exceptionBoxNo) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        WhCheckInException whCheckInException = domain.getWhCheckInException();
        WhCheckInException exist = null;
        if (whCheckInException.getId() != null) {
            exist = whCheckInExceptionDao.queryWhCheckInException(whCheckInException.getId());
            if (exist == null || !StringUtils.equalsIgnoreCase(HandleTypeModal.ADD, type)
                    && ExceptionStatus.getCanNotEditCode().contains(exist.getStatus())) {
                responseJson.setMessage("该异常单不存在或已被编辑，请确认后再操作！");
                return responseJson;
            }
        }
        WhCheckInException create = null;
        WhCheckInExceptionHandle createHandle = null;

        WhCheckInExceptionHandle handle = new WhCheckInExceptionHandle();
        if (whCheckInException != null && whCheckInException.getId() != null && exist != null) {

            boolean locationEqual = StringUtils.isEmpty(exist.getLocationNumber())
                    ? StringUtils.isEmpty(whCheckInException.getLocationNumber())
                    : exist.getLocationNumber().equals(whCheckInException.getLocationNumber());
            if (!locationEqual) {
                String msg = "修改库位->原库位：" + exist.getLocationNumber()
                        + " ，新库位：" + whCheckInException.getLocationNumber();
                handle.setHandleComment(msg);
                responseJson = this.checkUpdateLocation(Arrays.asList(exist.getId()),
                        whCheckInException.getLocationNumber());
                if (StatusCode.FAIL.equals(responseJson.getStatus()))
                    return responseJson;
            }
            if (StringUtils.isEmpty(whCheckInException.getLocationNumber()))
                whCheckInException.setLocationNumber("");

            // 来源为QC全检的异常单不做处理
            if (exist != null && !ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode().equals(exist.getExceptionFrom())) {
                // 驳回：待仓库处理-》待采购处理
                if (StringUtils.isNotBlank(type) && HandleTypeModal.RETURN_DOWN.equalsIgnoreCase(type)
                        && ExceptionStatus.getWarehousePendingCode().contains(exist.getStatus())) {
                    handle.setHandleWay(ExceptionHandleWay.TURN_DOWN.intCode());
                    Integer status = ExceptionStatus.PURCHASE_PENDING.intCode();
                    if (exist.isPurchaseUserVocationContainDev()){
                        status = ExceptionStatus.DEVELOP_CONFIRM.intCode();
                    }
                    whCheckInException.setStatus(status);
                    whCheckInException.setHandleWay(ExceptionHandleWay.TURN_DOWN.intCode());

                }
                // 报废：草稿-》报废
                if (StringUtils.isNotBlank(type) && HandleTypeModal.SCRAP.equalsIgnoreCase(type)
                        && (ExceptionStatus.UNCONFIRM.intCode().equals(exist.getStatus()))
                        || ExceptionStatus.WAIT_PUSH.intCode().equals(exist.getStatus())) {
                    handle.setHandleWay(ExceptionHandleWay.SCRAP.intCode());
                    whCheckInException.setStatus(ExceptionStatus.DISCARDED.intCode());
                    whCheckInException.setHandleWay(ExceptionHandleWay.SCRAP.intCode());
                    whCheckInException.setDiscardedDate(new Timestamp(System.currentTimeMillis()));// 废弃时间
                    whCheckInException.setDiscardedUser(DataContextHolder.getUserId());// 废弃员

                    // 报废解绑周转筐
                    String boxNo = whCheckInException.getBoxNo();
                    if (StringUtils.isNotBlank(boxNo)) {
                        String[][] logs = new String[][]{{"废弃异常单", ""},
                                {"relationNo", whCheckInException.getId().toString()}};
                        int updated = whBoxService.updateWhBoxOfUnbinding(boxNo, logs);
                        if (updated >= 1) {
                            logger.info("入库异常单废弃后释放周转码: boxNo[" + boxNo + "],  boxUpdated[" + updated + "]");
                        }
                    }
                }
                // 草稿/待仓库处理-》待采购
                if (StringUtils.isBlank(type) && (ExceptionStatus.UNCONFIRM.intCode().equals(exist.getStatus())
                        || ExceptionStatus.WAIT_PUSH.intCode().equals(exist.getStatus())
                        || ExceptionStatus.getWarehousePendingCode().contains(exist.getStatus()))) {
                    if (Objects.equals(exist.getStatus(),ExceptionStatus.QC_PENDING.intCode())){
                        whCheckInException.setCompletedQCHandleDate(new Timestamp(System.currentTimeMillis()));
                    }
                    Integer status = ExceptionStatus.PURCHASE_PENDING.intCode();
                    if (exist.isPurchaseUserVocationContainDev()){
                        status = ExceptionStatus.DEVELOP_CONFIRM.intCode();
                    }
                    whCheckInException.setStatus(status);
                    whCheckInException.setExceptionHandleDate(new Timestamp(System.currentTimeMillis()));// 异常处理时间
                    whCheckInException.setExceptionUser(DataContextHolder.getUserId());// 异常员
                }

                // 草稿/待仓库处理 只保存
                if (HandleTypeModal.ONLY_SAVE.equals(type) && (ExceptionStatus.UNCONFIRM.intCode().equals(exist.getStatus())
                        || ExceptionStatus.WAIT_PUSH.intCode().equals(exist.getStatus())
                        || ExceptionStatus.getWarehousePendingCode().contains(exist.getStatus()))) {
                    whCheckInException.setExceptionHandleDate(new Timestamp(System.currentTimeMillis()));// 异常处理时间
                    whCheckInException.setExceptionUser(DataContextHolder.getUserId());// 异常员
                    if (ExceptionStatus.UNCONFIRM.intCode().equals(exist.getStatus()))
                        whCheckInException.setStatus(ExceptionStatus.WAIT_PUSH.intCode());
                }
                // 推送至产品系统
                if (StringUtils.isBlank(type) && ExceptionStatus.getWarehousePendingCode().contains(exist.getStatus())
                        && ExceptionHandleWay.UPDATE_IMAGE.intCode().equals(exist.getHandleWay())) {
                    // 异常数量大于1,生成新异常
                    if (StringUtils.isNotBlank(exceptionBoxNo) && whCheckInException.getConfirmQuantity() > 1) {
                        try {
                            // 创建异常
                            create = new WhCheckInException();
                            BeanUtils.copyProperties(exist,create);
                            create.setBoxNo(exceptionBoxNo);
                            create.setId(null);
                            create.setStatus(ExceptionStatus.CHECK_IN_SAMPLE.intCode());
                            create.setQuantity(whCheckInException.getConfirmQuantity() - 1);
                            if (create.getConfirmQuantity() != null)
                                create.setConfirmQuantity(whCheckInException.getConfirmQuantity() - 1);
                            createWhCheckInException(create,null);
                            // 记录日志
                            createHandle = new WhCheckInExceptionHandle();
                            createHandle.setHandleComment(String.format("异常%s确认带样，生成新异常%s，数量：%s", whCheckInException.getId(),create.getId(),create.getQuantity()));
                            this.saveHandle(createHandle, create);
                            // 原来异常数改为1
                            whCheckInException.setQuantity(1);
                            whCheckInException.setConfirmQuantity(1);
                            whCheckInException.setStatus(ExceptionStatus.DEVELOP_RENEW_IMAGE.intCode());
                            // 记录日志，拼接到handle的HandleComment
                            String newComment = String.format("异常%s确认带样，生成新异常%s，调整异常数量：%s",
                                    whCheckInException.getId(), create.getId(), whCheckInException.getQuantity());

                            if (StringUtils.isNotBlank(handle.getHandleComment())) {
                                handle.setHandleComment(handle.getHandleComment() + "; " + newComment);
                            } else {
                                handle.setHandleComment(newComment);
                            }
                        } catch (Exception e) {
                            logger.error("带样换图/加图异常生成新异常失败",e);
                            throw new RuntimeException("带样换图/加图异常生成新异常失败,"+e.getMessage());
                        }
                    }

                    // 1. 判断该异常单之前是否有推送过产品系统
                    WhCheckInExceptionHandleQueryCondition condition = new WhCheckInExceptionHandleQueryCondition();
                    condition.setExceptionId(whCheckInException.getId());
                    condition.setHandleWay(ExceptionHandleWay.UPDATE_IMAGE.intCode());
                    condition.setStatus(ExceptionStatus.DEVELOP_RENEW_IMAGE.intCode());
                    WhCheckInExceptionHandle updateImageHandle = whCheckInExceptionHandleService
                            .queryWhCheckInExceptionHandle(condition);
                    if (updateImageHandle == null || updateImageHandle.getId() == null) {
                        // 2. 若无推送产品记录则推送
                        if (Objects.equals(exist.getStatus(),ExceptionStatus.QC_PENDING.intCode())){
                            whCheckInException.setCompletedQCHandleDate(new Timestamp(System.currentTimeMillis()));
                        }
                        whCheckInException.setStatus(ExceptionStatus.DEVELOP_RENEW_IMAGE.intCode());
                        whCheckInException.setHandleWay(ExceptionHandleWay.UPDATE_IMAGE.intCode());
                        whCheckInException.setSku(exist.getSku());
                        whCheckInException.setPurchaseUser(exist.getPurchaseUser());
                        whCheckInException.setExceptionType(exist.getExceptionType());
                        whCheckInException.setPurchaseOrderNo(exist.getPurchaseOrderNo());
                        ApiResult result = this.pushCheckInExceptionToProduct(whCheckInException);
                        if (!result.isSuccess()) {
                            // 推送失败，将入库异常状态变更为待推送开发
                            whCheckInException.setStatus(ExceptionStatus.WAIT_PUSH_PRODUCT.intCode());
                            //responseJson.setMessage("推送至产品系统失败：" + result.getErrorMsg());
                            //return responseJson;

                        }
                    }
                }

                this.updateWhCheckInException(whCheckInException);
                if (ExceptionStatus.DISCARDED.intCode().equals(whCheckInException.getStatus()))
                    this.updateLocationAuto(whCheckInException, exist.getLocationNumber());
                this.saveHandle(handle, whCheckInException);
                responseJson.setStatus(StatusCode.SUCCESS);
            } else {
                responseJson.setMessage("QC全检自动生成的异常单不能编辑！");
                return responseJson;
            }
        } else if ((StringUtils.isBlank(type) || HandleTypeModal.ADD.equalsIgnoreCase(type)) && whCheckInException != null
                && whCheckInException.getId() == null) {
            whCheckInException.setStatus(ExceptionStatus.UNCONFIRM.intCode());// 新增为草稿状态
            whCheckInException.setCreatedBy(DataContextHolder.getUserId());// 添加员
            whCheckInException.setCreationDate(new Timestamp(System.currentTimeMillis()));// 添加时间
            whCheckInException.setExceptionFrom(ExceptionFrom.ADD_EXCEPTION.intCode());
            this.createWhCheckInException(whCheckInException, null);
            this.saveHandle(handle, whCheckInException);
            WhCheckInException exception = whCheckInExceptionDao.queryWhCheckInException(whCheckInException.getId());
            if (exception != null)
                whCheckInException = exception;
            responseJson.setMessage(whCheckInException.getId().toString());
            responseJson.setStatus(StatusCode.SUCCESS);

            if (StringUtils.isNotBlank(whCheckInException.getTrackingNumber())) {
                WhPurchaseOrderQueryCondition query = new WhPurchaseOrderQueryCondition();
                query.setTrackingNumber(whCheckInException.getTrackingNumber());
                query.setQueryExpress(true);
                List<WhPurchaseOrder> whPurchaseOrderList = whPurchaseOrderService
                        .queryWhPurchaseExpressOrderAndItemList(query, null);
                if (CollectionUtils.isNotEmpty(whPurchaseOrderList)) {
                    for (WhPurchaseOrder purchaseOrder : whPurchaseOrderList) {
                        // 修改采购单状态,待入库
                        whPurchaseExpressRecordService.updatePurchaseOrderPurchaseStatus(
                                purchaseOrder.getPurchaseOrderNo(), WmsPurchaseOrderStatus.WAIT_STOCK_IN.intCode());
                        // 修改采购快递单状态,已处理
                        whPurchaseExpressRecordService.updatePurchaseExpressStatus(purchaseOrder.getPurchaseOrderNo(),
                                whCheckInException.getTrackingNumber(), PurchaseExpressStatus.PROCESSED.intCode());
                    }

                }
            }
        }
        // 仓库处理为保存的单、来源为QC全检的异常单不发送消息到采购
        if (!(HandleTypeModal.ONLY_SAVE.equals(type)
                || ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode().equals(whCheckInException.getExceptionFrom()))) {

            whCheckInException.setPurchaseUserNameToPms(whCheckInException.getPurchaseUserNameToPms());
            // 推送保存异常信息的消息到采购系统
            handle = handle.getId() != null
                    ? whCheckInExceptionHandleService.getWhCheckInExceptionHandle(handle.getId())
                    : null;
            logger.info("start to send saveWhCheckException message to pms ====exception:"
                    + whCheckInException.toString() + "===exceptionHandle:" + handle);
            rabbitmqProducerService.pushCheckInExceptionMsgToPms(whCheckInException, handle,
                    new PushCheckInException());
            logger.info("send saveWhCheckException message to pms end ");

            // 带样换图，创建新异常的
            if (create!= null) {
                // 推送保存异常信息的消息到采购系统
                createHandle = createHandle.getId()!= null
                       ? whCheckInExceptionHandleService.getWhCheckInExceptionHandle(createHandle.getId())
                        : null;
                logger.info("start to send saveWhCheckException create message to pms ====exception:"
                        + whCheckInException.toString() + "===exceptionHandle:" + handle);
                rabbitmqProducerService.pushCheckInExceptionMsgToPms(create, createHandle,
                        new PushCheckInException());
                logger.info("send saveWhCheckException create message to pms end ");
            }
        }

        return responseJson;
    }

    /**
     * 推送至产品系统
     *
     * @param whCheckInException
     */
    @Override
    public ApiResult pushCheckInExceptionToProduct(WhCheckInException whCheckInException) {
        try {
            
            WhCheckInExceptionHandleQueryCondition handleQuery = new WhCheckInExceptionHandleQueryCondition();
            handleQuery.setExceptionId(whCheckInException.getId());
            handleQuery.setHandleWay(ExceptionHandleWay.UPDATE_IMAGE.intCode());
            handleQuery.setStatusList(Arrays.asList(ExceptionStatus.WAREHOUSE_PENDING.intCode(),ExceptionStatus.QC_PENDING.intCode()));
            List<WhCheckInExceptionHandle> handles = whCheckInExceptionHandleService
                    .queryWhCheckInExceptionHandles(handleQuery, null);
            String dealRemark = null;
            if (CollectionUtils.isNotEmpty(handles)
                    && handles.stream().anyMatch(h -> StringUtils.contains(h.getHandleComment(), "处理备注"))) {
                String handleComment = handles.stream().filter(h -> StringUtils.contains(h.getHandleComment(), "处理备注"))
                        .findFirst().orElse(new WhCheckInExceptionHandle()).getHandleComment();
                dealRemark = StringUtils.substringAfterLast(handleComment, "处理备注：");
            }

            WhCheckInExceptionToProductDTO dto = new WhCheckInExceptionToProductDTO();
            if (StringUtils.isNotBlank(whCheckInException.getSku())) {
                WhSkuExtendQueryCondition condition = new WhSkuExtendQueryCondition();
                condition.setSku(whCheckInException.getSku());
                WhSkuExtend whSkuExtend = whSkuExtendService.queryWhSkuExtend(condition);
                dto.setSonSku(whCheckInException.getSku());
                dto.setMainSku(whSkuExtend == null ? null : whSkuExtend.getMainSku());
            }
            SaleUserQueryCondition query = new SaleUserQueryCondition();
            query.setUserId(DataContextHolder.getUserId());
            SaleUser user = saleUserService.querySaleUser(query);
            if (user != null) {
                dto.setUserName(DataContextHolder.getUserId() + "-" + user.getName());
            } else {
                dto.setUserName(DataContextHolder.getUserId() + "-" + DataContextHolder.getUsername());
            }
            dto.setAbnormalId(String.valueOf(whCheckInException.getId()));
            dto.setAbnormalType(whCheckInException.getExceptionType());
            dto.setDealCase(whCheckInException.getHandleWay());
            dto.setDealRemark(dealRemark);
            dto.setPurchaseUser(GetUserNameOrEmployeeNameUtil.getEmployeeName(null, whCheckInException.getPurchaseUser()));
            dto.setPurchasePo(whCheckInException.getPurchaseOrderNo());
            dto.setIsEnable(1);
            dto.setWarehouseId((long) CacheUtils.getLocalWarehouseId());
            logger.info("入库异常单推送至产品系统===>>>请求参数：" + JSON.toJSONString(dto));
            ApiResult result = HttpUtils.post(productConfigProperties.getPushCheckInExceptionUrl(), HttpUtils.ACCESS_TOKEN,
                    Collections.singletonList(dto), ApiResult.class);
            logger.info("入库异常单推送至产品系统===>>>响应结果：" + JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 新异常页面编辑
     *
     * @param domain
     * @param type
     * @return
     */
    @Override
    public ResponseJson saveNewWhCheckException(WhCheckInExceptionDo domain, String type, String abandonReason) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        WhCheckInException whCheckInException = domain.getWhCheckInException();

        WhCheckInException exist = null;
        if (whCheckInException.getId() != null) {
            exist = whCheckInExceptionDao.queryWhCheckInException(whCheckInException.getId());
            if (exist == null || !StringUtils.equalsIgnoreCase(HandleTypeModal.ADD, type)
                    && ExceptionStatus.getCanNotEditCode().contains(exist.getStatus())) {
                responseJson.setMessage("该异常单不存在或已被编辑，请确认后再操作！");
                return responseJson;
            }
        }
        
        WhCheckInExceptionHandle handle = new WhCheckInExceptionHandle();
        // 用于标识原先是否为待质控处理状态
        boolean isOriginalWaitQC = false;
        if (whCheckInException != null && whCheckInException.getId() != null && exist != null) {

            boolean locationEqual = StringUtils.isEmpty(exist.getLocationNumber())
                    ? StringUtils.isEmpty(whCheckInException.getLocationNumber())
                    : exist.getLocationNumber().equals(whCheckInException.getLocationNumber());
            if (!locationEqual) {
                String msg = "修改库位->原库位：" + exist.getLocationNumber()
                        + " ，新库位：" + whCheckInException.getLocationNumber();
                handle.setHandleComment(msg);
                responseJson = this.checkUpdateLocation(Arrays.asList(exist.getId()), whCheckInException.getLocationNumber());
                if (StatusCode.FAIL.equals(responseJson.getStatus()))
                    return responseJson;
            }
            if (StringUtils.isEmpty(whCheckInException.getLocationNumber()))
                whCheckInException.setLocationNumber("");

            // 来源为QC全检的异常单不做处理
            if (!ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode().equals(exist.getExceptionFrom())) {
                // 驳回：待仓库处理-》待采购处理
                if (StringUtils.isNotBlank(type) && HandleTypeModal.RETURN_DOWN.equalsIgnoreCase(type)
                        && ExceptionStatus.getWarehousePendingCode().contains(exist.getStatus())) {
                    handle.setHandleWay(ExceptionHandleWay.TURN_DOWN.intCode());
                    Integer status = ExceptionStatus.PURCHASE_PENDING.intCode();
                    if (exist.isPurchaseUserVocationContainDev()){
                        status = ExceptionStatus.DEVELOP_CONFIRM.intCode();
                    }
                    whCheckInException.setStatus(status);
                    whCheckInException.setHandleWay(ExceptionHandleWay.TURN_DOWN.intCode());

                    // 为待质控处理时，设置其为上一条日志的处理状态
                    if (Objects.equals(exist.getStatus(), ExceptionStatus.QC_PENDING.intCode())) {
                        isOriginalWaitQC = true;
                        WhCheckInExceptionHandleQueryCondition queryCondition = new WhCheckInExceptionHandleQueryCondition();
                        queryCondition.setExceptionId(exist.getId());
                        queryCondition.setOrderByStr(" ORDER BY creation_date DESC,id DESC ");
                        List<WhCheckInExceptionHandle> lastHandle = whCheckInExceptionHandleService.queryWhCheckInExceptionHandles(queryCondition, null);
                        lastHandle = Optional.ofNullable(lastHandle)
                                .orElse(new ArrayList<>())
                                .stream()
                                .filter(v -> !Objects.equals(v.getStatus(),ExceptionStatus.QC_PENDING.intCode()))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(lastHandle)) {
                            responseJson.setMessage("入库异常单ID = " + exist.getId() + "，不存在最近一条非待质控处理状态的处理日志");
                            return responseJson;
                        } else {
                            Integer lastHandleStatus = lastHandle.get(0).getStatus();
                            whCheckInException.setStatus(lastHandleStatus);
                            whCheckInException.setCompletedQCHandleDate(new Timestamp(System.currentTimeMillis()));
                        }
                    }
                }
                // 报废：草稿-》报废
                if (StringUtils.isNotBlank(type) && HandleTypeModal.SCRAP.equalsIgnoreCase(type)
                        && (ExceptionStatus.UNCONFIRM.intCode().equals(exist.getStatus())
                        || ExceptionStatus.WAIT_PUSH.intCode().equals(exist.getStatus()))) {
//                    handle.setHandleWay(ExceptionHandleWay.SCRAP.intCode());
                    whCheckInException.setStatus(ExceptionStatus.DISCARDED.intCode());
//                    whCheckInException.setHandleWay(ExceptionHandleWay.SCRAP.intCode());
                    whCheckInException.setDiscardedDate(new Timestamp(System.currentTimeMillis()));// 废弃时间
                    whCheckInException.setDiscardedUser(DataContextHolder.getUserId());// 废弃员
                    whCheckInException.setAbandonReason(abandonReason);

                    // 报废解绑周转筐
                    String boxNo = whCheckInException.getBoxNo();
                    if (StringUtils.isNotBlank(boxNo)) {
                        String[][] logs = new String[][]{{"废弃异常单", ""},
                                {"relationNo", whCheckInException.getId().toString()}};
                        int updated = whBoxService.updateWhBoxOfUnbinding(boxNo, logs);
                        if (updated >= 1) {
                            logger.info("入库异常单废弃后释放周转码: boxNo[" + boxNo + "],  boxUpdated[" + updated + "]");
                        }
                    }
                }
                // 草稿/待仓库处理-》待采购
                if (StringUtils.isBlank(type) && (ExceptionStatus.UNCONFIRM.intCode().equals(exist.getStatus())
                        || ExceptionStatus.WAIT_PUSH.intCode().equals(exist.getStatus())
                        || ExceptionStatus.getWarehousePendingCode().contains(exist.getStatus()))) {
                    Integer status = ExceptionStatus.PURCHASE_PENDING.intCode();
                    if (exist.isPurchaseUserVocationContainDev()){
                        status = ExceptionStatus.DEVELOP_CONFIRM.intCode();
                    }
                    whCheckInException.setStatus(status);
                    whCheckInException.setExceptionHandleDate(new Timestamp(System.currentTimeMillis()));// 异常处理时间
                    whCheckInException.setExceptionUser(DataContextHolder.getUserId());// 异常员
                }

                // 草稿/待仓库处理 只保存
                if (HandleTypeModal.ONLY_SAVE.equals(type) && (ExceptionStatus.UNCONFIRM.intCode().equals(exist.getStatus())
                        || ExceptionStatus.WAIT_PUSH.intCode().equals(exist.getStatus())
                        || ExceptionStatus.getWarehousePendingCode().contains(exist.getStatus()))) {
                    whCheckInException.setExceptionHandleDate(new Timestamp(System.currentTimeMillis()));// 异常处理时间
                    whCheckInException.setExceptionUser(DataContextHolder.getUserId());// 异常员
                    if (ExceptionStatus.UNCONFIRM.intCode().equals(exist.getStatus()))
                        whCheckInException.setStatus(ExceptionStatus.WAIT_PUSH.intCode());
                }

                this.updateWhCheckInException(whCheckInException);
                if (ExceptionStatus.DISCARDED.intCode().equals(whCheckInException.getStatus()))
                    this.updateLocationAuto(whCheckInException, exist.getLocationNumber());
                saveHandle(handle, whCheckInException);
                if (!whCheckInException.getStatus().equals(exist.getStatus())) {
                    // 记录关联表完成时间
                    handleExceptionDateService.doSaveExceptionDate(whCheckInException, exist.getStatus());
                }
                responseJson.setStatus(StatusCode.SUCCESS);
            } else {
                responseJson.setMessage("QC全检自动生成的异常单不能编辑！");
                return responseJson;
            }
        } else if ((StringUtils.isBlank(type) || HandleTypeModal.ADD.equalsIgnoreCase(type)) && whCheckInException != null
                && whCheckInException.getId() == null) {
            whCheckInException.setStatus(ExceptionStatus.UNCONFIRM.intCode());// 新增为草稿状态
            whCheckInException.setCreatedBy(DataContextHolder.getUserId());// 添加员
            whCheckInException.setCreationDate(new Timestamp(System.currentTimeMillis()));// 添加时间
            whCheckInException.setExceptionFrom(ExceptionFrom.ADD_EXCEPTION.intCode());
            this.createWhCheckInException(whCheckInException, null);
            saveHandle(handle, whCheckInException);
            WhCheckInException exception = this.getWhCheckInException(whCheckInException.getId());
            if (exception != null)
                whCheckInException = exception;
            responseJson.setMessage(whCheckInException.getId().toString());
            responseJson.setStatus(StatusCode.SUCCESS);

            if (StringUtils.isNotBlank(whCheckInException.getTrackingNumber())) {
                WhPurchaseOrderQueryCondition query = new WhPurchaseOrderQueryCondition();
                query.setTrackingNumber(whCheckInException.getTrackingNumber());
                query.setQueryExpress(true);
                List<WhPurchaseOrder> whPurchaseOrderList = whPurchaseOrderService
                        .queryWhPurchaseExpressOrderAndItemList(query, null);
                if (CollectionUtils.isNotEmpty(whPurchaseOrderList)) {
                    for (WhPurchaseOrder purchaseOrder : whPurchaseOrderList) {
                        // 修改采购单状态,待入库
                        whPurchaseExpressRecordService.updatePurchaseOrderPurchaseStatus(
                                purchaseOrder.getPurchaseOrderNo(), WmsPurchaseOrderStatus.WAIT_STOCK_IN.intCode());
                        // 修改采购快递单状态,已处理
                        whPurchaseExpressRecordService.updatePurchaseExpressStatus(purchaseOrder.getPurchaseOrderNo(),
                                whCheckInException.getTrackingNumber(), PurchaseExpressStatus.PROCESSED.intCode());
                    }

                }
            }
        }
        // 仓库处理为保存的单、来源为QC全检的异常单不发送消息到采购
        if (!ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode().equals(whCheckInException.getExceptionFrom())) {
            // 捞取需要推送到采购的异常单
            List<WhCheckInException> whCheckInExceptionList = getPushCheckInException(whCheckInException);
            if (CollectionUtils.isNotEmpty(whCheckInExceptionList)) {
                for (WhCheckInException whCheckInExce : whCheckInExceptionList) {
                    Integer beforeStatus = whCheckInExce.getStatus();
                    if (ExceptionStatus.UNCONFIRM.intCode().equals(whCheckInExce.getStatus())
                            || ExceptionStatus.WAIT_PUSH.intCode().equals(whCheckInExce.getStatus())
                            || ExceptionStatus.getWarehousePendingCode().contains(whCheckInExce.getStatus())) {
                        // 原先对象为待质控处理时，不修改为待采购处理/待开发确认状态，保持修改为日志上一条处理状态
                        if (!isOriginalWaitQC && Objects.equals(whCheckInException.getId(), whCheckInExce.getId())) {
                            Integer status = ExceptionStatus.PURCHASE_PENDING.intCode();
                            if (whCheckInExce.isPurchaseUserVocationContainDev()){
                                status = ExceptionStatus.DEVELOP_CONFIRM.intCode();
                            }
                            whCheckInExce.setStatus(status);
                        }
                        whCheckInExce.setExceptionHandleDate(new Timestamp(System.currentTimeMillis()));// 异常处理时间
                        whCheckInExce.setExceptionUser(DataContextHolder.getUserId());// 异常员
                    }
                    // 当前操作异常 更新日志状态
                    WhCheckInExceptionHandle whCheckInExceptionHandle = null;
                    WhCheckInExceptionHandle exceptionHandle = null;
                    if (whCheckInException.getId().equals(whCheckInExce.getId())) {
                        // 推送保存异常信息的消息到采购系统
                        if (handle.getId() != null)
                            exceptionHandle = whCheckInExceptionHandleService
                                    .getWhCheckInExceptionHandle(handle.getId());
                        if (exceptionHandle == null) {
                            whCheckInExceptionHandle = new WhCheckInExceptionHandle();
                            saveHandle(whCheckInExceptionHandle, whCheckInExce);
                        } else if (!exceptionHandle.getStatus().equals(ExceptionStatus.DISCARDED.intCode())) {
                            // 原先为待质控处理时，不修改为待采购处理状态保持原先的状态
                            if (!isOriginalWaitQC) {
                                Integer status = ExceptionStatus.PURCHASE_PENDING.intCode();
                                if (whCheckInExce.isPurchaseUserVocationContainDev()){
                                    status = ExceptionStatus.DEVELOP_CONFIRM.intCode();
                                }
                                exceptionHandle.setStatus(status);
                                whCheckInExceptionHandleService.updateWhCheckInExceptionHandle(exceptionHandle);
                            }
                            whCheckInExceptionHandle = exceptionHandle;
                        }
                    } else {
                        whCheckInExceptionHandle = new WhCheckInExceptionHandle();
                        saveHandle(whCheckInExceptionHandle, whCheckInExce);
                    }
                    this.updateWhCheckInException(whCheckInExce);
                    if (whCheckInExce.getStatus().equals(ExceptionStatus.PURCHASE_PENDING.intCode())
                            || whCheckInExce.getStatus().equals(ExceptionStatus.DEVELOP_CONFIRM.intCode())) {
                        // 记录关联表完成时间
                        handleExceptionDateService.doSaveExceptionDate(whCheckInExce, beforeStatus);
                    }
                    whCheckInExce.setPurchaseUserNameToPms(whCheckInExce.getPurchaseUserNameToPms());
                    logger.info("start to send saveWhCheckException message to pms ====exception:"
                            + whCheckInExce.toString() + "===exceptionHandle:" + whCheckInExceptionHandle);
                    rabbitmqProducerService.pushCheckInExceptionMsgToPms(whCheckInExce, whCheckInExceptionHandle,
                            new PushCheckInException());
                    logger.info("send saveWhCheckException message to pms end ");
                }
            }
        }
        return responseJson;
    }

    // 获取需要推送到采购系统的数据
    public List<WhCheckInException> getPushCheckInException(WhCheckInException whCheckInException) {
        // 非草稿状态直接返回
        if (!ExceptionStatus.UNCONFIRM.intCode().equals(whCheckInException.getStatus())
                && !ExceptionStatus.WAIT_PUSH.intCode().equals(whCheckInException.getStatus())) {
            return Collections.singletonList(whCheckInException);
        }
        // 异常来源 添加、或收货的
        if (whCheckInException.getConfirmQuantity() != null && whCheckInException.getConfirmQuantity() > 0
                && (ExceptionFrom.ADD_EXCEPTION.intCode().equals(whCheckInException.getExceptionFrom())
                || ExceptionFrom.SCAN_RECEIPT_EXCEPTION.intCode().equals(whCheckInException.getExceptionFrom()))) {
            WhCheckInExceptionQueryCondition exceptionQuery = new WhCheckInExceptionQueryCondition();
            exceptionQuery.setId(whCheckInException.getId());
            WhCheckInException exception = this.queryWhCheckInException(exceptionQuery);
            exception.setConfirmQuantity(whCheckInException.getConfirmQuantity());
            exception.setExceptionComment(whCheckInException.getExceptionComment());
            return Collections.singletonList(exception);
        }

        WhCheckInExceBatchQueryCondition batchQueryCondition = new WhCheckInExceBatchQueryCondition();
        batchQueryCondition.setExceId(whCheckInException.getId());
        WhCheckInExceBatch whCheckInExceBatch = whCheckInExceBatchService.queryWhCheckInExceBatch(batchQueryCondition);
        if (whCheckInExceBatch == null)
            return null;
        // 获取同批次的快递单
        String batchNo = whCheckInExceBatch.getBatchNo();
        // 是否点数完成
        if ((whCheckInExceBatch.getPurchaseOrderNo() + "-" + whCheckInExceBatch.getTrackingNumber())
                .equalsIgnoreCase(batchNo)) {
            return null;
        }
        WhCheckInExceptionQueryCondition queryCondition = new WhCheckInExceptionQueryCondition();
        queryCondition.setBatchNo(batchNo);
        queryCondition
                .setStatusList(Arrays.asList(ExceptionStatus.UNCONFIRM.intCode(), ExceptionStatus.WAIT_PUSH.intCode()));
        List<WhCheckInException> whCheckInExceptionList = this.queryNewWhCheckInExceptions(queryCondition, null);
        if (CollectionUtils.isEmpty(whCheckInExceptionList))
            return null;

        // 当前批次只有一条，并且无入库单记录
        boolean bool1 = whCheckInExceptionList.stream()
                .allMatch(w -> w.getInId() == null && w.getConfirmQuantity() != null && w.getConfirmQuantity() > 0);
        if (bool1)
            return whCheckInExceptionList;

        boolean verifyBool = whCheckInExceptionList.stream()
                .allMatch(w -> (w.getConfirmQuantity() != null && w.getConfirmQuantity() > 0)
                        || ExceptionStatus.CONFIRMED.intCode().equals(w.getStatus())
                        || ExceptionStatus.DISCARDED.intCode().equals(w.getStatus()));
        if (!verifyBool)
            return null;

        // 获取所有入库单
        List<WhCheckIn> allWhCheckInList = new ArrayList<>();
        for (WhCheckInException w : whCheckInExceptionList) {
            if (StringUtils.isNotBlank(w.getTrackingNumber()) && StringUtils.isNotBlank(w.getPurchaseOrderNo())) {
                WhCheckInQueryCondition query = new WhCheckInQueryCondition();
                query.setPurchaseOrderNo(w.getPurchaseOrderNo());
                query.setTrackingNumber(w.getTrackingNumber());
                List<WhCheckIn> whCheckInList = whCheckInService.queryWhCheckIns(query, null);
                if (CollectionUtils.isNotEmpty(whCheckInList)) {
                    allWhCheckInList.addAll(whCheckInList);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(allWhCheckInList)) {
            // 入库单状态，不为 QC不良品、待上架、上架中
            boolean bool = allWhCheckInList.stream()
                    .anyMatch(w -> !CheckInStatus.QC_NG.intCode().equals(w.getStatus())
                            && !CheckInStatus.WAITING_UP.intCode().equals(w.getStatus())
                            && !CheckInStatus.UPING.intCode().equals(w.getStatus())
                            && !CheckInStatus.UPERROR.intCode().equals(w.getStatus())
                            && !CheckInStatus.CONFIRMED.intCode().equals(w.getStatus())
                            && !CheckInStatus.DISCARDED.intCode().equals(w.getStatus()));
            if (bool)
                return null;
        }
        return whCheckInExceptionList;

    }

    /**
     * 保存日志
     *
     * @param handle
     * @param whCheckInException
     */
    @Override
    public void saveHandle(WhCheckInExceptionHandle handle, WhCheckInException whCheckInException) {
        handle.setCreatedBy(DataContextHolder.getUserId());
        handle.setExceptionId(whCheckInException.getId());
        if (whCheckInException.getConfirmQuantity() != null
                && (handle.getQuantity() == null || handle.getQuantity() > whCheckInException.getConfirmQuantity())) {
            handle.setQuantity(whCheckInException.getConfirmQuantity());
        }
        handle.setStatus(whCheckInException.getStatus());
        handle.setHandleWay(whCheckInException.getHandleWay());
        String msg = (StringUtils.isEmpty(whCheckInException.getExceptionComment()) ? ""
                : whCheckInException.getExceptionComment())
                + (StringUtils.isEmpty(handle.getHandleComment()) ? "" : "<br>" + handle.getHandleComment());
        if (StringUtils.isNotBlank(whCheckInException.getAbandonReason())) {
            msg += "<br>" + "废弃理由：" + whCheckInException.getAbandonReason();
        }
        handle.setHandleComment(msg);
        handle.setCreationDate(new Timestamp(System.currentTimeMillis()));
        handle.setCreateUserName(handle.getCreateUserName());
        whCheckInExceptionHandleService.createWhCheckInExceptionHandle(handle);
    }

    @Override
    public ResponseJson batchUpdateLocation(List<Integer> ids, String location) {
        ResponseJson response = new ResponseJson();
        try {
            WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();
            query.setIds(ids);
            List<WhCheckInException> existList = this.queryWhCheckInExceptions(query, null);
            if (CollectionUtils.isEmpty(existList)) {
                response.setStatus(StatusCode.FAIL);
                response.setMessage("没有找到要修改的异常单!");
                return response;
            }
            existList.removeIf(e -> e.getStatus().equals(ExceptionStatus.UNCONFIRM.intCode()));
            if (CollectionUtils.isEmpty(existList)) {
                response.setStatus(StatusCode.FAIL);
                response.setMessage("没有找到要修改的异常单,草稿状态的入库异常单不支持修改库位!");
                return response;
            }

            Map<Integer, WhCheckInException> existMap = existList.stream()
                    .collect(Collectors.toMap(WhCheckInException::getId, e -> e));
            ids = new ArrayList<>(existMap.keySet());

            response = this.checkUpdateLocation(ids, location);
            if (StatusCode.FAIL.equals(response.getStatus()))
                return response;

            whCheckInExceptionDao.batchUpdateLocation(ids, location);
            ids.stream().forEach(id -> {
                WhCheckInException exception = existMap.get(id);
                WhCheckInExceptionHandle handle = new WhCheckInExceptionHandle();
                handle.setExceptionId(id);
                handle.setCreatedBy(DataContextHolder.getUserId());
                handle.setQuantity(exception != null ? exception.getConfirmQuantity() : null);
                handle.setStatus(exception != null ? exception.getStatus() : null);
                handle.setHandleComment(
                        "修改库位->原库位：" + (exception != null ? exception.getLocationNumber() : "") + " 新库位：" + location);
                // 创建入库异常单处理详情
                whCheckInExceptionHandleService.createWhCheckInExceptionHandle(handle);
            });
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }

        return response;
    }

    @Override
    public ResponseJson checkUpdateLocation(List<Integer> ids, String location) {
        ResponseJson response = new ResponseJson();
        if (StringUtils.isNotEmpty(location)) {
            WhExLocationQueryCondition locationQuery = new WhExLocationQueryCondition();
            locationQuery.setLocation(location);
            WhExLocation whLocation = whExLocationService.queryWhExLocation(locationQuery);
            if (null == whLocation) {
                response.setStatus(StatusCode.FAIL);
                response.setMessage("无效库位, 请确认是否是入库异常库位！");
                return response;
            }
            if (whLocation.getLocationStatus() != null
                    && LocationStatus.CLOSEUP.intCode().equals(whLocation.getLocationStatus())) {
                response.setStatus(StatusCode.FAIL);
                response.setMessage("无效库位, 请确认入库异常库位是否启用！");
                return response;
            }
            // 实际品类
            Integer categoryActual = whLocation.getCategoryActual() == null ? 0 : whLocation.getCategoryActual();
            // 品类上限
            Integer categoryLimit = whLocation.getCategoryLimit() == null ? 0 : whLocation.getCategoryLimit();

            if (categoryActual > categoryLimit) {
                response.setStatus(StatusCode.FAIL);
                response.setMessage("此货位已达品类上限！");
                return response;
            }

            if (CollectionUtils.isNotEmpty(ids) && ids.size() + categoryActual > categoryLimit) {
                response.setStatus(StatusCode.FAIL);
                response.setMessage("此货位品类上限不足！");
                return response;
            }
        }
        return response;
    }

    /**
     * 全检生成少货异常
     *
     * @param inId
     * @param qcNum
     */
    @Override
    public void createLossSkuException(Integer inId, Integer qcNum) {
        if (inId == null) {
            return;
        }
        WhCheckIn whCheckIn = whCheckInService.getWhCheckInDetail(inId);
        if (whCheckIn != null && whCheckIn.getWhCheckInItem() != null) {
            WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();
            query.setInId(inId);
            query.setStatus(ExceptionStatus.UNCONFIRM.intCode());
            query.setExceptionType(ExceptionType.LESS_QUANTITY.intCode().toString());
            query.setExceptionFrom(ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode());
            query.setSku(whCheckIn.getWhCheckInItem().getSku());
            WhCheckInException dbWhCheckInException = this.queryWhCheckInException(query);

            WhCheckInException whCheckInException = new WhCheckInException();
            whCheckInException.setExceptionFrom(ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode());// 设置异常来源，“QC全检自动生成”
            qcNum = qcNum == null ? 0 : qcNum;
            Integer exceptionQuantity = whCheckIn.getWhCheckInItem().getQuantity() - qcNum;

            // TODO 记录未扫描的全检唯一码
            WhUniqueSkuQueryCondition queryCondition = new WhUniqueSkuQueryCondition();
            queryCondition.setRelationId(inId);
            queryCondition.setSku(whCheckIn.getWhCheckInItem().getSku());
            queryCondition.setAllCheckScanStatus(false);
            queryCondition.setType(SkuBusinessType.CHECK_IN.intCode());
            List<WhUniqueSku> whUniqueSkuList = whUniqueSkuService.queryWhUniqueSkus(queryCondition, null);
            String unScanSkuUuidStrs = "";
            String returnInformationJson = "";
            if (CollectionUtils.isNotEmpty(whUniqueSkuList)) {
                for (WhUniqueSku whUniqueSku : whUniqueSkuList) {
                    unScanSkuUuidStrs += whUniqueSku.getUuid() + ",";
                }
                // 记录未扫描的全检唯一码
                returnInformationJson = StringUtils.substringBeforeLast(unScanSkuUuidStrs, ",");
            }

            if (dbWhCheckInException != null) {
                // 存在异常单，更新异常数量
                whCheckInException.setId(dbWhCheckInException.getId());
                whCheckInException.setQuantity(exceptionQuantity);
                whCheckInException.setExceptionComment("QC全检修改异常数量");
                // 如果异常数量扣减到0，异常单变为已废弃
                if (exceptionQuantity == 0) {
                    whCheckInException.setStatus(ExceptionStatus.DISCARDED.intCode());
                    whCheckInException.setReturnInformationJson("");
                } else {
                    whCheckInException.setReturnInformationJson(returnInformationJson);
                }
                this.updateWhCheckInException(whCheckInException);
                if (ExceptionStatus.DISCARDED.intCode().equals(whCheckInException.getStatus()))
                    this.updateLocationAuto(whCheckInException, dbWhCheckInException.getLocationNumber());
            } else if (exceptionQuantity > 0 && CollectionUtils.isNotEmpty(whUniqueSkuList)) {
                // 创建异常入库单参数设置
                whCheckInException.setQuantity(whCheckIn.getWhCheckInItem().getQuantity() - qcNum);
                whCheckInException.setSku(whCheckIn.getWhCheckInItem().getSku());
                whCheckInException.setCreatedBy(DataContextHolder.getUserId());// 创建人
                whCheckInException.setWarehouseId(whCheckIn.getWarehouseId());// 仓库编号
                whCheckInException.setInId(whCheckIn.getInId());// 入库单编号
                whCheckInException.setPurchaseOrderNo(whCheckIn.getPurchaseOrderNo());// 采购单号
                whCheckInException.setTrackingNumber(whCheckIn.getTrackingNumber());// 快递单号
                whCheckInException.setStatus(ExceptionStatus.UNCONFIRM.intCode());// 设置初始状态，“草稿”
                whCheckInException.setExceptionType(ExceptionType.LESS_QUANTITY.intCode().toString());// 异常类型"少货"
                whCheckInException.setPurchaseUser(GetUserNameOrEmployeeNameUtil.getPurchaseUserId(whCheckIn));
                // 获取采购员编号。
                whCheckInException.setPurchaseUser(GetUserNameOrEmployeeNameUtil.getPurchaseUserId(whCheckIn));
                whCheckInException.setReturnInformationJson(returnInformationJson);

                this.createWhCheckInException(whCheckInException, null);
            }

            if (whCheckInException != null && whCheckInException.getId() != null) {
                // 生成异常日志，发送消息到采购
                skuScanExceptionService.saveHandle(whCheckInException);
            }
        }

    }

    /**
     * 批量废弃
     *
     * @param ids
     * @param reason
     * @return
     */
    @Override
    public List<Integer> batchUpdateToDiscarded(List<Integer> ids,String remark, String reason) {
        if (CollectionUtils.isEmpty(ids))
            return null;
        WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();
        query.setIds(ids);
        List<WhCheckInException> exceptionList = this.queryWhCheckInExceptions(query, null);
        if (CollectionUtils.isEmpty(exceptionList))
            return null;
        List<Integer> cantScrapIds = exceptionList.stream()
                .filter(e -> e.getStatus().equals(ExceptionStatus.COMPLETE.intCode())
                        || e.getStatus().equals(ExceptionStatus.WAIT_CHECK_IN.intCode())
                        || e.getStatus().equals(ExceptionStatus.STOCK_IN_ING.intCode())
                        || e.getStatus().equals(ExceptionStatus.DISCARDED.intCode())
                        || e.getStatus().equals(ExceptionStatus.RETURNED.intCode()))
                .map(WhCheckInException::getId).collect(Collectors.toList());
        exceptionList.removeIf(e -> cantScrapIds.contains(e.getId()));
        if (CollectionUtils.isEmpty(exceptionList))
            return cantScrapIds;
        exceptionList.stream().forEach(exception -> {
            WhCheckInException updateException = new WhCheckInException();
            updateException.setId(exception.getId());
            updateException.setStatus(ExceptionStatus.DISCARDED.intCode());
            updateException.setDiscardedUser(DataContextHolder.getUserId());// 废弃员
            updateException.setDiscardedDate(new Timestamp(System.currentTimeMillis()));// 废弃时间
            if (Objects.equals(ExceptionStatus.UNCONFIRM.intCode(),exception.getStatus())) {
                updateException.setExceptionComment(remark);
            }else{
                updateException.setExceptionComment(reason);
            }
            updateException.setQuantity(0);
            updateException.setExceptionFrom(exception.getExceptionFrom());
            updateException.setReturnInformationJson("");
            this.updateWhCheckInException(updateException);
            // 组装入库异常单处理详情数据
            WhCheckInExceptionHandle whCheckInExceptionHandle = new WhCheckInExceptionHandle();
            whCheckInExceptionHandle.setCreatedBy(DataContextHolder.getUserId());
            whCheckInExceptionHandle.setExceptionId(updateException.getId());
            whCheckInExceptionHandle.setQuantity(updateException.getQuantity());
            whCheckInExceptionHandle.setStatus(updateException.getStatus());
            whCheckInExceptionHandle.setHandleComment(updateException.getExceptionComment());
            // 创建入库异常单处理详情
            whCheckInExceptionHandleService.createWhCheckInExceptionHandle(whCheckInExceptionHandle);

            // QC全检不发送到采购系统
            if (!ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode().equals(exception.getExceptionFrom())) {
                updateException.setPurchaseUserNameToPms(exception.getPurchaseUserNameToPms());
                // 推送QC异常信息的消息到采购系统
                logger.info("start to send QcException message to pms ====exception:" + updateException.toString()
                        + "===exceptionHandle:" + whCheckInExceptionHandle.toString());
                rabbitmqProducerService.pushCheckInExceptionMsgToPms(updateException,
                        whCheckInExceptionHandleService.getWhCheckInExceptionHandle(whCheckInExceptionHandle.getId()),
                        new PushCheckInException());
                logger.info("send QcException message to pms end ");
            }
        });
        return cantScrapIds;
    }

    /**
     * 批量标记异常单
     * 
     * @param ids 异常单ID列表
     * @param markReason 标记原因
     * @param markRemark 标记备注
     * @return ApiResult
     */
    @Override
    public ApiResult<?> updateMarkExceptions(List<Integer> ids, String markReason, String markRemark) {
        try {
            if (CollectionUtils.isEmpty(ids)) {
                return ApiResult.newError("请选择要标记的异常单");
            }

            if (StringUtils.isBlank(markReason)) {
                return ApiResult.newError("请输入标记原因");
            }

            WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();
            query.setIds(ids);
            List<WhCheckInException> exceptionList = this.queryWhCheckInExceptions(query, null);
            if (CollectionUtils.isEmpty(exceptionList)) {
                return ApiResult.newError("异常单不存在");
            }

            Map<Integer, WhCheckInException> exceptionMap = exceptionList.stream().collect(Collectors.toMap(WhCheckInException::getId, e -> e));

            Integer currentUserId = DataContextHolder.getUserId();
            Timestamp currentTime = new Timestamp(System.currentTimeMillis());

            // 批量更新异常单标记信息
            AtomicInteger successCount = new AtomicInteger();
            List<WhCheckInException> updateList = new ArrayList<>();
            List<WhCheckInExceptionHandle> handleList = new ArrayList<>();
            ids.forEach(id -> {
                if (!exceptionMap.containsKey(id)) {
                    return;
                }

                WhCheckInException updateException = new WhCheckInException();
                updateException.setId(id);
                updateException.setMarkReason(markReason);
                updateException.setMarkTime(currentTime);
                updateException.setMarkUserId(currentUserId);
                updateList.add(updateException);

                // 记录日志
                WhCheckInException exception = exceptionMap.get(id);
                Integer qty = exception != null ? exception.getQuantity() : null;
                WhCheckInExceptionHandle handle = new WhCheckInExceptionHandle();
                handle.setExceptionId(id);
                handle.setHandleWay(ExceptionMarkWay.MARK.intCode());
                handle.setHandleComment("标记原因：" + markReason + "，备注：" + markRemark);
                handle.setCreatedBy(currentUserId);
                handle.setQuantity(qty);
                handleList.add(handle);
                successCount.getAndIncrement();
            });
            if (CollectionUtils.isNotEmpty(updateList)) {
                whCheckInExceptionDao.batchUpdateWhCheckInException(updateList);
            }
            if (CollectionUtils.isNotEmpty(handleList)) {
                whCheckInExceptionHandleService.batchCreateWhCheckInExceptionHandle(handleList);
            }

            return ApiResult.newSuccess("成功标记" + successCount.get() + "个异常单");
        }
        catch (Exception e) {
            logger.error("标记异常单失败", e);
            return ApiResult.newError("标记失败：" + e.getMessage());
        }
    }

    /**
     * 取消标记异常单
     * 
     * @param id 异常单ID
     * @return ApiResult
     */
    @Override
    public ApiResult<?> updateCancelMark(Integer id) {
        try {
            if (id == null) {
                return ApiResult.newError("异常单ID不能为空");
            }

            WhCheckInException exception = this.getWhCheckInException(id);
            if (exception == null) {
                return ApiResult.newError("异常单不存在");
            }

            // 清除标记信息
            whCheckInExceptionDao.batchCancelMarkWhCheckInException(List.of(id), DataContextHolder.getUserId());

            // 记录日志
            Integer currentUserId = DataContextHolder.getUserId();
            WhCheckInExceptionHandle handle = new WhCheckInExceptionHandle();
            handle.setExceptionId(id);
            handle.setHandleWay(ExceptionMarkWay.CANCEL_MARK.intCode());
            handle.setHandleComment("取消标记");
            handle.setCreatedBy(currentUserId);
            handle.setQuantity(exception.getQuantity());
            whCheckInExceptionHandleService.createWhCheckInExceptionHandle(handle);

            return ApiResult.newSuccess("取消标记成功");
        }
        catch (Exception e) {
            logger.error("取消标记失败", e);
            return ApiResult.newError("取消标记失败：" + e.getMessage());
        }
    }

    @Override
    public List<String> queryDistinctMarkReasons() {
        return whCheckInExceptionDao.queryDistinctMarkReasons();
    }
}