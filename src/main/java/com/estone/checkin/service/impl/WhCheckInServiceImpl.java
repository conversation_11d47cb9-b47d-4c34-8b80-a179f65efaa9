package com.estone.checkin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.estone.allocation.util.WarehouseProperties;
import com.estone.android.domain.AndroidProductDo;
import com.estone.checkin.bean.*;
import com.estone.checkin.dao.WhCheckInDao;
import com.estone.checkin.domain.WhCheckInDo;
import com.estone.checkin.enums.*;
import com.estone.checkin.service.*;
import com.estone.checkin.utils.CheckInClothingUtils;
import com.estone.checkin.utils.GetSkuQcCategoryDescUtil;
import com.estone.checkin.utils.GetUserNameOrEmployeeNameUtil;
import com.estone.common.enums.LogModule;
import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.foreign.bean.CheckInQuantityInfoDTO;
import com.estone.multiplelocation.bean.AllocateLocationMatchRecordBO;
import com.estone.multiplelocation.bean.ExpRuleSetting;
import com.estone.multiplelocation.enums.AllocatePhaseEnum;
import com.estone.multiplelocation.enums.HandleResultEnum;
import com.estone.multiplelocation.service.ExpRuleSettingService;
import com.estone.multiplelocation.service.LocationMatchRecordService;
import com.estone.pac.bean.TakeStockDTO;
import com.estone.pac.enums.RecordSourceEnum;
import com.estone.pac.enums.TakeStockReasonEnum;
import com.estone.pac.service.TakeStockRecordService;
import com.estone.sku.bean.*;
import com.estone.sku.enums.*;
import com.estone.sku.service.*;
import com.estone.sku.utils.UniqueSkuUtils;
import com.estone.statistics.enums.DrpTurnoverOderType;
import com.estone.system.param.bean.SystemParam;
import com.estone.system.param.service.SystemParamService;
import com.estone.system.rabbitmq.model.ProductSkuUpTimeMessage;
import com.estone.system.rabbitmq.model.PushCheckInException;
import com.estone.system.rabbitmq.producer.RabbitmqProducerService;
import com.estone.system.rabbitmq.service.AmqMessageService;
import com.estone.system.rabbitmq.utils.AssembleMessageDataUtils;
import com.estone.system.user.bean.SaleUser;
import com.estone.system.user.service.SaleUserService;
import com.estone.transfer.bean.WhFbaPurchaseData;
import com.estone.transfer.bean.WhFbaPurchaseDataQueryCondition;
import com.estone.transfer.service.WhFbaPurchaseDataService;
import com.estone.warehouse.aspect.StockServicelock;
import com.estone.warehouse.bean.*;
import com.estone.warehouse.enums.BoxType;
import com.estone.warehouse.enums.ItemOrderTypeEnum;
import com.estone.warehouse.enums.LocationTagEnum;
import com.estone.warehouse.enums.LocationType;
import com.estone.warehouse.service.*;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.json.ResponseJson;
import com.whq.tool.sqler.SqlerException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

@Service("whCheckInService")
public class WhCheckInServiceImpl implements WhCheckInService {

    private static Logger logger = LoggerFactory.getLogger(WhCheckInServiceImpl.class);

    final static SystemLogUtils CHECKINLOG = SystemLogUtils.create(LogModule.CHECKIN.getCode());

    private final static ExecutorService executors = ExecutorUtils.newFixedThreadPool(5);

    private final static ExecutorService apportionCostExecutors = ExecutorUtils.newFixedThreadPool(1);

    @Resource
    private WhCheckInDao whCheckInDao;

    @Resource
    private WhCheckInItemService whCheckInItemService;

    @Resource
    private WhBoxService whBoxService;

    @Resource
    private WhPurchaseExpressRecordService whPurchaseExpressRecordService;

    @Resource
    private PmsCheckInService pmsCheckInService;

    @Resource
    private CheckInUpdateStockService checkInUpdateStockService;

    @Resource
    private WhCheckInExceptionService whCheckInExceptionService;

    @Resource
    private WhCheckInExceptionHandleService whCheckInExceptionHandleService;

    @Resource
    private WhSkuService whSkuService;

    @Resource
    private RabbitmqProducerService rabbitmqProducerService;

    @Resource
    private WhWarehouseService whWarehouseService;

    @Resource
    private SystemParamService systemParamService;

    @Resource
    private WhPurchaseOrderService whPurchaseOrderService;

    @Resource
    private SaleUserService saleUserService;

    @Resource
    private WhStockService whStockService;

    @Resource
    private HandleExceptionDateService handleExceptionDateService;

    @Resource
    private LocationMoveInfoService locationMoveInfoService;

    @Resource
    private WhCheckInClothingService whCheckInClothingService;

    @Resource
    private WhCheckInExceBatchService whCheckInExceBatchService;

    @Resource
    private WhFbaPurchaseDataService whFbaPurchaseDataService;

    @Resource
    private PurchaseCostApportionService purchaseCostApportionService;

    @Resource
    private PurchaseCostApportionItemService purchaseCostApportionItemService;

    @Resource
    private AmqMessageService amqMessageService;

    @Resource
    private WhUniqueSkuService whUniqueSkuService;

    @Resource
    private ExpManageService expManageService;

    @Resource
    private ExpManageItemService expManageItemService;

    @Resource
    private UniqueSkuExpRelationService uniqueSkuExpRelationService;

    @Resource
    private TakeStockRecordService takeStockRecordService;

    @Resource
    private WhAllocateLocationRuleService whAllocateLocationRuleService;

    @Resource
    private WhLocationService whLocationService;

    @Resource
    private ExpRuleSettingService expRuleSettingService;

    @Resource
    private AfterSaleSettlementService afterSaleSettlementService;

    @Resource
    private LocationMatchRecordService locationMatchRecordService;

    @Resource
    private ExpWaitShipmentsService expWaitShipmentsService;
    
    @Resource
    private OutStockMatchHandelService outStockMatchHandelService;

    @Resource
    private PurchaseApvOutStockMatchService purchaseApvOutStockMatchService;

    @Resource
    private WhSkuSaleStatisticRecordService whSkuSaleStatisticRecordService;
    
    @Resource
    private NewProductMaintenanceService newProductMaintenanceService;

    @Resource
    private WhCheckInExcessService whCheckInExcessService;

    /**
     * This method corresponds to the database table wh_check_in
     *
     * @mbggenerated Tue Aug 14 11:26:19 CST 2018
     */
    public WhCheckIn getWhCheckIn(Integer id) {
        WhCheckIn whCheckIn = whCheckInDao.queryWhCheckIn(id);
        return whCheckIn;
    }

    /**
     * This method corresponds to the database table wh_check_in
     *
     * @mbggenerated Tue Aug 14 11:26:19 CST 2018
     */
    public WhCheckIn getWhCheckInDetail(Integer id) {
        WhCheckIn whCheckIn = whCheckInDao.queryWhCheckIn(id);

        // 关联查询
        /*
         * WhCheckInItemQueryCondition query = new WhCheckInItemQueryCondition();
         * query.setInId(whCheckIn.getInId()); WhCheckInItem whCheckInItem =
         * whCheckInItemService.queryWhCheckInItem(query);
         * whCheckIn.setWhCheckInItem(whCheckInItem);
         */

        return whCheckIn;
    }

    /**
     * This method corresponds to the database table wh_check_in
     *
     * @mbggenerated Tue Aug 14 11:26:19 CST 2018
     */
    public WhCheckIn queryWhCheckIn(WhCheckInQueryCondition query) {
        Assert.notNull(query);
        WhCheckIn whCheckIn = whCheckInDao.queryWhCheckIn(query);
        return whCheckIn;
    }

    /**
     * This method corresponds to the database table wh_check_in
     *
     * @mbggenerated Tue Aug 14 11:26:19 CST 2018
     */
    public List<WhCheckIn> queryAllWhCheckIns() {
        return whCheckInDao.queryWhCheckInList();
    }

    /**
     * This method corresponds to the database table wh_check_in
     *
     * @mbggenerated Tue Aug 14 11:26:19 CST 2018
     */
    public List<WhCheckIn> queryWhCheckIns(WhCheckInQueryCondition query, Pager pager) {
        Assert.notNull(query);
        int count = whCheckInDao.queryWhCheckInCount(query);
        if (pager != null && pager.isQueryCount()) {
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<WhCheckIn>();
            }
        } else if (query.getDownload() && count > 100000) {
            // 导出限制10万条数据
            return new ArrayList<WhCheckIn>();
        }
        List<WhCheckIn> whCheckIns = whCheckInDao.queryWhCheckInList(query, pager);
        return whCheckIns;
    }

    /**
     * This method corresponds to the database table wh_check_in
     *
     * @mbggenerated Tue Aug 14 11:26:19 CST 2018
     */
    public void createWhCheckIn(WhCheckIn whCheckIn) {
        try {
            whCheckInDao.createWhCheckIn(whCheckIn);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_check_in
     *
     * @mbggenerated Tue Aug 14 11:26:19 CST 2018
     */
    public void batchCreateWhCheckIn(List<WhCheckIn> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whCheckInDao.batchCreateWhCheckIn(entityList);
            } catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    /**
     * This method corresponds to the database table wh_check_in
     *
     * @mbggenerated Tue Aug 14 11:26:19 CST 2018
     */
    public void deleteWhCheckIn(Integer id) {
        try {
            whCheckInDao.deleteWhCheckIn(id);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_check_in
     *
     * @mbggenerated Tue Aug 14 11:26:19 CST 2018
     */
    public int updateWhCheckIn(WhCheckIn whCheckIn) {
        try {
            return whCheckInDao.updateWhCheckIn(whCheckIn);
        } catch (SqlerException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    /**
     * This method corresponds to the database table wh_check_in
     *
     * @mbggenerated Tue Aug 14 11:26:19 CST 2018
     */
    public void batchUpdateWhCheckIn(List<WhCheckIn> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whCheckInDao.batchUpdateWhCheckIn(entityList);
            } catch (SqlerException e) {
                logger.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public ResponseJson createWhCheckInAndWhCheckInItem(WhCheckIn whCheckIn) {
        ResponseJson response = handleCheckInParam(whCheckIn);
        if (StatusCode.FAIL.equals(response.getStatus())) {
            return response;
        }
        WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();

        if (whCheckInItem == null) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("入库单明细为空");
            return response;
        }

        whCheckIn.setSkuSerialNum(whCheckInItem.getQuantity());

        // 生成入库单时如果扫描的是采购单号，则需要查出最近的该采购单的入库记录绑定快递单
        WhPurchaseExpressRecordQueryCondition recordQuery = new WhPurchaseExpressRecordQueryCondition();
        String trackingNumber = whCheckIn.getTrackingNumber();
        if (StringUtils.isNotBlank(trackingNumber)) {
            recordQuery.setTrackingNumber(trackingNumber);
        } else {
            recordQuery.setPurchaseOrderNo(whCheckIn.getPurchaseOrderNo());
        }
        // 默认以创建时间降序排列，取第一条
        List<WhPurchaseExpressRecord> records = whPurchaseExpressRecordService.queryWhPurchaseExpressRecords(recordQuery,
                null);
        // 补发配件标签
        if (CollectionUtils.isNotEmpty(records) && records.get(0) != null
                && StringUtils.isNotEmpty(records.get(0).getLogisticsMark()))
            whCheckInItem.setFirstOrderType(StringUtils.isEmpty(whCheckInItem.getFirstOrderType())
                    ? CheckInFlags.REPLACEMENT_PARTS.getCode()
                    : whCheckInItem.getFirstOrderType() + "," + CheckInFlags.REPLACEMENT_PARTS.getCode());

        if ((StringUtils.isBlank(whCheckIn.getTrackingNumber()) || whCheckIn.getWarehouseId() == null)
                && StringUtils.isNotBlank(whCheckIn.getPurchaseOrderNo())) {

            if (CollectionUtils.isNotEmpty(records) && records.get(0) != null) {
                whCheckIn.setTrackingNumber(records.get(0).getTrackingNumber());
                whCheckIn.setWarehouseId(records.get(0).getWarehouseId());
            }
        }
        // 存在收货单，物流类型，指定拆分人与拆分时间
        List<WhPurchaseExpressRecord> updateRecord = new ArrayList<>();
        for (WhPurchaseExpressRecord record : records) {
            if (record.getSerialNumber() != null && record.getSerialNumber() == 1) {
                record.setSplitUser(DataContextHolder.getUserId());
                record.setSplitDate(new Timestamp(System.currentTimeMillis()));
                updateRecord.add(record);
            }
        }

        if (CollectionUtils.isNotEmpty(updateRecord)) {
            this.whPurchaseExpressRecordService.batchUpdateWhPurchaseExpressRecord(updateRecord);
        }

        // 2019-5-9 WMS-750 打标人就是贴标人
        whCheckIn.setTagUser(DataContextHolder.getUserId());
        whCheckIn.setTagTime(new Timestamp(System.currentTimeMillis()));
        // 添加SHOPIFY独立站标签
        if (whCheckInItem.getHasShopifyTJSku() != null && whCheckInItem.getHasShopifyTJSku() && StringUtils.isNotBlank(whCheckIn.getFlags())) {
            whCheckIn.setFlags(whCheckIn.getFlags() + ",SHOPIFY_TJ");
        }
        // 多货异常需标记全检入库
        if (whCheckIn.getWhCheckInException() != null
                && ExceptionType.EXCESS_QUANTITY.getCode().equals(whCheckIn.getWhCheckInException().getExceptionType())) {
            whCheckIn.setIsAllCheck(true);
        }
        if(whCheckIn.existFlag(PurchaseOrderFlags.NO_LABEL.getEnCode())) {
            whCheckIn.setIsAllCheck(false);
        }
        //设置入库单类型 1：本仓，2：中转仓

            // 多货提交单独处理
            if (CollectionUtils.isNotEmpty(whCheckIn.getWhCheckInExcessList())
                    && whCheckIn.getWhCheckInExcessList().size() > 1
                    && whCheckIn.getWhCheckInExcessList().stream()
                    .anyMatch(w -> StringUtils.contains(w.getPurchaseOrderNo(), PurchaseOrderType.NCGHW.getCode()))
                    && whCheckIn.getWhCheckInExcessList().stream().anyMatch(w ->
                    !StringUtils.contains(w.getPurchaseOrderNo(), PurchaseOrderType.NCGHW.getCode()))) {
                whCheckIn.setExceptionType(CheckInWhType.LOCAL.intCode());
                WhFbaPurchaseData fbaPurchaseData = null;
                if (!StringUtils.startsWith(whCheckIn.getPurchaseOrderNo(), PurchaseOrderType.NCGHW.getCode())) {
                    if (fbaPurchaseData == null) {
                        fbaPurchaseData = new WhFbaPurchaseData();
                        fbaPurchaseData.setPurchaseorderno(whCheckIn.getPurchaseOrderNo());
                        fbaPurchaseData.setSku(whCheckInItem.getSku());
                        fbaPurchaseData.setShipmentId(CheckInWhType.LOCAL.getEnName());
                        fbaPurchaseData.setModifiedDate(new Timestamp(System.currentTimeMillis()));
                        fbaPurchaseData.setOrderQuantity(whCheckInItem.getQuantity());
                        whFbaPurchaseDataService.createWhFbaPurchaseData(fbaPurchaseData);
                    } else {
                        WhFbaPurchaseData updateFbaPurchaseData = new WhFbaPurchaseData();
                        updateFbaPurchaseData.setId(fbaPurchaseData.getId());
                        updateFbaPurchaseData.setOrderQuantity(fbaPurchaseData.getOrderQuantity() + whCheckInItem.getQuantity());
                        fbaPurchaseData.setOrderQuantity(fbaPurchaseData.getOrderQuantity() + whCheckInItem.getQuantity());
                        whFbaPurchaseDataService.updateWhFbaPurchaseData(updateFbaPurchaseData);
                    }
                }
            } else {
                if (StringUtils.indexOf(whCheckIn.getPurchaseOrderNo(), PurchaseOrderType.NCGHW.getCode()) >= 0) {
                    WhFbaPurchaseDataQueryCondition queryCondition = new WhFbaPurchaseDataQueryCondition();
                    queryCondition.setPurchaseorderno(whCheckIn.getPurchaseOrderNo());
                    List<WhFbaPurchaseData> whFbaPurchaseDataList = whFbaPurchaseDataService
                            .queryWhFbaPurchaseDatas(queryCondition, null);
                    if (CollectionUtils.isEmpty(whFbaPurchaseDataList)) {
                        whCheckIn.setExceptionType(CheckInWhType.LOCAL.intCode());
                    } else {
                        whCheckIn.setExceptionType(CheckInWhType.FBA.intCode());
                    }
                } else {
                    whCheckIn.setExceptionType(CheckInWhType.LOCAL.intCode());
                }
            }
        if (StringUtils.isNotBlank(whCheckInItem.getSku())) {
            WhSkuQueryCondition skuQuery = new WhSkuQueryCondition();
            skuQuery.setSku(whCheckInItem.getSku());
            WhSku whSku = whSkuService.queryWhSku(skuQuery);
            if (whSku != null) {
                whCheckIn.setInSystemPacking(
                        ProcessType.getIntCode(whSku.getSkuAlias(), whSku.getFloorLocation()));// 入库时系统加工装袋
            }
        }
        if (whCheckIn.getInId() == null) {
            this.createWhCheckIn(whCheckIn);
        }
        logger.info("create whCheckIn :" + whCheckIn);

        // 创建成功先绑定周转码、防止重复提交
        String boxNo = whCheckIn.getBoxNo();
        boolean addWaitingQcStockResult = false;
        if (whCheckIn.getInId() != null) {
            WhStock stock = alloLocation(whCheckIn);
            if (whCheckInItem.zfOrder() && (stock == null || StringUtils.isBlank(stock.getLocationNumber()))) {
                // 直发拣货库位为空时，指定库位
                stock = whAllocateLocationRuleService.matchSpecialZfNewStock(whCheckInItem.getSku(), stock);
            }
            String locationNumber = null;
            if (stock != null && stock.getId() != null) {
                locationNumber = stock.getLocationNumber();
                whCheckInItem.setComment(stock.getId().toString());
            }
            if (whCheckInItem.zfOrder()) {
                locationNumber = purchaseApvOutStockMatchService.doShelfStraightHairLocationNumber(whCheckInItem.getSku());
            }
            whCheckIn.setLocationNumber(locationNumber);
            boolean fristCheckIn = false;
            if (whCheckInItem.getItemId() == null) {
                whCheckInItem.setLocation(locationNumber);
                whCheckInItem.setInId(whCheckIn.getInId());
                whCheckInItemService.createWhCheckInItem(whCheckInItem);
                fristCheckIn = true;
                logger.info("create whCheckInItem :" + whCheckInItem);
            }
            // 添加待QC数量
            addWaitingQcStockResult = checkInUpdateStockService.batchUpdateStockByCheckIn(whCheckIn);
            if (!addWaitingQcStockResult) {
                throw new RuntimeException("点数入库添加待QC库存失败！");
            }
            if (StringUtils.isNotBlank(boxNo) && fristCheckIn) {
                String[][] logs = new String[][]{{"生成入库单", ""}, {"relationNo", whCheckIn.getInId().toString()}};
                int updated = whBoxService.updateWhBoxOfBinding(boxNo, whCheckIn.getInId().toString(), logs);
                if (updated >= 1) {
                    logger.info("绑定周转码: relationNo[" + whCheckIn.getInId() + "], boxNo[" + whCheckIn.getBoxNo()
                            + "], boxUpdated[" + updated + "]");
                }
            }
        }

        // 添加采购单入库数量
        String purchaseOrderNo = whCheckIn.getPurchaseOrderNo();
        boolean result = false;
        String errorMessage = "";
        if (StringUtils.isNotBlank(purchaseOrderNo) && whCheckIn.getInId() != null) {
            ApiResult apiResult = new ApiResult<>();
            result = apiResult.isSuccess();
            errorMessage = apiResult.getErrorMsg() == null ? "" : apiResult.getErrorMsg();
            WhCheckIn updateWhCheckIn = new WhCheckIn();
            updateWhCheckIn.setInId(whCheckIn.getInId());
            updateWhCheckIn.setIsAddCreateQuantity(result);
            this.updateWhCheckIn(updateWhCheckIn);
        }

        if (StringUtils.isNotBlank(errorMessage)) {
            errorMessage = ",ErrorMsg=" + errorMessage;
        }
        if (whCheckIn.getWhCheckInItem() != null) {
            CHECKINLOG.log(whCheckIn.getInId(), CheckInLogType.CREATE_CHECKIN.getName(),
                    new String[][]{{"SKU", whCheckIn.getWhCheckInItem().getSku()},
                            {"数量", whCheckIn.getWhCheckInItem().getQuantity().toString()},
                            {"添加采购单入库数量", result + "" + errorMessage}, {"添加待QC库存", addWaitingQcStockResult + ""}});
        }
        if (whCheckInItem != null && whCheckInItem.getItemId() != null && StringUtils.isNotBlank(purchaseOrderNo)
                && StringUtils.isNotBlank(whCheckInItem.getSku())) {

            WhCheckInQueryCondition checkInQuery = new WhCheckInQueryCondition();
            if (StringUtils.isBlank(whCheckIn.getPurchaseOrderNo())) {
                checkInQuery.setTrackingNumber(whCheckIn.getTrackingNumber());
            } else {
                checkInQuery.setPurchaseOrderNo(whCheckIn.getPurchaseOrderNo());
            }
            List<WhCheckIn> whCheckInList = whCheckInDao.queryWhCheckInList(checkInQuery, null);

            int purchaseQuantity = 0;
            int checkInQuantity = whCheckInItem.getQuantity();

            int inStockedQuantity = whCheckInItem.getQuantity();
            int skuPurchaseQuantity = whCheckInItem.getQuantity();
            int revokedQuantity = 0;

            String purchaseNos = whCheckIn.getPurchaseOrderNo();
            for (WhCheckIn checkIn : whCheckInList) {
                if (!purchaseNos.contains(checkIn.getPurchaseOrderNo())) {
                    purchaseNos = purchaseNos + "," + checkIn.getPurchaseOrderNo();
                }
            }
            if (StringUtils.isNotBlank(purchaseNos)) {
                WhPurchaseOrderQueryCondition query = new WhPurchaseOrderQueryCondition();
                query.setPurchaseOrderNoStr(purchaseNos);
                query.setQueryExpress(true);
                List<WhPurchaseOrder> whPurchaseOrderList = whPurchaseOrderService
                        .queryWhPurchaseExpressOrderAndItemList(query, null);
                if (CollectionUtils.isNotEmpty(whPurchaseOrderList)) {
                    for (WhPurchaseOrder purchaseOrder : whPurchaseOrderList) {
                        if (CollectionUtils.isNotEmpty(purchaseOrder.getItems())) {
                            for (WhPurchaseItem item : purchaseOrder.getItems()) {
                                purchaseQuantity += item.getQuantity() == null ? 0 : item.getQuantity();
                                checkInQuantity += item.getCheckinQuantity() == null ? 0 : item.getCheckinQuantity();
                                if (item.getSku().equals(whCheckInItem.getSku())) {
                                    int itemCheckInQuantity = item.getCheckinQuantity() == null ? 0
                                            : item.getCheckinQuantity();
                                    inStockedQuantity = inStockedQuantity + itemCheckInQuantity;
                                    revokedQuantity = item.getRevokedQuantity() == null ? 0 : item.getRevokedQuantity();
                                    skuPurchaseQuantity = item.getQuantity() == null ? 0 : item.getQuantity();
                                }
                            }
                        }
                    }
                }
            }

            // 避免仓库扫描关联出采购单后采购系统修改取消在途数量后入库数量+取消在途>采购数量
            if (inStockedQuantity + revokedQuantity > skuPurchaseQuantity) {
                throw new RuntimeException("SKU【" + whCheckInItem.getSku() + "】入库数量加取消在途数量大于采购数量，请重新扫描确认可入库数量！");
            }

            int orderStatus = WmsPurchaseOrderStatus.PART_STOCK_IN.intCode();
            if (checkInQuantity >= purchaseQuantity) {
                orderStatus = WmsPurchaseOrderStatus.ALL_STOCK_IN.intCode();
            }

            // 修改SKU状态,待QC
            whPurchaseExpressRecordService.updatePurchaseSkuStatus(purchaseOrderNo, whCheckInItem.getSku(),
                    PurchaseSkuStatus.WAIT_QC.intCode(), null);
            // 修改采购单状态,入库中
            whPurchaseExpressRecordService.updatePurchaseOrderPurchaseStatus(purchaseOrderNo, orderStatus);

            // 扫描快递单入库，生成入库单时去完成未匹配采购单的异常单
            // 第一次生成入库单whCheckInItem.getQuantity().equals(checkInQuantity)
            if (StringUtils.isNotBlank(whCheckIn.getTrackingNumber())) {
                updateNotMatchPurchaseOrderExceptionToFinish(whCheckIn.getTrackingNumber());
            }

        }
        return response;
    }

    @Override
    public void addLocationMatchReocrd(String sku, Boolean isAfterSettle, Boolean isExp, String location,
                                       String relationNo, AllocatePhaseEnum allocateEnum, HandleResultEnum handleEnum) {
        try {
            AllocateLocationMatchRecordBO record = new AllocateLocationMatchRecordBO();
            record.setSku(sku);
            record.setAllocatePhase(AllocatePhaseEnum.CHECK_IN.getCode());
            if (allocateEnum != null)
                record.setAllocatePhase(allocateEnum.getCode());
            record.setHandleResult(handleEnum.getCode());
            record.setLocation(location);
            if (StringUtils.isBlank(location)) {
                record.setLocation("未匹配到库位");
            }
            record.setCalculateAfterSell(isAfterSettle);
            record.setExp(isExp);
            record.setRelateReceiptNo(relationNo);
            locationMatchRecordService.addRecord(record);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 新增库存分配记录
     */
    private void addLocationMatchReocrd(String sku, Boolean isAfterSettle, Boolean isExp, WhStock stock,
            String relationNo, HandleResultEnum handleEnum) {
        String location = stock != null ? stock.getLocationNumber() : null;
        this.addLocationMatchReocrd(sku, isAfterSettle, isExp, location, relationNo, null, handleEnum);
    }

    /**
     * 分配库位
     *
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public WhStock alloLocation(WhCheckIn whCheckIn) {
        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null
                || StringUtils.isEmpty(whCheckIn.getWhCheckInItem().getSku()))
            return null;
        WhCheckInItem checkInItem = whCheckIn.getWhCheckInItem();
        String sku = checkInItem.getSku();

        boolean afterSale = whCheckIn.getAfterSaleQty() != null && whCheckIn.getAfterSaleQty() > 0
                || StringUtils.contains(checkInItem.getFirstOrderTypeName(), CheckInFlags.SALED_BALANCE.getName());
        String expDate = whCheckIn.getExpDate();
        String proDate = whCheckIn.getProDate();
        Integer days = whCheckIn.getDays();

        boolean expSku = StringUtils.isNotEmpty(expDate) && StringUtils.isNotEmpty(proDate) && days != null
                || StringUtils.contains(checkInItem.getFirstOrderTypeName(), CheckInFlags.SHELF_LIFE_STORAGE.getName());

        String firstOrderType = checkInItem.getFirstOrderType();
        if (afterSale
                && !StringUtils.contains(checkInItem.getFirstOrderTypeName(), CheckInFlags.SALED_BALANCE.getName()))
            firstOrderType = StringUtils.isEmpty(firstOrderType) ? CheckInFlags.SALED_BALANCE.getCode()
                    : firstOrderType + "," + CheckInFlags.SALED_BALANCE.getCode();
        if (expSku && !StringUtils.contains(checkInItem.getFirstOrderTypeName(),
                CheckInFlags.SHELF_LIFE_STORAGE.getName()))
            firstOrderType = StringUtils.isEmpty(firstOrderType) ? CheckInFlags.SHELF_LIFE_STORAGE.getCode()
                    : firstOrderType + "," + CheckInFlags.SHELF_LIFE_STORAGE.getCode();
        checkInItem.setFirstOrderType(firstOrderType);
        
        try {

            WhStockQueryCondition stockQuery = new WhStockQueryCondition();
            stockQuery.setSku(sku);
            stockQuery.setQueryLocationType(true);
            List<WhStock> whStockList = whStockService.queryWhStocks(stockQuery, null);
            whStockList = whStockList.stream().filter(stock -> StringUtils.isBlank(stock.getLocationNumber())
                    || (!stock.getLocationNumber().startsWith("ZF") && !stock.getLocationNumber().startsWith("ZJH")))
                    .collect(toList());
            // 如果sku库存记录为空，或者库位为空直接返回分配库位
            if (CollectionUtils.isEmpty(whStockList)
                    || whStockList.stream().allMatch(s -> StringUtils.isBlank(s.getLocationNumber()))) {
                WhStock stock = whAllocateLocationRuleService.matchSkuLocation(sku, null, expSku, afterSale);
                this.addLocationMatchReocrd(sku, afterSale, expSku, stock, whCheckIn.getPurchaseOrderNo(),
                        HandleResultEnum.NEW);
                return stock;
            }

            //按创建时间排序
            whStockList = whStockList.stream().sorted(Comparator.comparing(WhStock::getCreationDate))
                    .collect(toList());

            // 库位记录为空，返回分配库位
            if (whStockList.stream().allMatch(
                    l -> l.getLocationType() == null || LocationType.VIRTUAL.intCode().equals(l.getLocationType()))) {
                WhStock stock = whAllocateLocationRuleService.matchSkuLocation(sku, null, expSku, afterSale);
                HandleResultEnum handleEnum = HandleResultEnum.NEW;
                if (stock != null) {
                    this.addLocationMatchReocrd(sku, afterSale, expSku, stock, whCheckIn.getPurchaseOrderNo(),
                            handleEnum);
                    return stock;
                }
                else {
                    handleEnum = HandleResultEnum.ORIGIN;
                    this.addLocationMatchReocrd(sku, afterSale, expSku, stock, whCheckIn.getPurchaseOrderNo(),
                            handleEnum);
                    return whStockList.get(0);
                }
            }

            // 非保质期和售后结算，有非存货库位，不需要新库位；指定库位为原库位，否则分配新库位
            if (!afterSale && !expSku) {
                WhStock stock = whStockList.stream()
                        .filter(l -> l.getLocationType() != null
                                && !LocationType.VIRTUAL.intCode().equals(l.getLocationType())
                                && !LocationType.PRESTORE.intCode().equals(l.getLocationType()))
                        .findFirst().orElse(null);
                HandleResultEnum handleEnum = HandleResultEnum.ORIGIN;
                if (stock == null) {
                    stock = whAllocateLocationRuleService.matchSkuLocation(sku, null, expSku, afterSale);
                    handleEnum = HandleResultEnum.NEW;
                }
                this.addLocationMatchReocrd(sku, afterSale, expSku, stock, whCheckIn.getPurchaseOrderNo(),
                        handleEnum);
                return stock;
            }

            Map<Integer, WhStock> stockLocationMap = whStockList.stream()
                    .filter(l -> !LocationType.PRESTORE.intCode().equals(l.getLocationType()))
                    .collect(toMap(WhStock::getId, s -> s));
            // 校验保质期及售后计算
            List<Integer> pickStockIds = this.verifyExpAndAfterSaleSettle(afterSale, expSku, whCheckIn,
                    new ArrayList<>(stockLocationMap.values()));
            if (CollectionUtils.isNotEmpty(pickStockIds)) {
                // 有满足保质期和售后结算规则的库位、或不是保质期和售后结算SKU
                WhStock stock = stockLocationMap.get(pickStockIds.get(0));
                this.addLocationMatchReocrd(sku, afterSale, expSku, stock, whCheckIn.getPurchaseOrderNo(),
                        HandleResultEnum.ORIGIN);
                return stock;
            }
            // 得到不进行匹配的库位的集合
            List<String> unMatchLocationNumbers = whStockList.stream()
                    .filter(l -> LocationType.PICKING.intCode().equals(l.getLocationType()))
                    .map(WhStock::getLocationNumber)
                    .collect(toList());
            List<WhLocation> unMatchLocations = null;
            if (CollectionUtils.isNotEmpty(unMatchLocationNumbers)) {
                WhLocationQueryCondition query = new WhLocationQueryCondition();
                query.setLocationList(unMatchLocationNumbers);
                unMatchLocations = whLocationService.queryWhLocations(query, null);
            }
            WhStock stock = whAllocateLocationRuleService.matchSkuLocation(sku, unMatchLocations, expSku, afterSale);
            this.addLocationMatchReocrd(sku, afterSale, expSku, stock, whCheckIn.getPurchaseOrderNo(),
                    HandleResultEnum.NEW);
            return stock;
        }
        catch (Exception e) {
            logger.error(e.getMessage(), e);
            this.addLocationMatchReocrd(sku, afterSale, expSku, null, whCheckIn.getPurchaseOrderNo(),
                    HandleResultEnum.NEW);
            return null;
        }
    }

    /**
     * 校验售后结算和保质期
     */
    private List<Integer> verifyExpAndAfterSaleSettle(boolean afterSale, boolean expSku, WhCheckIn whCheckIn,
                                                      List<WhStock> whStockList) {
        if (CollectionUtils.isEmpty(whStockList))
            return null;
        //排除非拣货库位
        whStockList.removeIf(s->!LocationType.PICKING.intCode().equals(s.getLocationType()));
        
        if (CollectionUtils.isEmpty(whStockList))
            return null;
        String sku = whCheckIn.getWhCheckInItem().getSku();
        List<Integer> pickStockIds = null;
        if (afterSale) {
            return this.verifyLocationAfterSaleSettle(sku, whCheckIn.getSupplierId(), whStockList, whCheckIn);
        } else if (expSku) {
            pickStockIds = whStockList.stream().filter(c -> !c.existLocationTag(LocationTagEnum.SALED_BALANCE)).map(WhStock::getId).collect(toList());
            return this.verifyLocationExp(sku, pickStockIds, whCheckIn);
        }
        pickStockIds = whStockList.stream().filter(c -> !c.existLocationTag(LocationTagEnum.SALED_BALANCE)).map(WhStock::getId).collect(toList());
        // 非售后结算、非保质期
        return pickStockIds;
    }

    /**
     * 校验售后结算
     */
    private List<Integer> verifyLocationAfterSaleSettle(String sku, String vendorCode, List<WhStock> whStockList,
                                                        WhCheckIn whCheckIn) {

        if (CollectionUtils.isEmpty(whStockList))
            return null;
        //排除非拣货库位
        whStockList.removeIf(s->!LocationType.PICKING.intCode().equals(s.getLocationType()));

        if (CollectionUtils.isEmpty(whStockList))
            return null;

        List<Integer> pickStockIds = whStockList.stream().map(WhStock::getId).collect(toList());

        if (StringUtils.isEmpty(vendorCode))
            return pickStockIds;

        AfterSaleSettlementQueryCondition settleQuery = new AfterSaleSettlementQueryCondition();
        settleQuery.setSku(sku);
        settleQuery.setStockIdList(pickStockIds);
        List<AfterSaleSettlement> settleList = afterSaleSettlementService.queryAfterSaleSettlements(settleQuery, null);

        List<Integer> noStockList = whStockList.stream().filter(s -> s.getTotalQuantity() == null || s.getTotalQuantity() == 0).map(WhStock::getId).collect(toList());

        if (CollectionUtils.isEmpty(settleList) && CollectionUtils.isEmpty(noStockList)) {
            return null;
        }
        if (CollectionUtils.isEmpty(settleList) && CollectionUtils.isNotEmpty(noStockList)) {
            return noStockList;
        }

        Map<Integer, List<AfterSaleSettlement>> settleMap = settleList.stream()
                .collect(Collectors.groupingBy(AfterSaleSettlement::getStockId));

        Map<Integer, WhStock> stockMap = whStockList.stream().collect(toMap(WhStock::getId, c -> c));

        List<Integer> expIds = new ArrayList<>();
        List<Integer> stockIds = new ArrayList();
        for (Map.Entry<Integer, List<AfterSaleSettlement>> entry : settleMap.entrySet()) {
            List<AfterSaleSettlement> val = entry.getValue();
            boolean consistent = val.stream().allMatch(c -> StringUtils.equals(vendorCode, c.getVendorCode()));
            if (consistent) {
                // 同供应商
                expIds.add(entry.getKey());
            } else {
                WhStock whStock = stockMap.get(entry.getKey());
                if (whStock != null && whStock.getTotalQuantity() == 0) {
                    // 原库位中总仓库库存为0 则允许放入
                    stockIds.add(entry.getKey());
                }
            }
        }
        // 同供应商校验保质期
        stockIds.addAll(this.verifyLocationExp(sku, expIds, whCheckIn));
        return stockIds;
    }

    /**
     * 验证保质期
     */
    @Override
    public List<Integer> verifyLocationExp(String sku, List<Integer> pickStockIds, WhCheckIn whCheckIn) {
        if (CollectionUtils.isEmpty(pickStockIds) || whCheckIn == null || StringUtils.isBlank(sku)
                || whCheckIn.getDays() == null || StringUtils.isEmpty(whCheckIn.getExpDate())) {
            return pickStockIds;
        }

        // 校验保质期是否匹配
        ExpManageQueryCondition expQuery = new ExpManageQueryCondition();
        expQuery.setSku(sku);
        expQuery.setStockIdList(pickStockIds);
        List<ExpManage> expManages = expManageService.queryExpManages(expQuery, null);
        if (CollectionUtils.isEmpty(expManages)) {
            return pickStockIds;
        }
        List<ExpRuleSetting> expRuleSettings = expRuleSettingService.queryAll();
        if (CollectionUtils.isEmpty(expRuleSettings)) {
            return pickStockIds;
        }
        //当保质期SKU未找到配置规则时则视为无保质期间隔限制
        boolean noneMatch = expRuleSettings.stream().noneMatch(c -> whCheckIn.getDays() >= c.getExpGe() && whCheckIn.getDays() <= c.getExpLe());
        if (noneMatch) {
            return pickStockIds;
        }

        Map<Integer, List<ExpManage>> expMap = expManages.stream().collect(Collectors.groupingBy(c -> c.getStockId()));
        List<Integer> stockIds = new ArrayList();
        for (Map.Entry<Integer, List<ExpManage>> entry : expMap.entrySet()) {
            List<ExpManage> val = entry.getValue();
            boolean anyMatch = Optional.ofNullable(val).orElse(Collections.emptyList()).stream().anyMatch(c -> DateUtils
                    .stringToDate("2023-05-17 00:00:00", DateUtils.STANDARD_DATE_PATTERN).before(c.getCreationDate()));
            if (anyMatch) {
                val = val.stream()
                        .filter(c -> DateUtils.stringToDate("2023-05-17 00:00:00", DateUtils.STANDARD_DATE_PATTERN)
                                .before(c.getCreationDate()))
                        .collect(toList());
                val.sort(Comparator.comparing(ExpManage::getCreationDate));
            } else {
                // 根据创建时间倒序
                val.sort(Comparator.comparing(ExpManage::getCreationDate).reversed());
            }
            ExpManage exp = val.get(0);
            for (ExpRuleSetting rule : expRuleSettings) {
                if (rule.getExpLe() >= exp.getDays() && rule.getExpGe() <= exp.getDays()) {
                    // TODO 保质期规则按天数或按比例
                    int day = exp.getDays() * rule.getSameLocationExpSpan() / 100;
                    if (rule.getDayType() != null && rule.getDayType() == 2) {
                        day = rule.getSameLocationExpSpan();
                    }
                    Integer daysBetween = DateUtils
                            .getDaysBetween(Timestamp.valueOf(whCheckIn.getExpDate() + " 00:00:00"), exp.getExpDate());
                    if (Math.abs(daysBetween) <= day) {
                        // 库位保质期规则满足入库单保质期
                        stockIds.add(entry.getKey());
                        break;
                    }
                }
            }
        }
        return stockIds;
    }

    @Override
    public boolean doAlloLocationBtn(WhCheckIn whCheckIn, String newLocation) {
        WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();
        if (whCheckInItem == null) {
            throw new RuntimeException("未找到入库单明细条目");
        }
        if (StringUtils.isNotBlank(whCheckInItem.getLocation()) || whCheckInItem.getSkuId() == null) {
            throw new RuntimeException("入库单已存在库位、或入库单没有绑定的库存ID");
        }
        String sku = whCheckInItem.getSku();
        WhStockQueryCondition stockQuery = new WhStockQueryCondition();
        stockQuery.setId(whCheckInItem.getSkuId());
        WhStock whStock = whStockService.queryWhStock(stockQuery);
        if (whStock == null) {
            throw new RuntimeException("未找到库存信息");
        }

        stockQuery.setId(null);
        stockQuery.setLocationNumber(newLocation);
        stockQuery.setSku(whCheckInItem.getSku());
        List<WhStock> stockList = whStockService.queryWhStocks(stockQuery, null);
        if (CollectionUtils.isNotEmpty(stockList)) {
            // 手动分配库位时查询保质期信息
            WhUniqueSkuQueryCondition queryCondition = new WhUniqueSkuQueryCondition();
            queryCondition.setRelationId(whCheckIn.getInId());
            queryCondition.setSku(sku);
            queryCondition.setType(SkuBusinessType.CHECK_IN.intCode());
            List<WhUniqueSku> whUniqueSkuList = whUniqueSkuService.queryWhUniqueSkus(queryCondition, null);
            // 查询唯一码保质期
            UniqueSkuUtils.getExistExpInfo(whUniqueSkuList, whCheckIn);
            boolean afterSale = whCheckIn.getAfterSaleQty() != null && whCheckIn.getAfterSaleQty() > 0
                    || StringUtils.contains(whCheckInItem.getFirstOrderType(), CheckInFlags.SALED_BALANCE.getCode());

            String expDate = whCheckIn.getExpDate();
            String proDate = whCheckIn.getProDate();
            Integer days = whCheckIn.getDays();

            boolean expSku = StringUtils.isNotEmpty(expDate) && StringUtils.isNotEmpty(proDate) && days != null;

            // 当前SKU在新库位存在库存记录 校验保质期和售后结算
            List<Integer> stockIds = this.verifyExpAndAfterSaleSettle(afterSale, expSku, whCheckIn, stockList);
            if (CollectionUtils.isEmpty(stockIds)) {
                throw new RuntimeException("不符合保质期规则或售后结算规则，不允许分配到该库位");
            }
        }

        WhSkuQueryCondition skuQuery = new WhSkuQueryCondition();
        skuQuery.setSku(sku);
        WhSku whSku = whSkuService.queryWhSku(skuQuery);
        Map<String, Object> skuMap = new HashMap<>();
        skuMap.put(sku, whSku);
        List<LocationMoveInfo> list = new ArrayList<>();
        LocationMoveInfo locationMoveInfo = new LocationMoveInfo();
        locationMoveInfo.setSku(sku);
        locationMoveInfo.setRemark(whCheckIn.getInId() + "");
        locationMoveInfo.setNewLocation(newLocation);
        list.add(locationMoveInfo);
        ResponseJson responseJson = locationMoveInfoService.pcMoveLocation(list, skuMap);
        if (responseJson.getStatus().equals(StatusCode.FAIL)) {
            throw new RuntimeException("库位调整失败" + responseJson.getMessage());
        }
        return true;
    }

    @Override
    @StockServicelock
    public String doUpSkuToLocation(List<String> skuList, AndroidProductDo domain) {
        WhCheckIn whCheckIn = this.getWhCheckInDetail(domain.getInId());
        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null) {
            return "未找到入库单信息";
        }
        if (whCheckIn.getWhCheckInItem().getSkuId() == null) {
            return "入库单没有绑定的库存ID";
        }
        WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();
        // 先安排库位
        if (!domain.isPreStoreUp() && StringUtils.isNotBlank(domain.getLocation())
                && StringUtils.isBlank(whCheckInItem.getLocation())) {
            ResponseJson moveResponse = locationMoveInfoService.pcMoveLocationBySkuFromPDA(whCheckIn.getInId(),
                    whCheckInItem.getSku(), whCheckInItem.getSkuId(), domain.getLocation());
            if (moveResponse.getStatus().equals(StatusCode.FAIL)) {
                return moveResponse.getMessage();
            }
        }
        String requestTime = DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss.SSS");
        String result = null;
        boolean flag = false;
        if (CheckInStatus.UPERROR.intCode().equals(whCheckIn.getStatus())) {
            flag = true;
        }
        if (whCheckIn != null && (CheckInStatus.UPING.intCode().equals(whCheckIn.getStatus()) || flag)
                && whCheckInItem != null) {
            if (domain.getQuantity() != null && domain.getQuantity() > 0
                    && domain.getQuantity() <= (whCheckInItem.getQcQuantity() == null ? whCheckInItem.getQuantity()
                    : whCheckInItem.getQcQuantity())) {

                // 采购单入库为部分入库或已入库
                boolean allUp = false;
                String purchaseOrderNo = whCheckIn.getPurchaseOrderNo();
                String articleNumber = whCheckInItem.getSku();
                Integer quantity = domain.getQuantity();
                Integer checkinQuantity = whCheckInItem.getQuantity();
                boolean result1 = whCheckIn.getIsPurchaseStockIn() != null && whCheckIn.getIsPurchaseStockIn() ? true : false;
                String errorMessage = "";
                boolean isBlend = false;
                // 添加WMS库存
                Boolean result2 = whCheckIn.getIsInStock() == null ? false : whCheckIn.getIsInStock();

                boolean afterSale = whCheckIn.getAfterSaleQty() != null && whCheckIn.getAfterSaleQty() > 0
                        || StringUtils.contains(whCheckInItem.getFirstOrderType(), CheckInFlags.SALED_BALANCE.getCode());

                /**
                 * 上架前校验是否有取消在途
                 * 待发处理状态  "wait_Handler","待处理","part_Handler","部分处理","already_Handler","已处理"
                 */
                WhPurchaseOrderQueryCondition pQuery = new WhPurchaseOrderQueryCondition();
                pQuery.setPurchaseOrderNo(purchaseOrderNo);
                List<WhPurchaseOrder> whPurchaseOrders = whPurchaseOrderService.queryWhPurchaseOrderAndItems(pQuery, null);
                List<String> unAfterSaleOrderTypes = Arrays.asList(PurchaseOrderType.NCGZS.getCode(),PurchaseOrderType.NCGCW.getCode(),PurchaseOrderType.NCGHH.getCode());
                if (CollectionUtils.isNotEmpty(whPurchaseOrders)) {
                    WhPurchaseOrder order = whPurchaseOrders.get(0);
                    String purchaseOrderType = order.getPurchaseOrderType();
                    if (StringUtils.isNotBlank(order.getJsonData())) {
                        WhPurchaseOrder init = order.init();
                        for (WhPurchaseItem item : init.getItems()) {
                            if (StringUtils.equals(articleNumber, item.getSku())) {
                                if ((afterSale || StringUtils.isNotEmpty(init.getPayMethod())
                                        && StringUtils.contains(init.getPayMethod(), "售后"))
                                        && !unAfterSaleOrderTypes.contains(purchaseOrderType)){
                                    whCheckIn.setAfterSaleQty(item.getQuantity());
                                }
//                                if ("wait_Handler".equals(item.getCancelHandleStatus())
//                                        || "part_Handler".equals(item.getCancelHandleStatus())) {
//                                    throw new RuntimeException("当前采购单SKU还有采购未处理的取消在途任务，请联系采购处理后再上架");
//                                }
                            }
                        }
                    }
                }

                if (!result2) {
                    whCheckIn.getWhCheckInItem().setQuantity(domain.getQuantity());
                    // WMS加库存是否成功
                    whCheckIn.setLocationNumber(domain.getLocation());
                    whCheckIn.setPreStoreUp(domain.isPreStoreUp());
                    // 不入库直发
                    if (whCheckInItem.zfOrder()) {
                        whCheckIn.setLocationNumber(whCheckInItem.getLocation());
                        result2 = checkInUpdateStockService.batchUpdateStockByPick(whCheckIn);
                    }
                    else {
                        result2 = checkInUpdateStockService.batchUpdateStockByUp(whCheckIn);
                    }
                    isBlend = whCheckIn.isBlend();
                }


                int updateLines = 0;// 入库单状态修改是否成功


                //wms改库存成功才去调用采购上架接口
                if (result2) {
                    if (!result1) {

                        WhStockQueryCondition stockQuery = new WhStockQueryCondition();
                        stockQuery.setSku(articleNumber);
                        stockQuery.setLocationNumber(whCheckIn.getLocationNumber());
                        WhStock stock = whStockService.queryWhStock(stockQuery);
                        Integer recordQuantity = 0;
                        if (stock != null) {
                            recordQuantity = stock.getSurplusQuantity() == null ? 0 : stock.getSurplusQuantity();
                        }

                        //调用采购接口上架
                        whCheckIn.getWhCheckInItem().setQuantity(checkinQuantity);
                        ApiResult apiResult = pmsCheckInService.updatePurchaseOrder2StockIn(whCheckIn, quantity,
                                recordQuantity);
                        result1 = apiResult.isSuccess();
                        errorMessage = apiResult.getErrorMsg();

                        if (!result1 && StringUtils.isBlank(errorMessage)) {
                            errorMessage = "采购系统异常";
                        }
                        if (StringUtils.isNotBlank(errorMessage) && errorMessage.contains("Read timed out")) {
                            errorMessage = "Read timed out";
                        }
                        // 调用采购系统上架失败，抛异常回滚已扣库存
                        if (!result1) {
                            throw new RuntimeException("采购系统上架失败！" + errorMessage);
                        }
                        whCheckIn.getWhCheckInItem().setQuantity(quantity);
                    }
                    if (result1) {
                        whCheckIn.setStatus(CheckInStatus.CONFIRMED.intCode());// 已经入库
                        whCheckIn.setUpUser(DataContextHolder.getUserId());// 上架员
                        whCheckIn.setUpTime(new Timestamp(System.currentTimeMillis()));// 上架时间
                        whCheckIn.setIsPurchaseStockIn(result1);// 采购单入库是否成功
                        whCheckIn.setIsInStock(result2);// PMS加库存是否成功
                        whCheckIn.setNotStatus(CheckInStatus.CONFIRMED.intCode());

                        WhSkuQueryCondition skuQuery = new WhSkuQueryCondition();
                        skuQuery.setSku(articleNumber);
                        WhSku whSku = whSkuService.queryWhSku(skuQuery);

                        if (whSku != null) {
                            whCheckIn.setUpSystemPacking(
                                    ProcessType.getIntCode(whSku.getSkuAlias(), whSku.getFloorLocation()));// 上架时系统加工装袋
                        }

                        updateLines = this.updateWhCheckIn(whCheckIn);
                        if (updateLines > 0) {
                            CHECKINLOG.log(whCheckIn.getInId(), CheckInLogType.CONFIRMED.getName(),
                                    new String[][]{{"requestTime", requestTime}, {"SKU", whCheckInItem.getSku()},
                                            {"数量", domain.getQuantity().toString()}, {"采购单入库", result1 + ""},
                                            {"加库存", result2 + ""}});

                            // 删除Redis缓存
                            String key = PurchaseOrder.STRINGREDIS_KEY_PREFIX + whCheckIn.getPurchaseOrderNo() + "_"
                                    + whCheckInItem.getSku();
                            // Integer couldCheckInQuantity =
                            // StringRedisUtils.get(key);
                            String quantityStr = StringRedisUtils.get(key);
                            Integer couldCheckInQuantity = null;
                            if (StringUtils.isNotBlank(quantityStr)) {
                                couldCheckInQuantity = Integer.valueOf(quantityStr);
                            }
                            if (couldCheckInQuantity != null) {
                                Integer upQuantityCount = domain.getQuantity();
                                WhCheckInQueryCondition query = new WhCheckInQueryCondition();
                                query.setPurchaseOrderNo(whCheckIn.getPurchaseOrderNo());
                                query.setSku(whCheckInItem.getSku());
                                List<WhCheckIn> whCheckInList = this.queryWhCheckIns(query, null);
                                if (CollectionUtils.isNotEmpty(whCheckInList)) {
                                    Integer quantityCount = 0;
                                    for (WhCheckIn checkIn : whCheckInList) {
                                        if (!CheckInStatus.DISCARDED.intCode().equals(checkIn.getStatus())) {
                                            WhCheckInItem item = checkIn.getWhCheckInItem();
                                            if (item != null && item.getSku().equals(whCheckInItem.getSku())) {
                                                quantityCount += item.getQuantity() == null ? 0 : item.getQuantity();
                                                upQuantityCount += item.getUpQuantity() == null ? 0
                                                        : item.getUpQuantity();
                                            }
                                        }
                                    }
                                    // 达到采购数量清除缓存
                                    if (quantityCount.equals(couldCheckInQuantity)) {
                                        StringRedisUtils.del(key);
                                        logger.info("delete Redis: " + key);
                                    }
                                }
                                // 全部上架
                                if (upQuantityCount.equals(couldCheckInQuantity)) {
                                    allUp = true;
                                }
                            }

                            logger.info("PDA上架: inId[" + whCheckIn.getInId() + "], purchaseOrderNo["
                                    + whCheckIn.getPurchaseOrderNo() + "], sku[" + whCheckInItem.getSku()
                                    + "], quantity[" + domain.getQuantity() + "]");
                        }
                    }
                }
                if (!(result1 && result2 && updateLines > 0)) {
                    if (!(result1 && result2)) {
                        if (StringUtils.isNotBlank(errorMessage)) {
                            errorMessage = ",ErrorMsg=" + errorMessage;
                        }

                        whCheckIn.setStatus(CheckInStatus.UPERROR.intCode());// 上架失败
                        whCheckIn.setUpUser(DataContextHolder.getUserId());// 上架员
                        whCheckIn.setUpTime(new Timestamp(System.currentTimeMillis()));// 上架时间
                        whCheckIn.setIsPurchaseStockIn(result1);// 采购单入库是否成功
                        whCheckIn.setIsInStock(result2);// 加库存是否成功
                        whCheckIn.setNotStatus(CheckInStatus.CONFIRMED.intCode());
                        int updateCode = 0;
                        try {
                            updateCode = this.updateWhCheckIn(whCheckIn);
                        } catch (Exception e) {
                            logger.error(e.getMessage(), e);
                        }
                        if (updateCode >= 1) {
                            CHECKINLOG.log(whCheckIn.getInId(), CheckInLogType.UPERROR.getName(),
                                    new String[][]{{"requestTime", requestTime}, {"SKU", whCheckInItem.getSku()},
                                            {"数量", domain.getQuantity().toString()},
                                            {"采购单入库", result1 + "" + errorMessage}, {"加库存", result2 + ""}});
                        } else {
                            CHECKINLOG.log(whCheckIn.getInId(), CheckInLogType.UPERROR.getName(),
                                    new String[][]{{"requestTime", requestTime}, {"SKU", whCheckInItem.getSku()},
                                            {"数量", domain.getQuantity().toString()}, {"更新数据库失败"},
                                            {"采购单入库", result1 + "" + errorMessage}, {"加库存", result2 + ""}});
                        }
                        String stockErrorMsg = result2 ? "" : "仓库加库存失败！";
                        result = errorMessage + stockErrorMsg;
                        if (StringUtils.isBlank(result)) {
                            result = "失败原因:null";
                        }
                    } else {
                        CHECKINLOG.log(whCheckIn.getInId(), CheckInLogType.UPERROR.getName(),
                                new String[][]{{"requestTime", requestTime}, {"SKU", whCheckInItem.getSku()},
                                        {"数量", domain.getQuantity().toString()},
                                        {"采购单入库", result1 + "" + errorMessage}, {"加库存", result2 + ""},
                                        {"入库单更新行数", updateLines + ""}});
                        result = "失败原因:入库单更新状态失败";
                    }
                }
                WhCheckInItem updateItem = new WhCheckInItem();
                updateItem.setItemId(whCheckInItem.getItemId());
                updateItem.setUpQuantity(domain.getQuantity());
                updateItem.setLocation(whCheckIn.getLocationNumber());
                updateItem.setPacUpNum(whCheckIn.getWhCheckInItem().getPacUpNum());// 优选仓上架数量

                whCheckInItemService.updateWhCheckInItem(updateItem);// 更新上架数量

                if (CheckInStatus.CONFIRMED.intCode().equals(whCheckIn.getStatus())) {
                    // TODO 发消息给采购
                    whCheckInItem.setUpQuantity(domain.getQuantity());
                    whCheckIn.setWhCheckInItem(whCheckInItem);
                    pushCheckinDataToPms(whCheckIn,SkuBusinessType.UP_LOCATION.intCode());
                    //上架处理分摊运费数据
                    handlePurchaseCostApportionData(whCheckIn);

                    //创建保质期批次并绑定唯一码
                    createExpBatchAndBindUuid(whCheckIn);
                }

                boolean zfOrder = whCheckIn.getWhCheckInItem().zfOrder();
                //采购上架  1.生成盘点记录 2.实时同步wms修改后的可用库存到pms （直发的提货成功后再推库存）
                if (!CheckInWhType.FBA.intCode().equals(whCheckIn.getExceptionType()) && result2 && result1
                        && StringUtils.isBlank(result) && !isBlend && !zfOrder) {
                    TakeStockDTO takeStockDTO = new TakeStockDTO(articleNumber,
                            TakeStockReasonEnum.PURCHASE_UPLOADED.intCode(), purchaseOrderNo, quantity,
                            new Timestamp(System.currentTimeMillis()), RecordSourceEnum.SYSTEM_ADD.intCode());
                    takeStockRecordService.batchCreateRecordAndSyncStock(Arrays.asList(takeStockDTO));
                }
                //上架后处理其他逻辑
                updateAfterUpToLocation(whCheckIn,updateItem,allUp);

            } else {
                result = "上架数量大于入库数量";
            }
        } else if (CheckInStatus.CONFIRMED.intCode().equals(whCheckIn.getStatus())) {
            result = "重复操作,入库单已上架";
        } else {
            result = "信息有误,不允许上架";
        }
        return result;
    }

    @Override
    public void batchUpdateCheckInFromNG(List<Integer> inIds, Integer status) {
        if (status != null
                && (CheckInStatus.QC_NG.intCode().equals(status) || CheckInStatus.WAITING_UP.intCode().equals(status))) {
            for (Integer inId : inIds) {
                WhCheckIn whCheckIn = this.getWhCheckIn(inId);
                whCheckIn.setStatus(status);
                this.updateWhCheckIn(whCheckIn);
                CHECKINLOG.log(whCheckIn.getInId(), CheckInLogType.QC_NG_COMPLETE.getName(),
                        new String[][]{{"status", status.toString()}});
                logger.info(
                        "采购批量处理入库单: inId[" + inId + "], boxNo[" + whCheckIn.getBoxNo() + "], status[" + status + "]");

                if (status.equals(CheckInStatus.QC_NG.intCode()) && StringUtils.isNotBlank(whCheckIn.getBoxNo())) {
                    String boxNo = whCheckIn.getBoxNo();
                    String[][] logs = new String[][]{{"QC不良品", ""},
                            {"relationNo", whCheckIn.getInId().toString()}};
                    int updated = whBoxService.updateWhBoxOfUnbinding(boxNo, logs);
                    if (updated >= 1) {
                        logger.info("QC不良品后释放周转码: boxNo[" + boxNo + "],  boxUpdated[" + updated + "]");
                    }
                }
            }
        }
    }

    @Override
    @StockServicelock
    public ResponseJson batchMoveCheckInDiscarded(List<String> skuList, WhCheckIn whCheckIn, String reason,
                                                  String type) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        Map<String, Object> resultMap = new HashMap<>();
        Integer status = whCheckIn.getStatus();
        if (status < CheckInStatus.CONFIRMED.intCode()) {
            if (!"supper".equals(type) && CheckInStatus.WAITING_QC.intCode().equals(status)
                    && whCheckIn.getQcUser() != null) {
                resultMap.put("qcInId", whCheckIn.getInId());
                response.setBody(resultMap);
                return response;
            }
            // PMS系统删减采购单入库数量
            WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();
            String purchaseOrderNo = whCheckIn.getPurchaseOrderNo();
            boolean result = false;
            String errorMessage = "";
            if (StringUtils.isNotBlank(purchaseOrderNo) && whCheckInItem != null) {
                ApiResult apiResult = new ApiResult<>();
                result = apiResult.isSuccess();
                errorMessage = apiResult.getErrorMsg() == null ? "" : apiResult.getErrorMsg();
                if (StringUtils.isNotBlank(errorMessage)) {
                    errorMessage = ",ErrorMsg=" + errorMessage;
                }
            }
            whCheckIn.setIsDiscarded(result);

            WhCheckIn updateCheckIn = BeanConvertUtils.convert(whCheckIn, WhCheckIn.class);

            whCheckIn.setStatus(CheckInStatus.DISCARDED.intCode());
            String comment = whCheckIn.getComment();
            if (StringUtils.isNotBlank(reason)) {
                if (StringUtils.isNotBlank(comment)) {
                    whCheckIn.setComment(comment + "<br/>废弃原因: " + reason);
                } else {
                    whCheckIn.setComment("废弃原因: " + reason);
                }
            }
            if (this.updateWhCheckIn(whCheckIn) < 1) {
                throw new RuntimeException("废弃入库单失败，入库单： " + whCheckIn.getInId());
            }
            // 废弃唯一码
            whUniqueSkuService.scrapUniqueSku(whCheckIn.getInId());
            // 废弃入库单移库存
            if (!checkInUpdateStockService.batchUpdateStockByDiscardCheckIn(updateCheckIn)) {
                throw new RuntimeException(whCheckIn.getInId() + "");
            }

            //废弃QC全检的异常单
            WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();
            query.setInId(whCheckIn.getInId());
            query.setStatus(ExceptionStatus.UNCONFIRM.intCode());
            query.setExceptionType(ExceptionType.LESS_QUANTITY.intCode().toString());
            query.setExceptionFrom(ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode());
            query.setSku(whCheckIn.getWhCheckInItem().getSku());
            WhCheckInException exception = whCheckInExceptionService.queryWhCheckInException(query);
            if (exception != null && exception.getId() != null) {
                WhCheckInException updateException = new WhCheckInException();
                updateException.setId(exception.getId());
                updateException.setStatus(ExceptionStatus.DISCARDED.intCode());
                updateException.setExceptionComment("入库单废弃");
                updateException.setQuantity(0);
                updateException.setReturnInformationJson("");
                whCheckInExceptionService.updateWhCheckInException(updateException);
                // 组装入库异常单处理详情数据
                WhCheckInExceptionHandle whCheckInExceptionHandle = new WhCheckInExceptionHandle();
                whCheckInExceptionHandle.setCreatedBy(DataContextHolder.getUserId());
                whCheckInExceptionHandle.setExceptionId(updateException.getId());
                whCheckInExceptionHandle.setQuantity(updateException.getQuantity());
                whCheckInExceptionHandle.setStatus(updateException.getStatus());
                whCheckInExceptionHandle.setHandleComment(updateException.getExceptionComment());
                // 创建入库异常单处理详情
                whCheckInExceptionHandleService.createWhCheckInExceptionHandle(whCheckInExceptionHandle);
            }

            if (StringUtils.isNotBlank(reason)) {
                CHECKINLOG.log(whCheckIn.getInId(), CheckInLogType.DISCARDED.getName(),
                        new String[][]{{"status", CheckInStatus.DISCARDED.intCode().toString()},
                                {"reason", reason}, {"删减采购单入库数量", result + "" + errorMessage}});
            } else {
                CHECKINLOG.log(whCheckIn.getInId(), CheckInLogType.DISCARDED.getName(),
                        new String[][]{{"status", CheckInStatus.DISCARDED.intCode().toString()},
                                {"删减采购单入库数量", result + "" + errorMessage}});
            }
            logger.info("批量废弃入库单: inId[" + whCheckIn.getInId() + "], boxNo[" + whCheckIn.getBoxNo() + "], status["
                    + status + "], reason[" + reason + "]");

            if (StringUtils.isNotBlank(whCheckIn.getBoxNo())) {
                String boxNo = whCheckIn.getBoxNo();
                String[][] logs = new String[][]{{"废弃入库单", ""}, {"relationNo", whCheckIn.getInId().toString()}};
                int updated = whBoxService.updateWhBoxOfUnbinding(boxNo, logs);
                if (updated >= 1) {
                    logger.info("废弃入库单后释放周转码: boxNo[" + boxNo + "],  boxUpdated[" + updated + "]");
                }
            }
            // 如果入库单有冲抵数量 则取消冲抵
            expWaitShipmentsService.cancelCheckInOffset(whCheckIn.getInId());

            response.setStatus(StatusCode.SUCCESS);
            // TODO 发消息到采购系统
            pushCheckinDataToPms(whCheckIn,null);
            // 废弃异常重新入库单时修改异常单状态
            this.checkInExceptionDiscarded(whCheckIn);
            //废弃入库单修改缺货订单采购关联表数据
            if (whCheckInItem.zfOrder()) {
                purchaseApvOutStockMatchService.updateCheckInQty(whCheckIn);
            }

        } else {
            resultMap.put("upOrDiscardedInId", whCheckIn.getInId());
            logger.info("已上架(或已废弃)的入库单不能废弃: inId[" + whCheckIn.getInId() + "],  purchaseOrderNo["
                    + whCheckIn.getPurchaseOrderNo() + "], status[" + status + "]");
        }
        response.setBody(resultMap);
        return response;
    }

    /**
     * 异常重新入库的入库单废弃时修改异常单状态
     */
    private void checkInExceptionDiscarded(WhCheckIn whCheckIn) {
        if (!CheckInType.EXCEPTION.intCode().equals(whCheckIn.getCheckInType())) {
            return;
        }
        WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();
        query.setBoxNo(whCheckIn.getExceptionBoxNo());
        query.setStatus(ExceptionStatus.STOCK_IN_ING.intCode());
        query.setSku(whCheckIn.getWhCheckInItem().getSku());
        WhCheckInException exception = whCheckInExceptionService.queryWhCheckInException(query);
        if (exception == null) {
            return;
        }
        // 异常单状态 入库中->待入库
        WhCheckInException updateException = new WhCheckInException();
        updateException.setId(exception.getId());
        updateException.setStatus(ExceptionStatus.WAIT_CHECK_IN.intCode());
        whCheckInExceptionService.updateWhCheckInException(updateException);
        WhCheckInExceptionHandle handle = new WhCheckInExceptionHandle();
        handle.setHandleComment("废弃异常重新入库单");
        handle.setStatus(ExceptionStatus.WAIT_CHECK_IN.intCode());
        handle.setExceptionId(exception.getId());
        whCheckInExceptionHandleService.createWhCheckInExceptionHandle(handle);
        // 推送异常信息至采购
        exception.setStatus(ExceptionStatus.WAIT_CHECK_IN.intCode());
        logger.info("start to send saveWhCheckException message to pms ====exception:"
                + exception.toString() + "===exceptionHandle:" + handle);
        rabbitmqProducerService.pushCheckInExceptionMsgToPms(exception, handle, new PushCheckInException());
        logger.info("send saveWhCheckException message to pms end ");
    }

    @Override
    public WhCheckIn queryWhCheckInByBoxNo(String boxNo) {
        WhCheckIn whCheckIn = null;
        WhBox whBox = whBoxService.queryWhBoxByBoxNo(boxNo);
        if (whBox != null && whBox.getType().equals(BoxType.PURCHASE.intCode())
                && StringUtils.isNotBlank(whBox.getRelationNo())) {
            whCheckIn = this.getWhCheckIn(Integer.valueOf(whBox.getRelationNo()));
        }
        return whCheckIn;
    }

    /**
     * 重试添加采购单待入库数量
     */
    @Override
    public void updatePurchaseOrder2AddQuantity(Integer inId) {
        WhCheckIn whCheckIn = this.getWhCheckInDetail(inId);
        WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();
        // if (whCheckIn != null && whCheckIn.getStatus() !=
        // CheckInStatus.CONFIRMED.intCode()
        if (whCheckIn != null
                // && whCheckIn.getStatus() != CheckInStatus.DISCARDED.intCode()
                && whCheckIn.getIsAddCreateQuantity() != null && !whCheckIn.getIsAddCreateQuantity()
                && whCheckInItem != null && StringUtils.isNotBlank(whCheckInItem.getSku())) {

            // 重试添加采购单待入库数量
            String purchaseOrderNo = whCheckIn.getPurchaseOrderNo();
            String articleNumber = whCheckInItem.getSku();
            Integer quantity = whCheckInItem.getQuantity();
            if (CheckInStatus.DISCARDED.intCode().equals(whCheckIn.getStatus())) {
                quantity = 0 - quantity;// 废弃时为负数
            }
            boolean result = false;
            String errorMessage = "";
            if (StringUtils.isNotBlank(purchaseOrderNo)) {
                ApiResult apiResult = pmsCheckInService.addCreateCheckInInfo2PurchaseOrder(inId, purchaseOrderNo,
                        articleNumber, quantity);
                result = apiResult.isSuccess();
                errorMessage = apiResult.getErrorMsg();
            }
            if (StringUtils.isNotBlank(errorMessage)) {
                errorMessage = ",ErrorMsg=" + errorMessage;
            }
            // 重试添加采购单待入库数量
            CHECKINLOG.log(inId, CheckInLogType.RETRYADDCREATEQUANTITY.getName(),
                    new String[][]{{"SKU", whCheckInItem.getSku()}, {"数量", quantity.toString()},
                            {"result", result + "" + errorMessage}});

            WhCheckIn updateWhCheckIn = new WhCheckIn();
            updateWhCheckIn.setInId(inId);
            updateWhCheckIn.setIsAddCreateQuantity(result);// 重试添加采购单待入库数量是否成功
            this.updateWhCheckIn(updateWhCheckIn);

            logger.info("重试添加采购单待入库数量: inId[" + inId + "], purchaseOrderNo[" + whCheckIn.getPurchaseOrderNo()
                    + "], sku[" + whCheckInItem.getSku() + "], quantity[" + quantity + "]");

        }
    }

    @Override
    public void retryUpSkuToLocation(Integer inId) {
        WhCheckIn whCheckIn = this.getWhCheckInDetail(inId);
        WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();
        if (whCheckIn != null && CheckInStatus.UPERROR.intCode().equals(whCheckIn.getStatus())
                && whCheckIn.getIsPurchaseStockIn() != null && !whCheckIn.getIsPurchaseStockIn()
                && whCheckInItem != null && StringUtils.isNotBlank(whCheckInItem.getSku())) {
            // 重新上架
            String purchaseOrderNo = whCheckIn.getPurchaseOrderNo();
            String articleNumber = whCheckInItem.getSku();
            Integer quantity = whCheckInItem.getUpQuantity();
            boolean result1 = false;
            String errorMessage = "";
            if (StringUtils.isNotBlank(purchaseOrderNo)) {
                if (whCheckIn.getIsPurchaseStockIn() != null && whCheckIn.getIsPurchaseStockIn()) {
                    result1 = true;
                } else {
                    /*
                     * ApiResult apiResult =
                     * pmsCheckInService.updatePurchaseOrder2StockIn(whCheckIn. getInId(),
                     * purchaseOrderNo, articleNumber, quantity, whCheckInItem.getQuantity());
                     */
                    WhStockQueryCondition stockQuery = new WhStockQueryCondition();
                    stockQuery.setSku(articleNumber);
                    WhStock stock = whStockService.queryWhStock(stockQuery);
                    Integer recordQuantity = 0;
                    if (stock != null) {
                        recordQuantity = stock.getSurplusQuantity() == null ? 0 : stock.getSurplusQuantity();
                    }
                    ApiResult apiResult = pmsCheckInService.updatePurchaseOrder2StockIn(whCheckIn, quantity,
                            recordQuantity);
                    result1 = apiResult.isSuccess();
                    errorMessage = apiResult.getErrorMsg();

                    if (!result1 && StringUtils.isBlank(errorMessage)) {
                        errorMessage = "采购系统异常";
                    }
                    if (StringUtils.isNotBlank(errorMessage) && errorMessage.contains("Read timed out")) {
                        errorMessage = "Read timed out";
                    }
                }
            }

            // 添加WMS库存
            Boolean result2 = whCheckIn.getIsInStock() == null ? false : whCheckIn.getIsInStock();
            int updateLines = 0;// 入库单状态修改是否成功
            if (result1) {
                if (!result2) {
                    whCheckIn.getWhCheckInItem().setQuantity(quantity);
                    result2 = checkInUpdateStockService.batchUpdateStockByUp(whCheckIn);// WMS加库存是否成功
                }
                if (result2) {
                    whCheckIn.setStatus(CheckInStatus.CONFIRMED.intCode());// 已经入库
                    whCheckIn.setUpUser(DataContextHolder.getUserId());// 上架员
                    whCheckIn.setUpTime(new Timestamp(System.currentTimeMillis()));// 上架时间
                    whCheckIn.setIsPurchaseStockIn(result1);// 采购单入库是否成功
                    whCheckIn.setIsInStock(result2);// PMS加库存是否成功
                    updateLines = this.updateWhCheckIn(whCheckIn);
                    if (updateLines > 0) {
                        CHECKINLOG.log(whCheckIn.getInId(), CheckInLogType.RETRYUPIN.getName(),
                                new String[][]{{"SKU", whCheckInItem.getSku()}, {"数量", quantity.toString()},
                                        {"采购单入库", result1 + ""}, {"加库存", result2 + ""}});

                        // 删除Redis缓存
                        String key = PurchaseOrder.STRINGREDIS_KEY_PREFIX + whCheckIn.getPurchaseOrderNo() + "_"
                                + whCheckInItem.getSku();
                        // Integer couldCheckInQuantity =
                        // StringRedisUtils.get(key);
                        String quantityStr = StringRedisUtils.get(key);
                        Integer couldCheckInQuantity = null;
                        if (StringUtils.isNotBlank(quantityStr)) {
                            couldCheckInQuantity = Integer.valueOf(quantityStr);
                        }
                        if (couldCheckInQuantity != null) {
                            WhCheckInQueryCondition query = new WhCheckInQueryCondition();
                            query.setPurchaseOrderNo(whCheckIn.getPurchaseOrderNo());
                            query.setSku(whCheckInItem.getSku());
                            List<WhCheckIn> whCheckInList = this.queryWhCheckIns(query, null);
                            if (CollectionUtils.isNotEmpty(whCheckInList)) {
                                Integer quantityCount = 0;
                                for (WhCheckIn checkIn : whCheckInList) {
                                    if (!CheckInStatus.DISCARDED.intCode().equals(checkIn.getStatus())) {
                                        WhCheckInItem item = checkIn.getWhCheckInItem();
                                        if (item != null && item.getSku().equals(whCheckInItem.getSku())) {
                                            quantityCount += item.getQuantity() == null ? 0 : item.getQuantity();
                                        }
                                    }
                                }
                                // 达到采购数量清除缓存
                                if (quantityCount.equals(couldCheckInQuantity)) {
                                    StringRedisUtils.del(key);
                                    logger.info("delete Redis: " + key);
                                }
                            }
                        }

                        logger.info("重新上架: inId[" + whCheckIn.getInId() + "], purchaseOrderNo["
                                + whCheckIn.getPurchaseOrderNo() + "], sku[" + whCheckInItem.getSku() + "], quantity["
                                + quantity + "]");
                    }
                }
            }
            if (!(result1 && result2 && updateLines > 0)) {
                if (!(result1 && result2)) {
                    if (StringUtils.isNotBlank(errorMessage)) {
                        errorMessage = ",ErrorMsg=" + errorMessage;
                    }
                    CHECKINLOG.log(whCheckIn.getInId(), CheckInLogType.UPERROR.getName(),
                            new String[][]{{"SKU", whCheckInItem.getSku()}, {"数量", quantity.toString()},
                                    {"采购单入库", result1 + "" + errorMessage}, {"加库存", result2 + ""}});

                    whCheckIn.setStatus(CheckInStatus.UPERROR.intCode());// 上架失败
                    whCheckIn.setUpUser(DataContextHolder.getUserId());// 上架员
                    whCheckIn.setUpTime(new Timestamp(System.currentTimeMillis()));// 上架时间
                    whCheckIn.setIsPurchaseStockIn(result1);// 采购单入库是否成功
                    whCheckIn.setIsInStock(result2);// 加库存是否成功
                    this.updateWhCheckIn(whCheckIn);
                } else {
                    CHECKINLOG.log(whCheckIn.getInId(), CheckInLogType.UPERROR.getName(),
                            new String[][]{{"SKU", whCheckInItem.getSku()}, {"数量", quantity.toString()},
                                    {"采购单入库", result1 + "" + errorMessage}, {"加库存", result2 + ""},
                                    {"入库单更新行数", updateLines + ""}});
                }
            }
        }
    }

    @Override
    public void addWhCheckInBySelf(WhCheckIn create) {
        WhCheckInItem whCheckInItem = create.getWhCheckInItem();
        // 下一步，等待QC
        create.setStatus(CheckInStatus.WAITING_QC.intCode());
        create.setSkuSerialNum(whCheckInItem.getQuantity());
        this.createWhCheckIn(create);
        logger.info("create whCheckIn :" + create);

        if (create.getWhCheckInItem() != null) {
            CHECKINLOG.log(create.getInId(), CheckInLogType.CREATE_CHECKIN.getName(),
                    new String[][]{{"SKU", create.getWhCheckInItem().getSku()},
                            {"数量", whCheckInItem.getQuantity().toString()},
                            {"手动添加入库单", create.getCheckInTypeName()}});
        }

        String boxNo = create.getBoxNo();
        if (create.getInId() != null) {
            if (whCheckInItem != null) {
                whCheckInItem.setInId(create.getInId());
                whCheckInItemService.createWhCheckInItem(whCheckInItem);
                logger.info("create whCheckInItem :" + whCheckInItem);
            }
            if (StringUtils.isNotBlank(boxNo)) {
                String[][] logs = new String[][]{{"创建入库单", ""}, {"relationNo", create.getInId().toString()}};
                int updated = whBoxService.updateWhBoxOfBinding(boxNo, create.getInId().toString(), logs);
                if (updated >= 1) {
                    logger.info("绑定周转码: relationNo[" + create.getInId() + "], boxNo[" + create.getBoxNo()
                            + "], boxUpdated[" + updated + "]");
                }
            }
        }
    }

    @Override
    public Map<String, Integer> queryCheckInQuantityBySkusAndStatusList(List<String> skus, List<Integer> statusList) {
        Map<String, Integer> resultMap = null;
        List<Map<String, Object>> mapList = whCheckInDao.queryCheckInQuantityBySkusAndStatusList(skus, statusList);
        if (CollectionUtils.isNotEmpty(mapList)) {
            resultMap = new HashMap<>();
            for (Map<String, Object> map : mapList) {
                String sku = (String) map.get("sku");
                Integer quantity = Integer.parseInt(map.get("quantity").toString());
                resultMap.put(sku, quantity);
            }
        }
        return resultMap;
    }

    @Override
    public void batchUpdatePurchaseOrder2AddQuantity(List<Integer> inIds) {
        for (Integer inId : inIds) {
            updatePurchaseOrder2AddQuantity(inId);
        }
    }

    @Override
    public String updateCheckIn2tag(AndroidProductDo domain) {
        String result = null;
        WhCheckIn whCheckIn = this.getWhCheckInDetail(domain.getInId());
        if (whCheckIn != null) {
            if (CheckInStatus.WAITING_QC.intCode().equals(whCheckIn.getStatus())) {
                WhCheckIn updateCheckIn = new WhCheckIn();
                updateCheckIn.setInId(domain.getInId());
                updateCheckIn.setTagUser(DataContextHolder.getUserId());// 贴标人
                updateCheckIn.setTagTime(new Timestamp(System.currentTimeMillis()));// 贴标时间
                this.updateWhCheckIn(updateCheckIn);
                CHECKINLOG.log(whCheckIn.getInId(), CheckInLogType.TAG.getName());
                logger.info("点数入库贴标: inId[" + whCheckIn.getInId() + "], tagUser[" + updateCheckIn.getTagUser() + "]");
            } else {
                result = "不是待QC状态";
            }
        } else {
            result = "无此入库单";
        }
        return result;
    }

    @Override
    public void syncCheckInQuantityToPurchaseOrder() {
        List<Integer> statusList = new ArrayList<>();
        statusList.add(CheckInStatus.WAITING_QC.intCode());
        statusList.add(CheckInStatus.QC_NG_PENDING.intCode());
        statusList.add(CheckInStatus.WAITING_UP.intCode());
        statusList.add(CheckInStatus.UPING.intCode());
        statusList.add(CheckInStatus.UPERROR.intCode());
        statusList.add(CheckInStatus.DISCARDED.intCode());
        statusList.add(CheckInStatus.CONFIRMED.intCode());

        Date endTime = new Date();
        Date startTime = DateUtils.getBeforeDate(endTime, 1);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTimeStr = simpleDateFormat.format(startTime);
        String endTimeStr = simpleDateFormat.format(endTime);
        List<Map<String, Object>> mapList = whCheckInDao
                .queryCheckInIDsByIsAddCreateQuantityFalseAndStatusList(statusList, startTimeStr, endTimeStr);
        if (CollectionUtils.isNotEmpty(mapList)) {
            for (Map<String, Object> map : mapList) {
                Integer inId = Integer.parseInt(map.get("in_id").toString());
                updatePurchaseOrder2AddQuantity(inId);
            }
        }

    }

    @Override
    public void retryToUpdateCheckInToUpToLocation() {
        WhCheckInQueryCondition query = new WhCheckInQueryCondition();
        query.setStatus(CheckInStatus.UPERROR.intCode());
        List<WhCheckIn> errorList = whCheckInDao.queryWhCheckInList(query, null);
        if (CollectionUtils.isNotEmpty(errorList)) {
            for (WhCheckIn checkIn : errorList) {
                retryUpSkuToLocation(checkIn.getInId());
            }
        }
    }

    @Override
    public Map<String, List<ArrivaledInfoBO>> queryPurchaseOrderArrivaledInfo(List<String> purchaseOrderNos) {
        Map<String, List<ArrivaledInfoBO>> resultMap = null;
        List<String> trackingNumbers = null;
        List<Map<String, Object>> mapList = whCheckInDao.queryPurchaseOrderArrivaledInfo(purchaseOrderNos);
        if (CollectionUtils.isNotEmpty(mapList)) {
            resultMap = new HashMap<>();
            trackingNumbers = new ArrayList<>();
            for (Map<String, Object> map : mapList) {
                String purchaseOrderNo = (String) map.get("purchase_order_no");
                String trackingNumber = (String) map.get("tracking_number");
                Integer createdBy = Integer.parseInt(map.get("created_by").toString());
                Date receivingTime = (Date) map.get("creation_date");
                Integer quantity = Integer.parseInt(map.get("quantity") == null ? "0" : map.get("quantity").toString());
                Double weight = 0d;
                if (map.get("weight") != null) {
                    weight = Double.parseDouble(map.get("weight").toString());
                }
                // SaleUser receiveUser =
                // saleUserService.getSaleUser(createdBy);
                String name = (String) map.get("name");
                String username = (String) map.get("username");
                List<ArrivaledInfoBO> list = resultMap.get(purchaseOrderNo);
                if (CollectionUtils.isNotEmpty(list)) {
                    if (trackingNumbers.contains(trackingNumber)) {
                        continue;
                    }
                } else {
                    list = new ArrayList<>();
                }
                trackingNumbers.add(trackingNumber);
                ArrivaledInfoBO bo = new ArrivaledInfoBO();
                /*
                 * if (receiveUser != null) { bo.setReceivingUserNo(receiveUser.getUsername());
                 * bo.setReceivingUserName(receiveUser.getName()); }
                 */
                bo.setReceivingUserNo(username);
                bo.setReceivingUserName(name);
                bo.setWeight(weight);
                bo.setReceivingTime(receivingTime);
                bo.setOrderNo(trackingNumber);
                bo.setQuantity(quantity);
                list.add(bo);
                resultMap.put(purchaseOrderNo, list);
            }
        }
        return resultMap;
    }

    @Override
    public List<WhRecord> countCheckInInventory(List<WhRecord> list) {
        return whCheckInDao.countCheckInInventory(list);
    }

    @Override
    public Map<String, Object> queryCheckInSkuStatusCount(WhCheckInQueryCondition query) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> list = whCheckInDao.queryCheckInSkuStatusCount(query);
        JSONArray legendData = new JSONArray();
        JSONArray seriesData = new JSONArray();
        for (Map<String, Object> map : list) {
            String name = CheckInStatus.getNameByCode(map.get("status").toString());
            String statusCount = map.get("statusCount").toString();
            String quantity = map.get("quantity").toString();
            legendData.add(name);
            seriesData.add(JSON.parse(
                    "{name: '" + name + " : " + statusCount + " ， " + quantity + "', value: " + statusCount + "}"));
        }
        resultMap.put("legendData", legendData);
        resultMap.put("seriesData", seriesData);
        return resultMap;
    }

    @Override
    public Map<String, Object> queryCheckInPcsCount(WhCheckInQueryCondition query) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> list = whCheckInDao.queryCheckInPcsCount(query);
        JSONArray axisData = new JSONArray();
        JSONArray upPcsSeriesData = new JSONArray();
        JSONArray subPcsSeriesData = new JSONArray();
        JSONArray upSkuSeriesData = new JSONArray();
        JSONArray subSkuSeriesData = new JSONArray();
        for (Map<String, Object> map : list) {
            String date = map.get("date") + "";
            Integer pcsCount = map.get("pcsCount") == null ? 0 : Integer.parseInt(map.get("pcsCount").toString());
            Integer upPcsCount = map.get("upPcsCount") == null ? 0 : Integer.parseInt(map.get("upPcsCount").toString());
            Integer skuCount = map.get("skuCount") == null ? 0 : Integer.parseInt(map.get("skuCount").toString());
            Integer upSkuCount = map.get("upSkuCount") == null ? 0 : Integer.parseInt(map.get("upSkuCount").toString());
            axisData.add(date);
            upPcsSeriesData.add(upPcsCount);
            upSkuSeriesData.add(upSkuCount);
            subPcsSeriesData.add(pcsCount - upPcsCount);
            subSkuSeriesData.add(skuCount - upSkuCount);
        }
        resultMap.put("axisData", axisData);
        resultMap.put("upPcsSeriesData", upPcsSeriesData);
        resultMap.put("subPcsSeriesData", subPcsSeriesData);
        resultMap.put("upSkuSeriesData", upSkuSeriesData);
        resultMap.put("subSkuSeriesData", subSkuSeriesData);
        return resultMap;
    }

    @Override
    public List<Map<String, Object>> queryDownload(WhCheckInQueryCondition query, Pager pager) {
        Assert.notNull(query);
        List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
//        int count = whCheckInDao.queryWhCheckInCount(query);
//        if (query.getDownload() && count > 100000) {
//            // 导出限制10万条数据
//            return list;
//        }
        list = whCheckInDao.queryDownload(query, pager);
        return list;
    }

    public void buildCheckInClothingExceptionInfo(WhCheckIn whCheckIn, WhCheckInException whCheckInException) {
        if (ExceptionType.SIZE_ERROR.getCode().equals(whCheckInException.getExceptionType()) && CollectionUtils.isNotEmpty(whCheckIn.getCheckInClothing())
                && whCheckIn.getCheckInClothing().size() >= 2) {
            // 服装尺寸异常
            String errMes = CheckInClothingUtils.checkDiff(whCheckIn.getCheckInClothing().get(0), whCheckIn.getCheckInClothing().get(1));
            if (StringUtils.isNotBlank(errMes)) {
                if (StringUtils.isBlank(whCheckInException.getExceptionComment())) {
                    whCheckInException.setExceptionComment("异常尺寸：".concat(errMes));
                } else {
                    whCheckInException.setExceptionComment(whCheckInException.getExceptionComment() + ";异常尺寸：".concat(errMes));
                }
            }
        }
    }

    /**
     * 校验保质期临期待发
     */
    private ResponseJson checkExpWaitShipment(WhCheckIn whCheckIn) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        // 当前入库单已匹配到保质期待发
        ExpWaitShipments expWait = expWaitShipmentsService.getExpWaitShipments(whCheckIn.getExpWaitId());
        if (expWait == null) {
            if (whCheckIn.getExpFlag() != null && whCheckIn.getExpFlag() == 4) {
                response.setMessage("未找到保质期待发记录 id【" + whCheckIn.getExpWaitId() + "】，请提交异常单");
                return response;
            }
            response.setStatus(StatusCode.SUCCESS);
            return response;
        }
        ExpWaitRelationDetailDTO relationDetail = new ExpWaitRelationDetailDTO();
        relationDetail.setExpDate(whCheckIn.getExpDate());
        relationDetail.setExpDays(whCheckIn.getDays());
        Integer offsetQty = Optional.ofNullable(expWait.getOffsetQuantity()).orElse(0);
        int waitOffsetQty = expWait.getQuantity() - offsetQty;
        Integer checkinQuantity = whCheckIn.getWhCheckInItem().getQuantity();
        if (whCheckIn.getExpFlag() != null && whCheckIn.getExpFlag() == 4) {
            // 当前入库SKU限制入库
            if (waitOffsetQty == 0) {
                response.setMessage("当前保质期待发记录id【" + expWait.getId() + "】已冲抵完，请提交异常单");
                return response;
            }
            if (waitOffsetQty != checkinQuantity) {
                String msg = waitOffsetQty > checkinQuantity ? "良品数量[" + checkinQuantity + "]不满足待冲抵数量[" + waitOffsetQty + "]"
                        : "当前保质期待发记录id【" + expWait.getId() + "】只存在[" + waitOffsetQty + "]个未冲抵，只能入库[" + waitOffsetQty + "]个良品数量";
                response.setMessage(msg);
                return response;
            }
            relationDetail.setRelationOffsetQty(checkinQuantity);
            expWait.setOffsetQuantity(offsetQty + checkinQuantity);
        } else {
            String sku = whCheckIn.getWhCheckInItem().getSku();
            WhPurchaseOrderQueryCondition orderQuery = new WhPurchaseOrderQueryCondition();
            orderQuery.setPurchaseOrderNo(whCheckIn.getPurchaseOrderNo());
            List<WhPurchaseOrder> whPurchaseOrders = whPurchaseOrderService.queryWhPurchaseOrderAndItems(orderQuery, null);
            int localQuantity = whPurchaseOrders.get(0).getItems().stream()
                    .filter(c -> StringUtils.equalsIgnoreCase(sku, c.getSku()) && c.getQuantity() != null)
                    .mapToInt(WhPurchaseItem::getQuantity).sum();
            // 如果有中转仓采购数量 则采购SKU数量-中转仓采购数量
            WhFbaPurchaseDataQueryCondition queryCondition = new WhFbaPurchaseDataQueryCondition();
            queryCondition.setPurchaseorderno(whCheckIn.getPurchaseOrderNo());
            queryCondition.setSku(sku);
            List<WhFbaPurchaseData> whFbaPurchaseDataList = whFbaPurchaseDataService.queryWhFbaPurchaseDatas(queryCondition, null);
            if (CollectionUtils.isNotEmpty(whFbaPurchaseDataList)) {
                int fbaQty = whFbaPurchaseDataList.stream().filter(c -> c.getOrderQuantity() != null && !StringUtils.equalsIgnoreCase("local", c.getShipmentId()))
                        .mapToInt(WhFbaPurchaseData::getOrderQuantity).sum();
                localQuantity -= fbaQty;
            }
            Integer localCheckInQty = Math.min(localQuantity, checkinQuantity);
            // SKU保质期正常
            if (waitOffsetQty <= localCheckInQty) {
                expWait.setOffsetQuantity(offsetQty + waitOffsetQty);
                relationDetail.setRelationOffsetQty(waitOffsetQty);
            } else {
                expWait.setOffsetQuantity(offsetQty + localCheckInQty);
                relationDetail.setRelationOffsetQty(localCheckInQty);
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("expWait", expWait);
        map.put("expWaitRelation", relationDetail);
        response.setBody(map);
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 点数入库创建入库单和异常单
     *
     * @param whCheckIn
     * @param receiveBoxNo
     * @param trackingNumbers
     * @param arrayTrackingNo
     * @return
     */
    @Override
    @StockServicelock
    public ResponseJson createCheckInAndCheckInException(List<String> skuList, WhCheckIn whCheckIn, String receiveBoxNo,
                                                         String trackingNumbers, String arrayTrackingNo, Integer purchaseQuantity) {
        ResponseJson response = new ResponseJson();
        WhCheckInException whCheckInException = whCheckIn.getWhCheckInException();
        // 多货处理
        handleExcessCheckIn(whCheckIn);
        // 如果良品数量大于0，则走入库流程
        if (whCheckIn != null && whCheckIn.getWhCheckInItem() != null
                && whCheckIn.getWhCheckInItem().getQuantity() != null
                && whCheckIn.getWhCheckInItem().getQuantity() > 0) {
            ExpWaitShipments expWait = null;
            ExpWaitRelationDetailDTO relationDetail = null;
            if (whCheckIn.getExpWaitId() != null) {
                ResponseJson responseJson = this.checkExpWaitShipment(whCheckIn);
                if (!responseJson.isSuccess())
                    return responseJson;
                expWait = (ExpWaitShipments) responseJson.getBody().get("expWait");
                relationDetail = (ExpWaitRelationDetailDTO) responseJson.getBody().get("expWaitRelation");
            }
            // 带样换图异常入库 异常单入库设置入库单标签，更新异常单确认数量与处理数量
            exceptionStockIn(whCheckIn,false);
            response = this.createWhCheckInAndWhCheckInItem(whCheckIn);
            handleExcessCheckIn(whCheckIn);
            if (whCheckIn.getInId() != null && expWait != null) {
                Map<String, ExpWaitRelationDetailDTO> jsonMap;
                if (StringUtils.isBlank(expWait.getRelationDetailJson())) {
                    jsonMap = new HashMap<>();
                } else {
                    jsonMap = JSON.parseObject(expWait.getRelationDetailJson(), Map.class);
                }
                jsonMap.put(String.valueOf(whCheckIn.getInId()), relationDetail);
                expWait.setRelationDetailJson(JSON.toJSONString(jsonMap));
                expWait.addRelationId(whCheckIn.getInId());
                expWaitShipmentsService.updateExpWaitShipments(expWait);
            }
        }
        // 如果异常信息不为空，放入异常库
        if (whCheckInException != null && StringUtils.isNotBlank(whCheckInException.getExceptionType())) {
            if (whCheckInException.getQuantity() > 0) {
                // 带样换图异常入库 异常单入库设置入库单标签，更新异常单确认数量与处理数量
                //exceptionStockIn(whCheckIn,true);
            }
            // 创建入库异常单
            whCheckInExceptionService
                    .createWhCheckInException(prepareCreateExceptionParams(whCheckIn, null, null, null)
                            , PurchaseExpressRecordStatus.SCANNER.getCode());
            // 保存入库异常单处理详情数据

            // 处理服装尺寸不符异常数据
            buildCheckInClothingExceptionInfo(whCheckIn, whCheckInException);

            WhCheckInExceptionHandle whCheckInExceptionHandle = createExceptionHandle(whCheckInException);
            List<WhCheckInException> whCheckInExceptionList = new ArrayList<>();
            List<WhCheckInExceptionHandle> whCheckInExceptionHandleList = new ArrayList<>();
            whCheckInExceptionList.add(whCheckInException);
            whCheckInExceptionHandleList.add(whCheckInExceptionHandle);
            response.setBody(preparePushCheckInExceptionToPmsMsg(whCheckInExceptionList, whCheckInExceptionHandleList));
            // sku全部为异常时
            if (purchaseQuantity != null && purchaseQuantity.equals(whCheckInException.getQuantity())
                    && StringUtils.isNotBlank(whCheckInException.getPurchaseOrderNo())
                    && StringUtils.isNotBlank(whCheckInException.getSku())) {
                // 修改SKU状态,到货异常
                whPurchaseExpressRecordService.updatePurchaseSkuStatus(whCheckInException.getPurchaseOrderNo(),
                        whCheckInException.getSku(), PurchaseSkuStatus.ARRIVE_EXCEPTION.intCode(), null);
            }
        }
        whCheckInClothingService.doWhCheckInClothing(whCheckIn);

        // 异常单重新入库记录日志
        if (StringUtils.isNotBlank(whCheckIn.getExceptionBoxNo())
                && CheckInType.EXCEPTION.intCode().equals(whCheckIn.getCheckInType())) {
            WhBox whBox = whBoxService.queryWhBoxByBoxNo(whCheckIn.getExceptionBoxNo());
            if (whBox != null && StringUtils.isNotBlank(whBox.getRelationNo())) {
                WhCheckInException dbException = whCheckInExceptionService
                        .getWhCheckInException(Integer.parseInt(whBox.getRelationNo()));
                if (dbException != null && StringUtils.isNotBlank(dbException.getReceiveBoxNo())) {
//                    whPurchaseExpressRecordService.updateExceptionAndSaveHandleLog(whCheckIn.getExceptionBoxNo(), null,null);
                    // 生成了新的入库异常单
                    if (Objects.nonNull(whCheckInException.getId())){
                        String message = "生成新入库异常单,id="+whCheckInException.getId();
                        this.saveHandle(dbException, dbException.getStatus(), message, false);
                        dbException.addNextGenerationExceptionId(whCheckInException.getId());
                        WhCheckInException updateException = new WhCheckInException();
                        updateException.setId(dbException.getId());
                        updateException.setNextGenerationExceptionIds(dbException.getNextGenerationExceptionIds());
                        updateException.setReceiveBoxNo(dbException.getReceiveBoxNo());
                        whCheckInExceptionService.updateWhCheckInException(updateException);
                    }
                    // 生成了新的入库单
                    if (Objects.nonNull(whCheckIn.getInId())){
                        String message = "生成新入库单,id="+whCheckIn.getInId();
                        this.saveHandle(dbException, dbException.getStatus(), message, false);
                    }
                }
            }
        }
        // 解绑周转框
        if (StringUtils.isNotBlank(receiveBoxNo) && StringUtils.isNotBlank(trackingNumbers)
                && StringUtils.isNotBlank(arrayTrackingNo)) {
            unbindBoxNoAndUpdateSplitUser(receiveBoxNo, trackingNumbers, arrayTrackingNo);
        }

        int expressStatus = PurchaseExpressStatus.PROCESSED.intCode();

        // 扫描采购单号时，查找是否有收货单,
        if (StringUtils.isBlank(receiveBoxNo) && StringUtils.isNotBlank(whCheckIn.getTrackingNumber())) {
            logger.info("createCheckInAndCheckInException ,{}", whCheckIn.getTrackingNumber());
            WhPurchaseExpressRecordQueryCondition queryCondition = new WhPurchaseExpressRecordQueryCondition();
            queryCondition.setTrackingNumber(whCheckIn.getTrackingNumber());
            List<WhPurchaseExpressRecord> whPurchaseExpressRecordList = whPurchaseExpressRecordService
                    .queryWhPurchaseExpressRecords(queryCondition, null);
            if (CollectionUtils.isEmpty(whPurchaseExpressRecordList)) {
                // 没有收货单，待处理
                expressStatus = PurchaseExpressStatus.WAIT_RECEIVE.intCode();
            }
        }
        // 修改快递单状态,待处理/已处理
        whPurchaseExpressRecordService.updatePurchaseExpressStatus(whCheckIn.getPurchaseOrderNo(),
                whCheckIn.getTrackingNumber(), expressStatus);

        // 计算成功单数
        this.handleSuccessCount(whCheckIn);

        // TODO 发消息给采购
        whCheckIn.setCreateUser(DataContextHolder.getUserId());
        whCheckIn.setCreateDate(new Timestamp(System.currentTimeMillis()));


        WhCheckIn newCheckIn=BeanConvertUtils.convert(whCheckIn, WhCheckIn.class);

        WhCheckInItem newCheckInItem = new WhCheckInItem();
        if (whCheckIn.getWhCheckInItem() != null) {
            newCheckInItem =BeanConvertUtils.convert(whCheckIn.getWhCheckInItem(), WhCheckInItem.class);
        }

        newCheckIn.setWhCheckInItem(newCheckInItem);
        pushCheckinDataToPms(newCheckIn,SkuBusinessType.CHECK_IN.intCode());
        return response;
    }

    /**
     * 处理多货入库的情况
     * @param domain 入库领域对象
     * @param whWarehouse 仓库信息
     * @return 处理后的多货列表
     */
    private void handleExcessCheckIn(WhCheckIn whCheckIn) {
        /*List<WhCheckInExcess> whCheckInExcessList = whCheckIn.getWhCheckInExcessList();
        if (CollectionUtils.isEmpty(whCheckInExcessList)) {
            return;
        }
        if (whCheckIn.getInId() != null) {
            for (WhCheckInExcess excess : whCheckInExcessList) {
                excess.setInId(whCheckIn.getInId());
            }
            whCheckInExcessService.batchCreateWhCheckInExcess(whCheckInExcessList);
            return;
        }
        WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();
        // 判断是否为售后商品
        boolean afterSale = whCheckIn.getAfterSaleQty() != null && whCheckIn.getAfterSaleQty() > 0
                || StringUtils.contains(whCheckInItem.getFirstOrderTypeName(), CheckInFlags.SALED_BALANCE.getName());

        // 处理每个多货项
        String firstOrderType = whCheckInItem.getFirstOrderType();
        String comment = "原采购单"+whCheckIn.getPurchaseOrderNo();
        String newPurchaseOrderNo = null;
        for (WhCheckInExcess excess : whCheckInExcessList) {
            // 根据处理方式和售后状态设置标签
            firstOrderType = StringUtils.isEmpty(firstOrderType) ? excess.getProcessingMethod()
                    : firstOrderType + "," + excess.getProcessingMethod();
           *//* if (ExcessHandleWayEnum.MATCH_EXISTING_ORDER.getCode().equals(excess.getProcessingMethod())) {
                // 匹配已有采购单的情况
                firstOrderType = StringUtils.isEmpty(firstOrderType) ? CheckInFlags.MULTI_GOODS_MATCH.getCode()
                        : firstOrderType + "," + CheckInFlags.MULTI_GOODS_MATCH.getCode();
            } else if (ExcessHandleWayEnum.CREATE_NEW_ORDER.getCode().equals(excess.getProcessingMethod())) {
                // 新建采购单的情况
                if (afterSale) {
                    // 售后结算的情况
                    firstOrderType = StringUtils.isEmpty(firstOrderType) ? CheckInFlags.MULTI_GOODS_GIFT.getCode()
                            : firstOrderType + "," + CheckInFlags.MULTI_GOODS_GIFT.getCode();
                } else {
                    // 非售后结算的情况
                    firstOrderType = StringUtils.isEmpty(firstOrderType) ? CheckInFlags.MULTI_GOODS_CREATE.getCode()
                            : firstOrderType + "," + CheckInFlags.MULTI_GOODS_CREATE.getCode();
                }
            }*//*
            newPurchaseOrderNo = StringUtils.isEmpty(newPurchaseOrderNo) ? excess.getPurchaseOrderNo()
                    : newPurchaseOrderNo + "," + excess.getPurchaseOrderNo();
        }
        // 更新标签
        whCheckInItem.setFirstOrderType(firstOrderType);
        whCheckIn.setComment(comment);
        whCheckIn.setPurchaseOrderNo(newPurchaseOrderNo);*/
    }

    /**
     * 当前入库员同一采购单、sku只有首次入库才计算成功数量
     * @param whCheckIn
     */
    private void handleSuccessCount(WhCheckIn whCheckIn){
        if (StringUtils.isBlank(whCheckIn.getPurchaseOrderNo()) || whCheckIn.getWhCheckInItem() == null
                || StringUtils.isBlank(whCheckIn.getWhCheckInItem().getSku())) {
            return;
        }
        WhCheckInQueryCondition inQuery = new WhCheckInQueryCondition();
        inQuery.setPurchaseOrderNo(whCheckIn.getPurchaseOrderNo());
        inQuery.setSku(whCheckIn.getWhCheckInItem().getSku());
        inQuery.setCreateUser(DataContextHolder.getUserId());
        int count = queryWhCheckInCount(inQuery);
        if (count == 1) {
            String key = RedisConstant.CHECKIN_SUCCESS_COUNT_KEY + DateUtils.formatDate(new Date(), DateUtils.DEFAULT_FORMAT) + DataContextHolder.getUserId();
            long successCount = StringRedisUtils.incrementAndGet(key);
            StringRedisUtils.setExpire(key,3, TimeUnit.DAYS);
            whCheckIn.setSuccessCount(Math.toIntExact(successCount));
        }
    }

    // 带样换图异常入库
    private void exceptionStockIn(WhCheckIn whCheckIn, boolean exceptionFlag) {
        // 是否异常入库
        if (StringUtils.isNotBlank(whCheckIn.getExceptionBoxNo())
                && CheckInType.EXCEPTION.intCode().equals(whCheckIn.getCheckInType())) {
            WhBox whBox = whBoxService.queryWhBoxByBoxNo(whCheckIn.getExceptionBoxNo());
            if (whBox != null && StringUtils.isNotBlank(whBox.getRelationNo())) {
                WhCheckInException dbException = whCheckInExceptionService
                        .getWhCheckInException(Integer.parseInt(whBox.getRelationNo()));
                if (dbException != null && ExceptionHandleWay.UPDATE_IMAGE.intCode().equals(dbException.getHandleWay())
                            && (ExceptionStatus.WAIT_CHECK_IN.intCode().equals(dbException.getStatus())
                                    || ExceptionStatus.STOCK_IN_ING.intCode().equals(dbException.getStatus()))) {
                    // 包含已带样，先入库的增加标识
                    WhCheckInExceptionHandleQueryCondition condition = new WhCheckInExceptionHandleQueryCondition();
                    condition.setExceptionId(dbException.getId());
                    condition.setHandleWay(ExceptionHandleWay.UPDATE_IMAGE.intCode());
                    condition.setStatus(ExceptionStatus.CHECK_IN_SAMPLE.intCode());
                    WhCheckInExceptionHandle updateImageHandle = whCheckInExceptionHandleService
                            .queryWhCheckInExceptionHandle(condition);
                    if (updateImageHandle != null) {
                        if (!exceptionFlag) {
                            String firstOrderType = whCheckIn.getWhCheckInItem().getFirstOrderType();
                            if (StringUtils.isBlank(firstOrderType)) {
                                whCheckIn.getWhCheckInItem().setFirstOrderType(CheckInFlags.UPDATE_IMAGE_STOCK_IN.getCode());
                            } else {
                                whCheckIn.getWhCheckInItem().setFirstOrderType(firstOrderType + "," + CheckInFlags.UPDATE_IMAGE_STOCK_IN.getCode());
                            }
                        }
                    }
                    /*
                        WhCheckInException updateException = new WhCheckInException();
                        updateException.setId(dbException.getId());
                        if (exceptionFlag) {
                           *//* if (ExceptionHandleWay.UPDATE_IMAGE.intCode().equals(whCheckIn.getWhCheckInException().getHandleWay()))
                                throw new RuntimeException("请勿重复提交待样换图异常");*//*
                            if (dbException.getConfirmQuantity() - whCheckIn.getWhCheckInException().getQuantity() <= 0)
                                throw new RuntimeException("异常数量必须小于确认数量");
                            updateException.setConfirmQuantity(dbException.getConfirmQuantity() - whCheckIn.getWhCheckInException().getQuantity());
                            updateException.setHandledQuantity((dbException.getHandledQuantity() == null ? 0 : dbException.getHandledQuantity()) + whCheckIn.getWhCheckInException().getQuantity());
                            whCheckInExceptionService.updateWhCheckInException(updateException);
                        }else {
                            if (whCheckIn.getWhCheckInException() != null) {
                                Integer excptionQuantity = whCheckIn.getWhCheckInException().getQuantity();
                                if (excptionQuantity != null && excptionQuantity > 0) {
                                    if (dbException.getConfirmQuantity() - whCheckIn.getWhCheckInItem().getQuantity() - excptionQuantity < 0)
                                        throw new RuntimeException("入库数量+异常数量必须小于确认数量");
                                }
                            }
                            if (dbException.getConfirmQuantity() - whCheckIn.getWhCheckInItem().getQuantity() < 0)
                                throw new RuntimeException("入库数量必须小于确认数量");
                            updateException.setConfirmQuantity(dbException.getConfirmQuantity() - whCheckIn.getWhCheckInItem().getQuantity());
                            updateException.setHandledQuantity((dbException.getHandledQuantity() == null ? 0 : dbException.getHandledQuantity()) + whCheckIn.getWhCheckInItem().getQuantity());
                            whCheckInExceptionService.updateWhCheckInException(updateException);
                            String firstOrderType = whCheckIn.getWhCheckInItem().getFirstOrderType();
                            if (StringUtils.isBlank(firstOrderType)) {
                                whCheckIn.getWhCheckInItem().setFirstOrderType(CheckInFlags.UPDATE_IMAGE_STOCK_IN.getCode());
                            } else {
                                whCheckIn.getWhCheckInItem().setFirstOrderType(firstOrderType + "," + CheckInFlags.UPDATE_IMAGE_STOCK_IN.getCode());
                            }
                        }
                        dbException.setConfirmQuantity(updateException.getConfirmQuantity());
                        dbException.setHandledQuantity(updateException.getHandledQuantity());
                        WhCheckInExceptionHandle whCheckInExceptionHandle = new WhCheckInExceptionHandle();
                        if (exceptionFlag) {
                            whCheckInExceptionHandle.setHandleComment("异常单入库，数量：" + whCheckIn.getWhCheckInException().getQuantity());
                        }else {
                            whCheckInExceptionHandle.setHandleComment("异常单入库，数量：" + whCheckIn.getWhCheckInItem().getQuantity());
                        }
                        whCheckInExceptionHandle.setExceptionId(dbException.getId());
                        whCheckInExceptionHandle.setCreatedBy(DataContextHolder.getUserId());
                        whCheckInExceptionHandle.setCreationDate(new Timestamp(System.currentTimeMillis()));
                        whCheckInExceptionHandle.setStatus(updateException.getStatus());
                        whCheckInExceptionHandleService.createWhCheckInExceptionHandle(whCheckInExceptionHandle);

                        if (StringUtils.isNotBlank(dbException.getImage())) {
                            dbException.setPurchasePushImage(true);
                        }

                        // 推送采购
                        // 将修改了状态之后的入库异常单推送至采购系统
                        PushCheckInException pushCheckInException = new PushCheckInException();
                        pushCheckInException.setWhCheckInExceptionHandleList(Arrays.asList(whCheckInExceptionHandle));
                        pushCheckInException.setWhCheckInExceptionList(Arrays.asList(dbException));
                        // 手动同步异常信息到采购系统
                        logger.info("start to send batchReSendExceptionToPms message to pms ====pushSize:"
                                + pushCheckInException.getWhCheckInExceptionList().size());
                        rabbitmqProducerService.pushCheckInExceptionMsgToPms(null, whCheckInExceptionHandle, pushCheckInException);
                        logger.info("send batchReSendExceptionToPms message to pms end ");*/
                    }
                }
            }
    }

    /**
     * 点数入库创建入库单
     *
     * @param whCheckIn
     * @return
     */
    public ResponseJson handleCheckInParam(WhCheckIn whCheckIn) {
        ResponseJson response = new ResponseJson();
        Integer couldCheckInQuantity = null;
        Integer quantityCount = 0;

        if (whCheckIn.getWhCheckInItem() != null && whCheckIn.getWhCheckInItem().getQuantity() != null
                && whCheckIn.getWhCheckInItem().getQuantity() > 0) {

            // 防止多人同时提交一个采购单入库
            String key = PurchaseOrder.STRINGREDIS_KEY_PREFIX + whCheckIn.getPurchaseOrderNo() + "_"
                    + whCheckIn.getWhCheckInItem().getSku();
            String quantityStr = StringRedisUtils.get(key);
            if (StringUtils.isNotBlank(quantityStr)) {
                couldCheckInQuantity = Integer.valueOf(quantityStr);
            }

            if (couldCheckInQuantity != null) {
                WhCheckInQueryCondition query = new WhCheckInQueryCondition();
                query.setPurchaseOrderNo(whCheckIn.getPurchaseOrderNo());
                query.setSku(whCheckIn.getWhCheckInItem().getSku());
                List<WhCheckIn> whCheckInList = whCheckInDao.queryWhCheckInList(query, null);
                if (CollectionUtils.isNotEmpty(whCheckInList)) {
                    quantityCount = getCheckInCount(whCheckInList, whCheckIn.getWhCheckInItem().getSku());
                    // 超过采购数量不提交，提示重新扫描后再试
                    if (quantityCount + whCheckIn.getWhCheckInItem().getQuantity() > couldCheckInQuantity) {
                        response.setStatus(StatusCode.FAIL);
                        response.setMessage("累计入库和异常数量超过采购数量");
                        return response;
                    }
                }
            }
            // 查询sku标签，并标记入库单
            WhSkuQueryCondition query = new WhSkuQueryCondition();
            query.setSku(whCheckIn.getWhCheckInItem().getSku());
            WhSku whSku = whSkuService.queryWhSku(query);
            if (whSku != null) {
                String checkInSkuFlags = "";
                if (whSku.getShearSign() != null && whSku.getShearSign() == 1) {
                    checkInSkuFlags += SkuFlags.CLIPPING.intCode() + ",";
                }
                if (whSku.getNoStockUp() != null && whSku.getNoStockUp() == 1) {
                    checkInSkuFlags += SkuFlags.NOT_STOCKED.intCode() + ",";
                }
                if (StringUtils.isNotBlank(checkInSkuFlags)) {
                    WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();
                    whCheckInItem.setCheckInSkuFlags(StringUtils.substringBeforeLast(checkInSkuFlags, ","));
                    whCheckIn.setWhCheckInItem(whCheckInItem);
                }
            }
            // 下一步，等待QC
            whCheckIn.setStatus(CheckInStatus.WAITING_QC.intCode());
            WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();
            if (whCheckInItem.getIsFreeCheck()!=null && whCheckInItem.getIsFreeCheck()) {
                whCheckIn.setStatus(CheckInStatus.UPING.intCode());
                whCheckInItem.setQcQuantity(whCheckInItem.getQuantity());
                whCheckInItem.setQcNum(whCheckInItem.getQuantity());
            }
        }
        return response;
    }

    /**
     * 解绑周转框，更新拆分人
     *
     * @param receiveBoxNo
     * @param trackingNumbers
     * @param arrayTrackingNo
     */
    public void unbindBoxNoAndUpdateSplitUser(String receiveBoxNo, String trackingNumbers, String arrayTrackingNo) {
        List<String> bindTrackingNos = Arrays.asList(StringUtils.split(trackingNumbers, ","));
        List<String> returnTrackingNos = Arrays.asList(StringUtils.split(arrayTrackingNo, ","));

        List<String> intersection = bindTrackingNos.stream().filter(item -> returnTrackingNos.contains(item))
                .collect(toList());

        // 更新拆分人
        intersection.stream().forEach(updateExpressId -> {
            WhPurchaseExpressRecord whPurchaseExpressRecord = new WhPurchaseExpressRecord();
            whPurchaseExpressRecord.setTrackingNumber(updateExpressId);
            whPurchaseExpressRecord.setSplitUser(DataContextHolder.getUserId());
            whPurchaseExpressRecord.setSplitDate(new Timestamp(System.currentTimeMillis()));
            whPurchaseExpressRecordService.updatePurchaseExpressRecordByTrackingNumber(whPurchaseExpressRecord);
        });
      /*  logger.info("要解绑的快递单号：" + intersection + " ,周转框：" + receiveBoxNo);
        // 批量解绑周转框
        WhPurchaseExpressRecordQueryCondition queryCondition = new WhPurchaseExpressRecordQueryCondition();
        queryCondition.setTrackingNos(intersection);
        queryCondition.setBoxNo(receiveBoxNo);
        List<WhPurchaseExpressRecord> whPurchaseExpressRecordList = whPurchaseExpressRecordService
                .queryWhPurchaseExpressRecords(queryCondition, null);
        if (CollectionUtils.isNotEmpty(whPurchaseExpressRecordList)) {
            whPurchaseExpressRecordList.forEach(whPurchaseExpressRecord -> {
                // 有扫描时间才能解绑周转框
                if (whPurchaseExpressRecord.getCheckInScanTime() != null) {
                    whBoxService.unbindItem(receiveBoxNo, whPurchaseExpressRecord.getId().toString(), true);
                }
            });
        }*/

    }

    /**
     * 点数完成，生成少货/少sku异常
     *
     * @param purchaseOrderArrayStr
     * @return
     */
    @Override
    public ResponseJson batchCreateCheckInExceptionToPms(String purchaseOrderArrayStr, String intersection,
                                                         String receiveBoxNo, String expressId) {
        ResponseJson rsp = new ResponseJson();
        if (StringUtils.isBlank(purchaseOrderArrayStr)) {
            rsp.setStatus(StatusCode.FAIL);
            rsp.setMessage("没有相关采购单！");
            return rsp;
        } else {
            List<PurchaseOrder> purchaseOrders = JSONArray.parseArray(purchaseOrderArrayStr, PurchaseOrder.class);
            List<WhCheckInException> whCheckInExceptions = new ArrayList<>();
            List<WhCheckInExceptionHandle> whCheckInExceptionHandles = new ArrayList<>();
            Map<String, Timestamp> map = new HashMap<>();
            Map<String, Integer> boxItemMap = new HashMap<>();


            if (CollectionUtils.isEmpty(purchaseOrders)) {
                rsp.setStatus(StatusCode.FAIL);
                rsp.setMessage("没有相关采购单！");
                return rsp;
            }
            for (PurchaseOrder purchaseOrder : purchaseOrders) {
                String allCheckIn = "";
                if (CollectionUtils.isEmpty(purchaseOrder.getPurchaseOrderItems())) {
                    rsp.setStatus(StatusCode.FAIL);
                    rsp.setMessage("采购单对应的sku为空！");
                    return rsp;
                }

                purchaseOrder.setExpressId(intersection);

                for (PurchaseOrderItem item : purchaseOrder.getPurchaseOrderItems()) {
                    if (StringUtils.isBlank(item.getSku())) {
                        rsp.setMessage("sku不存在，请确认！");
                        rsp.setStatus(StatusCode.FAIL);
                        return rsp;
                    }
                    // 根据采购单，sku查询入库单，统计入库数量
                    WhCheckInQueryCondition query = new WhCheckInQueryCondition();
                    query.setPurchaseOrderNo(purchaseOrder.getPurchaseOrderNo());
                    query.setSku(item.getSku());
                    List<WhCheckIn> whCheckIns = whCheckInDao.queryWhCheckInList(query, null);
                    int checkInCount = getCheckInCount(whCheckIns, item.getSku());

                    if (checkInCount >= item.getQuantity()) {
                        allCheckIn += ",true";
                    } else if (checkInCount >= 0) {
                        allCheckIn += ",false";
                    }

                    // 根据采购单，sku，草稿状态，异常类型（点数）查询异常单，统计异常数量
                    WhCheckInExceptionQueryCondition queryCondition = new WhCheckInExceptionQueryCondition();
                    queryCondition.setPurchaseOrderNo(purchaseOrder.getPurchaseOrderNo());
                    queryCondition.setSku(item.getSku());
                    queryCondition.setExceptionFrom(ExceptionFrom.CHECK_IN_EXCEPTION.intCode());
                    queryCondition.setStatus(ExceptionStatus.UNCONFIRM.intCode());
                    List<WhCheckInException> whCheckInExceptionList = whCheckInExceptionService
                            .queryWhCheckInExceptions(queryCondition, null);
                    int exceptionCount = getExceptionCount(whCheckInExceptionList, item.getSku());

                    // 根据采购单，sku，异常类型（自动生成少货异常）查询异常单
                    queryCondition.setExceptionFrom(ExceptionFrom.AUTO_CHECK_IN_EXCEPTION.intCode());
                    queryCondition.setStatus(null);
                    WhCheckInException dbWhCheckInException = whCheckInExceptionService
                            .queryWhCheckInException(queryCondition);
                    // 是否少SKU入库良品数量+不良品数量为0，缺少sku
                    Boolean lackSku = checkInCount + exceptionCount == 0;
                    WhCheckInException whCheckInException = prepareCreateExceptionParams(null, purchaseOrder, item,
                            lackSku);
                    whCheckInException.setQuantity((item.getQuantity() - checkInCount - exceptionCount) <= 0 ? 0
                            : (item.getQuantity() - checkInCount - exceptionCount));

                    // 异常单重新入库，自动完结
                    WhCheckInException originalException = null;
                    if (StringUtils.isNotEmpty(expressId) && StringUtils.contains(expressId, "RKYC")) {
                        originalException = getWhCheckInException(expressId,purchaseOrder.getPurchaseOrderNo(), item.getSku());
                        updateScanExceptionToFinish(expressId, purchaseOrder.getPurchaseOrderNo(), item.getSku());
                        if (Objects.nonNull(originalException)) {
                            String id = String.valueOf(originalException.getId());
                            map.put(id, new Timestamp(System.currentTimeMillis()));
                            boxItemMap.put(id, ItemOrderTypeEnum.WH_CHECK_IN_EXCEPTION.intCode());
                        }
                    }

                    // 异常数量大于0才允许生成异常单
                    if (dbWhCheckInException == null && checkInCount < item.getQuantity()
                            && whCheckInException.getQuantity() > 0) {
                        whCheckInExceptionService.createWhCheckInException(whCheckInException, PurchaseExpressRecordStatus.FINISH.getCode());
                        if(originalException != null && originalException.getId() != null) {
                            originalException = whCheckInExceptionService.getWhCheckInException(originalException.getId());
                            if (originalException != null){
                                String message = "生成新入库异常单,id="+whCheckInException.getId();
                                this.saveHandle(originalException,ExceptionStatus.COMPLETE.intCode(),message, false);
                                originalException.addNextGenerationExceptionId(whCheckInException.getId());
                                WhCheckInException updateException = new WhCheckInException();
                                updateException.setId(originalException.getId());
                                updateException.setNextGenerationExceptionIds(originalException.getNextGenerationExceptionIds());
                                updateException.setReceiveBoxNo(originalException.getReceiveBoxNo());
                                whCheckInExceptionService.updateWhCheckInException(updateException);
                            }
                        }
                        // 完成点数时，sku完全无货
                        if (whCheckInException.getQuantity().equals(item.getQuantity())
                                && StringUtils.isNotBlank(whCheckInException.getPurchaseOrderNo())
                                && StringUtils.isNotBlank(whCheckInException.getSku())) {
                            // 修改SKU状态,未到货
                            whPurchaseExpressRecordService.updatePurchaseSkuStatus(
                                    whCheckInException.getPurchaseOrderNo(), whCheckInException.getSku(),
                                    PurchaseSkuStatus.UN_ARRIVED.intCode(), null);
                        }
                    }
                    // 草稿状态才允许修改
                    else if (dbWhCheckInException != null
                            && ExceptionStatus.UNCONFIRM.intCode().equals(dbWhCheckInException.getStatus())
                            && dbWhCheckInException.getQuantity() > 0 && whCheckInException.getQuantity() >= 0
                            && !dbWhCheckInException.getQuantity().equals(whCheckInException.getQuantity())) {
                        //异常数量扣减到0，自动废弃
                        if (whCheckInException.getQuantity() == 0)
                            whCheckInException.setStatus(ExceptionStatus.DISCARDED.intCode());
                        whCheckInException.setId(dbWhCheckInException.getId());
                        whCheckInExceptionService.updateWhCheckInException(whCheckInException, PurchaseExpressRecordStatus.FINISH.getCode());
                        if (ExceptionStatus.DISCARDED.intCode().equals(whCheckInException.getStatus()))
                            whCheckInExceptionService.updateLocationAuto(whCheckInException, dbWhCheckInException.getLocationNumber());
                    } else {
                        // 点数完成,更新统一异常批次
                        whCheckInExceBatchService.createWhCheckInExceptionBatch(whCheckInException, PurchaseExpressRecordStatus.FINISH.getCode());
                    }
                    if (whCheckInException.getId() != null) {
                        WhCheckInExceptionHandle whCheckInExceptionHandle = createExceptionHandle(whCheckInException);
                        whCheckInExceptions.add(whCheckInException);
                        whCheckInExceptionHandles.add(whCheckInExceptionHandle);
                        rsp.setBody(
                                preparePushCheckInExceptionToPmsMsg(whCheckInExceptions, whCheckInExceptionHandles));
                        rsp.setStatus(StatusCode.SUCCESS);
                    }
                }
                if (StringUtils.isNotBlank(allCheckIn) && !allCheckIn.contains("false")) {
                    // 完成点数时，sku入库数量等于采购数量，修改采购单状态为全部入库
                    whPurchaseExpressRecordService.updatePurchaseOrderPurchaseStatus(purchaseOrder.getPurchaseOrderNo(),
                            WmsPurchaseOrderStatus.ALL_STOCK_IN.intCode());
                } else if (StringUtils.isNotBlank(allCheckIn)) {
                    // 完成点数时，sku入库数量小于采购数量，修改采购单状态为全部入库
                    whPurchaseExpressRecordService.updatePurchaseOrderPurchaseStatus(purchaseOrder.getPurchaseOrderNo(),
                            WmsPurchaseOrderStatus.PART_STOCK_IN.intCode());
                }
                // 修改快递单状态,已处理
                whPurchaseExpressRecordService.updatePurchaseExpressStatus(purchaseOrder.getPurchaseOrderNo(),
                        purchaseOrder.getTrackingNumber(), PurchaseExpressStatus.PROCESSED.intCode());

                // 标记快递单已点数完成
                whPurchaseExpressRecordService.updateCheckInFinish(purchaseOrder, map, boxItemMap);
            }
            // 周转筐未解绑
            if (StringUtils.isNotBlank(receiveBoxNo)) {
                logger.info("要解绑的快递单号：" + intersection + " ,周转框：" + receiveBoxNo+",expressId："+expressId);
                // 批量解绑周转框
                for (String id : map.keySet()) {
                    // 有扫描时间才能解绑周转框
                    if (map.get(id) != null && boxItemMap.get(id) != null) {
                        whBoxService.unbindItem(receiveBoxNo, id, boxItemMap.get(id),true);
                    }

                }

                if (StringUtils.isNotBlank(receiveBoxNo)) {
                    whBoxService.checkUnSplitCount(receiveBoxNo);
                }
            }


        }
        return rsp;
    }

    /**
     *
     * 通过周转框绑定的入库异常单和采购单，sku尽可能的匹配入库异常单
     * @param boxNo
     * @param purchaseOrderNo
     * @param sku
     * @return
     */
    private WhCheckInException getWhCheckInException(String boxNo, String purchaseOrderNo, String sku) {
        if (StringUtils.isEmpty(boxNo) || StringUtils.isEmpty(purchaseOrderNo) || StringUtils.isEmpty(sku)) {
            return null;
        }
        // 查询异常周转筐对应的异常单
        WhBox whBox = whBoxService.queryWhBoxByBoxNo(boxNo);
        WhCheckInException existException = null;
        if (whBox != null && StringUtils.isNotBlank(whBox.getRelationNo())) {
            existException = whCheckInExceptionService.getWhCheckInException(Integer.valueOf(whBox.getRelationNo()));
        }
        if (existException == null) {
            WhCheckInExceptionQueryCondition exceptionQuery = new WhCheckInExceptionQueryCondition();
            exceptionQuery.setBoxNo(boxNo);
            exceptionQuery.setPurchaseOrderNo(purchaseOrderNo);
            exceptionQuery.setTrackingNumber(purchaseOrderNo);
            exceptionQuery.setSku(sku);
            existException = whCheckInExceptionService.queryWhCheckInException(exceptionQuery);
            if (existException == null) {
                exceptionQuery.setPurchaseOrderNo(null);
                exceptionQuery.setNewPurchaseOrderNo(purchaseOrderNo);
                existException = whCheckInExceptionService.queryWhCheckInException(exceptionQuery);
            }
        }
        return existException;
    }

    /**
     * 异常单重新入库,自动完成
     *
     * @param boxNo
     * @param purchaseOrderNo
     * @param sku
     */
    private void updateScanExceptionToFinish(String boxNo, String purchaseOrderNo, String sku) {
        WhCheckInException existException = this.getWhCheckInException(boxNo, purchaseOrderNo, sku);
        if (existException == null)
            return;

        if (!ExceptionStatus.WAIT_CHECK_IN.intCode().equals(existException.getStatus())
                && !ExceptionStatus.STOCK_IN_ING.intCode().equals(existException.getStatus())
                && !ExceptionStatus.QC_PENDING.intCode().equals(existException.getStatus()))
            return;

        WhCheckInException updateException = new WhCheckInException();
        updateException.setId(existException.getId());
        updateException.setBoxNo(existException.getBoxNo());
        updateException.setLastPurchaseHandleComment("异常单重新入库,自动完成！");
        updateException.setAutoHandle(true);
        updateException.setQuantity(existException.getQuantity());
        whCheckInExceptionService.updateExceptionToFinished(updateException);

        WhCheckInExceptionHandleQueryCondition query = new WhCheckInExceptionHandleQueryCondition();
        query.setExceptionId(existException.getId());
        int count = whCheckInExceptionHandleService.exceptionCount(query);
        WhCheckInExceptionHandle whCheckInExceptionHandle = whCheckInExceptionHandleService
                .queryWhCheckInExceptionHandles(query, null).get(count - 1);
        updateException.setStatus(ExceptionStatus.COMPLETE.intCode());// 状态标记为已完成
        updateException.setFinishDate(new Timestamp(System.currentTimeMillis()));// 完成时间（仓库处理时间）
        updateException.setFinishUser(DataContextHolder.getUserId());// 完成员（仓库处理员）

        handleExceptionDateService.doSaveExceptionDate(updateException, existException.getStatus());

        logger.info("start to send exception message to pms ====exceptionId:" + updateException.getId());
        rabbitmqProducerService.pushCheckInExceptionMsgToPms(updateException, whCheckInExceptionHandle,
                new PushCheckInException());
        logger.info("send exception message to pms end ");

    }

    /**
     * 创建入库异常单处理详情
     *
     * @param whCheckInException
     * @return
     */
    public WhCheckInExceptionHandle createExceptionHandle(WhCheckInException whCheckInException) {
        WhCheckInExceptionHandle whCheckInExceptionHandle = new WhCheckInExceptionHandle();
        if (whCheckInException != null) {
            // 组装入库异常单处理详情数据
            whCheckInExceptionHandle.setCreatedBy(DataContextHolder.getUserId());
            whCheckInExceptionHandle.setCreationDate(new Timestamp(System.currentTimeMillis()));
            whCheckInExceptionHandle.setExceptionId(whCheckInException.getId());
            whCheckInExceptionHandle.setQuantity(whCheckInException.getQuantity());
            whCheckInExceptionHandle.setStatus(whCheckInException.getStatus());
            whCheckInExceptionHandle.setHandleComment(whCheckInException.getExceptionComment());
            // 创建入库异常单处理详情
            whCheckInExceptionHandleService.createWhCheckInExceptionHandle(whCheckInExceptionHandle);
        }
        return whCheckInExceptionHandle;
    }

    /**
     * 组装异常入库参数
     *
     * @param whCheckIn
     * @return
     */
    public WhCheckInException prepareCreateExceptionParams(WhCheckIn whCheckIn, PurchaseOrder purchaseOrder,
                                                           PurchaseOrderItem purchaseOrderItem, Boolean lackSku) {
        WhCheckInException whCheckInException = new WhCheckInException();
        if (whCheckIn != null && purchaseOrder == null && purchaseOrderItem == null) {
            whCheckInException = whCheckIn.getWhCheckInException();
            // 创建异常入库单参数设置
            whCheckInException.setSku(whCheckIn.getWhCheckInItem().getSku());// sku
            // TODO 图片，默认选用sku图片，后期拍照功能完善后可更改
            // whCheckInException.setImage(whCheckIn.getWhCheckInItem().getWhSku().getImageUrl());
            whCheckInException.setInId(whCheckIn.getInId());// 入库单编号
            if(whCheckInException.getExcessExceptionFlag() == null || !whCheckInException.getExcessExceptionFlag()) {
                whCheckInException.setPurchaseOrderNo(whCheckIn.getPurchaseOrderNo());// 采购单号
                whCheckInException.setTrackingNumber(whCheckIn.getTrackingNumber());// 快递单号
            }
            whCheckInException.setExceptionFrom(ExceptionFrom.CHECK_IN_EXCEPTION.intCode());// 设置异常来源，"点数异常"
            if (whCheckIn.getInId() != null) {
                whCheckInException.setCheckInUser(DataContextHolder.getUserId());
            }
            whCheckInException.setBoxNo(whCheckInException.getBoxNo().toUpperCase());// 防止周转码小写
        } else if (whCheckIn == null && purchaseOrder != null && purchaseOrderItem != null) {
            whCheckInException.setUserName(purchaseOrder.getPurchaseUser());
            // 创建异常入库单参数设置
            whCheckInException.setSku(purchaseOrderItem.getSku());// sku
            whCheckInException.setPurchaseOrderNo(purchaseOrder.getPurchaseOrderNo());// 采购单号
            whCheckInException.setTrackingNumber(purchaseOrder.getTrackingNumber());// 快递单号
            whCheckInException.setExceptionFrom(ExceptionFrom.AUTO_CHECK_IN_EXCEPTION.intCode());// 设置异常来源
            whCheckInException.setExceptionComment("点数完成，生成【少货/少SKU】异常");
            if (lackSku != null && lackSku) {
                whCheckInException.setExceptionType(ExceptionType.LESS_SKU.intCode().toString());
            } else if (!lackSku) {
                whCheckInException.setExceptionType(ExceptionType.LESS_QUANTITY.intCode().toString());
            }
        }
        WhWarehouse whWarehouse = whWarehouseService.queryOriginalWhWarehouse(true);
        if (whWarehouse != null) {
            whCheckInException.setWarehouseId(whWarehouse.getId());// 设置仓库为本仓
        }
        whCheckInException.setStatus(ExceptionStatus.UNCONFIRM.intCode());// 设置初始状态，"草稿"
        whCheckInException.setCreatedBy(DataContextHolder.getUserId());// 创建人
        whCheckInException.setCheckInUser(DataContextHolder.getUserId());// 入库员
        whCheckInException.setCreationDate(new Timestamp(System.currentTimeMillis()));
        // 获取采购员编号。
        if (whCheckInException.getUserName() != null) {
            SystemParam systemParam = CacheUtils.SystemParamGet("PURCHASE_USERS.PURCHASE_USERS");
            List<PmsPurchaseUsers> purchaseUsersList = JSONArray.parseArray(systemParam.getParamValue(),
                    PmsPurchaseUsers.class);
            if (CollectionUtils.isNotEmpty(purchaseUsersList)) {
                for (PmsPurchaseUsers purchaseUsers : purchaseUsersList) {
                    if (purchaseUsers.getUserName().equals(whCheckInException.getUserName())) {
                        whCheckInException.setPurchaseUser(purchaseUsers.getId());
                        break;
                    }
                }
            }
            // 用户系统新增的采购员仓库缓存中没有，匹配采购员编号不成功
            // 将缓存清空后重新获取
            if (whCheckInException.getPurchaseUser() == null) {
                SystemParam updateSystemParam = new SystemParam();
                updateSystemParam.setParamId(systemParam.getParamId());
                updateSystemParam.setParamKey("PURCHASE_USERS");
                updateSystemParam.setParamValue("");
                systemParamService.updateSystemParam(updateSystemParam);
                whCheckInException
                        .setPurchaseUser(GetUserNameOrEmployeeNameUtil.getUserId(whCheckInException.getUserName()));
            }
        }
        whCheckInException.setPurchaseUserNameToPms(whCheckInException.getPurchaseUserNameToPms());
        return whCheckInException;
    }

    /**
     * 组装要推送到采购的异常数据
     *
     * @param whCheckInExceptionList
     * @param whCheckInExceptionHandleList
     * @return
     */
    public Map<String, Object> preparePushCheckInExceptionToPmsMsg(List<WhCheckInException> whCheckInExceptionList,
                                                                   List<WhCheckInExceptionHandle> whCheckInExceptionHandleList) {
        Map<String, Object> map = new HashMap<>();
        if (whCheckInExceptionList != null && whCheckInExceptionHandleList != null) {
            PushCheckInException pushCheckInException = new PushCheckInException();
            pushCheckInException.setWhCheckInExceptionList(whCheckInExceptionList);
            pushCheckInException.setWhCheckInExceptionHandleList(whCheckInExceptionHandleList);
            map.put("pushCheckInException", pushCheckInException);
        }
        return map;
    }

    /**
     * 获取当前sku已入库数量
     *
     * @param whCheckIns
     * @param sku
     * @return
     */
    public int getCheckInCount(List<WhCheckIn> whCheckIns, String sku) {
        int checkInCount = 0;
        if (CollectionUtils.isNotEmpty(whCheckIns)) {
            for (WhCheckIn checkIn : whCheckIns) {
                if (!CheckInStatus.DISCARDED.intCode().equals(checkIn.getStatus())) {
                    WhCheckInItem item = checkIn.getWhCheckInItem();

                    if (item != null && item.getSku().equals(sku)) {

                        if (CheckInStatus.CONFIRMED.intCode().equals(checkIn.getStatus())) {
                            checkInCount += item.getUpQuantity() == null ? 0 : item.getUpQuantity();
                            // WAITING_UP("等待上架", "9"),UPING("上架中",
                            // "11"),UPERROR("上架失败", "12"),
                        } else if (CheckInStatus.WAITING_UP.intCode().equals(checkIn.getStatus())
                                || CheckInStatus.UPING.intCode().equals(checkIn.getStatus())
                                || CheckInStatus.UPERROR.intCode().equals(checkIn.getStatus())) {
                            checkInCount += item.getQcQuantity() == null ? item.getQuantity() : item.getQcQuantity();
                        } else {
                            checkInCount += item.getQuantity() == null ? 0 : item.getQuantity();
                        }
                    }

                }
            }
        }
        return checkInCount;
    }

    /**
     * 获取当前sku的入库异常数量
     *
     * @param whCheckInExceptions
     * @param sku
     * @return
     */
    public int getExceptionCount(List<WhCheckInException> whCheckInExceptions, String sku) {
        int exceptionCount = 0;
        if (CollectionUtils.isNotEmpty(whCheckInExceptions)) {
            for (WhCheckInException whCheckInException : whCheckInExceptions) {
                if (whCheckInException.getSku() != null && whCheckInException.getSku().equals(sku)) {
                    exceptionCount += whCheckInException.getQuantity() == null ? 0 : whCheckInException.getQuantity();
                }
            }
        }
        return exceptionCount;
    }

    @Override
    public void pushCheckinDataToPms(WhCheckIn checkin,Integer status) {
        logger.info("================start pushCheckinDataToPms ===============");
        whCheckInExcessService.mergeWhCheckInExcessList(Collections.singletonList(checkin));
        List<WhCheckInExcess> whCheckInExcessList = checkin.getWhCheckInExcessList();
        if (CollectionUtils.isNotEmpty(whCheckInExcessList) && !SkuBusinessType.CHECK_IN.intCode().equals(status)) {
            //修改多货表数量
            whCheckInExcessService.updateWhCheckInExcessList(checkin,whCheckInExcessList,status);
            String originalpurchaseOrderNo=null;
            Integer originalInId=null;
            if (StringUtils.isNotBlank(checkin.getComment()) && checkin.getComment().contains(",") && checkin.getComment().contains("原采购单") && checkin.getComment().contains("原入库单ID")) {
                originalpurchaseOrderNo = checkin.getComment().split(",")[0].replace("原采购单", "");
                String checkInId = checkin.getComment().split(",")[1].replace("原入库单ID", "");
                if (StringUtils.isNotBlank(checkInId)) {
                    originalInId = Integer.parseInt(checkInId);
                }

            }
            for (WhCheckInExcess whCheckInExcess : whCheckInExcessList) {
                WhCheckIn newCheckIn=BeanConvertUtils.convert(checkin, WhCheckIn.class);
                newCheckIn.setOriginalInId(originalInId);
                newCheckIn.setOriginalpurchaseOrderNo(originalpurchaseOrderNo);

                WhCheckInItem newCheckInItem = new WhCheckInItem();
                if (checkin.getWhCheckInItem() != null) {
                    newCheckInItem =BeanConvertUtils.convert(checkin.getWhCheckInItem(), WhCheckInItem.class);
                }
                newCheckIn.setWhCheckInItem(newCheckInItem);

                newCheckIn.setPurchaseOrderNo(whCheckInExcess.getPurchaseOrderNo());
                if (StringUtils.isNotBlank(whCheckInExcess.getTrackingNumber())) {
                    newCheckIn.setTrackingNumber(whCheckInExcess.getTrackingNumber());
                }
                newCheckIn.getWhCheckInItem().setQuantity(whCheckInExcess.getMatchedQuantity());
                newCheckIn.getWhCheckInItem().setQcQuantity(whCheckInExcess.getQcQuantity());
                newCheckIn.getWhCheckInItem().setUpQuantity(whCheckInExcess.getUpQuantity());

                if (StringUtils.isNotBlank(whCheckInExcess.getProcessingMethod())) {
                    newCheckIn.setMatchType(Integer.valueOf(whCheckInExcess.getProcessingMethod()));
                }

                Integer userId = DataContextHolder.getUserId();
                if (userId != null) {
                    newCheckIn.setWmsOperator(TaglibUtils.getEmployeeNameByUserId(userId));
                }

                executors.execute(()->{
                    try {
                        if (newCheckIn.getWhCheckInItem() != null) {
                            WhSkuQueryCondition querySku = new WhSkuQueryCondition();
                            querySku.setSku(newCheckIn.getWhCheckInItem().getSku());
                            WhSku whSku = whSkuService.queryWhSku(querySku);
                            newCheckIn.getWhCheckInItem().setWhSku(whSku);
                            if (StringUtils.isNotBlank(newCheckIn.getTrackingNumber())) {
                                WhPurchaseExpressRecordQueryCondition queryRecord = new WhPurchaseExpressRecordQueryCondition();
                                queryRecord.setTrackingNumber(newCheckIn.getTrackingNumber());
                                WhPurchaseExpressRecord record = whPurchaseExpressRecordService
                                        .queryWhPurchaseExpressRecord(queryRecord);
                                newCheckIn.setWhPurchaseExpressRecord(record);
                            }
                            rabbitmqProducerService.pushCheckinDataToPms(newCheckIn);
                        }
                    } catch (Exception e) {
                        logger.error("pushCheckinDataToPms error", e);
                    }
                });
            }
            return;
        }

        executors.execute(new Runnable() {
            @Override
            public void run() {
                if (checkin != null && checkin.getWhCheckInItem() != null) {
                    WhSkuQueryCondition querySku = new WhSkuQueryCondition();
                    querySku.setSku(checkin.getWhCheckInItem().getSku());
                    WhSku whSku = whSkuService.queryWhSku(querySku);
                    checkin.getWhCheckInItem().setWhSku(whSku);
                    if (StringUtils.isNotBlank(checkin.getTrackingNumber())) {
                        WhPurchaseExpressRecordQueryCondition queryRecord = new WhPurchaseExpressRecordQueryCondition();
                        queryRecord.setTrackingNumber(checkin.getTrackingNumber());
                        WhPurchaseExpressRecord record = whPurchaseExpressRecordService
                                .queryWhPurchaseExpressRecord(queryRecord);
                        checkin.setWhPurchaseExpressRecord(record);
                    }
                    rabbitmqProducerService.pushCheckinDataToPms(checkin);
                }
            }
        });
    }

    //上架后处理其他表数据
    public void updateAfterUpToLocation(WhCheckIn checkin, WhCheckInItem updateItem, boolean allUp) {
        String operationId = DataContextHolder.getOperationId();
        String username = DataContextHolder.getUsername();
        Object saleUserId = DataContextHolder.getContext(Constant.SALE_USER_ID);
        executors.execute(new Runnable() {
            @Override
            public void run() {
                DataContextHolder.setOperationId(operationId);
                DataContextHolder.setUsername(username);
                DataContextHolder.setContext(Constant.SALE_USER_ID, saleUserId);
                if (checkin != null && checkin.getWhCheckInItem() != null) {
                    WhCheckInItem whCheckInItem = checkin.getWhCheckInItem();
                    if (checkin.getExceptionType().equals(CheckInWhType.LOCAL.intCode())
                            || updateItem.getPacUpNum() == null
                            || updateItem.getUpQuantity() > updateItem.getPacUpNum())
                        // 记录sku上架时间，推送给产品系统
                        amqMessageService.createAmqMessage(
                                AssembleMessageDataUtils.assembleSkuUpTimeData(new ProductSkuUpTimeMessage(
                                        whCheckInItem.getSku(), new Timestamp(System.currentTimeMillis()))));

                    try {
                        whUniqueSkuService.updateWhUniqueSKuList(checkin.getInId(), CheckInType.PURCHASE.intCode());
                    }
                    catch (Exception e) {
                        logger.error("采购上架唯一码添加日志失败！" + e.getMessage());
                    }
                    if (CheckInStatus.CONFIRMED.intCode().equals(checkin.getStatus())) {

                        if (StringUtils.isNotBlank(checkin.getPurchaseOrderNo())
                                && StringUtils.isNotBlank(whCheckInItem.getSku())) {
                            // 修改SKU状态,部分上架/已上架
                            whPurchaseExpressRecordService
                                    .updatePurchaseSkuStatus(checkin.getPurchaseOrderNo(), whCheckInItem.getSku(),
                                            allUp ? PurchaseSkuStatus.CONFIRMED.intCode()
                                                    : PurchaseSkuStatus.PART_CONFIRMED.intCode(),
                                            updateItem.getUpQuantity());
                        }
                    }
                }
                //生成新品核查
                createNewProductMaintenance(checkin);
            }
        });
    }


    /**
     * 生成新品核查
     * 
     * @param checkin
     */
    public void createNewProductMaintenance(WhCheckIn checkin) {
        if (checkin == null || checkin.getWhCheckInItem() == null
                || StringUtils.isBlank(checkin.getWhCheckInItem().getSku())) {
            return;
        }
        String sku = checkin.getWhCheckInItem().getSku();
        Integer stockId = checkin.getWhCheckInItem().getSkuId();

        WhSkuQueryCondition query = new WhSkuQueryCondition();
        query.setSku(sku);
        query.setIsSkuList(true);
        List<WhSku> whSkus = whSkuService.queryWhSkuStocks(query);
        if (CollectionUtils.isEmpty(whSkus))
            return;
        WhSku whSku = whSkus.get(0);
        // SKU销售属性为新品,当SKU包装标准图片为空
        if (!StringUtils.equalsIgnoreCase(whSku.getSaleAttributeSettingStr1(), SalePropertyEnums.NEWPRODUCT.getName())
                || !StringUtils.isBlank(whSku.getPackImage()))
            return;
        WhStockQueryCondition stockQuery = new WhStockQueryCondition();
        stockQuery.setSku(sku);
        stockQuery.setId(stockId);
        stockQuery.setQueryLocationType(true);
        List<WhStock> whStocks = whStockService.queryWhStocks(stockQuery, null);
        if (CollectionUtils.isEmpty(whStocks))
            return;
        boolean pickLocation = whStocks.get(0).getLocationType() != null
                && LocationType.PICKING.intCode().equals(whStocks.get(0).getLocationType());
        // 入库单上架到正常拣货库位
        if (!pickLocation)
            return;
        NewProductMaintenanceQueryCondition mQuery = new NewProductMaintenanceQueryCondition();
        mQuery.setSku(sku);
        mQuery.setStockId(stockId);
        List<NewProductMaintenance> existMaintenances = newProductMaintenanceService.queryNewProductMaintenances(mQuery,
                null);
        if (CollectionUtils.isNotEmpty(existMaintenances))
            return;
        NewProductMaintenance newProductMaintenance = new NewProductMaintenance();
        newProductMaintenance.setSku(sku);
        newProductMaintenance.setStockId(stockId);
        newProductMaintenance.setLocationNumber(whStocks.get(0).getLocationNumber());
        newProductMaintenance.setStatus(MaintenanceStatus.WAIT_CHECK.getCode());
        newProductMaintenance.setCreationDate(new Timestamp(System.currentTimeMillis()));
        newProductMaintenanceService.createNewProductMaintenance(newProductMaintenance);
    }

    /**
     * 保存唯一码保质期明细
     *
     * @param checkin
     */
    @Override
    public void saveUniqueSkuExpDetail(WhCheckIn checkin) {
        // 唯一码保质期
        WhUniqueSkuQueryCondition queryCondition = new WhUniqueSkuQueryCondition();
        queryCondition.setRelationId(checkin.getInId());
        queryCondition.setSku(checkin.getWhCheckInItem().getSku());
        queryCondition.setType(SkuBusinessType.CHECK_IN.intCode());
        List<WhUniqueSku> whUniqueSkuList = whUniqueSkuService.queryWhUniqueSkus(queryCondition, null);
        if (CollectionUtils.isNotEmpty(whUniqueSkuList)) {
            // 查询是否存在
            UniqueSkuExpRelationQueryCondition query = new UniqueSkuExpRelationQueryCondition();
            query.setRelationId(checkin.getInId());
            query.setSku(checkin.getWhCheckInItem().getSku());
            query.setUuid(whUniqueSkuList.stream().map(WhUniqueSku::getUuid).collect(Collectors.joining(",")));
            List<UniqueSkuExpRelation> expRelations = uniqueSkuExpRelationService.queryUniqueSkuExpRelations(query,
                    null);
            if (CollectionUtils.isEmpty(expRelations)) {
                List<UniqueSkuExpRelation> addList = UniqueSkuUtils.buildUniqueSkuExpRelation(whUniqueSkuList, checkin);
                uniqueSkuExpRelationService.batchCreateUniqueSkuExpRelation(addList);
            } else {
                List<UniqueSkuExpRelation> updateList = new ArrayList<>();
                expRelations.forEach(exist -> {
                    UniqueSkuExpRelation skuExpRelation = new UniqueSkuExpRelation();
                    skuExpRelation.setId(exist.getId());
                    skuExpRelation.setDays(checkin.getDays());
                    if (StringUtils.isNotEmpty(checkin.getExpDate()))
                        skuExpRelation.setExpDate(Timestamp.valueOf(checkin.getExpDate() + " 00:00:00"));
                    if (StringUtils.isNotEmpty(checkin.getProDate()))
                        skuExpRelation.setProDate(Timestamp.valueOf(checkin.getProDate() + " 00:00:00"));
                    updateList.add(skuExpRelation);
                });
                uniqueSkuExpRelationService.batchUpdateUniqueSkuExpRelation(updateList);
            }
        }
    }

    /**
     * 创建保质期批次并绑定唯一码
     *
     * @param checkin
     */
    @Override
    public void createExpBatchAndBindUuid(WhCheckIn checkin) {
        if (checkin == null || checkin.getWhCheckInItem() == null)
            return;
        List<String> expSkuList = GetSkuQcCategoryDescUtil
                .getExpSkuList(Arrays.asList(checkin.getWhCheckInItem().getSku()));
        if (CollectionUtils.isEmpty(expSkuList))
            return;
        ExpManageItemQueryCondition query = new ExpManageItemQueryCondition();
        query.setRelationId(checkin.getInId());
        query.setType(DrpTurnoverOderType.CHECK_IN.intCode());
        List<ExpManageItem> existItemList = expManageItemService.queryExpManageItems(query, null);
        if (CollectionUtils.isNotEmpty(existItemList))
            return;
        WhStockQueryCondition stockQuery = new WhStockQueryCondition();
        stockQuery.setSku(checkin.getWhCheckInItem().getSku());
        stockQuery.setId(checkin.getWhCheckInItem().getSkuId());
        if (checkin.getWhCheckInItem().zfOrder() && StringUtils.isNotBlank(checkin.getWhCheckInItem().getComment())) {
            stockQuery.setId(Integer.valueOf(checkin.getWhCheckInItem().getComment()));
        }
        WhStock stock = whStockService.queryWhStock(stockQuery);
        if (stock == null) {
            logger.error("创建保质期批次未找到库存记录：sku:" + checkin.getWhCheckInItem().getSku() + "locationNumber:" + checkin.getLocationNumber());
            return;
        }
        // 根据入库单ID和SKU查询唯一码保质期批次关联表
        UniqueSkuExpRelationQueryCondition queryCondition = new UniqueSkuExpRelationQueryCondition();
        queryCondition.setRelationId(checkin.getInId());
        queryCondition.setSku(checkin.getWhCheckInItem().getSku());
        List<UniqueSkuExpRelation> relationList = uniqueSkuExpRelationService.queryUniqueSkuExpRelations(queryCondition,
                null);
        String batchNo = CreateTaskNoUtils.createBatNo("EXP", "expManage");
        Timestamp proDate = null;
        Timestamp expDate = null;
        Integer days = null;
        List<UniqueSkuExpRelation> updateList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(relationList)) {
            proDate = relationList.get(0).getProDate();
            expDate = relationList.get(0).getExpDate();
            days = relationList.get(0).getDays();

            relationList.forEach(expRelation -> {
                UniqueSkuExpRelation skuExpRelation = new UniqueSkuExpRelation();
                skuExpRelation.setId(expRelation.getId());
                skuExpRelation.setExpNo(batchNo);
                updateList.add(skuExpRelation);
            });
        }
        // 创建保质期批次
        ExpManage expManage = new ExpManage();
        expManage.setBatchNo(batchNo);
        expManage.setStockId(stock.getId());
        expManage.setCheckInType(CheckInWhType.LOCAL.intCode());
        expManage.setSource(DrpTurnoverOderType.CHECK_IN.intCode());
        expManage.setSku(checkin.getWhCheckInItem().getSku());
        expManage.setDays(days);
        expManage.setProDate(proDate);
        expManage.setExpDate(expDate);
        expManage.setCreateBy(checkin.getUpUser());
        expManage.setCreationDate(checkin.getUpTime());
        expManage.setCheckInQuantity(checkin.getWhCheckInItem().getUpQuantity());
        expManage.setQuantity(checkin.getWhCheckInItem().getUpQuantity());
        expManageService.createExpManage(expManage);
        SystemLogUtils.EXP_MANAGE_LOG.log(expManage.getId(), "创建保质期批次");

        // 批次明细
        ExpManageItem manageItem = new ExpManageItem();
        manageItem.setBatchNo(expManage.getBatchNo());
        manageItem.setType(DrpTurnoverOderType.CHECK_IN.intCode());
        manageItem.setQuantity(checkin.getWhCheckInItem().getUpQuantity());
        manageItem.setRelationId(checkin.getInId());
        manageItem.setCreationDate(checkin.getUpTime());
        expManageItemService.createExpManageItem(manageItem);

        // 唯一码关联
        uniqueSkuExpRelationService.batchUpdateUniqueSkuExpRelation(updateList);

        //修改库位属性
        WhStock updateStock = new WhStock();
        updateStock.setId(stock.getId());
        stock.addLocationTag(LocationTagEnum.SHELF_LIFE);
        updateStock.setLocationTag(stock.getLocationTag());
        whStockService.updateWhStock(updateStock);

    }

    /**
     * 异常单重新入库上架成功后修改异常单状态为完成, 存操作日志并发送消息到采购
     * 异常单状态已经是完成的，则不再次进行记录和推送操作
     *
     * @param exceptionBoxNo
     * @param status
     */
    @Override
    public void changeExceptionStatusAndSaveLogAndSendMsgToPms(WhCheckIn whCheckIn, String exceptionBoxNo,
                                                               Integer status) {
        logger.info("================start pushExceptionStatusAndSaveLogAndSendMsgToPms ===============");
        if (StringUtils.isBlank(exceptionBoxNo) && whCheckIn == null) {
            return;
        }
        List<WhCheckInException> whCheckInExceptionList = new ArrayList<WhCheckInException>();

        if (StringUtils.isBlank(exceptionBoxNo)) {
            WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();
            query.setStatusList(Arrays.asList(ExceptionStatus.WAREHOUSE_PENDING.intCode(), ExceptionStatus.QC_PENDING.intCode(), ExceptionStatus.STOCK_IN_ING.intCode()));
            query.setHandleWays(Arrays.asList(ExceptionHandleWay.CHECK_IN.intCode(),
                    ExceptionHandleWay.CREATE_NEW_ORDER_TO_CHECK_IN.intCode(), ExceptionHandleWay.REISSUE.intCode(),
                    ExceptionHandleWay.UPDATE_DESCRIPTION.intCode(), ExceptionHandleWay.UPDATE_IMAGE.intCode(), ExceptionHandleWay.UPDATE_IMG_SIZE.intCode(), ExceptionHandleWay.CHANG_SIZE.intCode()));
            query.setExceptionTypes(Arrays.asList(ExceptionType.LESS_QUANTITY.intCode().toString(),
                    ExceptionType.LESS_SKU.intCode().toString()));
            query.setSku(whCheckIn.getWhCheckInItem().getSku());
            query.setTrackingNumber(whCheckIn.getTrackingNumber());
            query.setPurchaseOrderNo(whCheckIn.getPurchaseOrderNo());
            whCheckInExceptionList = whCheckInExceptionService.queryWhCheckInExceptions(query, null);
            if (CollectionUtils.isEmpty(whCheckInExceptionList)) {
                query.setPurchaseOrderNo(null);
                query.setNewPurchaseOrderNo(whCheckIn.getPurchaseOrderNo());
                whCheckInExceptionList = whCheckInExceptionService.queryWhCheckInExceptions(query, null);
            }
        } else {
            WhBox whBox = whBoxService.queryWhBoxByBoxNo(exceptionBoxNo);
            if (whBox != null && StringUtils.isNotBlank(whBox.getRelationNo())) {

                WhCheckInException whCheckInException = whCheckInExceptionService
                        .getWhCheckInException(Integer.valueOf(whBox.getRelationNo()));
                // 处理方式为入库或建单入库、改尺寸、改描述、带样换图加图,状态为待仓库处理、入库中的异常单，QC后自动完成
                if ((ExceptionStatus.getWarehousePendingCode().contains(whCheckInException.getStatus())
                        || ExceptionStatus.STOCK_IN_ING.intCode().equals(whCheckInException.getStatus()))
                        && (ExceptionHandleWay.CHECK_IN.intCode().equals(whCheckInException.getHandleWay())
                        || ExceptionHandleWay.CREATE_NEW_ORDER_TO_CHECK_IN.intCode().equals(whCheckInException.getHandleWay())
                        || ExceptionHandleWay.UPDATE_DESCRIPTION.intCode().equals(whCheckInException.getHandleWay())
                        || ExceptionHandleWay.REISSUE.intCode().equals(whCheckInException.getHandleWay())
                        || ExceptionHandleWay.UPDATE_IMAGE.intCode().equals(whCheckInException.getHandleWay()))
                        || ExceptionHandleWay.UPDATE_IMG_SIZE.intCode().equals(whCheckInException.getHandleWay())
                        || ExceptionHandleWay.CHANG_SIZE.intCode().equals(whCheckInException.getHandleWay())) {
                    whCheckInExceptionList.add(whCheckInException);
                }
            }

        }

        if (CollectionUtils.isNotEmpty(whCheckInExceptionList)) {
            for (WhCheckInException whCheckInException : whCheckInExceptionList) {
                // 异常单状态已经是完成的，则不再次进行记录和推送操作
                if (Objects.equals(ExceptionStatus.COMPLETE.intCode(),whCheckInException.getStatus())){
                    continue;
                }
                if (ExceptionHandleWay.UPDATE_IMAGE.intCode().equals(whCheckInException.getHandleWay())
                        && (ExceptionStatus.WAREHOUSE_PENDING.intCode().equals(whCheckInException.getStatus())
                        || ExceptionStatus.PRODUCT_PENDING.intCode().equals(whCheckInException.getStatus())
                        || ExceptionStatus.WAIT_PUSH_PRODUCT.intCode().equals(whCheckInException.getStatus()))
                        && whCheckInException.getConfirmQuantity() > 0) continue;
                WhCheckInException updateException = new WhCheckInException();
                updateException.setId(whCheckInException.getId());
                if (Objects.equals(ExceptionStatus.QC_PENDING.intCode(), whCheckInException.getStatus())) {
                    updateException.setCompletedQCHandleDate(new Timestamp(System.currentTimeMillis()));
                }
                updateException.setStatus(ExceptionStatus.COMPLETE.intCode());
                updateException.setFinishDate(new Timestamp(System.currentTimeMillis()));// 完成时间（仓库处理时间）
                updateException.setFinishUser(DataContextHolder.getUserId());// 完成员（仓库处理员）
                whCheckInExceptionService.updateWhCheckInException(updateException);

                handleExceptionDateService.doSaveExceptionDate(updateException, whCheckInException.getStatus());

                whCheckInException.setInId(whCheckIn.getInId());
                String message = "入库单QC，自动完成，入库单ID:" + whCheckInException.getInId();
                saveHandle(whCheckInException, status, message,true);
            }
        }

    }

    public void saveHandle(WhCheckInException whCheckInException, Integer status, String message, boolean unbindBoxNo) {
        if (whCheckInException != null && whCheckInException.getId() != null) {

            // 解绑周转筐
            String boxNo = whCheckInException.getBoxNo();

            if (StringUtils.isNotBlank(boxNo) && ExceptionStatus.COMPLETE.intCode().equals(status) && unbindBoxNo) {
                String[][] logs = new String[][]{{"入库异常单完成", ""},
                        {"relationNo", whCheckInException.getId().toString()}};
                int updated = whBoxService.updateWhBoxOfUnbinding(boxNo, logs);
                if (updated >= 1) {
                    logger.info("入库异常单完成后释放周转码: boxNo[" + boxNo + "],  boxUpdated[" + updated + "]");
                }
            }

            WhCheckInExceptionHandle handle = new WhCheckInExceptionHandle();
            handle.setExceptionId(whCheckInException.getId());
            handle.setCreatedBy(DataContextHolder.getUserId());
            handle.setCreationDate(new Timestamp(System.currentTimeMillis()));
            handle.setStatus(status);
            handle.setHandleComment(message);

            whCheckInExceptionHandleService.createWhCheckInExceptionHandle(handle);

            // 推送状态消息到采购系统
            whCheckInException.setStatus(status);
            logger.info("start to send exception message to pms ====exceptionId:" + whCheckInException.getId());
            rabbitmqProducerService.pushCheckInExceptionMsgToPms(whCheckInException, handle,
                    new PushCheckInException());
            logger.info("send exception message to pms end ");

        }
    }

    @Override
    public int queryWhCheckInCount(WhCheckInQueryCondition query) {
        Assert.notNull(query, "query is null");
        return whCheckInDao.queryWhCheckInCount(query);
    }

    private static final String PUSH_CHECKIN_SKU_TO_OMS = "PUSH_CHECKIN_SKU_TO_OMS:";

    // String url = "http://192.168.3.172/oms//report/skuAvailableDays";
    @Override
    public boolean pushLastDateCheckinSkuListToOms() {
        boolean result = false;
        String today = DateUtils.dateToString(new Date(), "yyyy-MM-dd");
        Date endDate = DateUtils.stringToDate(today, "yyyy-MM-dd");
        Date startDate = DateUtils.getBeforeDate(endDate, 1);
        logger.info("startDate: " + startDate + ", endDate: " + endDate);
        String key = StringRedisUtils.get(PUSH_CHECKIN_SKU_TO_OMS + today);
        if (StringUtils.isBlank(key)) {
            String DAY_CHECKIN_SKU_URL = CacheUtils.SystemParamGet("OMS_PARAM.DAY_CHECKIN_SKU_URL").getParamValue();
            if (StringUtils.isBlank(DAY_CHECKIN_SKU_URL)) {
                logger.info("OMS_PARAM.DAY_CHECKIN_SKU_URL 参数为空！");
                return false;
            }
            List<String> skuList = whCheckInDao.queryLastDateCheckinSkuList(startDate, endDate);
            if (CollectionUtils.isNotEmpty(skuList)) {
                Integer warehouseId = WarehouseProperties.getWarehouseProperties().getLocalWarehouseId();
                Map<String, String> body = new HashMap();
                body.put("args", JSON.toJSONString(skuList));
                body.put("method", warehouseId.toString());
                logger.info("requestBody: " + JSON.toJSONString(body));
                String errorMessage = null;
                for (int i = 0; i < 5; i++) {
                    try {
                        ApiResult apiResult = HttpUtils.post(DAY_CHECKIN_SKU_URL, HttpUtils.ACCESS_TOKEN, body, ApiResult.class);
                        logger.info(
                                "采购接口返回结果：url：" + DAY_CHECKIN_SKU_URL + "===>result：" + JSON.toJSONString(apiResult));
                        if (!apiResult.isSuccess()) {
                            errorMessage = apiResult.getErrorMsg();
                            logger.error("errorMessage:" + errorMessage);
                        } else {
                            StringRedisUtils.set(PUSH_CHECKIN_SKU_TO_OMS + today, "success");
                            result = true;
                            break;
                        }
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                        errorMessage = e.getMessage();
                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        logger.error("Interrupted!"+e.getMessage(), e);
                        Thread.currentThread().interrupt();
                    }
                }
            } else {
                logger.warn("queryLastDateCheckinSkuList is null !");
                StringRedisUtils.set(PUSH_CHECKIN_SKU_TO_OMS + today, "success");
                result = true;
            }
        } else {
            logger.warn("重复操作!");
            result = true;
        }
        return result;
    }

    /**
     * 未匹配异常单自动完结
     *
     * @param trackingNumber
     */
    public void updateNotMatchPurchaseOrderExceptionToFinish(String trackingNumber) {
        if (StringUtils.isBlank(trackingNumber)) {
            return;
        }
        // 未匹配采购单的异常单，只要快递单绑定了采购单并生成了入库单时，将未匹配异常单（待仓库处理）自动完结
        WhCheckInExceptionQueryCondition queryCondition = new WhCheckInExceptionQueryCondition();
        queryCondition.setExceptionType(ExceptionType.NO_PURCHASE_ORDER_MATCH.intCode().toString());
        queryCondition.setTrackingNumber(trackingNumber);
        queryCondition.setStatusList(ExceptionStatus.getWarehousePendingCode());
        List<WhCheckInException> whCheckInExceptionList = whCheckInExceptionService
                .queryWhCheckInExceptions(queryCondition, null);
        if (CollectionUtils.isEmpty(whCheckInExceptionList)) {
            return;
        }

        List<WhCheckInExceptionHandle> whCheckInExceptionHandleList = new ArrayList<>();
        PushCheckInException pushCheckInException = new PushCheckInException();
        List<WhCheckInException> pushExceptionList = new ArrayList<>();
        for (WhCheckInException whCheckInException : whCheckInExceptionList) {
            whCheckInException.setLastPurchaseHandleComment("未匹配采购单的异常单，快递单绑定了采购单并生成了入库单,自动完成！");
            whCheckInException.setAutoHandle(true);
            whCheckInExceptionService.updateExceptionToFinished(whCheckInException);
            // 标记完成时，来源是QC全检的不发消息到采购系统
            if (!ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode().equals(whCheckInException.getExceptionFrom())) {
                WhCheckInExceptionHandleQueryCondition query = new WhCheckInExceptionHandleQueryCondition();
                query.setExceptionId(whCheckInException.getId());
                int count = whCheckInExceptionHandleService.exceptionCount(query);
                WhCheckInExceptionHandle whCheckInExceptionHandle = whCheckInExceptionHandleService
                        .queryWhCheckInExceptionHandles(query, null).get(count - 1);
                whCheckInExceptionHandleList.add(whCheckInExceptionHandle);

                whCheckInException.setStatus(ExceptionStatus.COMPLETE.intCode());// 状态标记为已完成
                whCheckInException.setFinishDate(new Timestamp(System.currentTimeMillis()));// 完成时间（仓库处理时间）
                whCheckInException.setFinishUser(DataContextHolder.getUserId());// 完成员（仓库处理员）
                pushExceptionList.add(whCheckInException);
            }
        }

        if (CollectionUtils.isNotEmpty(pushExceptionList)) {
            pushCheckInException.setWhCheckInExceptionHandleList(whCheckInExceptionHandleList);
            pushCheckInException.setWhCheckInExceptionList(pushExceptionList);
            // 推送异常完成的消息到采购系统
            logger.info("start to send updateNotMatchPurchaseOrderExceptionToFinish message to pms ====pushSize:"
                    + pushCheckInException.getWhCheckInExceptionList().size());
            rabbitmqProducerService.pushCheckInExceptionMsgToPms(null, null, pushCheckInException);
            logger.info("send updateNotMatchPurchaseOrderExceptionToFinish message to pms end ");
        }
    }

    @Override
    @StockServicelock
    public ResponseJson doUpIngCheckIn(List<String> skuList, WhCheckIn whCheckIn, Integer obtainUser) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(skuList) || whCheckIn == null) {
            response.setMessage("参数为空！");
            return response;
        }
        if (CheckInStatus.WAITING_UP.intCode().equals(whCheckIn.getStatus())
                || CheckInStatus.UPERROR.intCode().equals(whCheckIn.getStatus())) {
            boolean upError = CheckInStatus.UPERROR.intCode().equals(whCheckIn.getStatus()) ? true : false;
            whCheckIn.setStatus(CheckInStatus.UPING.intCode());
            whCheckIn.setObtainUser(obtainUser);// 提货员
            whCheckIn.setObtainTime(new Timestamp(System.currentTimeMillis()));// 提货时间
            SaleUser saleUser = saleUserService.getSaleUser(obtainUser);
            String obtainUserStr = "";
            if (saleUser != null) {
                obtainUserStr = saleUser.getName();
            }
            whCheckInDao.updateWhCheckIn(whCheckIn);

            // 提货完成移动库存（上架失败重新提货不修改库存）
            boolean updateStock = true;
            if (!upError) {
                updateStock = checkInUpdateStockService.batchUpdateStockByUping(whCheckIn);
            }
            if (!updateStock) {
                throw new RuntimeException("提货修改库存失败！");
            }

            CHECKINLOG.log(whCheckIn.getInId(), CheckInLogType.UPING.getName(),
                    new String[][]{{"提货员", obtainUserStr}});
            logger.info("提货扫描-上架中: inId[" + whCheckIn.getInId() + "], obtainUser[" + obtainUserStr + "], 提货改库存："
                    + updateStock);

            if (StringUtils.isNotBlank(whCheckIn.getPurchaseOrderNo()) && StringUtils.isNotBlank(skuList.get(0))) {
                // 修改SKU状态,上架中
                whPurchaseExpressRecordService.updatePurchaseSkuStatus(whCheckIn.getPurchaseOrderNo(), skuList.get(0),
                        PurchaseSkuStatus.UP_ING.intCode(), null);
            }

            WhCheckInItemQueryCondition itemQuery = new WhCheckInItemQueryCondition();
            itemQuery.setInId(whCheckIn.getInId());
            response.setMessage(JSON.toJSONString(whCheckInItemService.queryWhCheckInItem(itemQuery)));
            response.setStatus(StatusCode.SUCCESS);

            // TODO 发消息给采购
            pushCheckinDataToPms(whCheckIn,SkuBusinessType.UP_LOCATION.intCode());

        } else if (CheckInStatus.QC_NG.intCode().equals(whCheckIn.getStatus())) {
            response.setMessage("不良品，无法提货");
        } else if (whCheckIn.getStatus() < CheckInStatus.WAITING_UP.intCode()) {
            response.setMessage("未QC，无法提货");
        } else {
            response.setMessage("不是待上架或上架失败状态，无法提货");
        }
        return response;
    }

    /**
     * 查找sku入库单数量
     *
     * @param query
     * @return
     */
    @Override
    public Map<String, Integer> querySkuCheckInQuantityCount(WhCheckInQueryCondition query) {
        Map<String, Integer> quantityMap = new HashMap<String, Integer>();
        List<WhCheckIn> whCheckIns = queryWhCheckIns(query, null);
        if (CollectionUtils.isNotEmpty(whCheckIns)) {
            for (WhCheckIn whCheckIn : whCheckIns) {
                WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();
                if (whCheckInItem != null) {
                    Integer mapQuantity = quantityMap.get(whCheckInItem.getSku()) == null ? 0
                            : quantityMap.get(whCheckInItem.getSku());
                    Integer checkInQuantity = whCheckInItem.getQuantity() == null ? 0 : whCheckInItem.getQuantity();
                    quantityMap.put(whCheckInItem.getSku(), mapQuantity + checkInQuantity);
                }
            }
        }
        return quantityMap;
    }

    @Override
    public List<CheckInQuantityInfoDTO> queryCheckInQuantityInfoByPurchaseNo(List<String> purchaseNoList) {

        List<CheckInQuantityInfoDTO> upQuantityList = whCheckInDao
                .queryCheckInUpQuantityByPurchaseOrderNo(purchaseNoList);

        List<CheckInQuantityInfoDTO> exceptionQuantityList = whCheckInDao
                .queryExceptionQuantityByPurchaseOrderNo(purchaseNoList);

        for (CheckInQuantityInfoDTO upInfo : upQuantityList) {
            for (CheckInQuantityInfoDTO exceptionInfo : exceptionQuantityList) {
                if (upInfo.getPurchaseOrderNo().equals(exceptionInfo.getPurchaseOrderNo())) {
                    upInfo.setCheckInExceptionQuantity(exceptionInfo.getCheckInExceptionQuantity());
                    upInfo.setQcExceptionQuantity(exceptionInfo.getQcExceptionQuantity());
                    exceptionQuantityList.remove(exceptionInfo);
                    break;
                }
            }
        }
        for (CheckInQuantityInfoDTO exceptionInfo : exceptionQuantityList) {
            upQuantityList.add(exceptionInfo);
        }

        return upQuantityList;
    }

    /**
     * 上架处理分摊运费数据
     *
     * @param whCheckIn
     */
    @Override
    public void handlePurchaseCostApportionData(WhCheckIn whCheckIn) {
        logger.info("================start handlePurchaseCostApportionData ===============");
        apportionCostExecutors.execute(new Runnable() {
            @Override
            public void run() {
                if (whCheckIn != null && whCheckIn.getWhCheckInItem() != null) {
                    List<WhCheckIn> list = new ArrayList<>();
                    if (CheckInType.MULTI_GOODS.intCode().equals(whCheckIn.getCheckInType()) && whCheckIn.getInId() != null) {
                        // 多货入库
                        WhCheckInExcessQueryCondition excessQuery = new WhCheckInExcessQueryCondition();
                        excessQuery.setInIdList(List.of(whCheckIn.getInId()));
                        List<WhCheckInExcess> excessList = whCheckInExcessService.queryWhCheckInExcesss(excessQuery, null);
                        if (CollectionUtils.isNotEmpty(excessList)) {
                            for (WhCheckInExcess excess : excessList) {
                                if(excess.getUpQuantity() == null || excess.getUpQuantity() == 0) {
                                    continue;
                                }
                                WhCheckInItem item = new WhCheckInItem();
                                item.setSku(excess.getSku());
                                item.setPurchaseQuantity(excess.getPurchaseQuantity());
                                item.setUpQuantity(excess.getUpQuantity());
                                WhCheckIn newCheckIn = new WhCheckIn();
                                newCheckIn.setInId(whCheckIn.getInId());
                                newCheckIn.setUpTime(whCheckIn.getUpTime());
                                newCheckIn.setPurchaseOrderNo(excess.getPurchaseOrderNo());
                                newCheckIn.setTotalWeight(Optional.ofNullable(excess.getWeight()).orElse(0d));
                                newCheckIn.setShippingCost(excess.getShippingCost() == null?0d:excess.getShippingCost().doubleValue());
                                newCheckIn.setWhCheckInItem(item);
                                list.add(newCheckIn);
                            }
                        }
                    } else {
                        // 非多货入库
                        Double totalCost = whCheckIn.getShippingCost() == null ? 0d : whCheckIn.getShippingCost();
                        Double totalWeight = whCheckIn.getTotalWeight() == null ? 0d : whCheckIn.getTotalWeight();
                        WhCheckInQueryCondition query = new WhCheckInQueryCondition();
                        query.setPurchaseOrderNo(whCheckIn.getPurchaseOrderNo());
                        List<WhCheckIn> whCheckInList = whCheckInDao.queryWhCheckInList(query, null);
                        whCheckInList.sort(Comparator.comparing(WhCheckIn::getInId));
                        if (CollectionUtils.isNotEmpty(whCheckInList)) {
                            totalCost = whCheckInList.get(0).getShippingCost() == null ? 0d
                                    : whCheckInList.get(0).getShippingCost();
                            totalWeight = whCheckInList.get(0).getTotalWeight() == null ? 0d
                                    : whCheckInList.get(0).getTotalWeight();
                        }
                        whCheckIn.setTotalWeight(totalWeight);
                        whCheckIn.setShippingCost(totalCost);
                        list.add(whCheckIn);
                    }

                    for (WhCheckIn checkIn : list) {
                        Double totalWeight = checkIn.getTotalWeight();
                        Double totalCost = checkIn.getShippingCost();
                        Map<String, Double> skuWeightMap = new HashMap<>();

                        WhPurchaseOrderQueryCondition purchaseQuery = new WhPurchaseOrderQueryCondition();
                        purchaseQuery.setPurchaseOrderNo(checkIn.getPurchaseOrderNo());
                        List<WhPurchaseOrder> purchaseOrders = whPurchaseOrderService.queryWhPurchaseOrderAndItems(purchaseQuery,
                                null);
                        if (CollectionUtils.isNotEmpty(purchaseOrders)
                                && CollectionUtils.isNotEmpty(purchaseOrders.get(0).getItems())) {
                            List<String> skuList = purchaseOrders.get(0).getItems().stream().map(p -> p.getSku())
                                    .collect(toList());
                            WhSkuQueryCondition querySku = new WhSkuQueryCondition();
                            querySku.setSkus(skuList);
                            List<WhSku> whSkuList = whSkuService.queryWhSkus(querySku, null);
                            skuWeightMap = Optional.ofNullable(whSkuList).orElse(new ArrayList<>()).stream()
                                    .collect(toMap(WhSku::getSku, WhSku::getWeight));
                            Map<String, Double> finalSkuMap = skuWeightMap;
                            totalWeight = purchaseOrders.get(0).getItems().stream().mapToDouble(
                                            i -> i.getQuantity() * Optional.ofNullable(finalSkuMap.get(i.getSku())).orElse(0d))
                                    .sum();
                        }

                        PurchaseCostApportionQueryCondition queryCondition = new PurchaseCostApportionQueryCondition();
                        queryCondition.setPurchaseOrderNo(checkIn.getPurchaseOrderNo());
                        PurchaseCostApportion costApportion = purchaseCostApportionService
                                .queryPurchaseCostApportion(queryCondition);
                        if (costApportion == null) {
                            costApportion = new PurchaseCostApportion();
                            costApportion.setPurchaseOrderNo(checkIn.getPurchaseOrderNo());
                            costApportion.setCost(totalCost);
                            costApportion.setCreateTime(new Timestamp(System.currentTimeMillis()));
                            costApportion.setFirstUpTime(checkIn.getUpTime());
                            costApportion.setLastUpTime(checkIn.getUpTime());
                            costApportion.setFinishStatus(PurchaseFinishStatus.NOT_FINISH.intCode());
                            purchaseCostApportionService.createPurchaseCostApportion(costApportion);
                        } else {
                            PurchaseCostApportion updateItem = new PurchaseCostApportion();
                            updateItem.setId(costApportion.getId());
                            updateItem.setLastUpTime(checkIn.getUpTime());
                            purchaseCostApportionService.updatePurchaseCostApportion(updateItem);
                        }

                        if (costApportion.getId() != null) {
                            WhCheckInItem item = checkIn.getWhCheckInItem();
                            PurchaseCostApportionItem costItem = new PurchaseCostApportionItem();
                            costItem.setPId(costApportion.getId());
                            costItem.setInId(checkIn.getInId());
                            costItem.setQuantity(item.getPurchaseQuantity());
                            costItem.setWeight(skuWeightMap.get(item.getSku()));
                            costItem.setUpNum(item.getUpQuantity());
                            costItem.setUpTime(checkIn.getUpTime());
                            costItem.setSku(item.getSku());
                            // 入库上架时计算入库单分摊运费=采购单总运费/采购单初始总净重*上架sku净重
                            Double apportionCost = 0d;
                            if (totalWeight != 0) {
                                apportionCost = BigDecimal.valueOf(totalCost).divide(BigDecimal.valueOf(totalWeight), 6, BigDecimal.ROUND_HALF_UP)
                                        .multiply(new BigDecimal(costItem.getUpNum())
                                                .multiply(BigDecimal.valueOf(costItem.getWeight())))
                                        .setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                            }
                            costItem.setApportionCost(apportionCost);
                            PurchaseCostApportionItemQueryCondition itemQuery = new PurchaseCostApportionItemQueryCondition();
                            itemQuery.setPId(costApportion.getId());
                            itemQuery.setSku(item.getSku());
                            itemQuery.setInId(checkIn.getInId());
                            List<PurchaseCostApportionItem> apportionItem = purchaseCostApportionItemService.queryPurchaseCostApportionItems(itemQuery, null);
                            if (CollectionUtils.isEmpty(apportionItem)) {
                                purchaseCostApportionItemService.createPurchaseCostApportionItem(costItem);
                            } else {
                                apportionItem.forEach(i -> {
                                    costItem.setId(i.getId());
                                    purchaseCostApportionItemService.updatePurchaseCostApportionItem(costItem);
                                });
                            }
                        }
                    }
                }
            }
        });
    }

    @Override
    public Map<String, Object> querySkuUpingAreaCount(WhCheckInQueryCondition query) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> list = whCheckInDao.querySkuUpingAreaCount();
        JSONArray legendData = new JSONArray();
        JSONArray seriesData = new JSONArray();
        JSONArray skuNumData = new JSONArray();
        for (Map<String, Object> map : list) {
            String name = map.get("locationRegion").toString();
            String skuNum = map.get("skuNum").toString();
            String pcs = map.get("pcs").toString();
            legendData.add(name);
            seriesData.add(pcs);
            skuNumData.add(skuNum);
        }
        resultMap.put("legendData", legendData);
        resultMap.put("seriesData", seriesData);
        resultMap.put("skuNumData", skuNumData);
        return resultMap;
    }


    @Override
    public Map<String, Object> querySkuWaitQcAreaCount(WhCheckInQueryCondition query) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> list = whCheckInDao.querySkuWaitQcAreaCount();
        JSONArray legendData = new JSONArray();
        JSONArray seriesData = new JSONArray();
        JSONArray skuNumData = new JSONArray();
        for (Map<String, Object> map : list) {
            String name = map.get("locationRegion").toString();
            String skuNum = map.get("skuNum").toString();
            String pcs = map.get("pcs").toString();
            legendData.add(name);
            seriesData.add(pcs);
            skuNumData.add(skuNum);
        }
        resultMap.put("legendData", legendData);
        resultMap.put("seriesData", seriesData);
        resultMap.put("skuNumData", skuNumData);
        return resultMap;
    }

    @Override
    @StockServicelock
    public ResponseJson createExcessCheckInAndCheckInException(List<String> list, WhCheckIn whCheckIn, String receiveBoxNo, String trackingNumbers, String arrayTrackingNo, Integer purchaseQuantity) {
        WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();
        String firstOrderType = whCheckInItem.getFirstOrderType();
        String originalpurchaseOrderNo = whCheckIn.getPurchaseOrderNo();
        Integer originalInId = whCheckIn.getOriginalInId();
        String inIdStr = whCheckIn.getOriginalInId() == null ? "" : String.valueOf(whCheckIn.getOriginalInId());
        String comment = "原采购单"+originalpurchaseOrderNo+",原入库单ID"+inIdStr+",";
        ResponseJson response = new ResponseJson();
        Integer totalQty=0;
        WhCheckInItem originalWhCheckInItem = null;
        WhCheckIn originalWhCheckIn = null;
        if (originalInId != null) {
            originalWhCheckIn = getWhCheckInDetail(originalInId);
            if (originalWhCheckIn != null && originalWhCheckIn.getWhCheckInItem() != null)
                originalWhCheckInItem = originalWhCheckIn.getWhCheckInItem();
            if (whCheckIn.getExpDate() == null || whCheckIn.getDays() == null || whCheckIn.getProDate() == null) {
                //获取原入库单保质期SKU集合
                WhUniqueSkuQueryCondition uniqueSkuQuery = new WhUniqueSkuQueryCondition();
                uniqueSkuQuery.setRelationId(originalInId);
                uniqueSkuQuery.setSku(whCheckInItem.getSku());
                uniqueSkuQuery.setType(SkuBusinessType.CHECK_IN.intCode());
                List<WhUniqueSku> whUniqueSkuList = whUniqueSkuService.queryWhUniqueSkus(uniqueSkuQuery, null);
                whCheckIn.setInId(originalInId);
                if (CollectionUtils.isNotEmpty(whUniqueSkuList)) {
                    // 查询唯一码保质期
                    whCheckIn.setInId(originalInId);
                    UniqueSkuUtils.getExistExpInfo(whUniqueSkuList, whCheckIn);
                    whCheckIn.setInId(null);
                }
            }
        }
      /*  WhPurchaseOrderQueryCondition pQuery = new WhPurchaseOrderQueryCondition();
        pQuery.setPurchaseOrderNoStr(whCheckIn.getWhCheckInExcessList().stream().map(w -> w.getPurchaseOrderNo()).collect(Collectors.joining(",")));
        List<WhPurchaseOrder> whPurchaseOrders = whPurchaseOrderService.queryWhPurchaseOrderAndItems(pQuery, null);
        if (CollectionUtils.isEmpty(whPurchaseOrders)) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("未查询到采购单信息");
            return response;
        }
        Map<String, WhPurchaseOrder> existPurchaseMap = whPurchaseOrders.stream().collect(toMap(p -> p.getPurchaseOrderNo(),
                p -> p, (k1, k2) -> k1));*/
        //List<String> unAfterSaleOrderTypes = Arrays.asList(PurchaseOrderType.NCGZS.getCode(),PurchaseOrderType.NCGCW.getCode(),PurchaseOrderType.NCGHH.getCode());
        for (WhCheckInExcess whCheckInExcess : whCheckIn.getWhCheckInExcessList()) {
            firstOrderType = whCheckInItem.getFirstOrderType();
            // 异常只执行一次
            WhCheckInException whCheckInException = whCheckIn.getWhCheckInException();
            if (whCheckInException != null ) {
                if (whCheckIn.getInId() != null) {
                    whCheckIn.setWhCheckInException(null);
                } else if (StringUtils.isNotBlank(originalpurchaseOrderNo)){
                    whCheckInException.setTrackingNumber(originalWhCheckIn.getTrackingNumber());
                    whCheckInException.setPurchaseOrderNo(originalpurchaseOrderNo);
                    whCheckInException.setExceptionType(ExceptionType.EXCESS_QUANTITY.getCode());
                    WhPurchaseOrderQueryCondition purchaseOrderQueryCondition = new WhPurchaseOrderQueryCondition();
                    purchaseOrderQueryCondition.setPurchaseOrderNo(originalpurchaseOrderNo);
                    List<WhPurchaseOrder> whPurchaseOrders = whPurchaseOrderService.queryWhPurchaseOrders(purchaseOrderQueryCondition, null);
                    if (CollectionUtils.isNotEmpty(whPurchaseOrders)) {
                        whCheckInException.setUserName(whPurchaseOrders.get(0).getPurchaseUser());
                    }
                    whCheckInException.setExcessExceptionFlag(true);
                }
            }
           /* WhPurchaseOrder whPurchaseOrder = existPurchaseMap.get(whCheckInExcess.getPurchaseOrderNo());
            if (whPurchaseOrder == null) {
                response.setStatus(StatusCode.FAIL);
                response.setMessage(whCheckInExcess.getPurchaseOrderNo()+"采购单未查询到采购单信息");
                return response;
            }
            WhPurchaseOrder init = whPurchaseOrder.init();
            if (init != null && CollectionUtils.isNotEmpty(init.getItems())) {
                for (WhPurchaseItem item : init.getItems()) {
                    if (StringUtils.equalsIgnoreCase(whCheckInExcess.getSku(), item.getSku())) {
                        if (StringUtils.isNotBlank(init.getPayMethod())
                                && StringUtils.contains(init.getPayMethod(), "售后")
                                && !unAfterSaleOrderTypes.contains(init.getPurchaseOrderType())) {
                            whCheckIn.setAfterSaleQty(item.getQuantity());
                            whCheckIn.setSupplierId(whCheckInExcess.getSupplierId());
                            whCheckIn.setVendorName(whCheckInExcess.getVendorName());
                            break;
                        }
                    }
                }
            } else {
                whCheckIn.setSupplierId(null);
                whCheckIn.setVendorName(null);
                whCheckIn.setAfterSaleQty(null);
            }*/
            if (originalWhCheckInItem != null && StringUtils.contains(originalWhCheckInItem.getFirstOrderType(),
                    CheckInFlags.SALED_BALANCE.getCode())) {
                whCheckIn.setAfterSaleQty(whCheckInExcess.getMatchedQuantity());
                whCheckIn.setSupplierId(whCheckInExcess.getSupplierId());
                whCheckIn.setVendorName(whCheckInExcess.getVendorName());
            }

            whCheckIn.setPurchaseOrderNo(whCheckInExcess.getPurchaseOrderNo());
            whCheckIn.getWhCheckInItem().setQuantity(whCheckInExcess.getMatchedQuantity());
            // 根据处理方式和售后状态设置标签
            firstOrderType = StringUtils.isEmpty(firstOrderType) ? whCheckInExcess.getProcessingMethod()
                    : firstOrderType + "," + whCheckInExcess.getProcessingMethod();
            whCheckInItem.setFirstOrderType(firstOrderType);
            whCheckIn.setComment(comment);
            if (StringUtils.isNotBlank(whCheckInExcess.getProcessingMethod()))
                whCheckIn.setMatchType(Integer.valueOf(whCheckInExcess.getProcessingMethod()));
            whCheckIn.setWmsRemark(comment);
            whCheckIn.setOriginalpurchaseOrderNo(originalpurchaseOrderNo);
            Integer userId = DataContextHolder.getUserId();
            if (userId != null)
                whCheckIn.setWmsOperator( TaglibUtils.getEmployeeNameByUserId(userId) );
            response = createCheckInAndCheckInException(Arrays.asList(whCheckInItem.getSku()),
                    whCheckIn, receiveBoxNo, trackingNumbers, arrayTrackingNo, purchaseQuantity);
            if (StatusCode.FAIL.equals(response.getStatus())) {
                return response;
            }
            whCheckInExcess.setInId(whCheckIn.getInId());
            totalQty+=whCheckInExcess.getMatchedQuantity();

        }
        if (originalInId != null) {
            // 服装尺寸
            WhCheckInClothingQueryCondition condition = new WhCheckInClothingQueryCondition();
            condition.setInId(originalInId);
            condition.setSku(whCheckInItem.getSku());
            List<WhCheckInClothing> whCheckInClothings = this.whCheckInClothingService.queryWhCheckInClothings(condition, null);
            if (CollectionUtils.isNotEmpty(whCheckInClothings)) {
                List<WhCheckInClothing> createWhCheckInClothings = whCheckInClothings.stream().map(w -> {
                    w.setId(null);
                    w.setInId(whCheckIn.getInId());
                    w.setCreationDate(null);
                    return w;
                }).collect(toList());
                whCheckInClothingService.batchCreateWhCheckInClothing(createWhCheckInClothings);
                whCheckInItem.setFirstOrderType(whCheckInItem.getFirstOrderType() + "," + CheckInFlags.CLOTHING_MEASURE.getCode());
            }
        }
        // 更新最终的入库数量与标识
        WhCheckIn updateCheckIn = new WhCheckIn();
        updateCheckIn.setInId(whCheckIn.getInId());
        updateCheckIn.setComment(comment);
        updateCheckIn.setCheckInType(CheckInType.MULTI_GOODS.intCode());
        updateWhCheckIn(updateCheckIn);
        WhCheckInItem updateItem = new WhCheckInItem();
        updateItem.setItemId(whCheckInItem.getItemId());
        updateItem.setQuantity(totalQty);
        updateItem.setFirstOrderType( Arrays.stream(whCheckInItem.getFirstOrderType().split(","))
                .distinct() // 去重
                .collect(Collectors.joining(",")));
        whCheckIn.setAfterSaleQty(totalQty);
        whCheckInItemService.updateWhCheckInItem(updateItem);
        whCheckInExcessService.batchCreateWhCheckInExcess(whCheckIn.getWhCheckInExcessList());
        return response;
    }

    @Override
    public ResponseJson doUpSkuToZfLocation(AndroidProductDo domain) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        WhCheckIn whCheckIn = this.getWhCheckInDetail(domain.getInId());
        if (whCheckIn == null || whCheckIn.getWhCheckInItem() == null) {
            rsp.setMessage("未找到入库单信息");
            return rsp;
        }
        WhCheckInItem whCheckInItem = whCheckIn.getWhCheckInItem();
        if (whCheckInItem.getSkuId() == null) {
            rsp.setMessage("入库单没有绑定的库存ID");
            return rsp;
        }
        if (whCheckInItem.getQcQuantity() == null || whCheckInItem.getQcQuantity() == 0) {
            rsp.setMessage("没有待上架的入库单明细");
            return rsp;
        }
        Integer pickQty = domain.getQuantity();

        domain.setQuantity(whCheckInItem.getQcQuantity());
        String result = doUpSkuToLocation(Collections.singletonList(whCheckInItem.getSku()), domain);
        if (StringUtils.isNotBlank(result)) {
            throw new RuntimeException("上架失败:" + result);
        }
        WhCheckIn updateCheckIn = new WhCheckIn();
        updateCheckIn.setInId(whCheckIn.getInId());
        updateCheckIn.setObtainUser(DataContextHolder.getUserId());
        updateCheckIn.setObtainTime(new Timestamp(System.currentTimeMillis()));
        whCheckInDao.updateWhCheckIn(updateCheckIn);

        WhCheckInItem updateItem = new WhCheckInItem();
        updateItem.setItemId(whCheckInItem.getItemId());
        updateItem.setPickQty(pickQty);
        whCheckInItemService.updateWhCheckInItem(updateItem);
       
        outStockMatchHandelService.allotStockApv(Collections.singletonList(whCheckInItem.getSku()), whCheckInItem);

        rsp.setStatus(StatusCode.SUCCESS);
        return rsp;
    }

    @Override
    public Map<String, Boolean> getExemptedSku(ExemptionQcConfiguration exemptionQcConfiguration, List<PurchaseOrderItem> items) {
        items=items.stream()
                .filter(item -> item.getFirstOrderType()==null || (item.getFirstOrderType()!=1 && item.getFirstOrderType()!=2)).collect(toList());
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        List<String> skuList = items.stream().map(PurchaseOrderItem::getSku).collect(toList());
        if (CollectionUtils.isEmpty(skuList)) {
            return null;
        }
        WhSkuSaleStatisticRecordQueryCondition recordQueryCondition=new WhSkuSaleStatisticRecordQueryCondition();
        recordQueryCondition.setSkuList(skuList);
        recordQueryCondition.setSaleAttributeSettingStr("新品");
        List<WhSkuSaleStatisticRecord> whSkuSaleStatisticRecords = whSkuSaleStatisticRecordService.queryWhSkuSaleStatisticRecords(recordQueryCondition, null);

        List<String> skus = Optional.ofNullable(whSkuSaleStatisticRecords).orElse(new ArrayList<>()).stream().map(WhSkuSaleStatisticRecord::getSku).collect(toList());

        if (CollectionUtils.isNotEmpty(skus)) {
            skuList.removeAll(skus);
        }

        if (CollectionUtils.isEmpty(skuList)) {
            return null;
        }

        if(Objects.isNull(exemptionQcConfiguration)){
            return null;
        }
         //sku近未出现异常天数
        Integer exceptionDay = exemptionQcConfiguration.getUnhappenExceptionDay();
        //供应商-SKU近未出现异常采购单数
        Integer skuAbnormalPurchaseNum = exemptionQcConfiguration.getSkuAbnormalPurchaseNum();
        // 采购单价
        Double purchasePrice = exemptionQcConfiguration.getPurchasePrice();
        //入库异常的类型
        String exceptionType = exemptionQcConfiguration.getExceptionType();

        if (exceptionDay==null || skuAbnormalPurchaseNum == null){
            return null;
        }
        //当前时间
        LocalDate today = LocalDate.now();
        //当前日期往前推exceptionDay天的日期
        LocalDate dateBefore = today.minusDays(exceptionDay);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.DEFAULT_FORMAT);
        String fromCreateDate = dateBefore.format(formatter);
        String toCreateDate = today.format(formatter);

       // 供应商-SKU近未出现异常天数,有无异常
//        WhCheckInExceptionQueryCondition exceptionQueryCondition = new WhCheckInExceptionQueryCondition();
//        exceptionQueryCondition.setFromCreateDate(fromCreateDate + " 00:00:00");
//        exceptionQueryCondition.setToCreateDate(toCreateDate + " 23:59:59");
//        if (StringUtils.isNotBlank(exceptionType)) {
//            exceptionQueryCondition.setExceptionTypes(Arrays.asList(exceptionType.split(",")));
//        }
//        exceptionQueryCondition.setSkuList(skuList);
//        List<WhCheckInException> whCheckInExceptions = whCheckInExceptionService.queryWhCheckInExceptions(exceptionQueryCondition, null);
        Map<String, Boolean> map = items.stream().collect(toMap(purchaseOrderItem -> purchaseOrderItem.getSku() + purchaseOrderItem.getVendorName(), item -> true));
//        if (CollectionUtils.isNotEmpty(whCheckInExceptions)) {
//            List<String> purchaseOrderNoList = whCheckInExceptions.stream().map(WhCheckInException::getPurchaseOrderNo).collect(toList());
//            List<WhPurchaseOrder> whPurchaseOrders = new ArrayList<>();
//            if (CollectionUtils.isNotEmpty(purchaseOrderNoList)) {
//                WhPurchaseOrderQueryCondition orderQueryCondition = new WhPurchaseOrderQueryCondition();
//                orderQueryCondition.setPurchaseOrderNoStr(StringUtils.join(purchaseOrderNoList, ","));
//                whPurchaseOrders = whPurchaseOrderService.queryWhPurchaseOrders(orderQueryCondition, null);
//            }
//            Map<String, WhPurchaseOrder> purchaseOrderMap = whPurchaseOrders.stream().collect(toMap(WhPurchaseOrder::getPurchaseOrderNo, WhPurchaseOrder::init));
//
//            for (WhCheckInException whCheckInException : whCheckInExceptions) {
//                String sku = whCheckInException.getSku();
//                String vendorName = null;
//                if (purchaseOrderMap.get(whCheckInException.getPurchaseOrderNo()) != null) {
//                    WhPurchaseOrder whPurchaseOrder = purchaseOrderMap.get(whCheckInException.getPurchaseOrderNo());
//                    vendorName = whPurchaseOrder.getVendorName();
//                }
//                // 使用 Iterator 遍历并根据条件移除元素
//                Iterator<Map.Entry<String, Boolean>> iterator = map.entrySet().iterator();
//                while (iterator.hasNext()) {
//                    Map.Entry<String, Boolean> entry = iterator.next();
//                    if (StringUtils.equals(entry.getKey(), sku + vendorName)) { // 根据条件，移除值为 异常的元素
//                        iterator.remove();
//                    }
//                }
//            }
//
//        }
        // 校验采购单价限制
        if (Objects.nonNull(purchasePrice)) {
            for (PurchaseOrderItem purchaseOrderItem : items) {
                String key = purchaseOrderItem.getSku() + purchaseOrderItem.getVendorName();
                Double price = purchaseOrderItem.getPrice();
                if (map.containsKey(key) && price > purchasePrice) {
                    map.put(key, false);
                }
            }
        }

        //供应商-SKU近未出现异常采购单数,有无异常
        WhCheckInQueryCondition query = new WhCheckInQueryCondition();
        query.setSkus(skuList);
        List<WhCheckIn> whCheckIns = queryWhCheckIns(query, null);
        if (CollectionUtils.isEmpty(whCheckIns)) {
            return null;
        }
        //找符合条件的入库单id
        Map<String, List<Integer>> idMap = whCheckIns.stream().filter(item -> StringUtils.isNotBlank(item.getVendorName()))
                .collect(Collectors.groupingBy(
                        item -> item.getWhCheckInItem().getSku() + item.getVendorName(),
                        Collectors.mapping(WhCheckIn::getInId, toList())
                ));
        if (MapUtils.isEmpty(idMap)) {
            return null;
        }
        // 使用 Iterator 遍历并根据条件移除元素
        Iterator<Map.Entry<String, Boolean>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Boolean> entry = iterator.next();
            List<Integer> ids = idMap.get(entry.getKey());
            if (CollectionUtils.isEmpty(ids)) {
                map.put(entry.getKey(), false);
                continue;
            }
            WhCheckInExceptionQueryCondition exceptionQueryCondition = new WhCheckInExceptionQueryCondition();
            exceptionQueryCondition.setFromCreateDate(fromCreateDate + " 00:00:00");
            exceptionQueryCondition.setToCreateDate(toCreateDate + " 23:59:59");
            if (StringUtils.isNotBlank(exceptionType)) {
                exceptionQueryCondition.setExceptionTypes(Arrays.asList(exceptionType.split(",")));
            }
            exceptionQueryCondition.setInIds(ids);
            List<WhCheckInException> whCheckInExceptions = whCheckInExceptionService.queryWhCheckInExceptions(exceptionQueryCondition, null);
            if (CollectionUtils.isNotEmpty(whCheckInExceptions)) { // 根据条件，移除值为 异常的元素
                iterator.remove();
            }
        }
        if (MapUtils.isEmpty(map)) {
            return null;
        }

        Map<String, Date> purchaseOrderDateMap = createPurchaseOrderDateMap(whCheckIns); // 创建采购单号到日期的映射
        // 分组并找到每个分组入库时间最新的配置的不同的采购单号
        Map<String, List<String>> result = whCheckIns.stream().filter(item -> StringUtils.isNotBlank(item.getVendorName()))
                .collect(Collectors.groupingBy(
                        item -> item.getWhCheckInItem().getSku() + item.getVendorName(),
                        Collectors.mapping(WhCheckIn::getPurchaseOrderNo, toList())
                ))
                .entrySet().stream()
                .collect(toMap(
                        Map.Entry::getKey,
                        entry -> {
                            List<String> nonNullOrders = entry.getValue().stream()
                                    .filter(order -> purchaseOrderDateMap.containsKey(order)) // 只取有日期的订单
                                    .sorted((a, b) -> compareByLatestDate(a, b, purchaseOrderDateMap)) // 排序
                                    .limit(skuAbnormalPurchaseNum) // 限制数量18 = {WhCheckIn@25081} "WhCheckIn{inId=586653, supplierId=************, checkInType=1, platformOrderNo='null', purchaseOrderNo='NCGPT23072765779', platformSerialNumber='null', purchaseDate=null, createDate=2023-07-27 15:34:57.0, skuSerialNum=50, createUser=10093, comment='null', warehouseId=1, status=3, confirmDate=null, updateUser=null, updateDate=null, trackingNumber='SZCSDPT23072765779', whCheckInItem=WhCheckInItem{itemId=586614, inId=586653, skuId=1008180, sku='2SS308934', quantity=50, purchasePrice=6.0, comment='null', location='D05-08-03-09', qcPass=null, upQuantity=null, qcQuantity=null, qcNum=null, purchaseQuantity=50, checkInSkuFlags='null', whSku=null}, whCheckInException=null, whPurchaseExpressRecord=WhPurchaseExpressRecord{id=6160, trackingNumber='null', purchaseOrderNo='null', serialNumber=null, createdBy=10093, creationDate=2023-07-27 15:34:46.0, splitUser=null, splitDate=null, lastUpdatedBy=null, lastUpdateDate=null, comment='null', reason='null', status=null, warehouseId=null, weight=null, tot"… View
                                    .collect(toList());

                            // 如果数量不足，则加入空日期的订单
                            if (nonNullOrders.size() < skuAbnormalPurchaseNum) {
                                List<String> nullOrders = entry.getValue().stream()
                                        .filter(order -> !purchaseOrderDateMap.containsKey(order)) // 取没有日期的订单
                                        .collect(toList());

                                // 补充空日期的订单
                                int remainingCount = skuAbnormalPurchaseNum - nonNullOrders.size();
                                nonNullOrders.addAll(nullOrders.stream().limit(remainingCount).collect(toList()));
                            }

                            return nonNullOrders;
                        }
                ))
                // 过滤掉小于 skuAbnormalPurchaseNum 的结果
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() >= skuAbnormalPurchaseNum)
                .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (MapUtils.isEmpty(result)) {
            return null;
        }
        for (String key : result.keySet()) {
            for (String itemKey : map.keySet()) {
                if (StringUtils.equals(key, itemKey)) {
                    WhCheckInExceptionQueryCondition exceptionQuery = new WhCheckInExceptionQueryCondition();
                    exceptionQuery.setPurchaseOrderNos(result.get(key));
                    List<WhCheckInException> exceptions = whCheckInExceptionService.queryWhCheckInExceptions(exceptionQuery, null);
                    if (CollectionUtils.isNotEmpty(exceptions)) {
                        map.put(key, false);
                    }
                }
            }
        }

      //  Map<String, Boolean> map = items.stream().collect(toMap(purchaseOrderItem -> purchaseOrderItem.getSku() + purchaseOrderItem.getVendorName(), item -> true));
//        List<String> purchaseOrderNoList = whCheckInExceptions.stream().map(WhCheckInException::getPurchaseOrderNo).collect(toList());
//        List<WhPurchaseOrder> whPurchaseOrders=new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(purchaseOrderNoList)) {
//            WhPurchaseOrderQueryCondition orderQueryCondition = new WhPurchaseOrderQueryCondition();
//            orderQueryCondition.setPurchaseOrderNoStr(StringUtils.join(purchaseOrderNoList,","));
//            whPurchaseOrders = whPurchaseOrderService.queryWhPurchaseOrders(orderQueryCondition, null);
//        }
//        Map<String, WhPurchaseOrder> purchaseOrderMap = whPurchaseOrders.stream().collect(toMap(WhPurchaseOrder::getPurchaseOrderNo, WhPurchaseOrder::init));
//
//        for (WhCheckInException whCheckInException : whCheckInExceptions) {
//            String sku = whCheckInException.getSku();
//            String vendorName = null;
//            if (purchaseOrderMap.get(whCheckInException.getPurchaseOrderNo()) != null) {
//                WhPurchaseOrder whPurchaseOrder = purchaseOrderMap.get(whCheckInException.getPurchaseOrderNo());
//                vendorName=whPurchaseOrder.getVendorName();
//            }
//            map.put(sku+vendorName,false);
//        }
        return map;
    }

    // 创建采购单号到创建日期的映射
    private static Map<String, Date> createPurchaseOrderDateMap(List<WhCheckIn> whCheckIns) {
        return whCheckIns.stream()
                .filter(item -> item.getCreateDate() != null) // 过滤掉空日期
                .collect(toMap(
                        WhCheckIn::getPurchaseOrderNo,   // 键：采购单号
                        WhCheckIn::getCreateDate,         // 值：创建日期
                        (date1, date2) -> date1.after(date2) ? date1 : date2 // 合并策略：保留最新日期
                ));
    }

    private static int compareByLatestDate(String a, String b, Map<String, Date> purchaseOrderDateMap) {
        Date dateA = purchaseOrderDateMap.get(a);
        Date dateB = purchaseOrderDateMap.get(b);
        // 处理 null 日期的情况
        if (dateA == null && dateB == null) {
            return 0; // 都为 null，认为相等
        } else if (dateA == null) {
            return 1; // 将 dateA 放在后面
        } else if (dateB == null) {
            return -1; // 将 dateB 放在后面
        }
        return dateB.compareTo(dateA); // 正常的降序排列
    }
}