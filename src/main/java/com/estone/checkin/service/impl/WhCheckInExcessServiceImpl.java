package com.estone.checkin.service.impl;

import com.estone.checkin.bean.WhCheckIn;
import com.estone.checkin.bean.WhCheckInExcess;
import com.estone.checkin.bean.WhCheckInExcessQueryCondition;
import com.estone.checkin.dao.WhCheckInExcessDao;
import com.estone.checkin.enums.CheckInWhType;
import com.estone.checkin.service.WhCheckInExcessService;
import com.estone.sku.enums.SkuBusinessType;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service("whCheckInExcessService")
@Slf4j
public class WhCheckInExcessServiceImpl implements WhCheckInExcessService {
    @Resource
    private WhCheckInExcessDao whCheckInExcessDao;

    @Override
    public WhCheckInExcess getWhCheckInExcess(Integer id) {
        WhCheckInExcess whCheckInExcess = whCheckInExcessDao.queryWhCheckInExcess(id);
        return whCheckInExcess;
    }

    @Override
    public WhCheckInExcess getWhCheckInExcessDetail(Integer id) {
        WhCheckInExcess whCheckInExcess = whCheckInExcessDao.queryWhCheckInExcess(id);
        // 关联查询
        return whCheckInExcess;
    }

    @Override
    public WhCheckInExcess queryWhCheckInExcess(WhCheckInExcessQueryCondition query) {
        Assert.notNull(query, "query is null!");
        WhCheckInExcess whCheckInExcess = whCheckInExcessDao.queryWhCheckInExcess(query);
        return whCheckInExcess;
    }

    @Override
    public List<WhCheckInExcess> queryAllWhCheckInExcesss() {
        return whCheckInExcessDao.queryWhCheckInExcessList();
    }

    @Override
    public List<WhCheckInExcess> queryWhCheckInExcesss(WhCheckInExcessQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = whCheckInExcessDao.queryWhCheckInExcessCount(query);
            pager.setTotalCount(count);
            if ( count == 0) {
                return new ArrayList<WhCheckInExcess>();
            }
        }
        List<WhCheckInExcess> whCheckInExcesss = whCheckInExcessDao.queryWhCheckInExcessList(query, pager);
        return whCheckInExcesss;
    }

    @Override
    public void createWhCheckInExcess(WhCheckInExcess whCheckInExcess) {
        try {
            whCheckInExcessDao.createWhCheckInExcess(whCheckInExcess);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateWhCheckInExcess(List<WhCheckInExcess> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whCheckInExcessDao.batchCreateWhCheckInExcess(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteWhCheckInExcess(Integer id) {
        try {
            whCheckInExcessDao.deleteWhCheckInExcess(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateWhCheckInExcess(WhCheckInExcess whCheckInExcess) {
        try {
            whCheckInExcessDao.updateWhCheckInExcess(whCheckInExcess);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateWhCheckInExcess(List<WhCheckInExcess> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                whCheckInExcessDao.batchUpdateWhCheckInExcess(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void mergeWhCheckInExcessList(List<WhCheckIn> whCheckIns) {
        List<Integer> inIdList = whCheckIns.stream().map(WhCheckIn::getInId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(inIdList)) {
            return;
        }
         WhCheckInExcessQueryCondition query = new WhCheckInExcessQueryCondition();
         query.setInIdList(inIdList);
         List<WhCheckInExcess> whCheckInExcessList = queryWhCheckInExcesss(query, null);
         if (CollectionUtils.isEmpty(whCheckInExcessList)) {
             return;
         }
        Map<Integer, List<WhCheckInExcess>> inIdMap = whCheckInExcessList.stream().collect(Collectors.groupingBy(WhCheckInExcess::getInId));

        for (WhCheckIn whCheckIn : whCheckIns) {
            List<WhCheckInExcess> whCheckInExcesses = inIdMap.get(whCheckIn.getInId());
            if (CollectionUtils.isEmpty(whCheckInExcesses)) {
                continue;
            }
            whCheckIn.setWhCheckInExcessList(whCheckInExcesses);
        }

    }

    @Override
    public void updateWhCheckInExcessList(WhCheckIn checkin, List<WhCheckInExcess> whCheckInExcessList, Integer status) {
        if (CollectionUtils.isEmpty(whCheckInExcessList) || checkin==null || status==null || checkin.getWhCheckInItem()==null)return;
        int qcQuantity = Optional.ofNullable(checkin.getWhCheckInItem().getQcQuantity()).orElse(0);
        int qcNum =Optional.ofNullable(checkin.getWhCheckInItem().getQcNum()).orElse(0);
        int upQuantity = Optional.ofNullable(checkin.getWhCheckInItem().getUpQuantity()).orElse(0);


        List<WhCheckInExcess> updateList = new ArrayList<>();
        for (WhCheckInExcess whCheckInExcess : whCheckInExcessList) {
            WhCheckInExcess update = new WhCheckInExcess();
            update.setId(whCheckInExcess.getId());
            if (SkuBusinessType.QC.intCode().equals(status)) {
                Integer eQcQuantity = Optional.ofNullable(whCheckInExcess.getMatchedQuantity()).orElse(0);
                if (qcQuantity >= eQcQuantity) {
                    whCheckInExcess.setQcQuantity(eQcQuantity);
                    update.setQcQuantity(eQcQuantity);
                    qcQuantity -= eQcQuantity;
                } else {
                    whCheckInExcess.setQcQuantity(qcQuantity);
                    update.setQcQuantity(qcQuantity);
                    qcQuantity = 0;
                }

                if (qcNum >= eQcQuantity) {
                    whCheckInExcess.setQcNum(eQcQuantity);
                    update.setQcNum(eQcQuantity);
                    qcNum -= eQcQuantity;
                } else {
                    whCheckInExcess.setQcNum(qcNum);
                    update.setQcNum(qcNum);
                    qcNum = 0;
                }
                updateList.add(update);
            }
            if (SkuBusinessType.UP_LOCATION.intCode().equals(status) && StringUtils.isNotBlank(whCheckInExcess.getPurchaseOrderNo())
               && (whCheckInExcess.getPurchaseOrderNo().startsWith("NCGZW") || whCheckInExcess.getPurchaseOrderNo().startsWith("NCGHW"))) {
                Integer eTransitionUpQuantity = Optional.ofNullable(whCheckInExcess.getQcQuantity()).orElse(0);
                if (upQuantity>=eTransitionUpQuantity){
                    whCheckInExcess.setUpQuantity(eTransitionUpQuantity);
                    update.setTransitionUpQuantity(eTransitionUpQuantity);
                    upQuantity-=eTransitionUpQuantity;
                }else{
                    whCheckInExcess.setUpQuantity(upQuantity);
                    update.setTransitionUpQuantity(upQuantity);
                    upQuantity=0;
                }
                updateList.add(update);
            }
            if (SkuBusinessType.UP_LOCATION.intCode().equals(status) && StringUtils.isNotBlank(whCheckInExcess.getPurchaseOrderNo())
                && !(whCheckInExcess.getPurchaseOrderNo().startsWith("NCGZW") || whCheckInExcess.getPurchaseOrderNo().startsWith("NCGHW"))) {
                Integer eUpQuantity = Optional.ofNullable(whCheckInExcess.getQcQuantity()).orElse(0);
                if (upQuantity>=eUpQuantity){
                    whCheckInExcess.setUpQuantity(eUpQuantity);
                    update.setUpQuantity(eUpQuantity);
                    upQuantity-=eUpQuantity;
                }else{
                    whCheckInExcess.setUpQuantity(upQuantity);
                    update.setUpQuantity(upQuantity);
                    upQuantity=0;
                }
                updateList.add(update);
            }

        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            batchUpdateWhCheckInExcess(updateList);
        }
    }


}