package com.estone.checkin.domain;

import com.estone.checkin.bean.*;
import com.estone.checkin.enums.ExceptionFrom;
import com.estone.checkin.enums.ExceptionHandleWay;
import com.estone.checkin.enums.ExceptionStatus;
import com.estone.checkin.enums.ExceptionType;
import com.estone.common.util.CacheUtils;
import com.estone.warehouse.bean.WhWarehouse;
import com.whq.tool.component.Pager;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class WhCheckInExceptionDo {
    private WhCheckInException whCheckInException;

    private WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();

    private List<WhCheckInException> whCheckInExceptions = new ArrayList<WhCheckInException>();

    private Map<String,List<WhCheckInException>> whCheckInExceptionMap = new HashMap<>();

    private Pager page = new Pager();

    private List<WhWarehouse> warehouseList = new ArrayList<>();// 仓库

    private ExceptionFrom[] exceptionFromList = new ExceptionFrom[] {};// 异常来源

    private ExceptionType[] exceptionTypeList = new ExceptionType[] {};// 异常类型

    private ExceptionStatus[]  exceptionStatuseList = new ExceptionStatus[] {};//异常状态

    private ExceptionHandleWay[] exceptionHandleWayList = new ExceptionHandleWay[] {};//异常处理方式

    private String handleWays;
    private String statuses;
    private String exceptionFroms;
    private String exceptionTypes;
    private String markReasons;

    private WhCheckInExceptionHandle whCheckInExceptionHandle;
    
    private List<WhCheckInExceptionHandle> whCheckInExceptionHandles = new ArrayList<WhCheckInExceptionHandle>();

    private List<PmsPurchaseUsers> purchaseUsers;

    private List<String> exceptionImages;

    private String errorMsg;

    // 服装尺寸列表
    private Map<String, CheckInClothingBO> clothingMap = new HashMap<>();
    // 特殊标签 2023
    private Integer specialType;

    /*
     * 废弃原因配置
     * */
    private String abandonReasonConfig;

    private String orderFlags;

    private String carryType;

    private Integer printUserId;

    public String getAbandonReasonConfig() {
        if (abandonReasonConfig == null) {
            abandonReasonConfig = CacheUtils.SystemParamGet("REDIS_PARAM.EXCEPTION_ORDER_ABANDON_REASON").getParamValue();
        }
        return abandonReasonConfig;
    }
}