package com.estone.checkin.action;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.Base64.Decoder;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.checkin.bean.*;
import com.estone.checkin.dao.WhCheckInExceptionDao;
import com.estone.checkin.domain.WhCheckInDo;
import com.estone.checkin.domain.WhCheckInExceptionDo;
import com.estone.checkin.enums.*;
import com.estone.checkin.service.*;
import com.estone.checkin.utils.GetUserNameOrEmployeeNameUtil;
import com.estone.checkin.utils.ExceptionMarkConfigUtil;
import com.estone.common.SelectJson;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.rule.common.HandleTypeModal;
import com.estone.sku.bean.WhSku;
import com.estone.sku.bean.WhSkuQueryCondition;
import com.estone.sku.bean.WhSkuSpecialGoods;
import com.estone.sku.bean.WhSkuSpecialGoodsQueryCondition;
import com.estone.sku.enums.PrintSkuQrCodeRedisLock;
import com.estone.sku.enums.SpecialTypeEnums;
import com.estone.sku.service.WhSkuService;
import com.estone.sku.service.WhSkuSpecialGoodsService;
import com.estone.system.downloadcenter.enums.WhDownloadContentEnum;
import com.estone.system.downloadcenter.service.WhDownloadCenterService;
import com.estone.system.rabbitmq.model.PushCheckInException;
import com.estone.system.rabbitmq.producer.RabbitmqProducerService;
import com.estone.system.user.bean.SaleUser;
import com.estone.system.user.bean.SaleUserQueryCondition;
import com.estone.system.user.service.SaleUserService;
import com.estone.warehouse.service.WhWarehouseService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.json.ResponseJson;

import lombok.Data;

@Controller
@RequestMapping(value = "checkInException")
public class WhCheckInExceptionController extends BaseController {
    private static Logger logger = LoggerFactory.getLogger(WhCheckInExceptionController.class);

    String[] headers = {"异常单ID", "新建异常", "SKU", "周转码", "库位", "采购单号","采购单类型", "新/建单采购单号" ,"快递单号", "异常数量", "沟通次数", "异常确认数量", "异常次数", "异常来源", "异常类型", "处理结果", "状态","标签", "收货周转框", "时效",
            "备注", "异常员", "创建员", "采购员", "入库员", "完成员", "废弃员", "添加时间", "异常处理时间", "采购处理时间", "仓库处理时间", "待入库时间", "入库中时间", "是否首单", "入库单ID","异常绑定周转框时间","异常完成时间","待入库操作人","完成人","草稿提交人", "退货原因", "退货地址", "收件人", "收件人联系方式",
            "退货金额", "退货条件", "退货人", "物流公司", "追踪号","标记原因","标记人","标记时间"};

    /**
     * 新入库异常单-需忽略的处理方式
     */
    private static final List<Integer> ignoreHandleWays = Arrays.asList(ExceptionHandleWay.CHECK_IN.intCode(),
            ExceptionHandleWay.UPDATE_IMAGE.intCode(), ExceptionHandleWay.UPDATE_DESCRIPTION.intCode(), ExceptionHandleWay.UPDATE_IMG_SIZE.intCode(), ExceptionHandleWay.CHANG_SIZE.intCode(),
            ExceptionHandleWay.RETURN.intCode(), ExceptionHandleWay.CREATE_NEW_ORDER_TO_CHECK_IN.intCode(), ExceptionHandleWay.REISSUE.intCode());
    @Resource
    private WhCheckInExceptionService whCheckInExceptionService;
    @Resource
    private WhWarehouseService whWarehouseService;
    @Resource
    private WhCheckInExceptionHandleService whCheckInExceptionHandleService;
    @Resource
    private RabbitmqProducerService rabbitmqProducerService;

    @Resource
    private HistoryCheckInExceptionCountService historyCheckInExceptionCountService;

    @Resource
    private ReturnPurchaseOrderService returnPurchaseOrderService;

    @Resource
    private HandleExceptionDateService handleExceptionDateService;


    @Resource
    private WhSkuSpecialGoodsService whSkuSpecialGoodsService;

    @Resource
    private WhCheckInClothingService whCheckInClothingService;

    @Resource
    private WhDownloadCenterService whDownloadCenterService;

    @Resource
    private SaleUserService saleUserService;

    @Resource
    private WhSkuService whSkuService;

    @RequestMapping(method = {RequestMethod.GET})
    public String init(@ModelAttribute("domain") WhCheckInExceptionDo domain) {
        initFormData(domain);
        queryWhCheckInExceptions(domain);
        return "checkin/checkInExceptionList";
    }

    private void initFormData(@ModelAttribute("domain") WhCheckInExceptionDo domain) {
        domain.setPurchaseUsers(GetUserNameOrEmployeeNameUtil.getPurchaseUser());// 采购员
        domain.setWarehouseList(whWarehouseService.queryAllWhWarehouses());// 仓库
        // domain.setExceptionFromList(ExceptionFrom.values());// 来源
        // domain.setExceptionStatuseList(ExceptionStatus.values());// 状态
        // domain.setExceptionTypeList(ExceptionType.values());// 类型
        // domain.setExceptionHandleWayList(ExceptionHandleWay.values());// 处理方式
        ExceptionStatus[] exceptionStatus = ExceptionStatus.values();
        if (exceptionStatus.length > 0) {
            for (int i = 0; i < exceptionStatus.length; i++) {
                if (ExceptionStatus.WAIT_FINANCE_CHECK.intCode().equals(exceptionStatus[i].intCode())
                        || ExceptionStatus.DELETED.intCode().equals(exceptionStatus[i].intCode())) {
                    exceptionStatus[i] = null;
                }
            }
        }
        domain.setExceptionFroms(SelectJson.getList(ExceptionFrom.values()));// 来源
        domain.setStatuses(SelectJson.getList(exceptionStatus));// 状态
        // 入库异常类型屏蔽“待补发配件”
        /*ExceptionType[] exceptionTypes = ExceptionType.values();
        if (exceptionTypes.length > 0) {
            for (int i = 0; i < exceptionTypes.length; i++) {
                if (exceptionTypes[i].intCode() == 27) {
                    exceptionTypes[i] = null;
                }
            }
        }*/
        domain.setExceptionTypes(ExceptionType.getSelectJson(null, List.of("27")));// 类型
        domain.setHandleWays(SelectJson.getList(ExceptionHandleWay.values()));// 处理方式
        
        // 设置标记原因数据
        domain.setMarkReasons(getMarkReasonsJson());

        PurchaseOrderFlags[] purchaseOrderFlags = {PurchaseOrderFlags.TJ,PurchaseOrderFlags.ORDINARY,PurchaseOrderFlags.NO_LABEL};
        domain.setOrderFlags(SelectJson.getList(purchaseOrderFlags));
    }

    /**
     * 获取标记原因的JSON数据，格式化为前端可用的SelectJson格式
     * 合并用户自定义配置中的原因和数据库中实际使用的原因，确保完整性
     * @return 标记原因的JSON字符串
     */
    private String getMarkReasonsJson() {

        Set<String> allMarkReasons = new LinkedHashSet<>(); // 使用LinkedHashSet保持顺序且去重

        // 1. 从用户自定义配置中获取标记原因（不再有预设的初始化原因）
        try {
            ExceptionMarkConfig config = ExceptionMarkConfigUtil.getExceptionMarkConfig();
            List<String> configReasons = config.getMarkReasonList();
            if (CollectionUtils.isNotEmpty(configReasons)) {
                allMarkReasons.addAll(configReasons);
            }
        }
        catch (Exception e) {
            logger.warn("获取用户自定义标记原因失败", e);
        }

        // 2. 从数据库中获取已使用的标记原因
        try {
            List<String> dbReasons = whCheckInExceptionService.queryDistinctMarkReasons();
            if (CollectionUtils.isNotEmpty(dbReasons)) {
                allMarkReasons.addAll(dbReasons);
            }
        }
        catch (Exception e) {
            logger.warn("获取数据库中的标记原因失败", e);
        }

        // 3. 转换为SelectJson格式
        List<SelectJson> selectJsonList = new ArrayList<>();
        allMarkReasons.forEach(reason -> {
            selectJsonList.add(new SelectJson(reason, reason));
        });

        return JSON.toJSONString(selectJsonList);

    }

    private void queryWhCheckInExceptions(@ModelAttribute("domain") WhCheckInExceptionDo domain) {
        WhCheckInExceptionQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new WhCheckInExceptionQueryCondition();
            domain.setQuery(query);
        }
        // 兼容含有“=”的sku
        query.setSku(CompatibleSkuUtils.getSku(query.getSku()));
        List<WhCheckInException> whCheckInExceptions = whCheckInExceptionService.queryWhCheckInExceptions(query, page);
        domain.setWhCheckInExceptions(whCheckInExceptions);
    }

    private void queryNewWhCheckInExceptions(@ModelAttribute("domain") WhCheckInExceptionDo domain) {
        WhCheckInExceptionQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new WhCheckInExceptionQueryCondition();
            domain.setQuery(query);
        }
        // 兼容含有“=”的sku
        query.setSku(CompatibleSkuUtils.getSku(query.getSku()));
        if (CollectionUtils.isNotEmpty(query.getStatusList())
                && query.getStatusList().contains(ExceptionStatus.QC_PENDING.intCode()))
            query.setQueryLocationStock(true);
        List<WhCheckInException> whCheckInExceptions = whCheckInExceptionService.queryNewWhCheckInExceptions(query, page);
        domain.setWhCheckInExceptions(whCheckInExceptions);
        //Map<String,List<WhCheckInException>> whCheckInExceptionMap = whCheckInExceptions.stream().collect(Collectors.groupingBy(we -> we.getBatchNo()));
//        Map<String, List<WhCheckInException>> whCheckInExceptionMap = whCheckInExceptions.stream().collect(Collectors.groupingBy(WhCheckInException::getBatchNo,
//                LinkedHashMap::new, Collectors.toList()));
//        domain.setWhCheckInExceptionMap(whCheckInExceptionMap);
    }

    @RequestMapping(value = "search", method = {RequestMethod.POST})
    public String search(@ModelAttribute("domain") WhCheckInExceptionDo domain) {
        initFormData(domain);
        queryWhCheckInExceptions(domain);
        return "checkin/checkInExceptionList";
    }

    @RequestMapping(value = "newExce/search", method = {RequestMethod.GET})
    public String newSearchFromGet(@ModelAttribute("domain") WhCheckInExceptionDo domain) {
        initFormData(domain);
//        queryNewWhCheckInExceptions(domain);
        return "checkin/checkInExceptionNewList";
    }

    @RequestMapping(value = "newExce/search", method = {RequestMethod.POST})
    public String newExcesearch(@ModelAttribute("domain") WhCheckInExceptionDo domain) {
        initFormData(domain);
        queryNewWhCheckInExceptions(domain);
        return "checkin/checkInExceptionNewList";
    }

    @RequestMapping(value = "search", method = {RequestMethod.GET})
    public String searchFromGet(@ModelAttribute("domain") WhCheckInExceptionDo domain) {
        return "redirect:/checkInException";
    }

    @RequestMapping(value = "getRecentThreeExceptionsByExceptionType", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson getRecentThreeExceptionsByExceptionType(@ModelAttribute("domain") WhCheckInDo domain,
                                                                @RequestParam("exceptionType") String exceptionType, @RequestParam("sku") String sku) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if (StringUtils.isBlank(exceptionType) || StringUtils.isBlank(sku)) {
            responseJson.setMessage("没有相关异常单！");
            return responseJson;
        }
        WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();
        query.setSku(sku);
        /*
         * query.setTrackingNumber(trackingNumber);
         * query.setPurchaseOrderNo(purchaseOrderNo);
         */
        query.setExceptionType(exceptionType);
        responseJson.setStatus(StatusCode.SUCCESS);
        Map<String, Object> responseMap = new HashMap<String, Object>();
        responseMap.put("recentThreeExceptions",
                whCheckInExceptionService.queryRecentThreeExceptionsByExceptionType(query));
        responseJson.setBody(responseMap);
        return responseJson;
    }

    @RequestMapping(value = "getExceptionImages", method = {RequestMethod.GET})
    public String getExceptionImages(@ModelAttribute("domain") WhCheckInExceptionDo domain,
                                     @RequestParam("exceptionImages") List<String> exceptionImages) {
        domain.setExceptionImages(exceptionImages);
        return "sku/skuImgs";
    }

    /**
     * 标记完成
     *
     * @param domain
     * @param ids
     * @return
     */
    @RequestMapping(value = "batchUpdateExceptionToFinished", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson batchUpdateExceptionToFinished(@ModelAttribute("domain") WhCheckInExceptionDo domain,
                                                       @RequestParam("ids") List<Integer> ids, @RequestParam(required = false, name = "isNew") Boolean isNew) {
        ResponseJson response = new ResponseJson();
        List<Integer> successIdList = whCheckInExceptionService.batchUpdateExceptionToFinished(ids, null);
        if (CollectionUtils.isNotEmpty(successIdList)) {
            List<WhCheckInException> whCheckInExceptionList = new ArrayList<>();
            List<WhCheckInExceptionHandle> whCheckInExceptionHandleList = new ArrayList<>();
            PushCheckInException pushCheckInException = new PushCheckInException();
            for (Integer id : successIdList) {
                WhCheckInException dbWhCheckException = whCheckInExceptionService.getWhCheckInException(id);
                // 标记完成时，来源是QC全检的不发消息到采购系统
                if (dbWhCheckException != null
                        && !ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode().equals(dbWhCheckException.getExceptionFrom())) {
                    WhCheckInExceptionHandleQueryCondition query = new WhCheckInExceptionHandleQueryCondition();
                    query.setExceptionId(id);
                    int count = whCheckInExceptionHandleService.exceptionCount(query);
                    WhCheckInExceptionHandle whCheckInExceptionHandle = whCheckInExceptionHandleService
                            .queryWhCheckInExceptionHandles(query, null).get(count - 1);
                    whCheckInExceptionList.add(dbWhCheckException);
                    whCheckInExceptionHandleList.add(whCheckInExceptionHandle);
                }
            }

            if (CollectionUtils.isNotEmpty(whCheckInExceptionList)) {
                pushCheckInException.setWhCheckInExceptionHandleList(whCheckInExceptionHandleList);
                pushCheckInException.setWhCheckInExceptionList(whCheckInExceptionList);
                // 推送打印带货清单更新状态为待采购处理消息到采购系统
                logger.info("start to send updatePrintCarryItems message to pms ====pushSize:"
                        + pushCheckInException.getWhCheckInExceptionList().size());
                rabbitmqProducerService.pushCheckInExceptionMsgToPms(null, null, pushCheckInException);
                logger.info("send updatePrintCarryItems message to pms end ");
            }
            //移除成功的，其余都是不能标记完成的单
            ids.removeIf(id -> successIdList.contains(id));
        }

        if (CollectionUtils.isNotEmpty(ids))
            response.setMessage("编号【" + JSONObject.toJSONString(ids) + "】状态不是待仓库处理、待入库或入库中，不能标记完成！");
        return response;
    }


    /**
     * 批量废弃
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = "batchScrapException", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson batchScrapException(@RequestParam("ids") List<Integer> ids,
                                            @RequestParam(value = "remark", required = false) String remark) {
        ResponseJson response = new ResponseJson();
        List<Integer> cantScrapIds = whCheckInExceptionService.batchUpdateToDiscarded(ids, remark, "批量废弃");

        if (CollectionUtils.isNotEmpty(cantScrapIds))
            response.setMessage("编号【" + JSONObject.toJSONString(cantScrapIds) + "】状态是已完成、待入库、入库中、已废弃、已退货之一，不能废弃！");
        return response;
    }

    /**
     * 标记完成-只能完成不能自动完结的异常单
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = "batchUpdateExceptionToFinished2", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson batchUpdateExceptionToFinished2(@RequestParam("ids") List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        List<Integer> successIdList = whCheckInExceptionService.batchUpdateExceptionToFinished(ids, ignoreHandleWays);
        if (CollectionUtils.isNotEmpty(successIdList)) {
            List<WhCheckInException> whCheckInExceptionList = new ArrayList<>();
            List<WhCheckInExceptionHandle> whCheckInExceptionHandleList = new ArrayList<>();
            PushCheckInException pushCheckInException = new PushCheckInException();
            for (Integer id : successIdList) {
                WhCheckInException dbWhCheckException = whCheckInExceptionService.getWhCheckInException(id);
                // 标记完成时，来源是QC全检的不发消息到采购系统
                if (dbWhCheckException != null
                        && !ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode().equals(dbWhCheckException.getExceptionFrom())) {
                    WhCheckInExceptionHandleQueryCondition query = new WhCheckInExceptionHandleQueryCondition();
                    query.setExceptionId(id);
                    int count = whCheckInExceptionHandleService.exceptionCount(query);
                    WhCheckInExceptionHandle whCheckInExceptionHandle = whCheckInExceptionHandleService
                            .queryWhCheckInExceptionHandles(query, null).get(count - 1);
                    whCheckInExceptionList.add(dbWhCheckException);
                    whCheckInExceptionHandleList.add(whCheckInExceptionHandle);
                }
            }
            if (CollectionUtils.isNotEmpty(whCheckInExceptionList)) {
                pushCheckInException.setWhCheckInExceptionHandleList(whCheckInExceptionHandleList);
                pushCheckInException.setWhCheckInExceptionList(whCheckInExceptionList);
                // 推送打印带货清单更新状态为待采购处理消息到采购系统
                logger.info("start to send updatePrintCarryItems message to pms ====pushSize:"
                        + pushCheckInException.getWhCheckInExceptionList().size());
                rabbitmqProducerService.pushCheckInExceptionMsgToPms(null, null, pushCheckInException);
                logger.info("send updatePrintCarryItems message to pms end ");
            }
            //移除成功的，其余都是不能标记完成的单
            ids.removeIf(id -> successIdList.contains(id));
        }
        if (CollectionUtils.isNotEmpty(ids))
            response.setMessage("编号【" + JSONObject.toJSONString(ids) + "】状态不是(待仓库处理、待入库或入库中)或处理方式不匹配，不能标记完成！");
        return response;
    }

    /**
     * @param domain
     * @param ids
     * @return
     * @Title: printCarryItems
     * @Description: 打印带货清单
     */
    @RequestMapping(value = "printCarryItems", method = {RequestMethod.GET})
    public String printCarryItems(@ModelAttribute("domain") WhCheckInExceptionDo domain,
                                  @RequestParam("ids") List<Integer> ids) {
        WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();
        query.setIds(ids);
        // query.setStatus(ExceptionStatus.WAREHOUSE_PENDING.intCode());
        query.setHandleWay(ExceptionHandleWay.CARRY_PRODUCT.intCode());
        List<WhCheckInException> whCheckInExceptions = whCheckInExceptionService.queryWhCheckInExceptions(query, null);
        domain.setWhCheckInExceptions(whCheckInExceptions);
        domain.setQuery(query);
        return "checkin/printCarryItems";
    }

    /**
     * @param domain
     * @param ids
     * @return
     * @Title: updatePrintCarryItems
     * @Description: 打印带货清单更新状态为待采购处理
     */
    @RequestMapping(value = "updatePrintCarryItems", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson updatePrintCarryItems(@ModelAttribute("domain") WhCheckInExceptionDo domain,
                                              @RequestParam("ids") List<Integer> ids) {
        logger.info("批量修改打印带货清单更新状态为待采购处理：ids: " + ids);
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        List<WhCheckInException> whCheckInExceptionList = new ArrayList<>();
        List<WhCheckInExceptionHandle> whCheckInExceptionHandleList = new ArrayList<>();
        PushCheckInException pushCheckInException = new PushCheckInException();
        for (Integer id : ids) {
            WhCheckInException exception = whCheckInExceptionService.getWhCheckInException(id);
            // 处理日志,只处理来源不是QC全检的数据
            if (ExceptionStatus.getWarehousePendingCode().contains(exception.getStatus())
                    && !ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode().equals(exception.getExceptionFrom())) {
                Integer status = ExceptionStatus.PURCHASE_PENDING.intCode();
                if (exception.isPurchaseUserVocationContainDev()){
                    status = ExceptionStatus.DEVELOP_CONFIRM.intCode();
                }
                WhCheckInException update = new WhCheckInException();
                update.setId(id);
                update.setStatus(status);// 待采购处理
                whCheckInExceptionService.updateWhCheckInException(update);
                // 记录关联表完成时间
                handleExceptionDateService.doSaveExceptionDate(update, exception.getStatus());

                WhCheckInExceptionHandle whCheckInExceptionHandle = new WhCheckInExceptionHandle();
                whCheckInExceptionHandle.setCreatedBy(DataContextHolder.getUserId());
                whCheckInExceptionHandle.setExceptionId(id);
                whCheckInExceptionHandle.setQuantity(exception.getCarryQuantity());
                whCheckInExceptionHandle.setStatus(update.getStatus());
                whCheckInExceptionHandleService.createWhCheckInExceptionHandle(whCheckInExceptionHandle);
                whCheckInExceptionHandle = whCheckInExceptionHandleService
                        .getWhCheckInExceptionHandle(whCheckInExceptionHandle.getId());
                whCheckInExceptionList.add(update);
                whCheckInExceptionHandleList.add(whCheckInExceptionHandle);
            }
        }
        if (CollectionUtils.isNotEmpty(whCheckInExceptionList)) {
            pushCheckInException.setWhCheckInExceptionHandleList(whCheckInExceptionHandleList);
            pushCheckInException.setWhCheckInExceptionList(whCheckInExceptionList);
            // 推送打印带货清单更新状态为待采购处理消息到采购系统
            logger.info("start to send updatePrintCarryItems message to pms ====pushSize:"
                    + pushCheckInException.getWhCheckInExceptionList().size());
            rabbitmqProducerService.pushCheckInExceptionMsgToPms(null, null, pushCheckInException);
            logger.info("send updatePrintCarryItems message to pms end ");
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * @param domain
     * @param ids
     * @return
     * @Title: bathPrintCarryExceptions
     * @Description: 批量打印带货标签
     */
    @RequestMapping(value = "bathPrintCarryExceptions", method = {RequestMethod.GET})
    public String bathPrintCarryExceptions(@ModelAttribute("domain") WhCheckInExceptionDo domain,
                                           @RequestParam("ids") List<Integer> ids,
                                           @RequestParam(value = "carryType", required = false) String carryType) {
        WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();
        query.setIds(ids);
        query.setStatusList(ExceptionStatus.getWarehousePendingCode());
        query.setHandleWay(ExceptionHandleWay.CARRY_PRODUCT.intCode());
        List<WhCheckInException> whCheckInExceptions = whCheckInExceptionService.queryWhCheckInExceptions(query, null);
        domain.setWhCheckInExceptions(whCheckInExceptions);
        domain.setCarryType(carryType);
        domain.setPrintUserId(DataContextHolder.getUserId());
        return "checkin/bathPrintCarryExceptions";
    }

    /**
     * 编辑
     *
     * @param domain
     * @param whCheckInExceptionId
     * @return
     */
    @RequestMapping(value = "edit")
    public String toUpdateWhCheckInException(@ModelAttribute("domain") WhCheckInExceptionDo domain,
                                             @RequestParam("id") Integer whCheckInExceptionId) {
        // domain.setExceptionTypeList(ExceptionType.values());// 类型
        // 入库异常类型屏蔽"其他"
        /*ExceptionType[] exceptionTypes = ExceptionType.values();
        if (exceptionTypes.length > 0) {
            for (int i = 0; i < exceptionTypes.length; i++) {
                if (exceptionTypes[i].intCode() == 25) {
                    exceptionTypes[i] = null;
                }
            }
        }*/
        domain.setExceptionTypes(ExceptionType.getSelectJson(null, List.of("25")));
        if (whCheckInExceptionId == null) {
            domain.setWarehouseList(whWarehouseService.queryAllWhWarehouses());// 仓库
            domain.setPurchaseUsers(GetUserNameOrEmployeeNameUtil.getPurchaseUser());
            return "checkin/checkInExceptionAdd";
        } else {
            WhCheckInException whCheckInException = whCheckInExceptionService
                    .getWhCheckInException(whCheckInExceptionId);
            whCheckInException.setPurchaseUserName(whCheckInException.getPurchaseUserName());
            domain.setExceptionHandleWayList(ExceptionHandleWay.values());// 处理方式
            domain.setWhCheckInException(whCheckInException);
            // 查询日志
            WhCheckInExceptionHandleQueryCondition queryCondition = new WhCheckInExceptionHandleQueryCondition();
            queryCondition.setExceptionId(whCheckInExceptionId);
            List<WhCheckInExceptionHandle> whCheckInExceptionHandle = whCheckInExceptionHandleService
                    .queryWhCheckInExceptionHandles(queryCondition, null);
            domain.setWhCheckInExceptionHandles(whCheckInExceptionHandle);

            if (StringUtils.isNotBlank(whCheckInException.getExceptionType()) && StringUtils.isNotBlank(whCheckInException.getSku())) {
                String sku = whCheckInException.getSku().split("-")[0]; // 取主SKU
                domain.setWhCheckInExceptions(whCheckInExceptionService
                        .queryRecentFinishedExceptionsByExceptionType(whCheckInException.getExceptionType(), sku));
            }
        }
        return "checkin/checkInExceptionEdit";
    }

    /**
     * 编辑
     *
     * @param domain
     * @param whCheckInExceptionId
     * @return
     */
    @RequestMapping(value = "newEdit")
    public String toNewUpdateWhCheckInException(@ModelAttribute("domain") WhCheckInExceptionDo domain,
                                                @RequestParam("id") Integer whCheckInExceptionId) {
        // domain.setExceptionTypeList(ExceptionType.values());// 类型
        // 入库异常类型屏蔽"其他"和“待补发配件”
        /*ExceptionType[] exceptionTypes = ExceptionType.values();
        if (exceptionTypes.length > 0) {
            for (int i = 0; i < exceptionTypes.length; i++) {
                if (exceptionTypes[i].intCode() == 25
                        || exceptionTypes[i].intCode() == 27) {
                    exceptionTypes[i] = null;
                }
            }
        }*/
        if (whCheckInExceptionId == null) {
            domain.setExceptionTypes(ExceptionType.getSelectJson(true, List.of("25","27")));
            domain.setWarehouseList(Collections.singletonList(CacheUtils.getLocalWarehouse()));// 仓库
            domain.setPurchaseUsers(GetUserNameOrEmployeeNameUtil.getPurchaseUser());
            return "checkin/checkInExceptionNewAdd";
        } else {
            domain.setExceptionTypes(ExceptionType.getSelectJson(null, List.of("25","27")));
            WhCheckInException whCheckInException = whCheckInExceptionService
                    .getWhCheckInException(whCheckInExceptionId);
            whCheckInException.setPurchaseUserName(whCheckInException.getPurchaseUserName());
            domain.setExceptionHandleWayList(ExceptionHandleWay.values());// 处理方式
            domain.setWhCheckInException(whCheckInException);
            // 查询日志
            WhCheckInExceptionHandleQueryCondition queryCondition = new WhCheckInExceptionHandleQueryCondition();
            queryCondition.setExceptionId(whCheckInExceptionId);
            List<WhCheckInExceptionHandle> whCheckInExceptionHandle = whCheckInExceptionHandleService
                    .queryWhCheckInExceptionHandles(queryCondition, null);
            domain.setWhCheckInExceptionHandles(whCheckInExceptionHandle);

            if (StringUtils.isNotBlank(whCheckInException.getExceptionType()) && StringUtils.isNotBlank(whCheckInException.getSku())) {
                String sku = whCheckInException.getSku().split("-")[0]; // 取主SKU
                domain.setWhCheckInExceptions(whCheckInExceptionService
                        .queryRecentFinishedExceptionsByExceptionType(whCheckInException.getExceptionType(), sku));
            }
            // 查询是否有FZ标签
            WhSkuSpecialGoodsQueryCondition specCondition = new WhSkuSpecialGoodsQueryCondition();
            specCondition.setSpecialSku(whCheckInException.getSku());
            specCondition.setSpecialType(SpecialTypeEnums.FZ.intCode());
            List<WhSkuSpecialGoods> whSkuSpecialGoods = whSkuSpecialGoodsService.queryWhSkuSpecialGoodss(specCondition, null);
            if (CollectionUtils.isNotEmpty(whSkuSpecialGoods)) {
                domain.setSpecialType(whSkuSpecialGoods.get(0).getSpecialType());
                // 服装尺寸信息
                WhCheckInClothingQueryCondition clothingQuery = new WhCheckInClothingQueryCondition();
                clothingQuery.setExcId(whCheckInException.getId());
                List<WhCheckInClothing> whCheckInClothings = whCheckInClothingService.queryWhCheckInClothings(clothingQuery, null);
                if (CollectionUtils.isEmpty(whCheckInClothings) && whCheckInException.getInId() != null) {
                    clothingQuery.setInId(whCheckInException.getInId());
                    clothingQuery.setExcId(null);
                    whCheckInClothings = this.whCheckInClothingService.queryWhCheckInClothings(clothingQuery, null);
                }
                if (CollectionUtils.isNotEmpty(whCheckInClothings)) {
                    Map<String, CheckInClothingBO> clothingMap = new HashMap<>();
                    for (WhCheckInClothing wc : whCheckInClothings) {
                        CheckInClothingBO checkInClothingBO = JSON.parseObject(wc.getDataJson(), CheckInClothingBO.class);
                        String source = Integer.valueOf(1).equals(wc.getSource()) ? "product" : Integer.valueOf(2).equals(wc.getSource()) ? "checkIn" : "qc";
                        clothingMap.put(source, checkInClothingBO);
                    }
                    domain.setClothingMap(clothingMap);
                }
            }
        }
        return "checkin/checkInExceptionNewEdit";
    }

    /**
     * 保存异常编辑
     *
     * @param domain
     * @return
     */
    @RequestMapping(value = "save", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson saveWhCheckException(@ModelAttribute("domain") WhCheckInExceptionDo domain,
                                             @RequestParam(value = "type", required = false) String type,
                                             @RequestParam(value = "exceptionBoxNo", required = false) String exceptionBoxNo) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        WhCheckInException whCheckInException = domain.getWhCheckInException();
        if (whCheckInException == null
                || !StringUtils.equalsIgnoreCase(HandleTypeModal.ADD, type) && whCheckInException.getId() == null) {
            responseJson.setMessage("参数错误！");
            return responseJson;
        }
        String key = PrintSkuQrCodeRedisLock.CHECK_IN_EXCEPTION_EDIT + "&" + whCheckInException.getId();
        try {
            if (!StringUtils.equalsIgnoreCase(HandleTypeModal.ADD, type) && !RedissonLockUtil.tryLock(key, 60,
                    PrintSkuQrCodeRedisLock.CHECK_IN_EXCEPTION_EDIT.getTimeout())) {
                logger.info("保存异常编辑异常：正在被" + PrintSkuQrCodeRedisLock.getNameByCode(key) + "占用");
                responseJson.setMessage("有人正在操作该异常单，请稍后再提交！");
                return responseJson;
            }
            responseJson = whCheckInExceptionService.saveWhCheckException(domain, type, exceptionBoxNo);
        }
        catch (Exception e) {
            responseJson.setStatus(StatusCode.FAIL);
            logger.info("保存异常编辑异常" + e.getMessage(), e);
        }
        finally {
            RedissonLockUtil.unlock(key);
        }
        return responseJson;
    }


    /**
     * 保存异常编辑
     *
     * @param domain
     * @return
     */
    @RequestMapping(value = "newSave", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson saveNewWhCheckException(@ModelAttribute("domain") WhCheckInExceptionDo domain,
                                                @RequestParam(value = "type", required = false) String type,
                                                @RequestParam(value = "abandonReason", required = false) String abandonReason) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        WhCheckInException whCheckInException = domain.getWhCheckInException();
        if (whCheckInException == null
                || !StringUtils.equalsIgnoreCase(HandleTypeModal.ADD, type) && whCheckInException.getId() == null) {
            responseJson.setMessage("参数错误！");
            return responseJson;
        }
        String key = PrintSkuQrCodeRedisLock.CHECK_IN_EXCEPTION_EDIT + "&"+ whCheckInException.getId();
        try {
            if (!StringUtils.equalsIgnoreCase(HandleTypeModal.ADD, type) && !RedissonLockUtil.tryLock(key, 60,
                    PrintSkuQrCodeRedisLock.CHECK_IN_EXCEPTION_EDIT.getTimeout())) {
                responseJson.setMessage("有人正在操作该异常单，请稍后再提交！");
                return responseJson;
            }
            responseJson = whCheckInExceptionService.saveNewWhCheckException(domain, type, abandonReason);
        }
        catch (Exception e) {
            responseJson.setStatus(StatusCode.FAIL);
            logger.info("保存异常编辑异常" + e.getMessage(), e);
        }
        finally {
            RedissonLockUtil.unlock(key);
        }
        return responseJson;
    }


    // 删除图片
    @RequestMapping(value = "removePhoto", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson removePhoto(@RequestParam(value = "exceptionId", required = true) Integer exceptionId,
                                    @RequestParam(value = "imagePath") String imagePath, HttpServletRequest request) throws IOException {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        WhCheckInException exception = whCheckInExceptionService.getWhCheckInException(exceptionId);
        if (exception == null) {
            response.setMessage("没有找到入库异常单");
            return response;
        }

        int result = SeaWeedFSUtils.deleteFile(imagePath);
        if (result == 204) {
            List<String> newImgs = new ArrayList<>();
            String newImage = "";
            if (CollectionUtils.isNotEmpty(exception.getImages())) {
                newImgs = exception.getImages();
                newImgs.remove(imagePath);
                String url = SeaWeedFSUtils.URL + WhCheckInException.IMAGE_PATH + "/";

                String image = imagePath.split(url)[1];
                String imageUrl = exception.getImage();
                if (imageUrl.contains(",")) {
                    if (imageUrl.startsWith(image)) {
                        newImage = imageUrl.replace(image + ",", "");
                    } else {
                        newImage = imageUrl.replace("," + image, "");
                    }
                }
            }
            WhCheckInException update = new WhCheckInException();
            update.setId(exception.getId());
            update.setImage(newImage);
            update.setPurchasePushImage(true);
            whCheckInExceptionService.updateWhCheckInException(update);
            update.setCreatedBy(DataContextHolder.getUserId());

            if (!ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode().equals(exception.getExceptionFrom())) {
                // 组装入库异常单处理详情数据
                logger.info("start to send removePhoto message to pms ====exception:" + update.toString()
                        + "===exceptionHandle:" + null);
                rabbitmqProducerService.pushCheckInExceptionMsgToPms(update, null, new PushCheckInException());
                logger.info("send removePhoto message to pms end ");
            }
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage(JSON.toJSONString(newImgs));
        }
        return response;
    }

    // 拍照上传
    @RequestMapping(value = "uploadExceptionImageFromCamera", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson uploadExceptionImageFromCamera(
            @RequestParam(value = "exceptionId", required = true) Integer exceptionId,
            @RequestParam(value = "imageStr") String imageStr, HttpServletRequest request) throws IOException {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        WhCheckInException exception = whCheckInExceptionService.getWhCheckInException(exceptionId);
        if (exception == null) {
            response.setMessage("没有找到入库异常单");
            return response;
        }
        // logger.info("imageStr:"+imageStr);

        String sku = exception.getSku();
        List<String> images = new ArrayList<>();
        List<String> dbImgs = new ArrayList<>();
        int length = 1;
        if (CollectionUtils.isNotEmpty(exception.getImages())) {
            length = exception.getImages().size() + 1;
        }

        String fileName = sku + ".jpg";
        String url = SeaWeedFSUtils.URL;
        String dateStr = String.valueOf(new Date().getTime()) + "-" + length;
        String filePath = WhCheckInException.IMAGE_PATH + "/" + dateStr + "/";

        try {
            // base64解码并生成图片
            Decoder decoder = Base64.getMimeDecoder();
            byte[] b = decoder.decode(imageStr);

            String result = SeaWeedFSUtils.uploadFile(url, filePath, fileName, b);
            if (StringUtils.isNotBlank(result)) {
                String image = url + filePath + fileName;
                images.add(image);
                String dbImg = dateStr + "/" + fileName;
                dbImgs.add(dbImg);
                // 推送到fms
                //pushExceptionImageToFmsMq(fileName, filePath, result);
            }
        } catch (Exception e) {
            logger.warn(e.getMessage());
            response.setMessage(e.getMessage());
            return response;
        }

        if (CollectionUtils.isNotEmpty(dbImgs)) {
            List<String> newImgs = new ArrayList<>();
            String newImage = "";
            if (CollectionUtils.isNotEmpty(exception.getImages())) {
                newImgs = exception.getImages();
                exception.setImage(exception.getImage() + "," + StringUtils.join(dbImgs, ","));
                newImage = exception.getImage() + "," + StringUtils.join(dbImgs, ",");
            } else {
                exception.setImage(StringUtils.join(dbImgs, ","));
                newImage = StringUtils.join(dbImgs, ",");
            }
            newImgs.addAll(images);

            WhCheckInException update = new WhCheckInException();
            update.setId(exception.getId());
            update.setImage(newImage);
            update.setPurchasePushImage(true);
            whCheckInExceptionService.updateWhCheckInException(update);
            update.setCreatedBy(DataContextHolder.getUserId());
            if (!ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode().equals(exception.getExceptionFrom())) {
                logger.info("start to send uploadExceptionImageFromCamera message to pms ====exception:"
                        + exception.getId() + "===exceptionHandle:" + null);
                rabbitmqProducerService.pushCheckInExceptionMsgToPms(update, null, new PushCheckInException());
                logger.info("send uploadExceptionImageFromCamera message to pms end ");
            }
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage(JSON.toJSONString(newImgs));
        }
        return response;
    }

    /**
     * 推送到fms
     */
    private void pushExceptionImageToFmsMq(String fileName, String filePath, String result) {
        Map<String, String> msg = new HashMap<>();
        JSONObject json = JSONObject.parseObject(result);
        msg.put("source", "WMS");
        msg.put("module", "checkInExceptionImage");
        msg.put("relativePath", filePath + fileName);
        msg.put("operate", "add");
        msg.put("url", json.get("url").toString());
        msg.put("fid", json.get("fid").toString());
        msg.put("size", json.get("size").toString());
        rabbitmqProducerService.pushCheckInExceptionImage(JSON.toJSONString(msg));
    }

    /**
     * 本地图片上传
     *
     * @param exceptionId
     * @param request
     * @return
     */
    @RequestMapping(value = "uploadExceptionImage", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson uploadExceptionImage(@RequestParam(value = "exceptionId", required = true) Integer exceptionId,
                                             HttpServletRequest request) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        WhCheckInException exception = whCheckInExceptionService.getWhCheckInException(exceptionId);
        if (exception == null) {
            response.setMessage("没有找到入库异常单");
            return response;
        }
        // String purchaseOrderNo = exception.getPurchaseOrderNo();
        String sku = exception.getSku();

        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multiRequest.getFileMap();
        String[] extensions = {"jpg", "gif", "png", "jpeg"};
        List<String> images = new ArrayList<>();
        List<String> dbImgs = new ArrayList<>();
        int i = 1;
        if (CollectionUtils.isNotEmpty(exception.getImages())) {
            i = exception.getImages().size();
        }
        for (Map.Entry<String, MultipartFile> entry : fileMap.entrySet()) {
            MultipartFile file = entry.getValue();
            String extension = POIUtils.getFileExtensionName(file.getOriginalFilename());
            if (ArrayUtils.contains(extensions, extension)) {
                String fileName = sku + "." + extension;
                try {
                    String url = SeaWeedFSUtils.URL;// 测试环境
                    String dateStr = String.valueOf(new Date().getTime()) + "-" + i++;// DateUtils.dateToString(new
                    // Date(),
                    // "yyyy-MM-dd");
                    String filePath = WhCheckInException.IMAGE_PATH + "/" + dateStr + "/";
                    String result = SeaWeedFSUtils.uploadFile(url, filePath, fileName, file.getBytes());
                    if (StringUtils.isNotBlank(result)) {
                        String image = url + filePath + fileName;
                        images.add(image);
                        String dbImg = dateStr + "/" + fileName;
                        dbImgs.add(dbImg);
                        // 推送到fms
                        //pushExceptionImageToFmsMq(fileName, filePath, result);
                    }
                } catch (IOException e) {
                    logger.warn(e.getMessage());
                    response.setMessage(e.getMessage());
                    return response;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(dbImgs)) {
            List<String> newImgs = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(exception.getImages())) {
                newImgs = exception.getImages();
            }
            newImgs.addAll(images);
            String existImag = exception.getImage();
            if (StringUtils.isNotBlank(existImag)) {
                existImag = existImag + "," + StringUtils.join(dbImgs, ",");
            } else {
                existImag = StringUtils.join(dbImgs, ",");
            }
            exception.setImage(existImag);

            WhCheckInException updateException =  new WhCheckInException();
            updateException.setId(exception.getId());
            updateException.setImage(existImag);
            updateException.setPurchasePushImage(true);
            whCheckInExceptionService.updateWhCheckInException(updateException);
            updateException.setCreatedBy(DataContextHolder.getUserId());
            if (ExceptionFrom.ADD_EXCEPTION.intCode().equals(exception.getExceptionFrom())
                    && ExceptionStatus.UNCONFIRM.intCode().equals(exception.getStatus())){
                updateException=exception;
                updateException.setCreatedBy(DataContextHolder.getUserId());
            }

            // 组装入库异常单处理详情数据
            if (!ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode().equals(exception.getExceptionFrom())) {
                logger.info("start to send uploadExceptionImage message to pms ====exception:" + updateException.toString()
                        + "===exceptionHandle:" + null);
                rabbitmqProducerService.pushCheckInExceptionMsgToPms(updateException, null, new PushCheckInException());
                logger.info("send uploadExceptionImage message to pms end ");
            }
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage(JSON.toJSONString(newImgs));
        }
        return response;
    }

    /**
     * 手动同步异常信息到采购系统（mq消息稳定后可删除）
     *
     * @param domain
     * @param ids
     * @return
     */
    @RequestMapping(value = "batchReSendExceptionToPms", method = {RequestMethod.GET})
    public String batchReSendExceptionToPms(@ModelAttribute("domain") WhCheckInExceptionDo domain,
                                            @RequestParam("ids") List<Integer> ids, @RequestParam(required = false, name = "isNew") Boolean isNew) {
        WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();
        query.setIds(ids);
        List<WhCheckInException> dbWhCheckInExceptionList = whCheckInExceptionService.queryWhCheckInExceptions(query,
                null);
        List<WhCheckInExceptionHandle> whCheckInExceptionHandleList = new ArrayList<>();
        PushCheckInException pushCheckInException = new PushCheckInException();
        if (CollectionUtils.isNotEmpty(dbWhCheckInExceptionList)) {
            List<WhCheckInException> whCheckInExceptionList = new ArrayList<>();
            for (WhCheckInException whCheckInException : dbWhCheckInExceptionList) {
                // 来源为QC全检的异常单不发送消息到采购
                if (!ExceptionFrom.QC_CHECK_ALL_EXCEPTION.intCode().equals(whCheckInException.getExceptionFrom())) {
                    whCheckInException.setPurchaseUserNameToPms(whCheckInException.getPurchaseUserNameToPms());
                    WhCheckInExceptionHandle whCheckInExceptionHandle = new WhCheckInExceptionHandle();
                    whCheckInExceptionHandle.setCreatedBy(DataContextHolder.getUserId());
                    whCheckInExceptionHandle.setExceptionId(whCheckInException.getId());
                    whCheckInExceptionHandle.setQuantity(whCheckInException.getQuantity());
                    whCheckInExceptionHandle.setStatus(whCheckInException.getStatus());
                    whCheckInExceptionHandle.setHandleComment("手动同步异常信息到采购系统");
                    whCheckInExceptionHandleService.createWhCheckInExceptionHandle(whCheckInExceptionHandle);
                    whCheckInExceptionHandle = whCheckInExceptionHandleService
                            .getWhCheckInExceptionHandle(whCheckInExceptionHandle.getId());
                    whCheckInExceptionHandleList.add(whCheckInExceptionHandle);
                    if (StringUtils.isNotBlank(whCheckInException.getImage())) {
                        whCheckInException.setPurchasePushImage(true);
                    }
                    whCheckInExceptionList.add(whCheckInException);
                }
            }
            if (CollectionUtils.isNotEmpty(whCheckInExceptionList)) {
                pushCheckInException.setWhCheckInExceptionHandleList(whCheckInExceptionHandleList);
                pushCheckInException.setWhCheckInExceptionList(whCheckInExceptionList);
                // 手动同步异常信息到采购系统
                logger.info("start to send batchReSendExceptionToPms message to pms ====pushSize:"
                        + pushCheckInException.getWhCheckInExceptionList().size());
                rabbitmqProducerService.pushCheckInExceptionMsgToPms(null, null, pushCheckInException);
                logger.info("send batchReSendExceptionToPms message to pms end ");
            }
        }
        if (isNew == null || !isNew) {
            return "redirect:/checkInException";
        } else {
            return "redirect:/checkInException/newExce/search";
        }
    }

    // 导出
    @RequestMapping(value = "download", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson download(@ModelAttribute("domain") WhCheckInExceptionDo domain,
                                 @RequestParam(value = "ids", required = false) List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        WhCheckInExceptionQueryCondition query = domain.getQuery();
        if (query == null) {
            query = new WhCheckInExceptionQueryCondition();
            domain.setQuery(query);
        }
        if (CollectionUtils.isNotEmpty(ids))
            query.setIds(ids);
        // 兼容含有“=”的sku
        query.setSku(CompatibleSkuUtils.getSku(query.getSku()));
        String fileName = "入库异常单" + System.currentTimeMillis() + ".xlsx";

        // 获取退货信息
        Map<String, ExceptionReturnInformation> returnPurchaseOrderMap = getReturnInfo(ids);
        // 用户信息
        List<SaleUser> saleUsers = saleUserService.queryAllSaleUsers();
        Map<Integer, String> userMap = saleUsers.stream().collect(Collectors.toMap(u -> u.getUserId(), u -> u.getUsername() + " - " + u.getName(), (k1, k2) -> k2));
        logger.warn("download file name: " + fileName);
        WhCheckInExceptionQueryCondition finalQuery = query;
        String[] finalSelectHeaders = headers;
        boolean isAll = whDownloadCenterService.checkFieldAllNull(query);
        domain.getPage().setPageNo(-1);
        Map<Integer, String> purchaseUserMap = GetUserNameOrEmployeeNameUtil.getPurchaseUser().stream()
                .collect(Collectors.toMap(PmsPurchaseUsers::getId, purchaseUsers ->
                        purchaseUsers.getUserName() + " - " + purchaseUsers.getEmployeeName(), (key1, key2) -> key2));
        whDownloadCenterService.downloading(fileName, headers, WhDownloadContentEnum.CHECK_IN_EXCEPTION, isAll, domain.getPage(), (page) -> {
            List<WhCheckInException> exceptions = whCheckInExceptionService.queryNewWhCheckInExceptions(finalQuery, page);
            return getExportList(returnPurchaseOrderMap, purchaseUserMap, userMap, exceptions, finalSelectHeaders);
        });
        response.setStatus(StatusCode.SUCCESS);
        response.setMessage("导出任务已经创建，请到下载中心查看结果");
        return response;
    }

    @Data
    class ExceptionHandleDownload {
        // 入库单id
        private String inIdStr;
        // 绑定周转框时间
        private Timestamp bindBoxTime;
        // 异常完成时间
        private Timestamp completeTime;
        // 异常完成操作人
        private String completeEmp;
        // 待入库操作人
        private String waitCheckInEmp;
        // 草稿提交人
        private String unconfirmEmp;
    }

    private List<List<String>> getExportList(Map<String, ExceptionReturnInformation> returnPurchaseOrderMap,
                                             Map<Integer, String> purchaseUserMap,
                                             Map<Integer, String> userMap,
                                             List<WhCheckInException> exceptions, String[] headers) {
        List<List<String>> whCheckInExceptionData = new ArrayList<>();
        if (CollectionUtils.isEmpty(exceptions))
            return whCheckInExceptionData;

        Map<Integer, ExceptionHandleDownload> exceptionHandleDownloadMap = new HashMap<>();

        WhCheckInExceptionHandleQueryCondition query = new WhCheckInExceptionHandleQueryCondition();
        query.setExceptionIds(exceptions.stream().map(c -> c.getId()).collect(Collectors.toList()));
        query.setStatusList(Arrays.asList(ExceptionStatus.COMPLETE.intCode()
                ,ExceptionStatus.STOCK_IN_ING.intCode()
                ,ExceptionStatus.WAIT_CHECK_IN.intCode()
                ,ExceptionStatus.WAIT_PUSH.intCode()
                ,ExceptionStatus.PURCHASE_PENDING.intCode()));
        List<WhCheckInExceptionHandle> whCheckInExceptionHandles = whCheckInExceptionHandleService.queryWhCheckInExceptionHandles(query, null);
        if (CollectionUtils.isNotEmpty(whCheckInExceptionHandles)) {
            Map<Integer, List<WhCheckInExceptionHandle>> groupMap = whCheckInExceptionHandles.stream().collect(Collectors.groupingBy(w -> w.getExceptionId()));
            for (Map.Entry<Integer, List<WhCheckInExceptionHandle>> entrie : groupMap.entrySet()) {
                Integer key = entrie.getKey();
                List<WhCheckInExceptionHandle> values = entrie.getValue();
                values = values.stream()
                        .sorted(Comparator.comparing(WhCheckInExceptionHandle::getId).reversed())
                        .collect(Collectors.toList());
                ExceptionHandleDownload e = exceptionHandleDownloadMap.containsKey(key) ? exceptionHandleDownloadMap.get(key) : new ExceptionHandleDownload();
                exceptionHandleDownloadMap.put(key,e);
                for (WhCheckInExceptionHandle handle : values) {
                    Integer status = handle.getStatus();
                    if (status == null) continue;
                    ExceptionStatus exceptionStatus = ExceptionStatus.build(status.toString());
                    switch (exceptionStatus) {
                        case COMPLETE :
                            if (StringUtils.isBlank(e.getInIdStr()) && StringUtils.contains(handle.getHandleComment(), "入库单ID:"))
                                e.setInIdStr(handle.getHandleComment());
                            if (StringUtils.isBlank(e.getCompleteEmp()) && handle.getCreatedBy() != null)
                                e.setCompleteEmp(userMap.get(handle.getCreatedBy()));
                            if (e.getCompleteTime() == null && handle.getCreationDate() != null)
                                e.setCompleteTime(handle.getCreationDate());
                            break;
                        case WAIT_CHECK_IN :
                            if (StringUtils.isBlank(e.getWaitCheckInEmp()) && handle.getCreatedBy() != null)
                                e.setWaitCheckInEmp(userMap.get(handle.getCreatedBy()));
                            break;
                        case STOCK_IN_ING :
                            if (e.getBindBoxTime() == null && StringUtils.contains(handle.getHandleComment(), "绑定周转框") && handle.getCreationDate() != null)
                                e.setBindBoxTime(handle.getCreationDate());
                            break;
                        case PURCHASE_PENDING :
                        case WAIT_PUSH :
                                e.setUnconfirmEmp(userMap.get(handle.getCreatedBy()));
                            break;
                        default:
                            break;
                    }
                }
            }
        }


        for (WhCheckInException exception : exceptions) {
            // ID,SKU,周转码,采购单号,快递单号,异常数量,异常确认数量,异常来源,异常类型,处理结果,状态,备注,异常员,添加时间

            List<String> exceptionsList = new ArrayList<String>(headers.length);
            exceptionsList.add(POIUtils.transferObj2Str(exception.getId()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getNextGenerationExceptionIds()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getSku()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getBoxNo()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getLocationNumber()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getPurchaseOrderNo()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getFlagName()));
            if (ExceptionType.FOLLOW_ORDER.getName().equals(exception.getExceptionTypeName())
                    ||  ExceptionHandleWay.CREATE_NEW_ORDER_TO_CHECK_IN.getName().equals(exception.getExceptionHandleWayName())) {
                exceptionsList.add(POIUtils.transferObj2Str(exception.getNewPurchaseOrderNo()));
            } else {
                exceptionsList.add(POIUtils.transferObj2Str(null));
            }
            exceptionsList.add(POIUtils.transferObj2Str(exception.getTrackingNumber()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getQuantity()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getWmsHandleTimes()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getConfirmQuantity()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getExTimes()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getExceptionFromName()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getExceptionTypeName()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getExceptionHandleWayName()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getExceptionStatusName()));
            exceptionsList.add(POIUtils.transferObj2Str(StringUtils.isBlank(exception.getTags())?"": CheckExceptionTagTypeEnum.getNameByCode(exception.getTags())));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getReceiveBoxNo()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getAging()));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getExceptionComment()));
            exceptionsList.add(
                    POIUtils.transferObj2Str(userMap.get(exception.getExceptionUser())));// 异常员
            exceptionsList.add(
                    POIUtils.transferObj2Str(userMap.get(exception.getCreatedBy())));// 创建员
            if (exception.getPurchaseUser() != null) {
                exception.setPurchaseUser(exception.getPurchaseUser());
            }
            exceptionsList.add(POIUtils.transferObj2Str(purchaseUserMap.get(exception.getPurchaseUser())));// 采购员
            exceptionsList
                    .add(POIUtils.transferObj2Str(userMap.get(exception.getCheckInUser())));// 入库员
            exceptionsList
                    .add(POIUtils.transferObj2Str(userMap.get(exception.getFinishUser())));// 仓库处理员
            exceptionsList
                    .add(POIUtils.transferObj2Str(userMap.get(exception.getDiscardedUser())));//废弃员

            Timestamp creationDate = exception.getCreationDate();
            exceptionsList.add(POIUtils.transferObj2Str(creationDate));// 添加时间

            Timestamp exceptionHandleDate = exception.getExceptionHandleDate();
            exceptionsList.add(POIUtils.transferObj2Str(exceptionHandleDate));// 异常处理时间

            Timestamp purchaseHandleDate = exception.getPurchaseHandleDate();
            exceptionsList.add(POIUtils.transferObj2Str(purchaseHandleDate));// 采购处理时间

            Timestamp finishDate = exception.getFinishDate();
            exceptionsList.add(POIUtils.transferObj2Str(finishDate));// 仓库处理时间

            Timestamp waitCheckInDate = exception.getWaitCheckInDate();
            exceptionsList.add(POIUtils.transferObj2Str(waitCheckInDate));//待入库时间

            Timestamp doingCheckInDate = exception.getDoingCheckInDate();
            exceptionsList.add(POIUtils.transferObj2Str(doingCheckInDate));//入库中时间
            exceptionsList.add(POIUtils.transferObj2Str(exception.getFirstOrderTypeString()));//是否首单
            ExceptionHandleDownload exceptionHandleDownload = exceptionHandleDownloadMap.get(exception.getId());
            if (exceptionHandleDownload != null) {
                // 自动完成入库单ID
                String inIdStr = exceptionHandleDownload.getInIdStr();
                if (StringUtils.isBlank(inIdStr)) {
                    exceptionsList.add("");
                } else {
                    exceptionsList.add(StringUtils.substringAfterLast(inIdStr, "入库单ID:"));
                }
                // 绑定周转筐时间
                Timestamp bindBoxDate = exceptionHandleDownload.getBindBoxTime();
                exceptionsList.add(POIUtils.transferObj2Str(bindBoxDate));
                // 异常完成时间
                Timestamp completeDate = exceptionHandleDownload.getCompleteTime();
                exceptionsList.add(POIUtils.transferObj2Str(completeDate));
                // 待入库操作人
                exceptionsList.add(POIUtils.transferObj2Str(exceptionHandleDownload.getWaitCheckInEmp()));
                // 完成人
                exceptionsList.add(POIUtils.transferObj2Str(exceptionHandleDownload.getCompleteEmp()));
                // 草稿提交人
                exceptionsList.add(POIUtils.transferObj2Str(exceptionHandleDownload.getUnconfirmEmp()));
            } else {
                // 没有处理记录时添加空值，确保字段数量一致
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 入库单ID
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 异常绑定周转框时间
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 异常完成时间
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 待入库操作人
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 完成人
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 草稿提交人
            }

            // 退货相关字段 - 无论是否有退货信息都要添加对应数量的字段，确保数据对齐
            if (returnPurchaseOrderMap != null
                    && returnPurchaseOrderMap.get(exception.getId().toString()) != null) {
                // 有退货信息时添加实际数据
                exceptionsList.add(POIUtils
                        .transferObj2Str(returnPurchaseOrderMap.get(exception.getId().toString()).getReason()));
                exceptionsList.add(POIUtils
                        .transferObj2Str(returnPurchaseOrderMap.get(exception.getId().toString()).getAddress()));
                exceptionsList.add(POIUtils
                        .transferObj2Str(returnPurchaseOrderMap.get(exception.getId().toString()).getReceiver()));
                exceptionsList.add(POIUtils
                        .transferObj2Str(returnPurchaseOrderMap.get(exception.getId().toString()).getTel()));
                exceptionsList.add(POIUtils
                        .transferObj2Str(returnPurchaseOrderMap.get(exception.getId().toString()).getAmount()));
                exceptionsList.add(POIUtils
                        .transferObj2Str(returnPurchaseOrderMap.get(exception.getId().toString()).getCondition()));
                exceptionsList.add(POIUtils
                        .transferObj2Str(returnPurchaseOrderMap.get(exception.getId().toString()).getReturnUser()));
                exceptionsList.add(POIUtils.transferObj2Str(
                        returnPurchaseOrderMap.get(exception.getId().toString()).getShippingCompany()));
                exceptionsList.add(POIUtils
                        .transferObj2Str(returnPurchaseOrderMap.get(exception.getId().toString()).getTrackingNo()));
            } else {
                // 没有退货信息时添加空值，确保字段数量一致
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 退货原因
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 退货地址
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 收件人
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 收件人联系方式
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 退货金额
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 退货条件
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 退货人
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 物流公司
                exceptionsList.add(POIUtils.transferObj2Str(null)); // 追踪号
            }

            exceptionsList.add(POIUtils.transferObj2Str(exception.getMarkReason()));
            exceptionsList.add(POIUtils.transferObj2Str(
                    exception.getMarkUserId() == null ? null : userMap.get(exception.getMarkUserId())));
            exceptionsList.add(POIUtils.transferObj2Str(exception.getMarkTime()));
            whCheckInExceptionData.add(exceptionsList);

        }
        return whCheckInExceptionData;
    }

    /**
     * 从退货单中获取退货信息
     *
     * @param ids
     * @return
     */
    public Map<String, ExceptionReturnInformation> getReturnInfo(List<Integer> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ReturnPurchaseOrderQueryCondition queryCondition = new ReturnPurchaseOrderQueryCondition();
            queryCondition.setExceptionIds(ids);
            queryCondition.setSelectItem(true);

            List<ReturnPurchaseOrder> returnPurchaseOrders = returnPurchaseOrderService
                    .queryReturnPurchaseOrders(queryCondition, null);
            Map<String, ExceptionReturnInformation> returnPurchaseOrderMap = new HashMap<String, ExceptionReturnInformation>();
            if (CollectionUtils.isNotEmpty(returnPurchaseOrders)) {
                for (ReturnPurchaseOrder returnPurchaseOrder : returnPurchaseOrders) {
                    List<ReturnPurchaseOrderItem> returnPurchaseOrderItemList = returnPurchaseOrder.getReturnItems();
                    if (CollectionUtils.isNotEmpty(returnPurchaseOrderItemList)) {
                        for (ReturnPurchaseOrderItem item : returnPurchaseOrderItemList) {
                            if (StringUtils.isNotBlank(item.getExceptionId())) {
                                ExceptionReturnInformation exceptionReturnInformation = new ExceptionReturnInformation();
                                exceptionReturnInformation.setReason(returnPurchaseOrder.getReturnReason());
                                exceptionReturnInformation.setAddress(returnPurchaseOrder.getReturnAddress());
                                exceptionReturnInformation.setReceiver(returnPurchaseOrder.getReceiver());
                                exceptionReturnInformation.setTel(returnPurchaseOrder.getReceiverPhone());
                                exceptionReturnInformation.setAmount(returnPurchaseOrder.getRefundAmount());
                                exceptionReturnInformation.setCondition(returnPurchaseOrder.getRemark());
                                exceptionReturnInformation.setReturnUser(returnPurchaseOrder.getRefunder());
                                exceptionReturnInformation.setShippingCompany(
                                        ShippingCompanyEnum.getNameByCode(returnPurchaseOrder.getShippingCompany()));
                                exceptionReturnInformation
                                        .setTrackingNo(returnPurchaseOrder.getReturnShippingOrderNo());
                                String[] exceptionIds = StringUtils.split(item.getExceptionId(), ",");
                                for (String exceptionId : exceptionIds) {
                                    returnPurchaseOrderMap.put(exceptionId, exceptionReturnInformation);
                                }
                            }
                        }
                    }
                }
            }
            return returnPurchaseOrderMap;
        }
        return null;
    }

    /**
     * 批量提交
     *
     * @param domain
     * @param ids
     * @return
     */
    @RequestMapping(value = "batchUpdateExceptionToPurchasePending", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson batchUpdateExceptionToPurchasePending(@ModelAttribute("domain") WhCheckInExceptionDo domain,
                                                              @RequestParam("ids") List<Integer> ids) {
        return whCheckInExceptionService.batchUpdateExceptionToPurchasePending(ids);
    }

    /**
     * 打印标签
     *
     * @param domain
     * @param id
     * @return
     */
    @RequestMapping(value = "printExceptionTag", method = {RequestMethod.GET})
    public String printExceptionTag(@ModelAttribute("domain") WhCheckInExceptionDo domain,
                                    @RequestParam("id") Integer id) {
        if (id == null) {
            domain.setErrorMsg("参数错误!");
            return "checkin/printExceptionTag";
        }
        WhCheckInException whCheckInException = whCheckInExceptionService.getWhCheckInException(id);
        if (whCheckInException!=null && StringUtils.isNotBlank(whCheckInException.getSku())) {
            WhSkuQueryCondition queryCondition = new WhSkuQueryCondition();
            queryCondition.setSku(whCheckInException.getSku());
            WhSku whSku = whSkuService.queryWhSku(queryCondition);
            if (whSku!=null) {
                whCheckInException.setSkuName(whSku.getName());
            }

        }

        domain.setWhCheckInException(whCheckInException);
        SystemLogUtils.CHECK_IN_EXCEPTION_TAG_LOG.log( id, "打印标签");
        return "checkin/printExceptionTag";
    }


    /**
     * 标记待入库
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = "batchUpdateToWaitCheckIn", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson batchUpdateToWaitCheckIn(@RequestParam("ids") List<Integer> ids) {
        ResponseJson response = new ResponseJson();
        List<Integer> errorIdList = whCheckInExceptionService.batchUpdateToWaitCheckIn(ids);
        if (CollectionUtils.isNotEmpty(errorIdList))
            response.setMessage("编号【" + JSONObject.toJSONString(errorIdList)
                    + "】修改失败，状态为待仓库处理或待质控处理，且，处理方式为：入库、带样换图/加图、改描述、建单入库、补发配件，改尺寸、修改质检备注/标准尺寸其中之一的异常单才能修改为待入库！");
        return response;
    }


    /**
     * 修改库位
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = "batchUpdateLocation", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson batchUpdateLocation(@RequestParam("ids") List<Integer> ids,
                                            @RequestParam(value = "location", required = false) String location) {
        ResponseJson response = new ResponseJson();
        if (CollectionUtils.isEmpty(ids)) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage("参数id为空!");
            return response;
        }
        try {
            return whCheckInExceptionService.batchUpdateLocation(ids, location);
        } catch (Exception e) {
            response.setStatus(StatusCode.FAIL);
            response.setMessage(e.getMessage());
            return response;
        }
    }

    @GetMapping("/syncExceptionCount")
    public String syncExceptionCount() {
        historyCheckInExceptionCountService.syncHistoryCheckInExceptionCount();
        return "成功";
    }

    /**
     * 获取异常单信息（用于标记功能）
     */
    @GetMapping("/getExceptionInfo/{id}")
    @ResponseBody
    public ApiResult<?> getExceptionInfo(@PathVariable("id") Integer id) {
        try {
            if (id == null) {
                return ApiResult.newError("异常单ID不能为空");
            }
            WhCheckInException exception = whCheckInExceptionService.getWhCheckInException(id);
            return ApiResult.newSuccess(exception);
        } catch (Exception e) {
            logger.error("获取异常单信息失败", e);
            return ApiResult.newError("获取异常单信息失败");
        }
    }

    /**
     * 标记异常单  
     */
    @PostMapping("/markExceptions")
    @ResponseBody
    public ApiResult<?> markExceptions(@RequestParam("ids") String ids,
                                     @RequestParam("markReason") String markReason,
                                     @RequestParam(value = "markRemark", required = false) String markRemark) {
        try {
            if (StringUtils.isBlank(ids)) {
                return ApiResult.newError("请选择要标记的异常单");
            }
            
            // 解析ID列表
            List<Integer> idList = new ArrayList<>();
            for (String idStr : ids.split(",")) {
                if (StringUtils.isNotBlank(idStr.trim())) {
                    try {
                        idList.add(Integer.parseInt(idStr.trim()));
                    } catch (NumberFormatException e) {
                        return ApiResult.newError("异常单ID格式错误：" + idStr);
                    }
                }
            }
            
            if (idList.isEmpty()) {
                return ApiResult.newError("请选择要标记的异常单");
            }
            
            // 调用Service层方法
            return whCheckInExceptionService.updateMarkExceptions(idList, markReason, markRemark);
            
        } catch (Exception e) {
            logger.error("标记异常单失败", e);
            return ApiResult.newError("标记失败：" + e.getMessage());
        }
    }

    /**
     * 取消标记异常单
     */
    @PostMapping("/cancelMark")
    @ResponseBody
    public ApiResult<?> cancelMark(@RequestParam("id") Integer id) {
        try {
            // 调用Service层方法
            return whCheckInExceptionService.updateCancelMark(id);
        } catch (Exception e) {
            logger.error("取消标记失败", e);
            return ApiResult.newError("取消标记失败：" + e.getMessage());
        }
    }
}