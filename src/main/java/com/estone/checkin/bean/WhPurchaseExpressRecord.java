package com.estone.checkin.bean;

import com.estone.checkin.utils.GetUserNameOrEmployeeNameUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

public class WhPurchaseExpressRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 This field corresponds to the database column
     * wh_purchase_express_record.id
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    private Integer id;

    /**
     * 收货图片
     */
    private String imageUrl;

    /**
     * 快递单号 This field corresponds to the database column
     * wh_purchase_express_record.tracking_number
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    private String trackingNumber;

    /**
     * 采购单号 This field corresponds to the database column
     * wh_purchase_express_record.purchase_order_no
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    private String purchaseOrderNo;

    /**
     * 不为空就是物流单 This field corresponds to the database column
     * wh_purchase_express_record.serial_number
     */
    private Integer serialNumber;

    /**
     * 创建人 This field corresponds to the database column
     * wh_purchase_express_record.created_by
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    private Integer createdBy;

    /**
     * 创建时间 This field corresponds to the database column
     * wh_purchase_express_record.creation_date
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    private Timestamp creationDate;

    /**
     * 拆包人 This field corresponds to the database column
     * wh_purchase_express_record.split_user
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    private Integer splitUser;

    /**
     * 拆包时间 This field corresponds to the database column
     * wh_purchase_express_record.split_date
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    private Timestamp splitDate;

    /**
     * 最后修改人 This field corresponds to the database column
     * wh_purchase_express_record.last_updated_by
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    private Integer lastUpdatedBy;

    /**
     * 最后修改时间 This field corresponds to the database column
     * wh_purchase_express_record.last_update_date
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    private Timestamp lastUpdateDate;

    /**
     * 备注 This field corresponds to the database column
     * wh_purchase_express_record.comment
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    private String comment;

    /**
     * 原因 This field corresponds to the database column
     * wh_purchase_express_record.reason
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    private String reason;

    /**
     * 收货单状态 This field corresponds to the database column
     * wh_purchase_express_record.status
     *
     * @mbggenerated Fri Aug 17 10:33:25 CST 2018
     */
    private Integer status;

    private Integer warehouseId;// 仓库ID

    private Double weight;// 包裹实重(KG)

    private Double totalWeight;// 理论重量(KG)

    private Double length;// 长度（cm）
    private Double width;// 宽度（mc）
    private Double height;// 高度（cm）

    private Integer quantity;// 件数

    // TODO 目前只针对有子快递单号和一个采购单对应多个快递单号的订单
    private Timestamp checkInScanTime;// 点数入库扫描完的时间

    private Integer checkInScanner;// 点数入库扫描人

    private Boolean checkInScanStatus;// 点数入库是否扫描完，1:已扫描完所有子单号，0：未扫描完

    private String boxNo;// 周转框
    private String normalBoxNo;// 普通周转框
    private String urgentBoxNo;// 特急周转框
    private String wlBoxNo;

    private Timestamp receiveDate; // 领取时间

    private Integer receiveUser;// 领取人

    private Integer warehousingStatus;// 入库状态 1=未入库 2=已入库

    private List<WhCheckInException> checkinExceptions;

    private String oldPurchaseOrderNo;//原采购单号，用于日志记录

    @Setter
    @Getter
    private String shippingCpn;//物流公司

    // 采购员
    @Setter
    @Getter
    private String purchaseUser;

    //标签
    @Setter
    @Getter
    private String logisticsMark;

    /**
     * 标签(逗号分割) This field corresponds to the database column
     * wh_purchase_order.flags
     *
     * @mbggenerated Mon May 27 14:24:34 CST 2019
     */
    private String flags;

    /** 小软件收货扫描人 */
    private String scanner;

    /** 扫描标识 1：非本仓包裹 */
    private Integer scanMark;

    /**
     * 是否更新箱号为NULL
     */
    private Boolean updateBoxNoToNull;

    public Boolean getUpdateBoxNoToNull() {
        return updateBoxNoToNull;
    }

    public void setUpdateBoxNoToNull(Boolean updateBoxNoToNull) {
        this.updateBoxNoToNull = updateBoxNoToNull;
    }

    public Integer getScanMark() {
        return scanMark;
    }

    public void setScanMark(Integer scanMark) {
        this.scanMark = scanMark;
    }

    public String getScanner() {
        return scanner;
    }

    public void setScanner(String scanner) {
        this.scanner = scanner;
    }

    public String getFlags() {
        return flags;
    }

    public void setFlags(String flags) {
        this.flags = flags;
    }

    public String getPurchaseUserName() {
        if (StringUtils.contains(this.purchaseUser,",")){
            StringBuffer userName = new StringBuffer();
            String[] split = StringUtils.split(this.purchaseUser, ",");
            for (int i = 0; i<split.length;i++ ){
                userName.append(GetUserNameOrEmployeeNameUtil.getEmployeeName(split[i], null)).append(",");
            }
            return StringUtils.substringBeforeLast(userName.toString(),",");
        }
        return GetUserNameOrEmployeeNameUtil.getEmployeeName(this.purchaseUser, null);
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public String getPurchaseOrderNo() {
        return purchaseOrderNo;
    }

    public void setPurchaseOrderNo(String purchaseOrderNo) {
        this.purchaseOrderNo = purchaseOrderNo;
    }

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Timestamp getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Timestamp creationDate) {
        this.creationDate = creationDate;
    }

    public Integer getSplitUser() {
        return splitUser;
    }

    public void setSplitUser(Integer splitUser) {
        this.splitUser = splitUser;
    }

    public Timestamp getSplitDate() {
        return splitDate;
    }

    public void setSplitDate(Timestamp splitDate) {
        this.splitDate = splitDate;
    }

    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Timestamp getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Timestamp lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(Integer warehouseId) {
        this.warehouseId = warehouseId;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(Double totalWeight) {
        this.totalWeight = totalWeight;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Timestamp getCheckInScanTime() {
        return checkInScanTime;
    }

    public void setCheckInScanTime(Timestamp checkInScanTime) {
        this.checkInScanTime = checkInScanTime;
    }

    public Integer getCheckInScanner() {
        return checkInScanner;
    }

    public void setCheckInScanner(Integer checkInScanner) {
        this.checkInScanner = checkInScanner;
    }

    public Boolean getCheckInScanStatus() {
        return checkInScanStatus;
    }

    public void setCheckInScanStatus(Boolean checkInScanStatus) {
        this.checkInScanStatus = checkInScanStatus;
    }

    public String getBoxNo() {
        return boxNo;
    }

    public void setBoxNo(String boxNo) {
        this.boxNo = boxNo;
    }

    public String getNormalBoxNo() {
        return normalBoxNo;
    }

    public void setNormalBoxNo(String normalBoxNo) {
        this.normalBoxNo = normalBoxNo;
    }

    public String getUrgentBoxNo() {
        return urgentBoxNo;
    }

    public void setUrgentBoxNo(String urgentBoxNo) {
        this.urgentBoxNo = urgentBoxNo;
    }

    public String getWlBoxNo() {
        return wlBoxNo;
    }

    public void setWlBoxNo(String wlBoxNo) {
        this.wlBoxNo = wlBoxNo;
    }

    public Timestamp getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Timestamp receiveDate) {
        this.receiveDate = receiveDate;
    }

    public Integer getReceiveUser() {
        return receiveUser;
    }

    public void setReceiveUser(Integer receiveUser) {
        this.receiveUser = receiveUser;
    }

    public Integer getWarehousingStatus() {
        return warehousingStatus;
    }

    public void setWarehousingStatus(Integer warehousingStatus) {
        this.warehousingStatus = warehousingStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<WhCheckInException> getCheckinExceptions() {
        return checkinExceptions;
    }

    public void setCheckinExceptions(List<WhCheckInException> checkinExceptions) {
        this.checkinExceptions = checkinExceptions;
    }

    public String getOldPurchaseOrderNo() {
        return oldPurchaseOrderNo;
    }

    public void setOldPurchaseOrderNo(String oldPurchaseOrderNo) {
        this.oldPurchaseOrderNo = oldPurchaseOrderNo;
    }

    public Double getLength() {
        return length;
    }

    public void setLength(Double length) {
        this.length = length;
    }

    public Double getWidth() {
        return width;
    }

    public void setWidth(Double width) {
        this.width = width;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    @Override
    public String toString() {
        return "WhPurchaseExpressRecord{" + "id=" + id + ", trackingNumber='" + trackingNumber + '\''
                + ", purchaseOrderNo='" + purchaseOrderNo + '\'' + ", serialNumber=" + serialNumber + ", createdBy="
                + createdBy + ", creationDate=" + creationDate + ", splitUser=" + splitUser + ", splitDate=" + splitDate
                + ", lastUpdatedBy=" + lastUpdatedBy + ", lastUpdateDate=" + lastUpdateDate + ", comment='" + comment
                + '\'' + ", reason='" + reason + '\'' + ", status=" + status + ", warehouseId=" + warehouseId
                + ", weight=" + weight + ", totalWeight=" + totalWeight + ", quantity=" + quantity
                + ", checkInScanTime=" + checkInScanTime + ", checkInScanner=" + checkInScanner + ", checkInScanStatus="
                + checkInScanStatus + ", boxNo='" + boxNo + '\'' + ", normalBoxNo='" + normalBoxNo + '\'' + ", urgentBoxNo='" + urgentBoxNo
                + '\'' + ", receiveDate=" + receiveDate + ", receiveUser=" + receiveUser + ", warehousingStatus=" + warehousingStatus + '}';
    }

}