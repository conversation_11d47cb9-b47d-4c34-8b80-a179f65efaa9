package com.estone.checkin.bean;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

import com.estone.checkin.enums.CheckInFlags;
import com.estone.checkin.utils.GetUserNameOrEmployeeNameUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hpsf.Decimal;

@Data
public class WhCheckInExcess implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键 database column wh_check_in_excess.id
     */
    private Integer id;

    /**
     * 主表ID，用于关联主记录 database column wh_check_in_excess.in_id
     */
    private Integer inId;

    /**
     * 子表ID，字符串类型，用于关联子记录 database column wh_check_in_excess.sub_id
     */
    private String subId;

    /**
     * SKU编码 database column wh_check_in_excess.sku
     */
    private String sku;

    /**
     * 多货数量 database column wh_check_in_excess.excess_quantity
     */
    private Integer excessQuantity;

    /**
     * 采购单号 database column wh_check_in_excess.purchase_order_no
     */
    private String purchaseOrderNo;

    /**
     * 采购员ID database column wh_check_in_excess.purchase_user
     */
    private String purchaseUser;

    /**
     * 采购数量-取消在途 database column wh_check_in_excess.purchase_quantity
     */
    private Integer purchaseQuantity;

    /**
     * 匹配良品数量 database column wh_check_in_excess.matched_quantity
     */
    private Integer matchedQuantity;

    /**
     * 处理方式 database column wh_check_in_excess.processing_method
     */
    private String processingMethod;

    /**
     * 操作类型 database column wh_check_in_excess.operation
     */
    private String operation;

    /**
     * 创建时间 database column wh_check_in_excess.create_time
     */
    private Timestamp createTime;

    /**
     * 更新时间 database column wh_check_in_excess.update_time
     */
    private Timestamp updateTime;

    private Integer qcQuantity;

    private Integer upQuantity;

    private Integer transitionUpQuantity;

    private Double  weight;

    private BigDecimal shippingCost;

    private String trackingNumber;

    private String supplierId;

    private Integer qcNum;

    private String vendorName;

    /**
     * 差异数量 database column wh_check_in_excess.difference_quantity
     */
    private Integer differenceQuantity;

    //多个快递单号逗号分割,取第一个展示
    public String getTrackingNumberSplice() {
        if(StringUtils.isBlank(trackingNumber) || !trackingNumber.contains(",")){
            return trackingNumber;
        }
        String[] trackingNumbers = this.trackingNumber.split(",");
        return trackingNumbers[0];
    }

    public String getPurchaseUserName() {
        return GetUserNameOrEmployeeNameUtil.getEmployeeName(this.purchaseUser,null);
    }

    public String getProcessingMethodName() {
        return this.processingMethod == null ? null : CheckInFlags.getNameByCode((this.processingMethod));
    }
}