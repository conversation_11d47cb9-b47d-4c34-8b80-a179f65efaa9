package com.estone.checkin.bean;

import com.alibaba.fastjson.JSON;
import com.estone.checkin.enums.*;
import com.estone.checkin.utils.GetUserNameOrEmployeeNameUtil;
import com.estone.common.util.CommonUtils;
import com.estone.common.util.SeaWeedFSUtils;
import com.estone.system.qcproportion.bean.ExceptionTypeConfig;
import com.estone.system.user.bean.EmployeeEntity;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 * @version 0.0.2
 * @ClassName: WhCheckInException
 * @Description: 入库异常
 * @date 2018年11月19日
 */
@Data
public class WhCheckInException implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 This field corresponds to the database column wh_check_in_exception.id
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Integer id;

    /**
     * 入库单ID This field corresponds to the database column
     * wh_check_in_exception.in_id
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Integer inId;

    /**
     * 采购单号 This field corresponds to the database column
     * wh_check_in_exception.purchase_order_no
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private String purchaseOrderNo;

    /**
     * 新采购单号 This field corresponds to the database column
     * wh_check_in_exception.new_purchase_order_no
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private String newPurchaseOrderNo;

    /**
     * 快递单号 This field corresponds to the database column
     * wh_check_in_exception.tracking_number
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private String trackingNumber;

    /**
     * 创建人 This field corresponds to the database column
     * wh_check_in_exception.created_by
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Integer createdBy;

    /**
     * 入库人
     */
    private Integer checkInUser;

    /**
     * 创建时间 This field corresponds to the database column
     * wh_check_in_exception.creation_date
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Timestamp creationDate;

    /**
     * 完成时间（仓库处理时间）
     */
    private Timestamp finishDate;

    /**
     * 仓库处理员
     */
    private Integer finishUser;

    /**
     * 废弃时间
     */
    private Timestamp discardedDate;

    /**
     * 废弃员
     */
    private Integer discardedUser;

    /**
     * 异常类型 This field corresponds to the database column
     * wh_check_in_exception.exception_type
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private String exceptionType;

    /**
     * 状态 This field corresponds to the database column wh_check_in_exception.status
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Integer status;

    /**
     * 处理方式 This field corresponds to the database column
     * wh_check_in_exception.handle_way
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Integer handleWay;

    /**
     * 异常员 This field corresponds to the database column
     * wh_check_in_exception.exception_user
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Integer exceptionUser;

    /**
     * 异常处理时间（从草稿状态变为待采购处理的时间）
     */
    private Timestamp exceptionHandleDate;

    /**
     * 异常员名字
     */
    private String exceptionUserName;

    /**
     * 开始待质控处理时间(从别的状态变更为待质控处理状态的时间)
     */
    private Timestamp startQCHandleDate;

    /**
     * 完成待质控处理时间(从待质控处理状态变为别的状态的时间)
     */
    private Timestamp completedQCHandleDate;

    /**
     * 异常周转码 This field corresponds to the database column
     * wh_check_in_exception.box_no
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private String boxNo;

    /**
     * SKU This field corresponds to the database column wh_check_in_exception.sku
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private String sku;

    /**
     * 图片 This field corresponds to the database column wh_check_in_exception.image
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private String image;

    /**
     * 异常数量 This field corresponds to the database column
     * wh_check_in_exception.quantity
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Integer quantity;

    /**
     * 采购员 This field corresponds to the database column
     * wh_check_in_exception.purchase_user
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Integer purchaseUser;

    /**
     * 采购处理时间
     */
    private Timestamp purchaseHandleDate;

    private String purchaseUserName;

    private String purchaseUserNameToPms;

    /**
     * 接收页面参数，采购单号使用的user存的是username
     */
    private String userName;

    /**
     * 异常确认数量 This field corresponds to the database column
     * wh_check_in_exception.confirm_quantity
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Integer confirmQuantity;

    //异常处理数量
    private Integer  handledQuantity;

    /**
     * 异常描述 This field corresponds to the database column
     * wh_check_in_exception.exception_comment
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private String exceptionComment;

    /**
     * 最后修改人 This field corresponds to the database column
     * wh_check_in_exception.last_update_user
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Integer lastUpdateUser;

    /**
     * 最后修改时间 This field corresponds to the database column
     * wh_check_in_exception.last_update_date
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Timestamp lastUpdateDate;

    /**
     * 仓库 This field corresponds to the database column
     * wh_check_in_exception.warehouse_id
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Integer warehouseId;

    /**
     * 异常来源 This field corresponds to the database column
     * wh_check_in_exception.exception_form
     * {@link ExceptionFrom}
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Integer exceptionFrom;

    /**
     * 是否带货 This field corresponds to the database column
     * wh_check_in_exception.is_carry_product
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Boolean isCarryProduct;

    /**
     * 带货数量 This field corresponds to the database column
     * wh_check_in_exception.carry_quantity
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private Integer carryQuantity;

    /**
     * QC全检未扫描的唯一码 This field corresponds to the database column
     * wh_check_in_exception.return_information_json
     *
     * @mbggenerated Mon Nov 19 15:15:52 CST 2018
     */
    private String returnInformationJson;

    /**
     * 生成的下一代入库异常单id列表
     * wh_check_in_exception.next_generation_exception_ids
     */
    private String nextGenerationExceptionIds;

    /**
     * 异常单重新入库绑定的收货周转框
     */
    private String receiveBoxNo;

    /**
     * 提交时的备注
     */
    private String firstEditHandleComment;

    /**
     * 采购处理备注
     */
    private String lastPurchaseHandleComment;

    /**
     * 异常批次号
     */
    private String batchNo;
    /**
     * 首单标识 1:新品首单,2:供应商首单
     */
    private Integer firstOrderType;

    /**
     * 异常次数
     */
    private Integer exTimes;

    /**
     * 库位
     */
    private String locationNumber;

    /**
     * SKU名称
     */
    private String skuName;

    /**
     *  处理备注（,拼接）
     */
    private String handleCommon;

    /**
     * 待入库时间
     */
    private Timestamp waitCheckInDate;

    /**
     * 入库中时间
     */
    private Timestamp doingCheckInDate;

    /**
     * 沟通次数
     */
    private Integer wmsHandleTimes;

    /**
     * sku总库位库存
     */
    private Integer skuLocationQty;

    /**
     * 用于标记一个订单是否经过了开发/待质控处理状态(当前订单包括处于这两个状态中)
     */
    private Boolean exceptionHandled;

    //异常标签
    private String tags;

    /**
     * 标记原因
     */
    private String markReason;

    /**
     * 标记时间
     */
    private Timestamp markTime;

    /**
     * 标记人员ID
     */
    private Integer markUserId;

    public static final String IMAGE_PATH = "/checkInExceptionImage";

    private Boolean autoHandle;//自动处理
    /*
    * 废弃原因
    * */
    private String abandonReason;

    private boolean purchasePushImage=false;

    private String orderFlag;

    // 多货提交异常
    private Boolean excessExceptionFlag;

    public String getFlagName() {
        if (StringUtils.isBlank(orderFlag))
            return null;
        List<String> splitList = CommonUtils.splitList(orderFlag, ",");
        if (CollectionUtils.isEmpty(splitList))
            return null;
        Set<String> name = new HashSet<>();
        splitList.forEach(flag -> {
            String flagName = PurchaseOrderFlags.getNameByCode(flag);
            if (StringUtils.isBlank(flagName))
                flagName = PurchaseOrderFlags.getNameByEnCode(flag);
            if (StringUtils.isNotBlank(flagName))
                name.add(flagName);
        });
        if (CollectionUtils.isEmpty(name))
            return null;
        return StringUtils.join(name, ",");
    }

    public void addNextGenerationExceptionId(Integer nextGenerationExceptionId){
        if (nextGenerationExceptionId == null) {
            return;
        }
        String id = String.valueOf(nextGenerationExceptionId);
        if (StringUtils.isBlank(this.nextGenerationExceptionIds)){
            this.nextGenerationExceptionIds = id;
            return;
        }
        List<String> strings = CommonUtils.splitList(this.nextGenerationExceptionIds, ",");
        if (!strings.contains(id)) {
            strings.add(id);
            this.nextGenerationExceptionIds = StringUtils.join(strings, ",");
        }
    }

    public String getExceptionTypeName() {
        if (StringUtils.isNotBlank(exceptionType)) {
            String[] temp = exceptionType.split(",");// 以逗号拆分字符串
            String exceptionTypeName = "";
            for (int i = 0; i < temp.length; i++) {
                for (ExceptionTypeConfig type : ExceptionType.getValues(null, null)) {
                    if (StringUtils.isNotBlank(temp[i]) && type.getCode().equals(temp[i])) {
                        exceptionTypeName += (type.getName()) + "，";
                    }
                }
            }
            if (StringUtils.isEmpty(exceptionTypeName))
                return null;
            return exceptionTypeName.substring(0, exceptionTypeName.length() - 1);
        }
        return null;
    }

    public String getExceptionFromName() {

        if (this.exceptionFrom != null) {
            for (ExceptionFrom type : ExceptionFrom.values()) {
                if (type.intCode().equals(this.exceptionFrom)) {
                    return type.getName();
                }
            }
        }
        return null;
    }

    public String getExceptionHandleWayName() {
        if (this.handleWay != null) {
            for (ExceptionHandleWay type : ExceptionHandleWay.values()) {
                if (type.intCode().equals(this.handleWay)) {
                    return type.getName();
                }
            }
        }
        return null;
    }

    public String getPurchaseUserName() {
        return GetUserNameOrEmployeeNameUtil.getEmployeeName(null, this.purchaseUser);
    }

    /**
     * 用于判断采购员职位是否包含“开发”字段
     * @return
     */
    public boolean isPurchaseUserVocationContainDev(){
        if (Objects.isNull(this.getPurchaseUser())){
            return false;
        }
        Long purchaseUserId = (long)this.getPurchaseUser();
        EmployeeEntity employee = GetUserNameOrEmployeeNameUtil.getEmployeeByEmployeeId(purchaseUserId);
        if (Objects.nonNull(employee)){
            return StringUtils.contains(employee.getPositionName(),"开发");
        }
        return false;
    }

    public String getPurchaseUserNameToPms() {
        return GetUserNameOrEmployeeNameUtil.getUserName(this.purchaseUser);
    }


    public String getExceptionStatusName() {
        if (this.status != null) {
            for (ExceptionStatus type : ExceptionStatus.values()) {
                if (type.intCode().equals(this.status)) {
                    return type.getName();
                }
            }
        }
        return null;
    }

    public List<String> getImages() {
        if (StringUtils.isNotBlank(this.image)) {
            List<String> images = new ArrayList<>();
            String[] imgs = image.split(",");
            String url = SeaWeedFSUtils.URL;
            for (int i = 0; i < imgs.length; i++) {
                String img = url + IMAGE_PATH + "/" + imgs[i];
                images.add(img);
            }
            return images;
        }
        return null;
    }

    public String getImagesJson() {
        if (CollectionUtils.isNotEmpty(getImages())) {
            return JSON.toJSONString(getImages());
        }
        return null;
    }

    public String getExceptionUserName() {
        return GetUserNameOrEmployeeNameUtil.getWmsUserName(this.exceptionUser, null);
    }


    /**
     * 时效
     */
    public Long getAging() {
        Long aging = null;
        if (this.creationDate != null && this.status != null && this.status < ExceptionStatus.COMPLETE.intCode()) {
            long sec = System.currentTimeMillis() - this.creationDate.getTime();
            aging = (sec / (1000 * 60 * 60));
        }
        else if (this.creationDate != null && this.status != null
                && this.status >= ExceptionStatus.COMPLETE.intCode()) {
            if (this.finishDate != null) {
                long sec = this.finishDate.getTime() - this.creationDate.getTime();
                aging = (sec / (1000 * 60 * 60));
                return aging;
            }
            if (this.discardedDate != null) {
                long sec = this.discardedDate.getTime() - this.creationDate.getTime();
                aging = (sec / (1000 * 60 * 60));
                return aging;
            }
        }
        return aging;
    }

    public String getFirstOrderTypeString() {
        if (firstOrderType == null || firstOrderType == 0) {
            return "普通";
        }

        if (firstOrderType == 1) {
            return "新品首单";
        }

        if (firstOrderType == 2) {
            return "供应商首单";
        }

        return null;
    }

    /**
     * 通过当前状态判断是否要将exceptionHandled字段的值标记为true
     * @return
     */
    public Boolean judgeExceptionHandledByStatus(){
        if (Objects.isNull(this.getStatus())){
            return null;
        }
        List<Integer> status = Arrays.asList(ExceptionStatus.PRODUCT_PENDING.intCode(),ExceptionStatus.QC_PENDING.intCode());
        if (status.contains(this.getStatus())){
            return true;
        }
        return null;
    }

    /**
     * 判断异常类型是否为 尺寸不符,来货与图不符。处理方式为 入库，建单入库 中的一种组合，若是的话，
     * 则跳过待质控处理，变更为待仓库处理
     * @param handleWay 别的系统推送过来的处理方式
     * @return
     * @see ExceptionHandleWay.CHECK_IN,ExceptionHandleWay.CREATE_NEW_ORDER_TO_CHECK_IN
     * @see ExceptionType.SIZE_ERROR,ExceptionType.IMG_NOT_MATCH
     */
    public boolean isSkipQCPending(Integer handleWay) {
        if (Objects.isNull(handleWay)){
            return false;
        }
        List<Integer> handleWays = Arrays.asList(ExceptionHandleWay.CHECK_IN.intCode(),
                ExceptionHandleWay.CREATE_NEW_ORDER_TO_CHECK_IN.intCode());
        List<String> exceptionTypes = Arrays.asList(ExceptionType.SIZE_ERROR.getCode(),
                ExceptionType.IMG_NOT_MATCH.getCode());
        if (handleWays.contains(handleWay)) {
            if (StringUtils.isBlank(this.getExceptionType())){
                return false;
            }
            List<String> whExceptionTypes = CommonUtils.splitList(this.getExceptionType(), ",");
            for (String whExceptionType : whExceptionTypes){
                if (exceptionTypes.contains(whExceptionType)){
                    return true;
                }
            }
        }
        return false;
    }

    // 添加标签
    public boolean addTags(CheckExceptionTagTypeEnum checkExceptionTagTypeEnum) {
        if (StringUtils.isBlank(tags)) {
            tags = checkExceptionTagTypeEnum.getCode();
            return true;
        }
        List<String> tagList = CommonUtils.splitList(tags, ",");
        // 已存在
        if (tagList.stream().anyMatch(s -> StringUtils.equalsIgnoreCase(s, checkExceptionTagTypeEnum.getCode())))
            return false;
        tagList.add(checkExceptionTagTypeEnum.getCode());
        tags = StringUtils.join(tagList, ",");
        return true;
    }

    // 删除标签
    public boolean remTags(CheckExceptionTagTypeEnum checkExceptionTagTypeEnum) {
        if (StringUtils.isBlank(tags)) {
            return false;
        }
        List<String> buyerCheckouts = CommonUtils.splitList(tags, ",");
        if (buyerCheckouts.remove(checkExceptionTagTypeEnum.getCode())) {
            tags = StringUtils.join(buyerCheckouts, ",");
            return true;
        } else {
            return false;
        }
    }

    @Override
    public String toString() {
        return "WhCheckInException{" + "id=" + id + ", inId=" + inId + ", purchaseOrderNo='" + purchaseOrderNo + '\''
                + ", newPurchaseOrderNo='" + newPurchaseOrderNo + '\'' + ", trackingNumber='" + trackingNumber + '\''
                + ", createdBy=" + createdBy + ", creationDate=" + creationDate + ", finishDate=" + finishDate
                + ", finishUser=" + finishUser + ", discardedDate=" + discardedDate + ", discardedUser=" + discardedUser
                + ", exceptionType='" + exceptionType + '\'' + ", status=" + status + ", handleWay=" + handleWay
                + ", exceptionUser=" + exceptionUser + ", exceptionHandleDate=" + exceptionHandleDate
                + ", exceptionUserName='" + exceptionUserName + '\'' + ", boxNo='" + boxNo + '\'' + ", sku='" + sku
                + '\'' + ", image='" + image + '\'' + ", quantity=" + quantity + ", purchaseUser=" + purchaseUser
                + ", purchaseHandleDate=" + purchaseHandleDate + ", purchaseUserName='" + purchaseUserName + '\''
                + ", purchaseUserNameToPms='" + purchaseUserNameToPms + '\'' + ", userName='" + userName + '\''
                + ", confirmQuantity=" + confirmQuantity + ", exceptionComment='" + exceptionComment + '\''
                + ", lastUpdateUser=" + lastUpdateUser + ", lastUpdateDate=" + lastUpdateDate + ", warehouseId="
                + warehouseId + ", exceptionFrom=" + exceptionFrom + ", isCarryProduct=" + isCarryProduct
                + ", carryQuantity=" + carryQuantity + ", returnInformationJson='" + returnInformationJson + '\''
                + ", receiveBoxNo='" + receiveBoxNo + '\'' + ", firstEditHandleComment='" + firstEditHandleComment
                + '\'' + ", lastPurchaseHandleComment='" + lastPurchaseHandleComment + '\'' + '}';
    }
}