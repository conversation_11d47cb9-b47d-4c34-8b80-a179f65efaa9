package com.estone.checkin.bean;

import java.util.List;

import lombok.Data;

@Data
public class WhCheckInExceptionQueryCondition extends WhCheckInException {
    private static final long serialVersionUID = 1L;

    private String fromCreateDate;// 入库起始时间

    private String toCreateDate;// 入库终止时间

    private String fromExceptionHandleDate;// 异常处理查询开始时间

    private String toExceptionHandleDate;// 异常处理查询终止时间

    private String fromPurchaseHandleDate;// 采购处理查询开始时间

    private String toPurchaseHandleDate;// 采购处理查询终止时间

    private String fromFinishDate;// 完结查询开始时间

    private String toFinishDate;// 完结查询终止时间

    private String newpurchaseNos;// 新采购单号;分割字符串
    private List<String> newpurchaseOrderNos;// 新采购单号

    private List<String> purchaseOrderNos;// 采购单号
    private List<String> trackingNos;// 快递单号

    private List<String> exceptionTypes;// 异常类型

    private List<Integer> statusList;// 状态

    private List<Integer> exceptionFroms;// 异常来源

    private List<Integer> handleWays;// 处理方式

    private List<Integer> purchaseUserList;//采购员

    private List<Integer> ids;// IDs

    private List<Integer> inIds;

    private String orderBy = "ORDER BY id DESC";// 排序

    private Boolean isNew = false;

    /** 待入库时间-起始 */
    private String fromWaitCheckInDate;
    /** 待入库时间-结束 */
    private String toWaitCheckInDate;
    /**
     * 入库中时间-起始
     */
    private String fromDoingCheckInDate;
    /**
     * 入库中时间-结束
     */
    private String toDoingCheckInDate;

    /**
     *  标签
     */
    //private String tag;

    /**
     * 页面待质控处理查询sku总库位库存
     */
    private Boolean queryLocationStock;

    /**
     * 开始进行待质控处理时间起始查询时间
     */
    private String fromStartQCHandleDate;

    /**
     * 开始进行待质控处理时间截止查询时间
     */
    private String toStartQCHandleDate;

    /**
     * 待质控处理完成时间起始查询时间
     */
    private String fromCompletedQCHandleDate;

    /**
     * 待质控处理完成时间截止查询时间
     */
    private String toCompletedQCHandleDate;

    /**
     * pda入库单查询sku可用库存
     */
    private Boolean pdaQueryLocationStock;

    //排除标识
    private List<Integer>  exclusiveFirstOrderTypes;

    private List<String> skuList;

    private String spu;

    private String orderFlag;

    /**
     * 标记状态筛选 (marked: 已标记, unmarked: 未标记)
     */
    private String markStatus;

    /**
     * 标记时间查询开始时间
     */
    private String fromMarkTime;

    /**
     * 标记时间查询结束时间
     */
    private String toMarkTime;


}