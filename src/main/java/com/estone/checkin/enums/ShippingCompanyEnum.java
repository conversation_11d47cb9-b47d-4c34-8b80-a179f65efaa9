package com.estone.checkin.enums;

import com.alibaba.fastjson.JSONObject;
import com.estone.common.SelectJson;
import com.estone.common.util.CacheUtils;
import com.estone.system.param.bean.SystemParam;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * ---------------------------------------------------------------------------
 * Copyright (c) 2018, estone- All Rights Reserved.
 * Project Name:wms
 * Package Name:com.estone.checkin.enums
 * File Name:ShippingCompanyEnum.java
 * Description:物流公司
 * Author:Yimeil
 * Date:2019-07-16 14:20
 * ---------------------------------------------------------------------------
 */
public enum ShippingCompanyEnum {
    YD("韵达","1"),
    Z<PERSON>("中通","3"),
    Y<PERSON>("圆通","5"),
    YTO_3KG("圆通（大于3KG）","6"),
    BEST("百世汇通","7"),
    STO("申通","9"),
    SF("顺丰","11"),
    DEPPON("德邦","13"),
    JT_EXPRESS("极兔速递","15"),
    HUOLALA("货拉拉","17"),
    KUAYUE("跨越","18"),
    JYM("加运美","19"),
    SMTCF("仓发上门揽","SMTCF"),
    SMTZJ("仓发自寄","SMTZJ"),
    ;

    //todo 新增枚举后需要修改 参数配置->发货特殊规则->EXPRESS_COMPANY 注意ID不要冲突
    private String code;

    private String name;

    private ShippingCompanyEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        SystemParam systemParam = CacheUtils.SystemParamGet("SPECIFIED_DESC.EXPRESS_COMPANY");
        if (systemParam != null && StringUtils.isNotBlank(systemParam.getParamValue())) {
            List<SelectJson> selectJsons = JSONObject.parseArray(systemParam.getParamValue(), SelectJson.class);
            for (SelectJson selectJson : selectJsons) {
                String id = selectJson.getId();
                String text = selectJson.getText();
                if (id.equals(code)) {
                    return text;
                }
            }
        }
        else {
            ShippingCompanyEnum[] values = values();
            for (ShippingCompanyEnum type : values) {
                if (type.code.equals(code)) {
                    return type.getName();
                }
            }
        }
        
        return null;
    }

    public static String getCodeByName(String name) {
        SystemParam systemParam = CacheUtils.SystemParamGet("SPECIFIED_DESC.EXPRESS_COMPANY");
        if (systemParam != null && StringUtils.isNotBlank(systemParam.getParamValue())) {
            List<SelectJson> selectJsons = JSONObject.parseArray(systemParam.getParamValue(), SelectJson.class);
            for (SelectJson selectJson : selectJsons) {
                String id = selectJson.getId();
                String text = selectJson.getText();
                if (text.equals(name)) {
                    return id;
                }
            }
        }
        else {
            ShippingCompanyEnum[] values = values();
            for (ShippingCompanyEnum type : values) {
                if (type.name.equals(name)) {
                    return type.getCode();
                }
            }
        }
        return null;
    }


    public static ShippingCompanyEnum getInstanceByCode(String code){
        ShippingCompanyEnum[] values = values();
        for (ShippingCompanyEnum type : values){
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
}
