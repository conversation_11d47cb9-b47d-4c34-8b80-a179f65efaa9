package com.estone.common.util;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description Redis常量
 * @date 2020/8/17 10:07
 */
public class RedisConstant {

    /**
     * 订单调拨播种计算正常订单退（已拣>播种）的数量,在装车的时候退
     */
    public final static String APV_ALLOCATION_SOW_NORMAL_RETURN_PICK_KEY = "APV_ALLOCATION_SOW_NORMAL_RETURN_PICK_PREFIX:";

    /**
     * 交运包裹尺寸交运
     */
    public final static String WH_SHIPPING_METHOD_VOLUME_INFO = "WH_SHIPPING_METHOD_VOLUME_INFO_PREFIX:";

    /**
     * 服装类SKU分类ID
     */
    public final static String CLOTHING_SKU_CATEGORY_ID_LIST = "CLOTHING_SKU_CATEGORY_ID_LIST:ARR";

    /**
     * 美景跨仓库位移库SKU标记
     */
    public final static String ALLOCATON_MOVE_LOCATION = "ALLOCATON_MOVE_LOCATION:";

    /**
     * 菜鸟订单交运重试机制
     */
    public final static String WMS_ORDER_DELIVER_RETRY = "PAC:WMS_ORDER_DELIVER_RETRY";

    /**
     * 菜鸟订单确认重试机制
     */
    public final static String WMS_ORDER_CONFIRM_RETRY = "PAC:WMS_ORDER_CONFIRM_RETRY";

    /**
     * 菜鸟入库单确认重试机制
     */
    public final static String WMS_STOCK_IN_CONFIRM_RETRY = "PAC:WMS_STOCK_IN_CONFIRM_RETRY";

    /**
     * 优选仓剩余库存（可用+待确认+已拣返架-待分配订单库存）
     */
    public final static String WMS_PAC_STOCK = "PAC:WMS_PAC_STOCK";

    /**
     * 优选仓 盘点一起提取sku种类数
     */
    public final static String WMS_PAC_PICK_INVENTORY_NUM = "PAC:WMS_PAC_PICK_INVENTORY_NUM";

    /**
     * 菜鸟大小包关系回传重试
     */
    public final static String WMS_EXTEND_UPLOAD_RETRY = "PAC:WMS_EXTEND_UPLOAD_RETRY";

    /**
     * 预警库存
     */
    public final static String EARLY_WARNING_STOCK = "EARLY_WARNING_STOCK";
    /**
     * 修改sku标准尺寸
     */
    public final static String CHANGE_SKU_CONTAIN = "CHANGE_SKU_CONTAIN:";

    /**
     * 保质期sku批次最近更新时间
     */
    public final static String EXP_SKU_LAST_CALC_TIME = "EXP_SKU_LAST_CALC_TIME";

    /**
     * sku颜色尺寸
     */
    public final static String SKU_COLOR_SIZE = "SKU_COLOR_SIZE";

    /**
     * 出库单推单次数
     */
    public final static String APV_CANCEL_COUNT = "APV_CANCEL_COUNT:";
    /**
     * sku拦截
     */
    public final static String SKU_INTERCEPT_CONFIG = "SKU_INTERCEPT_CONFIG";

    /**
     * 一品多位二次配货 拣货sku
     */
    public final static String BZYC_KEY = "BZYC:";


    /**
     * 对比优选仓库存有差异的sku
     */
    public final static String PAC_DIFFERENCE_SKU = "PAC_DIFFERENCE_SKU";

    // 匹配库存
    public final static String MATCH_LOCATION_KEY = "MATCH_LOCATION:";


    /**
     * 多品包装 记录扫描数量
     */
    public final static String MM_APV_PACK_KEY = "MM_APV_PACK_KEY:";

    /**
     * 首单QC称重 记录是否跳过称重步骤
     */
    public final static String QC_WEIGHT_SKIP = "QC_WEIGHT_SKIP:";


    /**
     * 库位盘点，对应库位剩余未盘点的sku
     */
    public final static String LOCATION_INVENTORY = "LOCATION_INVENTORY_MAPS";


    /**
     * 滞销退换货装箱校验，暂存扫描了物流单号的信息
     */
    public final static String RETURN_PURCHASE_CHECKED_BOX_SHIPMENTS = "RETURN_PURCHASE_CHECKED_BOX_SHIPMENTS_MAPS";


    /**
     * FBA多品包装 记录扫描数量
     */
    public final static String JIT_MM_APV_PACK_KEY = "JIT_MM_APV_PACK_KEY:";


    public final static String TRANSFER_PICKUP_ORDER = "TRANSFER_PICKUP_ORDER:";


    public final static String TRANSFER_SHEIN_KEY = "TRANSFER_SHEIN_KEY:";

    public final static String TRANSFER_SHEIN_PDF_URL_KEY = "TRANSFER_SHEIN_PDF_URL_KEY:";

    /** PDD到仓地址 */
    public static final String TEMU_RECEIVE_ADDRESS = "TEMU_RECEIVE_ADDRESS";
    // pdd当天预计揽收时间
    public static final String TEMU_NOW_HOUR = "TEMU_NOW_HOUR";

    public final static String MATCH_OUT_STOCK_APV_KEY = "MATCH_OUT_STOCK_APV_KEY:";
    /** PDD固定分拣筐号 */
    public static final String TEMU_PICK_NUMBER = "TEMU_PICK_NUMBER";

    /**
     * 缺货直发包装 记录扫描数量
     */
    public final static String ZF_APV_PACK_KEY = "ZF_APV_PACK_KEY:";

    // 同步组合sku时间
    public static final String SYNC_COMBINE_SKU_LAST_TIME = "SYNC_COMBINE_SKU_LAST_TIME";

    //虚拟库位缓存
    public static final String VIRTUAL_LOCATION_CACHE = "VIRTUAL_LOCATION_CACHE:";

    /**
     * 加运美物流单号
     */
    public final static String JYM_EXPRESS_NO_KEY = "JYM_EXPRESS_NO_KEY:";

    /**
     * 加运美快递信息暂存
     */
    public final static String JYM_EXPRESS_INFO_KEY = "JYM_EXPRESS_INFO_KEY:";

    public final static String ZF_NOT_ALLOT_CHECK_IN_LIST = "wh:ZF_NOT_ALLOT_CHECK_IN_LIST";

    public final static String RECEIVE_HOUSE_MAP = "RECEIVE_HOUSE_MAP";

    /**
     * 跨越物流单号
     */
    public final static String KYE_EXPRESS_NO_KEY = "KYE_EXPRESS_NO_KEY:";
    /** 跨越回调打印用户 */
    public final static String KYE_EXPRESS_TASKID_KEY = "KYE_EXPRESS_TASKID:";


    public final static String CONTINUE_OLD_LOGIC = "CONTINUE_OLD_LOGIC";

    public final static String BATCH_GET_SKU_TAGS_LAST_ID = "BATCH_GET_SKU_TAGS_LAST_ID";

    //中转仓退仓推送OMS失败单据
    public final static String TRANSFER_RETURN_ORDER_PUSH_FAIL = "TRANSFER_RETURN_ORDER_PUSH_FAIL";

    public final static String SMT_CREATE_CO_ORDER_NUM = "SMT_CREATE_CO_ORDER_NUM:";

    public final static String SMT_CREATE_PICKUP_ORDER_NUM = "SMT_CREATE_PICKUP_ORDER_NUM:";
    public final static String SMT_CREATE_PICKUP_ORDER = "SMT_CREATE_PICKUP_ORDER:";
    public final static String SMT_CREATE_SELF_DELIVERY = "SMT_CREATE_SELF_DELIVERY:";

    //每日生成售后结算出入库数据文件路径
    public final static String AFTER_SALE_TURNOVER_DATA = "AFTER_SALE_TURNOVER_DATA:";
    public final static String SMT_COLLECTION_SHIPMENT_ALL = "SMT_COLLECTION_SHIPMENT_ALL:";
    public final static String SMT_COLLECTION_SHIPMENT_TIME = "SMT_COLLECTION_SHIPMENT_TIME:";

    public final static String PDA_FBA_PACKING="PDA_FBA_PACKING:";

    //仓发装车拆包数据
     public final static String WAREHOUSE_LOADING_SUMMARY = "WAREHOUSE_LOADING_SUMMARY:";

     // 从pms获取的包装属性缓存
     public final static String SKU_PACKAGE_ATTRIBUTE_CACHE = "SKU_PACKAGE_ATTRIBUTE_CACHE:";

     /** PC端交运扫描 */
     public final static String DELIVER_SCAN_PC = "DELIVER_SCAN_PC";


    /**
     * sku标签配置，标识是否正存在自动计算任务
     */
    public final static String SKU_TAG_CONFIG_AUTO_CALCULATE_FLAG = "SKU_TAG_CONFIG_AUTO_CALCULATE_FLAG:";
    /**
     * sku标签配置，分布式锁的key
     */
    public final static String SKU_TAG_CONFIG_AUTO_CALCULATE_FLAG_LOCK_KEY = "SKU_TAG_CONFIG_AUTO_CALCULATE_FLAG_LOCK_KEY:";


    /**
     * 用于缓存采购员信息
     */
    public final static String PURCHASE_USER_REDIS_KEY = "PURCHASE_USER_REDIS_KEY:";

    /** 点数入库成功数 */
    public final static String CHECKIN_SUCCESS_COUNT_KEY = "CHECKIN_SUCCESS_COUNT:";

    /**
     * JIT仓发装箱数量
     */
    public final static String JIT_APV_PACK_KEY = "JIT_APV_PACK_KEY:";

    /**
     * 销售账号Shein
     */
    public final static String SHEIN_SALE_ACCOUNT_KEY = "SHEIN_SALE_ACCOUNT_KEY";

    /**
     * SMT条码映射
     */
    public static final String SMT_SKU_BARCODE_MAPPING_KEY = "SMT_SKU_BARCODE_MAPPING_KEY";
    public static final String SHIPPING_MARK_PDF_URL_BASE = "SHIPPING_MARK_PDF_URL_BASE:";

    public static final String ZF_APV_PACK_LOCK_KEY = "ZF_APV_PACK_LOCK_KEY:";
    public static final String FBA_UP_MONITOR_NOTIFICATION_JOB_HANDLER = "FBA_UP_MONITOR_NOTIFICATION_JOB_HANDLER:";

    /** Redis Key常量定义 */
    public static final String ALLOCATION_SCAN_KEY_PREFIX = "ALLOCATION_SCAN:";


}

