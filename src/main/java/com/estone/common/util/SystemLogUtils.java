package com.estone.common.util;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.estone.common.enums.LogModule;
import com.estone.system.log.bean.WhSystemLog;
import com.estone.system.log.dao.WhSystemLogDao;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.log.LogContentUtils;
import org.apache.commons.lang.StringUtils;

/**
 * 系统日志工具类
 *
 * @Description: 添加系统日志工具类
 * @ClassName: SystemLogUtils
 * @Author: wuhuiqiang
 * @Date: 2018/08/16
 * @Version: 0.0.1
 */
public class SystemLogUtils {
    private final static ExecutorService executors = Executors.newFixedThreadPool(5);

    private final static String str = " - ";

    public final static SystemLogUtils BOXLOG = SystemLogUtils.create(LogModule.WHBOX.getCode());
    public final static SystemLogUtils RETURNLOG = SystemLogUtils.create(LogModule.WHRETURN.getCode());
    public final static SystemLogUtils ABROADRETURNLOG = SystemLogUtils.create(LogModule.WHABROADRETURN.getCode());
    public final static SystemLogUtils APVLOG = SystemLogUtils.create(LogModule.WHAPV.getCode());
    public final static SystemLogUtils APVSPANLOG = SystemLogUtils.create(LogModule.WHAPV_SPAN.getCode());
    public final static SystemLogUtils ASNSPANLOG = SystemLogUtils.create(LogModule.WHASN_SPAN.getCode());
    public final static SystemLogUtils WHASNLOG = SystemLogUtils.create(LogModule.WHASN.getCode());
    public final static SystemLogUtils WHASNPICKTASK = SystemLogUtils.create(LogModule.WHASN_PICK_TASK.getCode());
    public final static SystemLogUtils WHLENDLOG = SystemLogUtils.create(LogModule.WHLEND.getCode());
    public final static SystemLogUtils WHLENDRETURNLOG = SystemLogUtils.create(LogModule.WHLENDRETURN.getCode());
    public final static SystemLogUtils WHLENDDESTROYLOG = SystemLogUtils.create(LogModule.WHLENDDESTROY.getCode());
    public final static SystemLogUtils WHBADPRODUCTLOG = SystemLogUtils.create(LogModule.WHBADPRODUCT.getCode());
    public final static SystemLogUtils WHSCRAPLOG = SystemLogUtils.create(LogModule.WHSCRAP.getCode());

    public final static SystemLogUtils WHCHRECORDLOG = SystemLogUtils.create(LogModule.WHCHRECORD.getCode());

    public final static SystemLogUtils ERRORLOG = SystemLogUtils.create(LogModule.WHERROR.getCode());
    public final static SystemLogUtils SKULOG = SystemLogUtils.create(LogModule.SKU.getCode());
    public final static SystemLogUtils WHAPVALLOCATION = SystemLogUtils.create(LogModule.WHAPVALLOCATION.getCode());

    // 自动调拨
    public final static SystemLogUtils WHALLOCATIONDEMANDAUTO = SystemLogUtils.create(LogModule.WHALLOCATIONDEMANDAUTO.getCode());
    public final static SystemLogUtils WHAPVALLOCATIONITEM = SystemLogUtils
            .create(LogModule.WHAPVALLOCATIONITEM.getCode());
    public final static SystemLogUtils WHAPVALLOCATIONDEMAND = SystemLogUtils
            .create(LogModule.WHAPVALLOCATIONDEMAND.getCode());
    public final static SystemLogUtils WHALLOCATIONPICKING = SystemLogUtils
            .create(LogModule.WHALLOCATIONPICKING.getCode());
    public final static SystemLogUtils PICKINVENTORYDEMAND = SystemLogUtils
            .create(LogModule.WHPICKINVENTORYDEMAND.getCode());
    public final static SystemLogUtils INVENTORYTASK = SystemLogUtils.create(LogModule.WHINVENTORYTASK.getCode());
    public final static SystemLogUtils INVENTORYTASKITEM = SystemLogUtils
            .create(LogModule.WHINVENTORYTASKITEM.getCode());
    public final static SystemLogUtils WHALLOCATIONCHECKINEXCEPTION = SystemLogUtils
            .create(LogModule.WHALLOCATIONCHECKINEXCEPTION.getCode());

    public final static SystemLogUtils WHAPVRETURN = SystemLogUtils.create(LogModule.WHAPVRETURN.getCode());

    public final static SystemLogUtils INVENTORYITEMLOG = SystemLogUtils.create(LogModule.INVENTORYITEM.getCode());
    public final static SystemLogUtils BATCHRETURNLOG = SystemLogUtils.create(LogModule.BATCH_RETURN_LOG.getCode());
    public final static SystemLogUtils MOVELOCATIONTASKLOG = SystemLogUtils.create(LogModule.MOVE_LOCATION_TASK_LOG.getCode());
    public final static SystemLogUtils MOVESKULOCATIONTASKLOG = SystemLogUtils.create(LogModule.MOVE_LOCATION_TASK_LOG.getCode());

    public final static SystemLogUtils TRANSITRETURNLOG = SystemLogUtils.create(LogModule.TRANSIT_RETURN_LOG.getCode());
    public final static SystemLogUtils FBAALLOCATIONLOG = SystemLogUtils.create(LogModule.FBA_ALLOCATION_LOG.getCode());
    public final static SystemLogUtils FBASHIPMENTLOG = SystemLogUtils.create(LogModule.FBA_SHIPMENT.getCode());
    public final static SystemLogUtils PACSTOCKORDERLOG = SystemLogUtils.create(LogModule.PAC_STOCK_ORDER_LOG.getCode());

    public final static SystemLogUtils RETURN_FORM_ORDER_LOG = SystemLogUtils.create(LogModule.RETURN_FORM_ORDER_LOG.getCode());

    public final static SystemLogUtils MOVE_FORM_LOG = SystemLogUtils.create(LogModule.MOVE_FORM_LOG.getCode());

    public final static SystemLogUtils EXP_MANAGE_LOG = SystemLogUtils.create(LogModule.EXP_MANAGE_LOG.getCode());

    public final static SystemLogUtils PROMOTION_STATE_LOG = SystemLogUtils.create(LogModule.PROMOTION_STATE_LOG.getCode());

    public final static SystemLogUtils WH_EX_LOCATION = SystemLogUtils.create(LogModule.WH_EX_LOCATION.getCode());

    public final static SystemLogUtils RETURN_FORM_ORDER_TASK_LOG = SystemLogUtils.create(LogModule.RETURN_FORM_ORDER_TASK_LOG.getCode());

    public final static SystemLogUtils WH_LEND_PICKING_TASK = SystemLogUtils.create(LogModule.WH_LEND_PICKING_TASK.getCode());

    public final static SystemLogUtils SM_PACKING_EXCEPTION = SystemLogUtils.create(LogModule.SM_PACKING_EXCEPTION.getCode());

    public final static SystemLogUtils SM_PACKING_PICKING_TASK = SystemLogUtils.create(LogModule.SM_PACKING_PICKING_TASK.getCode());

    public final static SystemLogUtils WH_MATERIAL_INVENTORY = SystemLogUtils.create(LogModule.WH_MATERIAL_INVENTORY.getCode());

    public final static SystemLogUtils WH_MATERIAL_OUT_ORDER = SystemLogUtils.create(LogModule.WH_MATERIAL_OUT_ORDER.getCode());


    public final static SystemLogUtils WH_MATERIAL_PURCHASE = SystemLogUtils.create(LogModule.WH_MATERIAL_PURCHASE.getCode());

    public final static SystemLogUtils MATERIAL_CHECKIN = SystemLogUtils.create(LogModule.MATERIAL_CHECKIN.getCode());

    public final static SystemLogUtils ON_WAY_ORDER = SystemLogUtils.create(LogModule.ON_WAY_ORDER.getCode());

    public final static SystemLogUtils RFO_CHECK_IN = SystemLogUtils.create(LogModule.RFO_CHECK_IN.getCode());

    public final static SystemLogUtils LOCATION_RULES = SystemLogUtils.create(LogModule.LOCATION_RULES.getCode());

    public final static SystemLogUtils PRESTOCK_MIGRATION_RULES = SystemLogUtils.create(LogModule.PRESTOCK_MIGRATION_RULES.getCode());

    public final static SystemLogUtils AFTER_SALE = SystemLogUtils.create(LogModule.AFTER_SALE.getCode());

    public final static SystemLogUtils BOUTIQUE_PURCHASE_ORDER = SystemLogUtils.create(LogModule.BOUTIQUE_PURCHASE_ORDER.getCode());

    public final static SystemLogUtils BOUTIQUE_OUT_ORDER = SystemLogUtils.create(LogModule.BOUTIQUE_OUT_ORDER.getCode());

    public final static SystemLogUtils EXP_MANAGE_CHECK_TASK = SystemLogUtils.create(LogModule.EXP_MANAGE_CHECK_TASK.getCode());

    public final static SystemLogUtils LOCATION_CHECK_TASK = SystemLogUtils.create(LogModule.LOCATION_CHECK_TASK.getCode());
    public final static SystemLogUtils SHOP_ORDER = SystemLogUtils.create(LogModule.SHOP_ORDER.getCode());

    public final static SystemLogUtils CE_PHOTO_TASK = SystemLogUtils.create(LogModule.CE_PHOTO_TASK.getCode());

    public final static SystemLogUtils CE_MANAGE = SystemLogUtils.create(LogModule.CE_MANAGE.getCode());
    public final static SystemLogUtils PICKINGLOG = SystemLogUtils.create(LogModule.WHPICKING.getCode());
    public final static SystemLogUtils MERGE_SKU_LOG = SystemLogUtils.create(LogModule.MERGE_SKU.getCode());
    public final static SystemLogUtils MERGE_SKU_TASK_LOG = SystemLogUtils.create(LogModule.MERGE_SKU_TASK.getCode());

    public final static SystemLogUtils TEMU_PREPARE_ORDER = SystemLogUtils.create(LogModule.TEMU_PREPARE_ORDER.getCode());

    public final static SystemLogUtils COMBINE_SKU = SystemLogUtils.create(LogModule.COMBINE_SKU.getCode());
    public final static SystemLogUtils COMBINE_SKU_STOCK = SystemLogUtils.create(LogModule.COMBINE_SKU_STOCK.getCode());
    public final static SystemLogUtils TEMU_PACKAGE = SystemLogUtils.create(LogModule.TEMU_PACKAGE.getCode());
    public final static SystemLogUtils TEMU_SPLIT_CONFIG = SystemLogUtils.create(LogModule.TEMU_SPLIT_CONFIG.getCode());
    public final static SystemLogUtils SYSTEMLOG = SystemLogUtils.create(LogModule.SYSTEM.getCode());
    public final static SystemLogUtils COMBINE_SKU_TASK = SystemLogUtils.create(LogModule.COMBINE_SKU_TASK.getCode());
    public final static SystemLogUtils EXEMPTION_QC_CONFIGURATION = SystemLogUtils.create(LogModule.EXEMPTION_QC_CONFIGURATION.getCode());
    public final static SystemLogUtils SKU_ATTRIBUTE_TAGS_CONFIG_LOG = SystemLogUtils.create(LogModule.SKU_ATTRIBUTE_TAGS_CONFIG.getCode());

    public final static SystemLogUtils INTERMEDIATE_BATCH_RETURN_LOG = SystemLogUtils.create(LogModule.INTERMEDIATE_BATCH_RETURN_LOG.getCode());
    public final static SystemLogUtils NEW_PRODUCT_CHECK_TASK_LOG = SystemLogUtils.create(LogModule.NEW_PRODUCT_CHECK_TASK_LOG.getCode());
    public final static SystemLogUtils CHECK_IN_EXCEPTION_TAG_LOG = SystemLogUtils.create(LogModule.CHECK_IN_EXCEPTION_TAG_LOG.getCode());
    public final static SystemLogUtils VERIFY_CHECK_IN_TASK_LOG = SystemLogUtils.create(LogModule.VERIFY_CHECK_IN_TASK_LOG.getCode());
    public final static SystemLogUtils FBA_UP_MONITOR_NOTIFICATION_LOG = SystemLogUtils.create(LogModule.FBA_UP_MONITOR_NOTIFICATION_LOG.getCode());

    /**
     * 模块
     **/
    private String module = null;

    private SystemLogUtils(String module) {
        this.module = module;
    }

    public static SystemLogUtils create(String module) {
        return new SystemLogUtils(module);
    }

    /**
     * 添加日志
     *
     * @param relevanceId 模块关联ID
     * @param content     内容
     * @Description: 添加系统日志
     * @Author: wuhuiqiang
     * @Date: 2018/08/16
     * @Version: 0.0.1
     */
    public void log(Integer relevanceId, String content) {
        log(relevanceId, content, null);
    }

    /**
     * 添加日志
     *
     * @param relevanceId 模块关联ID
     * @param content     内容
     * @param appInfo     多类型
     * @Description: 添加系统日志
     * @Author: wuhuiqiang
     * @Date: 2018/08/16
     * @Version: 0.0.1
     */
    public void log(Integer relevanceId, String message, String[][] appInfo) {

        String logMsg = formInfo(message, appInfo);
        String content = LogContentUtils.strFormat(logMsg);

        addLog(relevanceId, content);
    }

    /**
     * 添加日志
     *
     * @param relevanceId 模块关联ID
     * @param content     内容
     * @Description: 添加系统日志
     * @Author: wuhuiqiang
     * @Date: 2018/08/16
     * @Version: 0.0.1
     */
    private void addLog(Integer relevanceId, String content) {
        WhSystemLogDao whSystemLogDao = SpringUtils.getBean("whSystemLogDao", WhSystemLogDao.class);
        WhSystemLog log = new WhSystemLog();
        log.setCreateBy(DataContextHolder.getUserId());
        log.setRelevanceId(relevanceId);
        log.setContent(content);
        log.setModule(module);
        executors.execute(new WhSystemLogThread(whSystemLogDao, log));
    }

    /**
     * 组装信息
     *
     * @param type   类型
     * @param status 状态
     * @param msg    信息
     * @return
     */
    public static String[][] getContent(String type, boolean status, String msg) {
        String[][] content = null;
        if (StringUtils.isNotBlank(msg)) {
            content = new String[3][2];
            content[0][0] = "类型";
            content[0][1] = type;
            content[1][0] = "状态";
            content[1][1] = status == true ? "成功" : "失败";
            content[2][0] = "信息";
            content[2][1] = msg;
        } else {
            content = new String[2][2];
            content[0][0] = "类型";
            content[0][1] = type;
            content[1][0] = "状态";
            content[1][1] = status == true ? "成功" : "失败";
        }
        return content;
    }

    /**
     * 添加日志线程
     *
     * @Description: 添加日志线程。异步提交
     * @ClassName: WhSystemLogThread
     * @Author: wuhuiqiang
     * @Date: 2018/08/16
     * @Version: 0.0.1
     */
    private static class WhSystemLogThread implements Runnable {

        private WhSystemLogDao whSystemLogDao;

        private WhSystemLog log;

        public WhSystemLogThread(WhSystemLogDao whSystemLogDao, WhSystemLog log) {
            this.whSystemLogDao = whSystemLogDao;
            this.log = log;
        }

        @Override
        public void run() {
            whSystemLogDao.createWhSystemLog(log);
        }

    }

    /**
     * 合并内容
     *
     * @param message 主内容
     * @param appInfo 参数内容
     * @return 内容
     * @Description: 主内容+根据连接符合并
     * @Author: wuhuiqiang
     * @Date: 2018/08/16
     * @Version: 0.0.1
     */
    private String formInfo(String message, String[][] appInfo) {

        StringBuffer sb = new StringBuffer(200);

        sb.append(message);

        String appendInfo = formAppendInfo(appInfo);
        if ((appendInfo != null) && (appendInfo.trim().length() != 0)) {
            sb.append(str);
            sb.append(appendInfo);
        }

        return sb.toString();
    }

    /**
     * 合并内容
     *
     * @param appInfo 参数内容
     * @return 内容
     * @Description: 根据连接符合并
     * @Author: wuhuiqiang
     * @Date: 2018/08/16
     * @Version: 0.0.1
     */
    private String formAppendInfo(String[][] appInfo) {
        StringBuffer sb = new StringBuffer(20);
        if (null != appInfo) {
            for (int i = 0; i < appInfo.length; i++) {
                int tempSize = appInfo[i].length;

                if ((tempSize >= 2) && (appInfo[i][0] != null)) {
                    sb.append(appInfo[i][0]);
                    sb.append('=');
                    if (appInfo[i][1] == null) {
                        sb.append("").append(", ");
                    } else {
                        sb.append(appInfo[i][1]).append(", ");
                    }
                } else {
                    if ((tempSize != 1) || (appInfo[i][0] == null))
                        continue;
                    sb.append(appInfo[i][0]).append(", ");
                }
            }

        }

        String appendInfo = sb.toString();
        if ((null == appendInfo) || (0 == appendInfo.length())) {
            return "";
        }

        appendInfo = appendInfo.substring(0, appendInfo.length() - 1);
        return appendInfo;
    }
}
