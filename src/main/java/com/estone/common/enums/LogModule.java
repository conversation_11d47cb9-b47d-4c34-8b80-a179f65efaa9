package com.estone.common.enums;

import com.estone.apv.bean.SaleChannel;

/**
 * 日志模块枚举
 * 
 * @Description: 日志模块枚举。。中文加模块英文
 * @ClassName: LogModule
 * @Author: wuh<PERSON><PERSON><PERSON>
 * @Date: 2018/08/16
 * @Version: 0.0.1
 */
public enum LogModule {
    SYSTEM("系统日志", "system"),

    PDAERROR("PDA报错日志", "pdaError"),

    CHECKIN("入库单", "checkIn"),

    ALLOCATION_CHECKIN("入库单", "allocationCheckIn"),

    SCANSHIPMENT("结袋卡", "scanShipment"),

    EXPRESSRECORD("快递收货", "expressRecord"),

    PURCHASE_ORDER("采购单", "purchaseOrder"),

    INVENTORYITEM("盘点明细改库存", "inventoryItem"),

    WHAPV("发货单日志", "whApv"),

    WHAPV_SPAN("发货单跨仓日志", "whApv"),

    WHASN_SPAN("海外仓调拨跨仓日志", "whSpanAsn"),

    WHASN("海外出库管理日志", "whAsn"),

    WHASN_PICK_TASK("海外出库拣货任务", "whAsnPickTask"),

    WHLEND("创建外借单管理日志", "whLend"),

    WHLENDRETURN("创建外借单归还管理日志", "whLendReturn"),

    WHLENDDESTROY("创建核销单管理日志", "whLendDestroy"),

    WHBADPRODUCT("创建不良品单管理日志", "whBadProduct"),

    WHSCRAP("创建报废单管理日志", "whScrap"),

    WHCHRECORD("创建存货移库","whChRecord"),

    WHERROR("监听报错日志", "WHERROR"),

    WHBOX("周转框日志", "whBox"),

    WHRETURN("返架日志", "whReturn"),

    WHABROADRETURN("海外退件日志", "whAbroadReturn"),

    WHPICKING("拣货任务日志", "whPicking"),

    MERGE_SKU("合并sku管理日志", "mergeSkuManage"),

    ROLE("角色", "role"),

    SKU("sku", "sku"),

    VERIFY_SKU_WEIGHT("SKU称重审核", "verifySkuWeight"),

    VERIFY_SKU_SIZE("SKU尺寸审核", "verifySkuSize"),

    SKU_WEIGHING_TASK("SKU称重任务", "skuWeighingTask"),

    WHAPVSERVICETASK("拣货任务服务", "whApvServiceTask"),

    SECURITY_CHECK_RETURN("安检退回", "securityCheckReturn"),

    WHAPVRETURN("安检退回", "whApvReturn"),

    WHAPVALLOCATION("库存调拨", "whApvAllocation"),

    WHALLOCATIONDEMANDAUTO("自动调拨", "whAllocationDemandAuto"),

    WHAPVALLOCATIONITEM("库存调拨明细拣货", "whApvAllocationItem"),

    WHAPVALLOCATIONDEMAND("调拨单列表", "whApvAllocationDemand"),

    ALLOCATE_RETURN_ORDER("调拨单列表", "allocateReturnOrder"),

    WHLOCATION("库位日志", "whLocation"),

    PICKINGBY("拣货通道人员配置", "pickingBy"),

    RETURN_AREA_LOG("返架区域配置", "whRuleReturnArea"),

    WHALLOCATIONPICKING("调拨拣货任务日志", "whAllocationPicking"),

    WHPICKINVENTORYDEMAND("拣货缺货盘点需求日志", "whPickInventoryDemand"),

    WHINVENTORYTASK("拣货缺货盘点任务日志", "whInventoryTask"),

    COSTSETTING("运费设置日志", "costSetting"),

    WHINVENTORYTASKITEM("拣货缺货盘点任务明细日志", "whInventoryTaskItem"),

    WHALLOCATIONCHECKINEXCEPTION("调拨入库异常日志", "whAllocationCheckInException"),

    PACKAGING_MATERIAL_LOG("包材管理日志", "packagingMaterialManagement"),

    BATCH_RETURN_LOG("退货批次日志", "whBatchReturn"),
    MOVE_LOCATION_TASK_LOG("库位移库日志", "moveLocationTask"),
    MOVE_SKU_LOCATION_TASK_LOG("SKU移库日志", "moveSkuLocationTask"),

    SYSTEMPARAM("系统参数日志", "systemParam"),

    TRANSIT_RETURN_LOG("中转仓返架日志", "transitReturn"),

    FBA_ALLOCATION_LOG("FBA调拨发货单管理日志", "fbaAllocation"),

    RETURN_FORM_ORDER_LOG("退换货单日志", "returnFormOrder"),

    PAC_STOCK_ORDER_LOG("优选仓入库单管理日志", "pacStockOrder"),

    MOVE_FORM_LOG("移库任务管理日志", "moveFormLog"),

    EXP_MANAGE_LOG("保质期批次管理日志", "expManage"),

    PROMOTION_STATE_LOG("产品系统取消促销标签日志", "promotionStateLog"),

    WH_EX_LOCATION("入库异常库位日志", "whExLocation"),

    RETURN_FORM_ORDER_TASK_LOG("滞销退换货拣货日志", "rfoPickingTask"),

    WH_LEND_PICKING_TASK("外借单拣货日志", "WhLendPickingTask"),

    SM_PACKING_EXCEPTION("多件包装异常日志", "smPackingException"),

    SM_PACKING_PICKING_TASK("异常补货拣货日志", "smPackingPickingTask"),

    WH_MATERIAL_INVENTORY("耗材盘点日志", "materialInventory"),

    WH_MATERIAL_OUT_ORDER("耗材出库单日志", "materialOutOrder"),


    WH_MATERIAL_PURCHASE("耗材出库单日志", "materialPurchase"),

    MATERIAL_CHECKIN("耗材入库单", "materialCheckIn"),

    ON_WAY_ORDER("头程在途管理", "onWayOrder"),

    RFO_CHECK_IN("退换货入库单日志", "rfoCheckIn"),

    LOCATION_RULES("库位规则日志","locationRules"),

    PRESTOCK_MIGRATION_RULES("存货迁移规则日志","preStockMigrationRules"),

    REPLENISHMENT_PICKING("挂单补货日志", "replenishmentPicking"),

    AFTER_SALE("售后结算日志", "afterSale"),

    BOUTIQUE_PURCHASE_ORDER("精品采购单", "bqPurchaseOrder"),

    BOUTIQUE_OUT_ORDER("精品出库单", "boutiqueOutOrder"),

    EXP_MANAGE_CHECK_TASK("保质期批次核对任务", "expManageCheckTask"),

    LOCATION_CHECK_TASK("库位校验任务", "locationCheckTask"),

    SHOP_ORDER("内购单", "shopOrder"),

    CE_PHOTO_TASK("CE拍照任务", "cePhotoTask"),

    CE_MANAGE("CE管理", "ceManage"),

    MERGE_SKU_TASK("合并sku任务日志", "mergeSkuTask"),

    TEMU_PREPARE_ORDER("TEMU备货单", "temuPrepareOrder"),

    TEMU_PACKAGE("TEMU包裹", "temuPrepareOrderItem"),

    COMBINE_SKU("组合SKU", "combineSku"),

    COMBINE_SKU_STOCK("组装任务", "combineSkuStock"),

    COMBINE_SKU_TASK("组装拣货任务", "combineSkuTask"),

    TEMU_SPLIT_CONFIG("TEMU拆分建议配置", "temuSplitConfig"),

    EXEMPTION_QC_CONFIGURATION("免QC配置", "exemptionQcConfig"),

    FBA_SHIPMENT("FBA货件单管理", "fbaShipment"),

    SKU_ATTRIBUTE_TAGS_CONFIG("sku标签配置", "skuAttributeTagsConfig"),

    INTERMEDIATE_BATCH_RETURN_LOG("退货批次日志", "whIntermediateReturn"),
    NEW_PRODUCT_CHECK_TASK_LOG("新品维护任务", "newProductCheckTask"),
    VERIFY_CHECK_IN_TASK_LOG("入库单核查任务", "verifyCheckInTask"),
    CHECK_IN_EXCEPTION_TAG_LOG("入库异常标签", "checkInExceptionTag"),

    FBA_UP_MONITOR_NOTIFICATION_LOG("FBA上架监控通知日志", "fbaUpMonitorNotification"),
    ;

    private String code;

    private String name;

    private LogModule(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
