package com.estone.warehouse.action;

import com.estone.checkin.bean.WhCheckInException;
import com.estone.checkin.bean.WhCheckInExceptionQueryCondition;
import com.estone.checkin.bean.WhPurchaseExpressRecord;
import com.estone.checkin.enums.ExceptionStatus;
import com.estone.checkin.service.WhCheckInExceptionService;
import com.estone.checkin.service.WhPurchaseExpressRecordService;
import com.estone.common.SelectJson;
import com.estone.common.util.CacheUtils;
import com.estone.warehouse.bean.WhBox;
import com.estone.warehouse.bean.WhBoxQueryCondition;
import com.estone.warehouse.domain.WhBoxDo;
import com.estone.warehouse.enums.BoxStatus;
import com.estone.warehouse.enums.BoxType;
import com.estone.warehouse.enums.ItemOrderTypeEnum;
import com.estone.warehouse.service.WhBoxService;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.Pager;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.json.ResponseJson;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Controller
@RequestMapping(value = "warehouse/boxsh")
public class WhBoxSHController extends BaseController {
    private static Logger logger = LoggerFactory.getLogger(WhBoxSHController.class);
    @Resource
    private WhBoxService whBoxService;
    @Resource
    private WhCheckInExceptionService whCheckInExceptionService;
    @Resource
    private WhPurchaseExpressRecordService whPurchaseExpressRecordService;

    @RequestMapping(method = { RequestMethod.GET })
    public String init(@ModelAttribute("domain") WhBoxDo domain) {
        initFormData(domain);
        return "warehouse/boxSHList";
    }

    private void initFormData(@ModelAttribute("domain") WhBoxDo domain) {
        List<BoxType> boxTypeList = Arrays.asList(BoxType.RECEIVE, BoxType.RECEIVE_TJ, BoxType.PF_ORDER, BoxType.WL_RECEIVE);
        BoxType[] boxTypeArrays = new BoxType[boxTypeList.size()];
        domain.setBoxTypes(SelectJson.getList(boxTypeList.toArray(boxTypeArrays)));
    }

    private void queryWhBoxs(@ModelAttribute("domain") WhBoxDo domain) {
        WhBoxQueryCondition query = domain.getQuery();
        Pager page = domain.getPage();
        if (query == null) {
            query = new WhBoxQueryCondition();
            domain.setQuery(query);
        }
        if (query.getType() == null) {
            query.setTypeList(BoxType.getShBoxIntCode());
        }
        query.setWhId(CacheUtils.getLocalWarehouseId());
        List<WhBox> whboxs = whBoxService.queryWhSHBoxs(query, page);
        domain.setWhBoxs(whboxs);
    }

    @RequestMapping(value = "search", method = { RequestMethod.POST })
    public String search(@ModelAttribute("domain") WhBoxDo domain) {
        initFormData(domain);
        queryWhBoxs(domain);
        return "warehouse/boxSHList";
    }

    @RequestMapping(value = "create", method = { RequestMethod.GET })
    public String toCreateWhBox(@ModelAttribute("domain") WhBoxDo domain) {
        return "warehouse/boxSHAdd";
    }

    @RequestMapping(value = "create", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson createWhBox(@ModelAttribute("domain") WhBoxDo domain, HttpSession session) {
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        WhBox whbox = domain.getWhBox();
        List<Integer> ids = new ArrayList<>();
        for (int i = 0; i < domain.getNum(); i++) {
            try {
                //whbox.setType(BoxType.RECEIVE.intCode());
                whbox.setWhId(CacheUtils.getLocalWarehouseId());
                whBoxService.createWhBoxSH(whbox);

                if (whbox.getId() != null) {
                    ids.add(whbox.getId());
                }

            }
            catch (Exception e) {
                logger.info(e.getMessage());
                continue;
            }
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            responseJson.setStatus(StatusCode.SUCCESS);
            responseJson.setMessage("成功添加：" + ids.size() + "个周转框！");
        }
        return responseJson;
    }

    @RequestMapping(value = "update", method = { RequestMethod.POST })
    public String updateWhBox(@ModelAttribute("domain") WhBoxDo domain, HttpSession session) {
        WhBox whbox = domain.getWhBox();
        whBoxService.updateWhBox(whbox);
        return "redirect:/{查询列表}";
    }

    @RequestMapping(value = "delete", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson deleteWhBox(@ModelAttribute("domain") WhBoxDo domain,
            @RequestParam("whboxId") Integer whboxId) {
        ResponseJson response = new ResponseJson();
        whBoxService.deleteWhBox(whboxId);
        return response;
    }

    @RequestMapping(value = "viewItems", method = { RequestMethod.GET })
    public String viewItems(@ModelAttribute("domain") WhBoxDo domain, @RequestParam("box_no") String boxNo) {
        WhBoxQueryCondition query = new WhBoxQueryCondition();
        query.setBoxNo(boxNo);
        WhBox whbox = whBoxService.viewItems(boxNo);
        domain.setWhBox(whbox);
        return "warehouse/boxSHItems";
    }

    @RequestMapping(value = "print", method = { RequestMethod.GET })
    public String print(@ModelAttribute("domain") WhBoxDo domain, @RequestParam("ids") List<Integer> whboxIds,
            @RequestParam(value = "remark", required = false) String remark) {
        WhBoxQueryCondition query = new WhBoxQueryCondition();
        query.setWhBoxIds(whboxIds);
        query.setTypeList(BoxType.getShBoxIntCode());
        List<WhBox> whboxs = whBoxService.queryWhBoxs(query, null);
        domain.setWhBoxs(whboxs);
        domain.setTitle(remark);
        return "warehouse/boxSHPrint";
    }

    @RequestMapping(value = "unbind", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson unbind(@ModelAttribute("domain") WhBoxDo domain, @RequestParam("type") Integer type,
            @RequestParam("ids") Integer id) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.SUCCESS);
        WhBoxQueryCondition query = new WhBoxQueryCondition();
        query.setId(id);
        query.setTypeList(BoxType.getShBoxIntCode());
        WhBox whbox = whBoxService.queryWhBox(query);
        if (type == 0 && BoxStatus.NOT_USED.intCode().equals(whbox.getStatus()) && whbox.getRelationNum() == 0) {
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage("当前周转框无需解绑！");
            return responseJson;
        }
        if (type == 1 && whbox.getPresentUser() == null) {
            responseJson.setStatus(StatusCode.FAIL);
            responseJson.setMessage("当前使用人为空，无需解绑！");
            return responseJson;
        }
        // 解锁
        int i = whBoxService.updateWhBoxSHOfUnbinding(type, id, false);
        if (i == 0) {
            responseJson.setStatus(StatusCode.FAIL);
        }
        return responseJson;
    }

    @RequestMapping(value = "unbindItem", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson unbindItem(@RequestParam("box_no") String boxNo,
                                   @RequestParam("relation_no") String relationNo,
                                   @RequestParam("order_type") Integer orderType) {
        ResponseJson responseJson = new ResponseJson();
        responseJson.setStatus(StatusCode.SUCCESS);
        // 解锁
        int i = whBoxService.unbindItem(boxNo, relationNo, orderType,false);
        if (i == 0) {
            responseJson.setStatus(StatusCode.FAIL);
        }
        // 手动解锁收货异常周转筐，需要调整订单状态为待入库
        if(Objects.equals(ItemOrderTypeEnum.WH_CHECK_IN_EXCEPTION.intCode(), orderType)){
            WhCheckInExceptionQueryCondition query = new WhCheckInExceptionQueryCondition();
            query.setId(Integer.parseInt(relationNo));
            WhCheckInException whCheckInException = whCheckInExceptionService.queryWhCheckInException(query);
            if (whCheckInException != null){
                String comment = "手动解锁收货异常周转筐"+boxNo+",标记待入库";
                whCheckInExceptionService.waitCheckInException(whCheckInException, comment);
            }
        }
        // 手动解锁物流收货周转筐，需要同时解绑物流单上的周转筐号
        if (Objects.equals(ItemOrderTypeEnum.WL_RECEIPT.intCode(), orderType)) {
            WhPurchaseExpressRecord expressRecord = whPurchaseExpressRecordService.getWhPurchaseExpressRecord(Integer.parseInt(relationNo));
            if (expressRecord != null){
                expressRecord.setUpdateBoxNoToNull(true);
                whPurchaseExpressRecordService.updateWhPurchaseExpressRecord(expressRecord);
            }
         }

        whBoxService.checkUnSplitCount(boxNo);
        return responseJson;
    }

    @RequestMapping(value = "success", method = { RequestMethod.GET })
    @ResponseBody
    public void success(@ModelAttribute("domain") WhBoxDo domain) {
        WhBoxQueryCondition query = domain.getQuery();
        whBoxService.updateWhBoxPrint(query.getWhBoxIds());
    }
}