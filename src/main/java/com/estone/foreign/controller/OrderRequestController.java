package com.estone.foreign.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.allocation.util.WarehouseProperties;
import com.estone.apv.bean.ApvOversize;
import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.apv.common.ApvStatus;
import com.estone.apv.enums.ApvOversizeStatus;
import com.estone.apv.enums.ApvTaxTypeEnum;
import com.estone.apv.service.ApvOversizeService;
import com.estone.apv.service.ApvStatusUpdateService;
import com.estone.apv.service.WhApvService;
import com.estone.asn.bean.WhAsn;
import com.estone.asn.bean.WhAsnExtra;
import com.estone.asn.bean.WhAsnQueryCondition;
import com.estone.asn.enums.AsnPackageMethodEnum;
import com.estone.asn.service.WhAsnCheckOutService;
import com.estone.asn.service.WhAsnExtraService;
import com.estone.asn.service.WhAsnService;
import com.estone.common.CacheName;
import com.estone.common.SaleChannel;
import com.estone.common.util.*;
import com.estone.common.util.model.ApiResult;
import com.estone.exquisite.bean.*;
import com.estone.exquisite.service.BoutiqueStockService;
import com.estone.exquisite.service.WhBoutiqueOutOrderService;
import com.estone.foreign.bean.*;
import com.estone.foreign.enums.OmsArgsStatusEnum;
import com.estone.foreign.service.OrderRequestService;
import com.estone.foreign.service.ReportRequestService;
import com.estone.pac.bean.TakeStockDTO;
import com.estone.pac.enums.RecordSourceEnum;
import com.estone.pac.service.TakeStockRecordService;
import com.estone.scan.deliver.service.WhScanShipmentToApvService;
import com.estone.statistics.bean.AssetChangeItem;
import com.estone.statistics.bean.AssetChangeItemCalcBean;
import com.estone.statistics.bean.AssetChangeItemQueryCondition;
import com.estone.statistics.service.AssetChangeItemService;
import com.estone.system.log.bean.WhSystemLog;
import com.estone.system.log.bean.WhSystemLogQueryCondition;
import com.estone.system.log.service.WhSystemLogService;
import com.estone.system.param.bean.SystemParam;
import com.estone.system.rabbitmq.model.PushApvStatusMessage;
import com.estone.system.user.bean.SaleUser;
import com.estone.temu.bean.*;
import com.estone.temu.enums.SourceFromEnum;
import com.estone.temu.enums.TemuOrderStatus;
import com.estone.temu.enums.TemuPackageStatus;
import com.estone.temu.service.TemuPrepareOrderItemService;
import com.estone.temu.service.TemuPrepareOrderService;
import com.estone.temu.service.TemuReturnPackageService;
import com.estone.transfer.bean.*;
import com.estone.transfer.enums.AsnPrepareStatus;
import com.estone.transfer.enums.AsnTagStatus;
import com.estone.transfer.enums.TmsSendMsgType;
import com.estone.transfer.service.TransferStockService;
import com.estone.transfer.service.WhFbaAllocationItemService;
import com.estone.transfer.service.WhFbaAllocationService;
import com.estone.transfer.service.WhFbaShipmentService;
import com.estone.warehouse.bean.WhStockMonitorsQueryCondition;
import com.whq.tool.action.BaseController;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import jodd.exception.ExceptionUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: wms
 * @description: 订单系统请求对外提供接口类
 * @author: wuhuiqiang
 * @create: 2020-01-03 16:05
 **/
@Slf4j
@Controller
@RequestMapping(value = "foreign/orders")
public class OrderRequestController extends BaseController {

    OrderRequestService orderRequestService = SpringUtils.getBean(OrderRequestService.class);

    @Resource
    private WhApvService whApvService;

    @Resource
    private WhSystemLogService whSystemLogService;

    @Resource
    private ReportRequestService reportRequestService;

    @Resource
    private WhAsnService whAsnService;

    @Resource
    private WhAsnCheckOutService whAsnCheckOutService;

    @Resource
    private WhFbaAllocationService whFbaAllocationService;

    @Resource
    private WhFbaAllocationItemService whFbaAllocationItemService;

    @Resource
    private TransferStockService transferStockService;

    @Resource
    private TakeStockRecordService takeStockRecordService;

    @Resource
    private AssetChangeItemService assetChangeItemService;

    @Resource
    private ApvOversizeService apvOversizeService;

    @Resource
    private WhBoutiqueOutOrderService whBoutiqueOutOrderService;

    @Resource
    private BoutiqueStockService boutiqueStockService;

    @Resource
    private WhAsnExtraService whAsnExtraService;

    @Resource
    private TemuPrepareOrderService temuPrepareOrderService;

    @Resource
    private TemuReturnPackageService temuReturnPackageService;

    @Resource
    private ApvStatusUpdateService apvStatusUpdateService;

    @Resource
    private TemuPrepareOrderItemService temuPrepareOrderItemService;

    @Resource
    private WhFbaShipmentService whFbaShipmentService;

    /**
     * OMS库存对比接口
     *
     * @param skuSet
     * @return
     */
    @RequestMapping(value = "validateStock", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson contrast(@RequestBody Set<String> skuSet) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(skuSet)) {
            response.setMessage("请求参数为空!");
            return response;
        }
        if (skuSet.size() > 10000) {
            response.setMessage("请求参数不能超过10000!，请分页查询");
            return response;
        }

        String skus = JSON.toJSONString(skuSet);
        skus = skus.replace("[", "").replace("]", "");
        log.warn(skus);
        // 查询可用库存和未匹配的库存
        // 可用 + 已拣返架 + 拣货缺货 + 取消 - 待分配订单的数量-拣货缺货
        List<Map<String, Object>> surplusStockList = DbTemplateUtils.executeSqlScript(
                " SELECT record.sku sku, " +
                        "  sum(surplus_quantity + pick_return_quantity + pick_not_quantity + cancel_quantity) + sum(IFNULL(f.frozen_quantity,0)) " +
                        " - (SELECT IFNULL(SUM(sale_quantity),0) FROM wh_apv_item item WHERE item.sku = record.sku " +
                        " AND apv_id IN (SELECT id FROM wh_apv WHERE `status` IN (1,8)))  count_surplus " +
                        " FROM wh_stock record LEFT JOIN frozen_stock f ON  f.stock_id = record.id " +
                        " Where record.sku in ("+skus+") group by record.sku;");

        Map<String, Integer> stockMap = new HashMap<>();
        // 转换格式
        surplusStockList.forEach(map -> stockMap.put(map.get("sku").toString(),
                map.get("count_surplus") == null ? null : Integer.valueOf(map.get("count_surplus").toString())));
        skuSet.removeAll(stockMap.keySet());
        // 无库存记录的项
        if (CollectionUtils.isNotEmpty(skuSet)){
            stockMap.putAll(skuSet.stream().collect(Collectors.toMap(sku -> sku, sku -> 0, (k1, k2) -> k1)));
        }
        response.setStatus(StatusCode.SUCCESS);
        response.setMessage(JSON.toJSONString(stockMap));
        return response;
    }

    @RequestMapping(value = "queryNewHouseSkuWaitAllot", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson queryNewHouseSkuWaitAllot(@RequestBody(required = true) Set<String> skuSet) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(skuSet)) {
            response.setMessage("请求参数为空!");
            return response;
        }
        String skus = JSON.toJSONString(skuSet);
        skus = skus.replace("[", "").replace("]", "");
        // 查询可用库存和未匹配的库存
        List<Map<String, Object>> surplusStockList = DbTemplateUtils
                .executeSqlScript("SELECT sku.sku sku, (SELECT SUM(sale_quantity) FROM wh_apv_item item "
                        + "WHERE item.sku = sku.sku AND apv_id IN (SELECT id FROM wh_apv WHERE `status` IN (1,8))) count_surplus "
                        + "FROM wh_sku sku WHERE sku.sku IN (" + skus + ");");
        Map<String, Integer> stockMap = new HashMap<>();
        // 转换格式
        surplusStockList.forEach(map -> stockMap.put(map.get("sku").toString(),
                map.get("count_surplus") == null ? null : Integer.valueOf(map.get("count_surplus").toString())));
        response.setStatus(StatusCode.SUCCESS);
        response.setMessage(JSON.toJSONString(stockMap));
        return response;
    }

    /**
     * 查询跨仓订单交运sku数量
     *
     * @param query
     * @return
     */
    @RequestMapping(value = "querySkuDeliverQuantity", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson querySkuDeliverQuantity(@RequestBody WhStockMonitorsQueryCondition query) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);

        response.setStatus(StatusCode.SUCCESS);
        response.setMessage(JSON.toJSONString(new ArrayList<Map<String, Object>>()));
        return response;
    }

    /**
     * 查询海外仓跨仓订单交运sku数量
     *
     * @param query
     * @return
     */
    @RequestMapping(value = "queryWhAsnSkuDeliverQuantity", method = { RequestMethod.POST })
    @ResponseBody
    public ResponseJson queryWhAsnSkuDeliverQuantity(@RequestBody WhStockMonitorsQueryCondition query) {
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        response.setStatus(StatusCode.SUCCESS);
        response.setMessage(JSON.toJSONString(new ArrayList<Map<String, Object>>()));
        return response;
    }

    @RequestMapping(value="pushSkuPreallocateStock/{type}", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson pushSkuPreallocateStock(@PathVariable("type") Integer type,@RequestBody List<String> skuList) {
        ResponseJson response = new ResponseJson();
        try {
            this.orderRequestService.pushSkuPreallocateStock(skuList,type);
            response.setMessage("成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("失败："+e.getMessage());
        }
        return response;
    }

    @RequestMapping(value="pushSkuReturnQty", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson pushSkuReturnQuantity(@RequestBody List<String> skuList) {
        ResponseJson response = new ResponseJson();
        try {
            this.orderRequestService.pushSkuReturnQuantity(skuList);
            response.setMessage("成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("失败："+e.getMessage());
        }
        return response;
    }

    @RequestMapping(value="pushAssetSumToReport", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson pushAssetSumToReport() {
        ResponseJson response = new ResponseJson();
        try {
            long startTime = System.currentTimeMillis();
            try {
                reportRequestService.pushAssetSumToReport();
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            log.warn("---invoke PushAssetSumToReportJob cost time: " + (System.currentTimeMillis() - startTime));
            response.setMessage("成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("失败："+e.getMessage());
        }
        return response;
    }

    @RequestMapping(value="pushSkuPreallocateStock", method = {RequestMethod.GET})
    @ResponseBody
    public ResponseJson pushSkuPreallocateStock() {
        ResponseJson response = new ResponseJson();
        try {
            WarehouseProperties properties = WarehouseProperties.getWarehouseProperties();
            Integer warehouseId = properties.getLocalWarehouseId();

            String ip = WarehouseProperties.getIp(warehouseId, properties);
            //ip = "http://************/wms";
            String url = "/foreign/orders/pushSkuPreallocateStock";

            List<Map<String, Object>> skuMap = DbTemplateUtils.executeSqlScript(
                    "select record.sku from  product_sku_record record INNER JOIN wh_sku s on  record.sku = s.sku" +
                            " where s.warehouse_id = "+warehouseId);

            Set<String> skuSet = skuMap.stream().map(map -> map.get("sku").toString()).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(skuSet)){
                response.setMessage("失败：无满足条件数据");
            }
            List<String> skuList = new ArrayList<>(skuSet);
            List<String> validSkuList = skuList.stream().filter(s -> StringUtils.isNotBlank(s)).collect(Collectors.toList());
            log.info("PushStockToProduct Count:"+validSkuList.size());

            // 分配保存数据 存在即跟新
            List<String> recordsList = null;
            do{
                try{
                    // subList 返回的是 被截取集合的视图，清空截取集合，被截取集合对应数据
                    // 也会被清除，释放内存
                    if (CollectionUtils.isNotEmpty(recordsList)) recordsList.clear();
                    recordsList = validSkuList.subList(0, validSkuList.size()>4999 ? 5000:validSkuList.size());

                    ResponseJson response1 = HttpExtendUtils.post(ip + url, "", recordsList, ResponseJson.class, 300000,300000);
                    if (response1.isSuccess()){
                        DbTemplateUtils.executeSqlScript(
                                "update product_sku_record set status =1 where sku in ("+ StringUtils.join(recordsList,",")+")");
                    }
                } catch (Exception e) {
                    log.error("推送库存失败："+e.getMessage());
                    log.error(e.getMessage(), e);
                }
            }while (!(recordsList.size()<5000));
            response.setMessage("成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("失败："+e.getMessage());
        }
        return response;
    }


    @RequestMapping(value="queryApvBySkuAndStatus", method = {RequestMethod.POST})
    @ResponseBody
    public ResponseJson queryApvBySkuAndStatus(@RequestBody List<String> skuList) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (CollectionUtils.isEmpty(skuList)) {
            response.setMessage("请求参数为空!");
            return response;
        }

        // 查询满足条件的apv
        List<Map<String, Object>> apvList = DbTemplateUtils
                .executeSqlScript("select wa.apv_no as apvNo from wh_apv wa" +
                        " inner join wh_apv_item wai on wa.id = wai.apv_id" +
                        " where wa.status in ('10','12','14','15','16')" +
                        " and wai.sku in ('"+StringUtils.join(skuList, "','")+"')");
        if (CollectionUtils.isNotEmpty(apvList)){
            List<String> apvNoList = apvList.stream().map(apv -> apv.get("apvNo").toString()).collect(Collectors.toList());
            response.setMessage(JSON.toJSONString(apvNoList));
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @RequestMapping(value = "adjustOrderAllocationStock",method = RequestMethod.GET)
    public ResponseJson adjustOrderAllocationStock()  {

        ResponseJson response = new ResponseJson();
        try {
            this.orderRequestService.adjustOrderAllocationStock2();
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage("成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setStatus(StatusCode.FAIL);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     * 中转仓发货单，shein平台的接收追踪单号
     * @param whFbaAllocation 接收的订单对象
     * @return 响应结果
     */
    @PostMapping(value = "updateFbaAllocation")
    @ResponseBody
    public ResponseJson updateFbaAllocation(@RequestBody WhFbaAllocation whFbaAllocation){
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if (Objects.isNull(whFbaAllocation)){
            response.setMessage("参数错误");
            return response;
        }
        try {
            if(StringUtils.isBlank(whFbaAllocation.getFbaNo())){
                throw new Exception(String.format("中转仓出库单[%s]出库单为空!",whFbaAllocation.getFbaNo()));
            }
            if(StringUtils.isBlank(whFbaAllocation.getTrackingNumber())){
                throw new Exception(String.format("中转仓出库单[%s]追踪号为空!",whFbaAllocation.getFbaNo()));
            }
            String message = whFbaAllocationService.doUpdateFbaAllocation(whFbaAllocation);
            response.setMessage(message);
            if (StringUtils.isNotBlank(message)){
                return response;
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            response.setMessage(e.getMessage());
            return response;
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;

    }

    /**
     * 用于更新标签信息
     *
     * @param whFbaAllocation
     * @return
     */
    @RequestMapping(value = "updateTags", method = RequestMethod.POST)
    @ResponseBody
    public ResponseJson updateTags(@RequestBody WhFbaAllocation whFbaAllocation) {
        ResponseJson response = new ResponseJson();
        if (whFbaAllocation == null) {
            response.setMessage("参数错误");
            response.setStatus(StatusCode.FAIL);
            return response;
        }
        try {
            if (StringUtils.isBlank(whFbaAllocation.getFbaNo())) {
                throw new Exception(String.format("海外出库单[%s]出库单为空!", whFbaAllocation.getFbaNo()));
            }

            List<WhFbaAllocationItem> items = whFbaAllocation.getItems();
            boolean lackItemMessage = Optional.ofNullable(items)
                    .orElse(new ArrayList<>())
                    .stream()
                    .allMatch(item -> StringUtils.isBlank(item.getProductSku()) || StringUtils.isBlank(item.getTemuCodeUrl()));
            WhAsnExtra whAsnExtra = whFbaAllocation.getWhAsnExtra();
            boolean lackAsnExtraMessage = Objects.isNull(whAsnExtra)
                    || (StringUtils.isBlank(whAsnExtra.getBoxMarkUrl()) && StringUtils.isBlank(whAsnExtra.getCollectLabelUrl()));
            if (lackItemMessage && lackAsnExtraMessage) {
                throw new Exception(String.format("海外出库单[%s]无更新明细数据!", whFbaAllocation.getFbaNo()));
            }

            String message = whFbaAllocationService.doUpdateTags(whFbaAllocation);
            response.setMessage(message);
            if (StringUtils.isNotBlank(message)) {
                response.setStatus(StatusCode.FAIL);
                return response;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setStatus(StatusCode.FAIL);
            response.setMessage(e.getMessage());
            return response;
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    // 生成FBA货件信息
    @RequestMapping(value = "generateWhAsn",method = RequestMethod.POST)
    @ResponseBody
    public ResponseJson generateFbaAllocation(@RequestBody WhFbaAllocation whFbaAllocation){
        ResponseJson response = new ResponseJson();
        if (whFbaAllocation == null){
            response.setMessage("参数错误");
            response.setStatus(StatusCode.FAIL);
            return response;
        }
        try {
            if(StringUtils.isBlank(whFbaAllocation.getFbaNo())){
                throw new Exception(String.format("海外出库单[%s]出库单为空!",whFbaAllocation.getFbaNo()));
            }
            if(StringUtils.isBlank(whFbaAllocation.getAccountNumber())){
                throw new Exception(String.format("海外出库单[%s]帐号为空!",whFbaAllocation.getFbaNo()));
            }
            if(!whFbaAllocation.isFba() && !whFbaAllocation.isPrepareTemu() && StringUtils.isBlank(whFbaAllocation.getSite())){
                throw new Exception(String.format("海外出库单[%s]站点为空!",whFbaAllocation.getFbaNo()));
            }
            if(StringUtils.isBlank(whFbaAllocation.getPurposeHouse())){
                throw new Exception(String.format("海外出库单[%s]备货仓库为空!",whFbaAllocation.getFbaNo()));
            }
            if(!whFbaAllocation.isFba()) { // TODO liurui FBA逻辑临时注释
                if (StringUtils.isBlank(whFbaAllocation.getShipmentId())) {
                    throw new Exception(String.format("海外出库单[%s]货件号为空!", whFbaAllocation.getFbaNo()));
                }
            }
            if(StringUtils.isBlank(whFbaAllocation.getSmCode()) && !whFbaAllocation.isPrepareTemu()){
                throw new Exception(String.format("海外出库单[%s]发货方式为空!",whFbaAllocation.getFbaNo()));
            }
            List<WhFbaAllocationItem> items = whFbaAllocation.getItems();
            if (CollectionUtils.isEmpty(items)){
                throw new Exception(String.format("海外出库单[%s]无明细数据!",whFbaAllocation.getFbaNo()));
            }

            WhAsnExtra whAsnExtra = whFbaAllocation.getWhAsnExtra();
            if (whAsnExtra == null){
                throw new Exception(String.format("海外出库单[%s]无收货信息!",whFbaAllocation.getFbaNo()));
            }
            if(whFbaAllocation.isTemu() && !whFbaAllocation.isPrepareTemu()){
                if (StringUtils.isBlank(whAsnExtra.getReceiptPerson()) || StringUtils.isBlank(whAsnExtra.getPhoneNumber())) {
                    throw new Exception(String.format("海外出库单[%s]收件人姓名或手机号缺失!",whFbaAllocation.getFbaNo()));
                }
            }
            if(whFbaAllocation.isPrepareTemu()){
                /*if (StringUtils.isBlank(whAsnExtra.getReceiptPerson()) || StringUtils.isBlank(whAsnExtra.getPhoneNumber())) {
                    throw new Exception(String.format("海外出库单[%s]收件人姓名或手机号缺失!",whFbaAllocation.getFbaNo()));
                }*/
                if (StringUtils.isBlank(whAsnExtra.getReceiptAddress())) {
                    throw new Exception(String.format("海外出库单[%s]收货地址缺失!",whFbaAllocation.getFbaNo()));
                }
                if (StringUtils.isBlank(whFbaAllocation.getSalesperson())) {
                    throw new Exception(String.format("海外出库单[%s]销售员缺失!",whFbaAllocation.getFbaNo()));
                }
                boolean bool = items.stream().anyMatch(i -> (StringUtils.isBlank(i.getProductSku())));
                if (bool){
                    throw new Exception(String.format("海外出库单[%s]存在sku为空的项!",whFbaAllocation.getFbaNo()));
                }
                bool = items.stream().anyMatch(i -> (Objects.isNull(i.getQuantity())));
                if (bool){
                    throw new Exception(String.format("海外出库单[%s]存在数量为空的项!",whFbaAllocation.getFbaNo()));
                }
                if (StringUtils.isBlank(whFbaAllocation.getPackageSn())){
                    throw new Exception(String.format("海外出库单[%s]存在包裹号为空的项!",whFbaAllocation.getFbaNo()));
                }
            }
           /* boolean bool = items.stream().anyMatch(i -> (StringUtils.isBlank(i.getSellSku())||StringUtils.isBlank(i.getFnSku())));
            if (bool && whFbaAllocation.isFba()){
                throw new Exception(String.format("海外出库单[%s]存在sellerSku或FNSku为空的项!",whFbaAllocation.getFbaNo()));
            }*/

            if (whFbaAllocation.isClothingBoutique()) {
                //判断是否操作库存记录
                whBoutiqueOutOrderService.doExistsStockRecord(whFbaAllocation);
                whBoutiqueOutOrderService.doGenerateBoutiqueOutOrder(whFbaAllocation);
                response.setStatus(StatusCode.SUCCESS);
                return response;
            }
            // 区分是否仓发还是jit
            if (whFbaAllocation.getIsAsn() == null || !whFbaAllocation.getIsAsn()) {
                // jit迁移到本地仓
                if (whFbaAllocation.isJit() || whAsnExtra.getBizType() != null && whAsnExtra.getBizType().equals(288000)) {
                    log.info("doGenerateJITAllocation ShipmentId:" + JSONObject.toJSONString(whFbaAllocation));
                    // 半托管
                    if (whAsnExtra.getBizType() != null && whAsnExtra.getBizType().equals(288000)) {
                        whAsnExtra.setPackageMethod(AsnPackageMethodEnum.JIT_HALF.getCode());
                    }
                    List<String> lockKeys = items.stream().map(WhFbaAllocationItem::getProductSku).distinct()
                            .collect(Collectors.toList());
                    if (whFbaAllocation.getItems().stream().anyMatch(WhFbaAllocationItem::isZhSuit)) {
                        lockKeys.addAll(items.stream().filter(WhFbaAllocationItem::isZhSuit)
                                .map(WhFbaAllocationItem::getSaleSuiteArticleNumber).distinct()
                                .collect(Collectors.toList()));
                    }
                    return apvStatusUpdateService.createAndAllotJitApv(lockKeys, whFbaAllocation);
                }
            } else {
                //仓发赋值bizType值
                if (AsnPackageMethodEnum.JIT_HALF.getCode().equals(whAsnExtra.getPackageMethod())){
                    whAsnExtra.setBizType(7668000);
                    whFbaAllocation.setTags(AsnTagStatus.getCodeByName(whFbaAllocation.getTags()));
                } else if (AsnPackageMethodEnum.JIT.getCode().equals(whAsnExtra.getPackageMethod())){
                    whAsnExtra.setBizType(5110000);
                    whFbaAllocation.setTags(AsnTagStatus.getCodeByName(whFbaAllocation.getTags()));
                }
                // 接收oms推送地发货单号和LBX号
              /*  for (WhFbaAllocationItem item : items) {
                    item.setTag(whFbaAllocation.getConsignOrderNo());
                    item.setTemuTagUrl(whFbaAllocation.getLogisticsNo());
                }*/
            }

            String message = null;
            if(whFbaAllocation.isPrepareTemu()){
                log.info("doGenerateFbaAllocation ShipmentId:" + JSONObject.toJSONString(whFbaAllocation));
                TemuPrepareOrder order = whFbaAllocation.buildTemuPrepareOrder();
                ApiResult<?> apiResult = this.checkTemuPrepareOrder(order);
                if (apiResult.isSuccess()){
                    String receiveHouseId = temuPrepareOrderService.matchReceiveHouseId(order.getReceiveHouse());
                    order.setReceiveHouseId(receiveHouseId);
                    apiResult = temuPrepareOrderService.doAccept(order);
                }
                message = apiResult.getErrorMsg();
            }else {
                message = whFbaAllocationService.doGenerateFbaAllocation(whFbaAllocation);
            }
            response.setMessage(message);
            if (StringUtils.isNotBlank(message)){
                response.setStatus(StatusCode.FAIL);
                return response;
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            response.setStatus(StatusCode.FAIL);
            response.setMessage(e.getMessage());
            return response;
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }



    /**
     * fba 取消
     */
    @RequestMapping(value = "cancelFbaAllocation",method = RequestMethod.GET)
    @ResponseBody
    public ResponseJson cancelFbaAllocation(@RequestParam("fbaNo") String fbaNo, @RequestParam(value = "isJit", required = false) Boolean isJit){
        ResponseJson response = new ResponseJson();
        if (StringUtils.isBlank(fbaNo)){
            response.setMessage("参数错误");
            response.setStatus(StatusCode.FAIL);
            return response;
        }
        try {

            //jit取消
            if (isJit!=null && isJit){
                return whApvService.doCancelJitApv(fbaNo);
           }

            //精品取消
            WhBoutiqueOutOrderQueryCondition outOrderQueryCondition=new WhBoutiqueOutOrderQueryCondition();
            outOrderQueryCondition.setApvNo(fbaNo);
            List<WhBoutiqueOutOrder> whBoutiqueOutOrders = whBoutiqueOutOrderService.queryWhBoutiqueOutOrderAndItemList(outOrderQueryCondition, null);
            if (CollectionUtils.isNotEmpty(whBoutiqueOutOrders)){
                List<WhBoutiqueOutOrderItem> items = whBoutiqueOutOrders.get(0).getItems();
                List<String> skuList = items.stream().map(WhBoutiqueOutOrderItem::getSku).collect(Collectors.toList());
                whBoutiqueOutOrderService.doCancelBoutiqueOutOrder(skuList,whBoutiqueOutOrders.get(0));
            }


            WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
            queryCondition.setFbaNo(fbaNo);
            List<WhFbaAllocation> existWhFbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(queryCondition,null);
            if (CollectionUtils.isEmpty(existWhFbaAllocationList)){
              /*  response.setMessage("海外仓发货单号不存在");
                response.setStatus(StatusCode.FAIL);
                return response;*/
                response.setStatus(StatusCode.SUCCESS);
                return response;
            }
            // 取消出库单
            whFbaAllocationService.doCancelFbaAllocation(existWhFbaAllocationList.get(0));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setStatus(StatusCode.FAIL);
            response.setMessage(e.getMessage());
            return response;
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @PostMapping(value = "notifyToGrid")
    @ResponseBody
    public ApiResult<?> pushFbaWaitGridStatusToOms(@RequestBody List<String> fbaNos) {
        try {
            if (CollectionUtils.isEmpty(fbaNos)){
                log.info("fbaNo为空");
                return ApiResult.newError("fbaNo为空");
            }
            WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
            queryCondition.setFbaNo(StringUtils.join(fbaNos,","));
            List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(queryCondition, null);
            if (CollectionUtils.isEmpty(whFbaAllocations)) {
                log.info("根据订单号"+JSON.toJSONString(fbaNos)+"查询fba发货单，其对象不存在");
                return ApiResult.newError("根据订单号"+JSON.toJSONString(fbaNos)+"查询fba发货单，其对象不存在");
            }
            whFbaAllocations = whFbaAllocations.stream()
                    .filter(f -> Objects.equals(AsnPrepareStatus.WAITING_GRID_CONFIRM.intCode(),f.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(whFbaAllocations)) {
                log.info("不存在待播种确认状态的fba订单!");
                return ApiResult.newError("不存在待播种确认状态的fba订单!");
            }
            String esg = whFbaAllocationService.notifyFbaToGrid(whFbaAllocations);
            if (StringUtils.isNotBlank(esg)){
                return ApiResult.newError(esg);
            }
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess();
    }

    /**
     * fba 部分取消
     */
    @RequestMapping(value = "cancelPortionFbaSku",method = RequestMethod.POST)
    @ResponseBody
    public ResponseJson cancelPortionFbaSku(@RequestBody WhFbaAllocation allocation){
        log.info("fba部分sku取消："+allocation);
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (allocation==null){
            response.setMessage("参数为空！");
            return response;
        }
        if(allocation.getFbaNo()==null){
            response.setMessage("发货单号不能为空！");
            return response;
        }
        List<WhFbaAllocationItem> items = allocation.getItems();
        if(CollectionUtils.isEmpty(items)){
            response.setMessage(allocation.getFbaNo()+" 发货单明细数据不能为空！");
            return response;
        }
        boolean allMatch = items.stream().allMatch(s -> s.getFnSku() != null && s.getProductSku() != null && s.getSkuQuantity()>0 && s.getQuantity()>0);

        if (!allMatch){
            response.setMessage(allocation.getFbaNo()+" 发货单明细数据SKU、FNSKU不能为空 并且 取消数量不能小于等于零！");
            return response;
        }

        try {
            response=whFbaAllocationService.doCancelPortionFbaSku(allocation);
        } catch (Exception e) {
            response.setMessage(e.getMessage());
            log.error(e.getMessage(), e);
        }
        return response;
    }




    /**
     * temu 部分取消
     */
    @RequestMapping(value = "cancelPortionTemuSku/{prepareOrderNo}",method = RequestMethod.POST)
    @ResponseBody
    public ResponseJson cancelPortionTemuSku(@PathVariable("prepareOrderNo")String prepareOrderNo,@RequestBody List<String> skus){
        log.info(String.format("temu发货单【%s】部分sku【%s】",prepareOrderNo,JSONObject.toJSONString(skus)));
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (StringUtils.isBlank(prepareOrderNo) || CollectionUtils.isEmpty(skus)){
            response.setMessage("参数为空！");
            return response;
        }
        TemuPrepareOrderQueryCondition query = new TemuPrepareOrderQueryCondition();
        query.setPrepareOrderNo(prepareOrderNo);
        List<TemuPrepareOrder> temuPrepareOrderList = temuPrepareOrderService.queryTemuPrepareOrderAndItems(query, null);
        if (CollectionUtils.isEmpty(temuPrepareOrderList)) {
            response.setMessage("发货单数据为空！");
            return response;
        }
        TemuPrepareOrder temuPrepareOrder = temuPrepareOrderList.get(0);
        List<TemuPrepareOrderItem> itemList = temuPrepareOrder.getItemList();
        boolean waitMerged = itemList.stream()
                .allMatch(i -> TemuPackageStatus.WAIT_MERGED.intCode().equals(i.getPackageStatus()));
        if (!TemuOrderStatus.WAITING_ALLOT.intCode().equals(temuPrepareOrder.getStatus())
                && !TemuOrderStatus.ALLOT.intCode().equals(temuPrepareOrder.getStatus()) && !waitMerged) {
            response.setMessage("发货单非待分配、已分配、待合单状态，不允许取消sku！");
            return response;
        }
        // 校验是否有拣货数量
        long count = itemList.stream().map(TemuPrepareOrderItem::getSku).distinct().count();
        if (count == 1){
            response.setMessage("发货单只有一种类型的sku，请取消整个发货单！");
            return response;
        }
        Map<String,Integer> returnQtyMap=new HashMap<>();
        for (String sku : skus) {
            List<TemuPrepareOrderItem> temuPrepareOrderItems = itemList.stream().filter(s -> sku.equals(s.getSku())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(temuPrepareOrderItems)) {
                response.setMessage(String.format("发货单明细数据SKU[%s]不存在！", sku));
                return response;
            }
            int pickQuantity = temuPrepareOrderItems.stream().mapToInt(s -> Optional.ofNullable(s.getPickQuantity()).orElse(0)).sum();
            if (pickQuantity > 0) {
                response.setMessage(String.format("发货单明细数据SKU[%s]已拣货，不允许废弃！",sku));
                return response;
            }
            int allotQuantity = temuPrepareOrderItems.stream().mapToInt(s -> Optional.ofNullable(s.getAllotQuantity()).orElse(0)).sum();
            if (allotQuantity > 0)
                returnQtyMap.put(sku,allotQuantity);
        }
        // 执行修改
        try {

            temuPrepareOrderService.doCancelPortionTemuSku(skus,temuPrepareOrder,returnQtyMap);
        } catch (Exception e) {
            response.setMessage(e.getMessage());
            log.error(e.getMessage(), e);
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }


    /**
     * 海外仓 部分取消
     */
    @RequestMapping(value = "cancelPortionAsnSku",method = RequestMethod.POST)
    @ResponseBody
    public ResponseJson cancelPortionAsnSku(@RequestBody WhFbaAllocation allocation){
        log.info(String.format("海外仓部分sku取消：%s",allocation));
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (allocation==null){
            response.setMessage("参数为空！");
            return response;
        }
        if(allocation.getFbaNo()==null){
            response.setMessage("发货单号不能为空！");
            return response;
        }
        List<WhFbaAllocationItem> items = allocation.getItems();
        if(CollectionUtils.isEmpty(items)){
            response.setMessage(allocation.getFbaNo()+" 发货单明细数据不能为空！");
            return response;
        }
        boolean allMatch = items.stream().allMatch(s -> StringUtils.isNotBlank(s.getSku()) && s.getQuantity()!=null &&  s.getQuantity()>0);

        if (!allMatch){
            response.setMessage(allocation.getFbaNo()+" 发货单明细数据存在SKU为空并且取消数量小于零！");
            return response;
        }
        items.forEach(whFbaAllocationItem->{
            whFbaAllocationItem.setSkuQuantity(whFbaAllocationItem.getQuantity());
            whFbaAllocationItem.setProductSku(whFbaAllocationItem.getSku());
        });

        try {
            response=whFbaAllocationService.doCancelPortionFbaSku(allocation);
        } catch (Exception e) {
            response.setMessage(e.getMessage());
            log.error(e.getMessage(), e);
        }
        return response;
    }

    /**
     * fba 生成调拨需求
     */
    @RequestMapping("genAllocationDemand")
    @ResponseBody
    public ResponseJson genAllocationDemand(@RequestBody List<WhFbaAllocationData> allocationDatas) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            if (CollectionUtils.isNotEmpty(allocationDatas)) {
                boolean bool = allocationDatas.stream().anyMatch(a -> StringUtils.isBlank(a.getAccountNumber())||a.getAllocationQuantity()==null
                                                                        ||StringUtils.isEmpty(a.getSku())
                );
                if (bool){
                    response.setMessage("参数错误，存在店铺、调拨数量、SKU为空项!");
                    return response;
                }
                Map<String, WhFbaAllocationData> itemMap = new HashMap<>();
                for (WhFbaAllocationData allocationData : allocationDatas) {
                    itemMap.put(allocationData.getSku(), allocationData);
                }
                // 从本仓可用到中转仓可用  sellerSku + sku 集合 同时加锁
                List<String> skus = allocationDatas.stream().map(a -> a.getSku()).collect(Collectors.toList());
                List<String> allSkuList = new ArrayList<>(skus);
                for (String sku : skus) {
                    if (itemMap.get(sku) != null) allSkuList.add(itemMap.get(sku).getItemLockKeys(allocationDatas.get(0).getNoPrefixAccountNumber()));
                }
                // 生成调拨需求
                whFbaAllocationService.doGenAllocationDemand(allSkuList, allocationDatas);
                response.setStatus(StatusCode.SUCCESS);
                response.setMessage("生成成功");
                return response;
            }
            response.setMessage("params is null");
            return response;
        } catch (Exception e) {
            response.setMessage("系统异常，" + e.getMessage());
            return response;
        }
    }

    /**
     * fba 查询中转仓库存
     */
    @GetMapping("getFbaStock")
    @ResponseBody
    public ResponseJson getFbaStock(@RequestParam("accountNumber") String accountNumber,
                                    @RequestParam(value = "site",required = false) String site,
                                    @RequestParam("sku") String sku,
                                    @RequestParam("saleChannel") String saleChannel) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            if (StringUtils.isNotEmpty(accountNumber)
                    && StringUtils.isNotEmpty(saleChannel)
                        && StringUtils.isNotBlank(sku)) {
                // 获取 可用+已拣返架
                TransferStockQueryCondition queryCondition = new TransferStockQueryCondition();
                if (StringUtils.equalsIgnoreCase(SaleChannel.CHANNEL_AMAZON, saleChannel)){
                    queryCondition.setStore(accountNumber.substring(accountNumber.indexOf("-") + 1));
                }else {
                    if (!StringUtils.equalsIgnoreCase(SaleChannel.CHANNEL_TEMU, saleChannel) && StringUtils.isBlank(site)){
                        response.setMessage("params is null");
                        return response;
                    }
                    queryCondition.setStore(accountNumber);
                    queryCondition.setSite(site);
                }
                queryCondition.setSku(sku);
                List<TransferStock> transferStocks = transferStockService.queryTransferStocks(queryCondition, null);

                if (CollectionUtils.isEmpty(transferStocks)){
                    response.setMessage(String.format("sku[%s]无库存记录!",sku));
                    return response;
                }
                // 待分配FBA货件占用库存
               /* WhFbaAllocationQueryCondition condition = new WhFbaAllocationQueryCondition();
                condition.setStatus(AsnPrepareStatus.WAITING_ALLOT.intCode());
                condition.setAccountNumber(accountNumber);
                condition.setSite(site);
                condition.setSku(sku);
                List<WhFbaAllocation> whFbaAllocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(condition, null);
                 // 待分配总库存
                int totalWaitSkuQty = 0;
                if (CollectionUtils.isNotEmpty(whFbaAllocationList)){
                    for(WhFbaAllocation whFbaAllocation:whFbaAllocationList){
                        if (CollectionUtils.isNotEmpty(whFbaAllocation.getItems())){
                            int sum = whFbaAllocation.getItems().stream().mapToInt(item -> item.getSkuQuantity() == null ? 0:item.getSkuQuantity()).sum();
                            totalWaitSkuQty+=sum;
                        }
                    }
                }*/
                int totalSkuQty = transferStocks.stream().collect(Collectors.summingInt(c ->c.getSurplusQuantity() + c.getPickReturnQuantity()));

                int pickReturnQuantity = transferStocks.stream().mapToInt(t -> t.getPickReturnQuantity()).sum();
                log.info("getFbaStock sku["+sku+"],totalSkuQty["+totalSkuQty+"],pickReturnQuantity["+pickReturnQuantity+"]");
                response.setStatus(StatusCode.SUCCESS);
                if (totalSkuQty<0) totalSkuQty = 0;
                response.setMessage(totalSkuQty+"");

                Map<String,Object> map=new HashMap<>();
                map.put("totalSkuQty",totalSkuQty);
                map.put("pickReturnQuantity",pickReturnQuantity);
                response.setBody(map);
                return response;
            }
            response.setMessage("params is null");
            return response;
        } catch (Exception e) {
            response.setMessage("系统异常，" + e.getMessage());
            return response;
        }
    }

    /**
     * 查询精品sku库存
     */
    @GetMapping("getBoutiqueStock")
    @ResponseBody
    public ResponseJson getBoutiqueStock(@RequestParam("accountNumber") String accountNumber, @RequestParam(value = "site",required = false) String site, @RequestParam("sku") String sku) {
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            if (StringUtils.isBlank(accountNumber) || StringUtils.isBlank(sku)) {
                response.setMessage("店铺或sku为空");
                return response;
            }
            BoutiqueStockQueryCondition queryCondition=new BoutiqueStockQueryCondition();
            queryCondition.setStore(accountNumber.substring(accountNumber.indexOf("-") + 1));
           // queryCondition.setSite(site);
            queryCondition.setSku(sku);
            List<BoutiqueStock> boutiqueStocks = boutiqueStockService.queryBoutiqueStocks(queryCondition, null);
            if (CollectionUtils.isEmpty(boutiqueStocks)){
                response.setMessage(String.format("sku[%s]无库存记录!",sku));
                return response;
            }
            int totalSkuQty = boutiqueStocks.stream().collect(Collectors.summingInt(c ->Optional.ofNullable(c.getGpSurplusQty()).orElse(0) ));
            log.info("getBoutiqueStock sku["+sku+"],boutiqueStock["+totalSkuQty+"]");
            if (totalSkuQty<0) totalSkuQty = 0;
            response.setMessage(totalSkuQty+"");
            response.setStatus(StatusCode.SUCCESS);
            return response;
        } catch (Exception e) {
            response.setMessage("系统异常，" + e.getMessage());
            return response;
        }
    }


    @RequestMapping(value = "uploadBoxInfoComplete",method = RequestMethod.GET)
    @ResponseBody
    public ResponseJson generateWhAsn(@RequestParam("receivingCode") String receivingCode){
        ResponseJson response = new ResponseJson();
        WhFbaAllocationQueryCondition query = new WhFbaAllocationQueryCondition();
        query.setFbaNo(receivingCode);
        List<WhFbaAllocation> allocationList = whFbaAllocationService.queryWhFbaAllocationAndItems(query, null);
        if (CollectionUtils.isEmpty(allocationList)){
            response.setMessage("参数错误，FBA出库单号不存在");
            response.setStatus(StatusCode.FAIL);
            return response;
        }
        try {
            whFbaAllocationService.uploadBoxInfoComplete(allocationList.get(0));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setStatus(StatusCode.FAIL);
            response.setMessage(e.getMessage());
            return response;
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    @RequestMapping(value = "abandonedWhAsn",method = RequestMethod.GET)
    @ResponseBody
    public ResponseJson cancelWhAsn(@RequestParam("receivingCode") String receivingCode){
        ResponseJson response = new ResponseJson();
        if (StringUtils.isBlank(receivingCode)){
            response.setMessage("参数错误");
            response.setStatus(StatusCode.FAIL);
            return response;
        }
        try {
            // 获取仓库属性
            Integer warehouseId = WarehouseProperties.getWarehouseProperties().getLocalWarehouseId();
            WhAsnQueryCondition queryCondition = new WhAsnQueryCondition();
            queryCondition.setReceivingCode(receivingCode);
            List<WhAsn> existWhAsnList = whAsnService.queryWhAsnsAndItems(queryCondition,null);
            if (CollectionUtils.isEmpty(existWhAsnList)){
                response.setMessage("出库单号不存在");
                response.setStatus(StatusCode.FAIL);
                return response;
            }
            // 取消出库单
            whAsnCheckOutService.doCancelAsn(existWhAsnList.get(0), warehouseId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setStatus(StatusCode.FAIL);
            response.setMessage(e.getMessage());
            return response;
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * OMS查询交运状态接口
     */
    @RequestMapping(value = "getOrderStatus/{platformOrderId}", method = RequestMethod.GET)
    @ResponseBody
    public ApiResult<List<PushApvStatusMessage>> getOrderStatus(@PathVariable("platformOrderId") List<String> platformOrderId) {
        ApiResult<List<PushApvStatusMessage>> response = new ApiResult<>();
        response.setSuccess(false);
        if (CollectionUtils.isEmpty(platformOrderId)){
            response.setErrorMsg("参数异常");
            return response;
        }
        try {
            WhApvQueryCondition apvQuery = new WhApvQueryCondition();
            apvQuery.setPlatformOrderIdList(platformOrderId);
            //只查询已交运、已装车的订单
            apvQuery.setStatusList(Arrays.asList(ApvStatus.DELIVER.intCode(), ApvStatus.LOADED.intCode()));
            List<WhApv> whApvList= whApvService.queryWhApvAndItemList(apvQuery, null);
            if (CollectionUtils.isEmpty(whApvList)) {
                response.setErrorMsg("查无此订单");
                return response;
            }
            List<PushApvStatusMessage> pushApvStatusMessages = new ArrayList<>();
            orderRequestService.getOrderStatus(whApvList, pushApvStatusMessages);
            response.setResult(pushApvStatusMessages);
            response.setSuccess(true);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            response.setErrorMsg(e.getMessage());
        }
        return response;
    }

    /**
     * 提供给OMS查询优选仓追踪号
     */
    @RequestMapping(value = "moveInterceptor", method = RequestMethod.POST)
    @ResponseBody
    public ApiResult<List<WhApvOrderInfoDTO>> getPacOrderInfo(@RequestBody WhApvOrderInfoDTO whApvOrderDTO) {
        log.info("OMS拦截订单:"+ JSONObject.toJSONString(whApvOrderDTO));
        ApiResult<List<WhApvOrderInfoDTO>> response = new ApiResult<>();
        response.setSuccess(false);
        try {
            WhApv whApv = whApvService.matchPacOrderInfoList(whApvOrderDTO);
            if (ApvStatus.DELIVER.equals(whApv.getStatus()) || ApvStatus.LOADED.equals(whApv.getStatus())) {
                response.setErrorMsg("该订单已交运！");
                return response;
            }
            if (ApvStatus.CANCEL.equals(whApv.getStatus())){
                response.setSuccess(true);
                return response;
            }
            whApvService.batchInterceptorWhApv(whApv,whApvOrderDTO.getInterceptor(), ApvTaxTypeEnum.INTERCEPTOR.getCode());
            response.setSuccess(true);
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            response.setErrorMsg(e.getMessage());
        }
        return response;
    }


    /**
     * OMS查询订单日志接口
     * @param orderNo
     * @return
     */
    @RequestMapping(value = "log/{orderNo}/{status}", method = RequestMethod.GET)
    @ResponseBody
    public ApiResult<List<ApvLog>> log(@PathVariable("orderNo") String orderNo,@PathVariable("status") Integer status) {
        ApiResult<List<ApvLog>> response = new ApiResult<List<ApvLog>>();
        response.setSuccess(false);
        if (StringUtils.isBlank(orderNo)||StringUtil.isEmpty(OmsArgsStatusEnum.getNameByCode(status))){
            response.setErrorMsg("参数异常");
            return response;
        }
        try {
            WhApvQueryCondition apvQuery = new WhApvQueryCondition();
            if (OmsArgsStatusEnum.SALES_RECORD_NUMBER.getCode().equals(status)){
                apvQuery.setSalesRecordNumber(orderNo);
            }
            if (OmsArgsStatusEnum.PLATFORM_ORDER.getCode().equals(status)){
                apvQuery.setPlatformOrderId(orderNo);
            }
            List<WhApv> apvs = whApvService.queryWhApvAndItemList(apvQuery, null);
            /** 不模糊查询-- 不查已取消发货的订单
             * if (CollectionUtils.isEmpty(apvs)){
                apvQuery.setSalesRecordNumber(null);
                apvQuery.setSalesRecordNumbers(orderNo);
                apvs = whApvService.queryWhApvAndItemList(apvQuery, null);
            }*/
            if (CollectionUtils.isEmpty(apvs)) {
                response.setErrorMsg("查无此订单");
            } else {
                // 查询日志
                WhSystemLogQueryCondition query = new WhSystemLogQueryCondition();
                query.setModule("whapv");
                query.setRelevanceId(apvs.get(0).getId());
                query.setTableIndex(CommonUtils.tableIndex(apvs.get(0).getId()));
                List<WhSystemLog> whSystemLogs = whSystemLogService.queryWhSystemLogs(query, null);
                if (CollectionUtils.isNotEmpty(whSystemLogs)) {
                    List<ApvLog> apvLogs = new ArrayList<>();
                    for (WhSystemLog whSystemLog : whSystemLogs) {
                        SaleUser cacheBuyer = null;
                        if (whSystemLog.getCreateBy() != null) {
                            cacheBuyer = CacheUtils.get(CacheName.USER_CACHE, String.valueOf(whSystemLog.getCreateBy()), SaleUser.class);
                        }
                        ApvLog apvLog = new ApvLog(whSystemLog.getCreationDate(), whSystemLog.getContent(), cacheBuyer == null ? null : cacheBuyer.getUsername() + " - " + cacheBuyer.getName());
                        apvLogs.add(apvLog);
                    }
                    response.setResult(apvLogs);
                }
                response.setSuccess(true);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setErrorMsg(e.getMessage());
        }
        return response;
    }

    class ApvLog {
        private Timestamp creationDate;// 时间
        private String content;// 详情
        private String createBy;// 操作人

        public ApvLog() {
        }

        public ApvLog(Timestamp creationDate, String content, String createBy) {
            this.creationDate = creationDate;
            this.content = content;
            this.createBy = createBy;
        }

        public Timestamp getCreationDate() {
            return creationDate;
        }

        public void setCreationDate(Timestamp creationDate) {
            this.creationDate = creationDate;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getCreateBy() {
            return createBy;
        }

        public void setCreateBy(String createBy) {
            this.createBy = createBy;
        }
    }


    @ResponseBody
    @RequestMapping(value = "pushTakeStockRecord", method = { RequestMethod.POST })
    public ApiResult<?> pushTakeStockRecord(@RequestBody List<TakeStockDTO> list) {

        if (CollectionUtils.isEmpty(list)) {
            return ApiResult.newError("参数为空");
        }

        List<TakeStockDTO> failList = new ArrayList();

        list.forEach(dto -> {
            try {
                dto.setRecordSource(RecordSourceEnum.OMS_PUSH.intCode());
                takeStockRecordService.doCreateRecordAndLog(dto, "OMS推送盘点记录");
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                //记录创建失败的数据，并将错误信息返回给调用方
                dto.setErrorMsg(ExceptionUtil.message(e));
                failList.add(dto);
            }
        });

        if (CollectionUtils.isNotEmpty(failList)) {
            return ApiResult.newError("失败数据：" + failList.toString());
        }

        return ApiResult.newSuccess();
    }

    @ResponseBody
    @RequestMapping(value = "pushTakeStockRecordBatchTest", method = { RequestMethod.POST })
    public ApiResult<?> pushTakeStockRecordBatchTest(@RequestBody List<TakeStockDTO> list) {

        if (CollectionUtils.isEmpty(list)) {
            return ApiResult.newError("参数为空");
        }

        takeStockRecordService.doBatchCreateRecordAndLog(list, "测试批量生成数据");

        return ApiResult.newSuccess();
    }



    @RequestMapping(value = "batchCreateAssetChangeItem", method = { RequestMethod.GET })
    @ResponseBody
    public ResponseJson batchCreateAssetChangeItem(@RequestParam(value = "time1", required = false) String time1,
            @RequestParam(value = "time2", required = false) String time2) {
        ResponseJson response = new ResponseJson();
        try {
            time1 = time1 == null ? "2021-11-13" : time1;
            time2 = time2 == null ? "2022-02-18" : time2;
            List<String> betweenDates = DateUtils.getBetweenDates(
                    DateUtils.stringToDate(time1, DateUtils.DEFAULT_FORMAT),
                    DateUtils.stringToDate(time2, DateUtils.DEFAULT_FORMAT));
            betweenDates = betweenDates.stream().distinct().collect(Collectors.toList());
            for (String time : betweenDates) {
                long startTime = System.currentTimeMillis();
                Date dateParam = new Date();
                if (StringUtils.isNotEmpty(time)) {
                    dateParam = DateUtils.stringToDate(time + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
                }
                // 跑前一天的数据
                String fromTime = DateUtils.dateToString(DateUtils.getDateStart(DateUtils.getYesterdayDate(dateParam)),
                        DateUtils.DEFAULT_FORMAT);
                AssetChangeItemQueryCondition query = new AssetChangeItemQueryCondition();
                query.setFromDate(fromTime);
                if (fromTime.equals("2021-11-12")) {
                    query.setFromDate(fromTime + " 04:00:00");
                }
                query.setToDate(time);
                //query.setSkuStr("1AA803072");
                List<AssetChangeItem> whAssetChangeItemList = assetChangeItemService.queryDiffAssetInventoryTypeQtyList(query);
                if (CollectionUtils.isEmpty(whAssetChangeItemList))
                    continue;
                CommonUtils.batchResolve(whAssetChangeItemList, 500, itemList -> {
                    assetChangeItemService.batchCreateAssetChangeItem(itemList);
                });
                log.warn("---invoke batchCreateAssetChangeItem cost time: " + (System.currentTimeMillis() - startTime));

                //计算第分组第一条SKU当期价格
                long startTime2 = System.currentTimeMillis();
                try {
                    assetChangeItemService.callFistAndPriceZeroAssetChangeItemGroupBySku();
                }
                catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                log.warn("---invoke PushAssetSumToReportJob cost time: " + (System.currentTimeMillis() - startTime2));

                //计算
                long startTime3 = System.currentTimeMillis();
                try {
                    List<AssetChangeItemCalcBean> calcBeanList = assetChangeItemService.queryAssetChangeItemCalcBean();
                    if (CollectionUtils.isNotEmpty(calcBeanList))
                        assetChangeItemService.batchUpdateAssetChangeItemByCalculate(calcBeanList);
                }
                catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                log.warn("---invoke PushAssetSumToReportJob cost time: " + (System.currentTimeMillis() - startTime3));
            }
            response.setMessage("成功！");
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("失败：" + e.getMessage());
        }
        return response;
    }

    /**
     *  fba 确认发货
     */
    @ResponseBody
    @RequestMapping(value = "fbaAllocationConfirm", method = { RequestMethod.POST })
    public ResponseJson doFbaAllocationConfirm(@RequestBody FbaAllocationConfirmDTO param){
        log.info("doFbaAllocationConfirm===>>>param：{}", JSON.toJSONString(param));
        ResponseJson responseJson = new ResponseJson(StatusCode.FAIL);
        if(StringUtils.isBlank(param.getFbaNo()) || param.getStatus() == null){
            responseJson.setMessage("参数缺失");
            return responseJson;
        }
        //是精品出库单
        if (param.isClothingBoutique()){
            try {
                whBoutiqueOutOrderService.doSalespersonConfirm(param);
            }catch (Exception e){
                log.error("销售确认精品出库单失败！"+e.getMessage(),e);
                responseJson.setMessage(e.getMessage());
                return responseJson;
            }
            responseJson.setStatus(StatusCode.SUCCESS);
            return responseJson;
        }


        WhFbaAllocationQueryCondition condition = new WhFbaAllocationQueryCondition();
        condition.setFbaNo(param.getFbaNo());
        List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(condition, null);
        if(CollectionUtils.isEmpty(whFbaAllocations)){
            responseJson.setMessage("未找到发货单信息");
            return responseJson;
        }
        WhFbaAllocation whFbaAllocation = whFbaAllocations.get(0);
        if(!AsnPrepareStatus.WAITING_CONFIRM.intCode().equals(whFbaAllocation.getStatus())){
            responseJson.setStatus(StatusCode.SUCCESS);
            responseJson.setMessage("发货单已确认");
            return responseJson;
        }
        WhFbaAllocation updateAlloca = new WhFbaAllocation();
        updateAlloca.setId(whFbaAllocation.getId());
        if(param.getStatus() == 1){
            // 通过
            updateAlloca.setStatus(AsnPrepareStatus.WAITING_DELIVER.intCode());
            updateAlloca.setRejectReason("");
            updateAlloca.setConfirmTime(new Timestamp(System.currentTimeMillis()));
            SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(), "销售确认发货");
            whFbaAllocation.setStatus(AsnPrepareStatus.WAITING_DELIVER.intCode());
        }else if(param.getStatus() == 2){
            // 驳回
            updateAlloca.setStatus(AsnPrepareStatus.WAITING_BOX.intCode());
            if(StringUtils.isNotBlank(param.getRemark())){
                updateAlloca.setRejectReason(param.getRemark());
            }else {
                updateAlloca.setRejectReason("");
            }
            List<WhFbaAllocationItem> items = whFbaAllocation.getItems();
            if(CollectionUtils.isNotEmpty(items)){
                for (WhFbaAllocationItem item : items) {
                    if(item.getBoxNo() == null || Integer.valueOf(1).equals(item.getBoxNo())) {
                        whFbaAllocationItemService.removeBoxInfo(item.getId());
                    }else{
                        whFbaAllocationItemService.deleteWhFbaAllocationItem(item.getId());
                    }
                }
            }
            SystemLogUtils.FBAALLOCATIONLOG.log(whFbaAllocation.getId(), "销售驳回，驳回原因：" + param.getRemark());
            whFbaAllocation.setStatus(AsnPrepareStatus.WAITING_BOX.intCode());
        }
        whFbaAllocationService.updateWhFbaAllocation(updateAlloca);

        if(whFbaAllocation.isFba()) whFbaAllocationService.sendMsg(whFbaAllocation);
        responseJson.setStatus(StatusCode.SUCCESS);
        return responseJson;
    }


    /**
     *  超体积发货单确认发货
     */
    @RequestMapping(value = "confirmApvOversize", method = RequestMethod.POST)
    @ResponseBody
    public ResponseJson doConfirmApvOversize(@RequestBody CustomerOrderConfirm confirm){
        log.info("confirmApvOversize===>>>请求参数：" + JSON.toJSONString(confirm));
        ResponseJson response = new ResponseJson(StatusCode.FAIL);
        if(confirm == null || StringUtils.isBlank(confirm.getYst())){
            response.setMessage("运输单号YST不能为空");
            return response;
        }
        WhApvQueryCondition query = new WhApvQueryCondition();
        query.setApvNo(confirm.getYst());
        query.setOversize(true);
        List<WhApv> apvList = whApvService.queryWhApvAndItemList(query, null);
        if (CollectionUtils.isEmpty(apvList)) {
            query.setApvNo(null);
            query.setPaymentStatus(confirm.getYst());
            apvList = whApvService.queryWhApvAndItemList(query, null);
            if (CollectionUtils.isEmpty(apvList)) {
                response.setMessage("YST号有误，查无此发货单！");
                return response;
            }
        }
        WhApv apv = apvList.get(0);
        if (!ApvStatus.WAITING_DELIVER.equals(apv.getStatus())) {
            response.setMessage("发货单不是等待发货状态！");
            return response;
        }
        ApvOversize apvOversize = apv.getApvOversize();
        if (apvOversize == null || apvOversize.getId() == null) {
            response.setMessage("超体积类型发货单数据未初始化!");
            return response;
        }
        if (ApvOversizeStatus.CONFIRM.intCode().equals(apvOversize.getStatus())) {
            response.setStatus(StatusCode.SUCCESS);
            response.setMessage("超体积发货单已确认！");
            return response;
        }
        if (!ApvOversizeStatus.UN_CONFIRM.intCode().equals(apvOversize.getStatus())) {
            response.setMessage("超体积发货单不是待确认状态！");
            return response;
        }
        apvOversize.setOldStatus(ApvOversizeStatus.UN_CONFIRM.intCode());
        apvOversize.setStatus(ApvOversizeStatus.CONFIRM.intCode());
        apvOversize.setConfirmDate(new Timestamp(System.currentTimeMillis()));
        try {
            this.updateLogisticsInfo(confirm, apv);
            if (apvOversizeService.updateApvOversizeByApvNo(apvOversize) > 0){
                response.setStatus(StatusCode.SUCCESS);
                response.setMessage("数据更新成功!");
                SystemLogUtils.APVLOG.log(apv.getId(), "OMS确认超体积发货单可发货");
            }else {
                response.setMessage("数据更新失败!");
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    /**
     *  修改物流信息
     */
    private void updateLogisticsInfo(CustomerOrderConfirm confirm, WhApv whApv){
        WhApv updateApv = new WhApv();
        updateApv.setId(whApv.getId());
        // 物流方式
        if(!StringUtils.equals(whApv.getLogisticsCompany(), confirm.getDeliverMethod())){
//            updateApv.setPaymentStatus(confirm.getYst());
            updateApv.setTrackingNumber(confirm.getLogisticsTrackingNumberNew());
            if(confirm.getShipmentTime() != null) {
                updateApv.setShipmentTime(new Timestamp(confirm.getShipmentTime().getTime()));
            }
//            updateApv.setServiceProviderNo(serviceProviderNo);
            updateApv.setLogisticsCompany(confirm.getDeliverMethod());
            SystemParam systemParam = CacheUtils.SystemParamGet("apv_logistics.LOGISTICS_COMPANY");
            if ((systemParam.getParamValue().contains(whApv.getLogisticsCompany())
                    && !systemParam.getParamValue().contains(confirm.getDeliverMethod()))
                    || (systemParam.getParamValue().contains(confirm.getDeliverMethod())
                    && !systemParam.getParamValue().contains(whApv.getLogisticsCompany()))) {
                updateApv.setSignRefund(true);
            }
            SystemLogUtils.APVLOG.log(whApv.getId(), "更换物流成功",
                    new String[][] { { "老追踪号", whApv.getTrackingNumber() }, { "新追踪号", updateApv.getTrackingNumber() },
                            { "新货代号", updateApv.getServiceProviderNo() }, { "新YST", updateApv.getPaymentStatus() },
                            { "新物流方式", updateApv.getLogisticsCompany() }});
        }
        whApvService.updateWhApv(updateApv);
    }


    /**
     * fba 批量创建中转仓sku
     */
    @PostMapping("pushTransferSku")
    @ResponseBody
    public ResponseJson pushTransferSku(@RequestBody List<TransferSkuDTO> transferSkuDTOS) {
        log.info("pushTransferSku===>>>请求参数：" + JSON.toJSONString(transferSkuDTOS));
        Assert.notEmpty(transferSkuDTOS);
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        try {
            CommonUtils.batchResolve(transferSkuDTOS, 500,
                    batchList -> transferStockService.batchCreateTransferSku(batchList));
            response.setStatus(StatusCode.SUCCESS);
            return response;
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage("系统异常，" + e.getMessage());
            return response;
        }
    }

    /**
     * 提供订单系统-查询运费单价
     */
    @RequestMapping(value = "getFreightPrice", method = RequestMethod.POST)
    @ResponseBody
    public ApiResult<WhFbaAllocationItem> getFreightPrice(@RequestBody WhFbaAllocationItem whFbaAllocationItem) {
        ApiResult<WhFbaAllocationItem> response = new ApiResult<>();
        response.setSuccess(false);
        log.info("OMS查询运费单价:"+ JSONObject.toJSONString(whFbaAllocationItem));
        if (StringUtils.isBlank(whFbaAllocationItem.getProductSku())||StringUtils.isBlank(whFbaAllocationItem.getFnSku())||StringUtils.isBlank(whFbaAllocationItem.getStore())) {
            response.setErrorMsg("店铺或FNSKU或SKU为空");
            return response;
        }
        try {
            WhFbaAllocationItemQueryCondition queryCondition=new WhFbaAllocationItemQueryCondition();
            queryCondition.setFnSku(whFbaAllocationItem.getFnSku());
            queryCondition.setStore(whFbaAllocationItem.getStore());
            queryCondition.setProductSku(whFbaAllocationItem.getProductSku());
            List<WhFbaAllocationItem> fbaAllocationItem = whFbaAllocationItemService.queryFreightPrice(queryCondition);
            if (CollectionUtils.isEmpty(fbaAllocationItem)) {
                response.setErrorMsg("数据不存在！");
                return response;
            }
            response.setResult(fbaAllocationItem.get(0));
        }catch(Exception e) {
            log.error(e.getMessage(), e);
            response.setErrorMsg(e.getMessage());
        }
        response.setSuccess(true);
        return response;
    }

    /**
     * 接收订单系统揽收单号
     */
    @RequestMapping(value = "acceptPickUpOrderNo", method = RequestMethod.POST)
    @ResponseBody
    public ApiResult<?> acceptPickUpOrderNo(@RequestBody PickUpOrderDTO pickUpOrderDTO) {
        log.info("接收订单系统揽收单号:"+ JSONObject.toJSONString(pickUpOrderDTO));
        if (pickUpOrderDTO == null || CollectionUtils.isEmpty(pickUpOrderDTO.getApvNos()) || StringUtils.isBlank(pickUpOrderDTO.getPickUpOrderId())) {
            return ApiResult.newError("param error");
        }
        WhFbaAllocationQueryCondition queryCondition = new WhFbaAllocationQueryCondition();
        queryCondition.setFbaNo(StringUtils.join(pickUpOrderDTO.getApvNos(), ","));
        queryCondition.setQueryWhAsnExtra(true);
        List<WhFbaAllocation> whFbaAllocations = whFbaAllocationService.queryWhFbaAllocationAndItems(queryCondition, null);
        if (CollectionUtils.isEmpty(whFbaAllocations)) return ApiResult.newError("param error");
        List<WhAsnExtra> whAsnExtras = whFbaAllocations.stream().map(w -> w.getWhAsnExtra()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(whAsnExtras)) return ApiResult.newError("param error");
        try {
            List<WhAsnExtra> updates = new ArrayList<>();
            for (WhAsnExtra whAsnExtra : whAsnExtras) {
                WhAsnExtra updateWhAsnExtra = new WhAsnExtra();
                updateWhAsnExtra.setId(whAsnExtra.getId());
                updateWhAsnExtra.setPickupOrderId(pickUpOrderDTO.getPickUpOrderId());
                updates.add(updateWhAsnExtra);
            }
            whAsnExtraService.batchUpdateWhAsnExtra(updates);
            return ApiResult.newSuccess();
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 接收订单推送拼多多备货单
     */
    @ResponseBody
    @PostMapping("acceptTemuPrepareOrder")
    public ApiResult<?> acceptTemuPrepareOrder(@RequestBody TemuPrepareOrder order){
        try {
            ApiResult<?> result = this.checkTemuPrepareOrder(order);
            if (!result.isSuccess()){
                return result;
            }
            for (TemuPrepareOrderItem item : order.getItemList()) {
                item.setSourceFrom(SourceFromEnum.PDD.intCode());
            }
            return temuPrepareOrderService.doAccept(order);
        } catch (Exception e) {
            log.error("accept TemuPrepareOrder error:" + e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    private ApiResult<?> checkTemuPrepareOrder(TemuPrepareOrder order){
        if (order == null) {
            return ApiResult.newError("参数错误");
        }
        log.info("acceptTemuPrepareOrder param: " + JSON.toJSONString(order));
        if (StringUtils.isBlank(order.getAccountNumber())) {
            return ApiResult.newError("店铺不能为空");
        }
        if (StringUtils.isBlank(order.getPrepareOrderNo())) {
            return ApiResult.newError("备货单号不能为空");
        }
        if (order.getCreationDate() == null) {
            return ApiResult.newError("备货单创建时间不能为空");
        }
        if (CollectionUtils.isEmpty(order.getItemList())) {
            return ApiResult.newError("备货单明细不能为空");
        }
        for (TemuPrepareOrderItem item : order.getItemList()) {
            if (StringUtils.isBlank(item.getSku())) {
                return ApiResult.newError("备货单明细SKU不能为空");
            }
            if (!Objects.equals(SourceFromEnum.ASN.intCode(), item.getSourceFrom()) && StringUtils.isBlank(item.getSkuId())) {
                return ApiResult.newError("备货单明细SKUID不能为空");
            }
            if (item.getRealQuantity() == null) {
                return ApiResult.newError("备货单明细SKU备货件数不能为空");
            }
        }
        return ApiResult.newSuccess();
    }


    /**
     * Temu 取消
     */
    @RequestMapping(value = "cancelTemuPrepareOrder",method = RequestMethod.GET)
    @ResponseBody
    public ResponseJson cancelTemuPrepareOrder(@RequestParam("prepareOrderNo") String prepareOrderNo){
        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);
        if (StringUtils.isBlank(prepareOrderNo)){
            response.setMessage("参数错误");
            return response;
        }
        try {
            TemuPrepareOrderQueryCondition orderQuery = new TemuPrepareOrderQueryCondition();
            orderQuery.setPrepareOrderNo(prepareOrderNo);
            List<TemuPrepareOrder> temuOrders = temuPrepareOrderService.queryTemuPrepareOrderAndItems(orderQuery, null);
            if (CollectionUtils.isEmpty(temuOrders)) {
                response.setMessage(String.format("备货单号：%s,单据不存在!",prepareOrderNo));
                return response;
            }
            TemuPrepareOrder prepareOrder = temuOrders.get(0);
            if (prepareOrder.getStatus()>=TemuOrderStatus.DELIVER.intCode()) {
                response.setMessage(String.format("备货单号：%s,已交运不能取消!",prepareOrderNo));
                return response;
            }
            List<TemuPrepareOrderItem> itemList = prepareOrder.getItemList();
            temuPrepareOrderService.doCancelTemuPrepareOrder(prepareOrder);
            SystemLogUtils.TEMU_PREPARE_ORDER.log(prepareOrder.getId(), "取消Temu备货单！");
            if (!TemuOrderStatus.WAITING_ALLOT.intCode().equals(prepareOrder.getStatus())) {
                temuPrepareOrderItemService.addLogs(itemList.stream().map(TemuPrepareOrderItem::getId).collect(Collectors.toList()),
                        "取消Temu备货单" , TemuPackageStatus.CANCEL.getName());
            }


        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.setMessage(e.getMessage());
            return response;
        }
        response.setStatus(StatusCode.SUCCESS);
        return response;
    }

    /**
     * 提供OMS查询退仓单
     */
    @RequestMapping(value = "getReturnOrder",method = RequestMethod.GET)
    @ResponseBody
    public ApiResult<?> getReturnOrder(@RequestParam("startOutBoundTime") String startOutBoundTime,@RequestParam("endOutBoundTime") String endOutBoundTime){
        if (StringUtils.isBlank(startOutBoundTime)|| StringUtils.isBlank(endOutBoundTime)) {
            return ApiResult.newError("参数错误");
        }
        TemuReturnPackageQueryCondition queryCondition = new TemuReturnPackageQueryCondition();
        queryCondition.setStartOutBoundTime(startOutBoundTime);
        queryCondition.setEndOutBoundTime(endOutBoundTime);
        List<TemuReturnPackage> returnPackages = temuReturnPackageService.queryTemuReturnPackages(queryCondition, null);
        return ApiResult.newSuccess(returnPackages);
    }

    /**
     * 接收FBA货件单信息(fba单确认发货调用此接口，驳回还是原接口：fbaAllocationConfirm)
     */
    @ResponseBody
    @RequestMapping(value = "acceptFbaShipment", method = RequestMethod.POST)
    public ApiResult<?> acceptFbaShipment(@RequestBody List<WhFbaShipment> fbaShipments){
        log.info("acceptFbaShipment param: " + JSON.toJSONString(fbaShipments));
        if (CollectionUtils.isEmpty(fbaShipments)) {
            return ApiResult.newError("参数为空");
        }
        try {
            return whFbaShipmentService.doAccept(fbaShipments);
        } catch (Exception e) {
            log.error("acceptFbaShipment error:" + e.getMessage(), e);
            return ApiResult.newError("保存FBA货件信息异常");
        }
    }

}
