package com.estone.statistics.dao.impl;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.estone.common.util.*;
import com.estone.warehouse.enums.BatchReturnType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.estone.checkin.bean.*;
import com.estone.checkin.dao.PurchaseOrderPercentageDao;
import com.estone.checkin.dao.WhCheckInDao;
import com.estone.checkin.dao.WhPurchaseOrderDao;
import com.estone.checkin.enums.CheckInStatus;
import com.estone.checkin.enums.CheckInWhType;
import com.estone.checkin.utils.PurchaseOrderPercentageUtils;
import com.estone.common.SaleChannel;
import com.estone.elasticsearch.model.SingleItemEs;
import com.estone.elasticsearch.service.IEsSingleItemService;
import com.estone.picking.enums.PickingTaskWarehouseType;
import com.estone.sku.enums.WhSkuStatus;
import com.estone.statistics.bean.AssetChangeItem;
import com.estone.statistics.bean.AssetChangeItemCalcBean;
import com.estone.statistics.bean.AssetChangeItemGroup;
import com.estone.statistics.bean.AssetChangeItemQueryCondition;
import com.estone.statistics.dao.AssetChangeItemDao;
import com.estone.statistics.dao.mapper.AssetChangeItemCalcMapper;
import com.estone.statistics.dao.mapper.AssetChangeItemDBField;
import com.estone.statistics.dao.mapper.AssetChangeItemMapper;
import com.estone.statistics.enums.DrpTurnoverOderType;
import com.estone.warehouse.dao.mapper.WhStockMonitorsDBField;
import com.estone.warehouse.enums.WhAllocateTypeEnum;
import com.whq.tool.component.Pager;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;
import com.whq.tool.sqler.dialect.DialectFactory;
import com.whq.tool.sqler.dialect.SQLDialect;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository("assetChangeItemDao")
public class AssetChangeItemDaoImpl implements AssetChangeItemDao {

    @Resource
    private WhPurchaseOrderDao whPurchaseOrderDao;

    @Resource
    private PurchaseOrderPercentageDao purchaseOrderPercentageDao;

    @Resource
    private WhCheckInDao whCheckInDao;
    @Resource
    private IEsSingleItemService singleItemService;

    private void setQueryCondition(SqlerRequest request, AssetChangeItemQueryCondition query) {
        if (query == null) {
            return;
        }
        if (Objects.nonNull(query.getReadOnly()) && query.getReadOnly()){
            request.setReadOnly(true);
        }
        // 添加查询条件
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam(AssetChangeItemDBField.ID, DataType.INT, query.getId());
        request.addDataParam("id_list", DataType.INT, query.getIds());
        request.addDataParam(AssetChangeItemDBField.SKU, DataType.STRING, query.getSku());
        if (StringUtils.isNotBlank(query.getSkuStr())) {
            if (StringUtils.contains(query.getSkuStr(), ",")) {
                List<String> skuList = CommonUtils.splitList(query.getSkuStr(), ",");
                request.addDataParam("sku_list", DataType.STRING, skuList);
            }
            else {
                request.addDataParam(AssetChangeItemDBField.SKU, DataType.STRING, query.getSkuStr());
            }
        }

        request.addDataParam("start_create_time", DataType.STRING, query.getFromDate());
        request.addDataParam("end_create_time", DataType.STRING, query.getToDate());

        request.addDataParam(AssetChangeItemDBField.INVENTORY_TYPE, DataType.INT, query.getInventoryType());
        request.addDataParam(AssetChangeItemDBField.ORDER_NO, DataType.STRING, query.getOrderNo());

        if (null != query.getIsPositive()) {
            if (query.getIsPositive()) {
                request.addSqlDataParam("get_is_positive", "AND item.current_checkin_quantity > 0");
            }
            else {
                request.addSqlDataParam("get_is_positive", "AND item.current_checkin_quantity < 0");
            }
        }

        if (StringUtils.isNotBlank(query.getSkuStatusList())) {
            List<String> statusList = CommonUtils.splitList(query.getSkuStatusList(), ",");
            List<Integer> statusIntegerList = new ArrayList<Integer>();
            for (String status : statusList) {
                statusIntegerList.add(WhSkuStatus.build(status).getId());
            }
            request.addDataParam("sku_status_list", DataType.INT, statusIntegerList);
            request.addSqlDataParam("LEFT_JOIN_SKU","LEFT JOIN wh_sku sku ON item.sku = sku.sku");
        }
    }

    @Override
    public int queryAssetChangeItemCount(AssetChangeItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryAssetChangeItemCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<AssetChangeItem> queryAssetChangeItemList() {
        SqlerRequest request = new SqlerRequest("queryAssetChangeItemList");
        return SqlerTemplate.query(request, new AssetChangeItemMapper(false,true));
    }

    @Override
    public List<AssetChangeItem> queryAssetChangeItemList(AssetChangeItemQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryAssetChangeItemList");
        setQueryCondition(request, query);
        if(pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new AssetChangeItemMapper(false,true));
    }

    @Override
    public AssetChangeItem queryAssetChangeItem(Integer primaryKey) {
        Assert.notNull(primaryKey, "primaryKey is null!");
        SqlerRequest request = new SqlerRequest("queryAssetChangeItemByPrimaryKey");
        request.addDataParam(AssetChangeItemDBField.ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new AssetChangeItemMapper());
    }

    @Override
    public AssetChangeItem queryAssetChangeItem(AssetChangeItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryAssetChangeItem");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new AssetChangeItemMapper());
    }

    @Override
    public void createAssetChangeItem(AssetChangeItem entity) {
        SqlerRequest request = new SqlerRequest("createAssetChangeItem");
        request.addDataParam(AssetChangeItemDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(AssetChangeItemDBField.CREATE_DATE, DataType.TIMESTAMP, entity.getCreateDate());
        request.addDataParam(AssetChangeItemDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(AssetChangeItemDBField.INVENTORY_TYPE, DataType.INT, entity.getInventoryType());
        request.addDataParam(AssetChangeItemDBField.ORDER_TYPE, DataType.INT, entity.getOrderType());
        request.addDataParam(AssetChangeItemDBField.ORDER_NO, DataType.STRING, entity.getOrderNo());
        request.addDataParam(AssetChangeItemDBField.OPENING_INVENTORY_QUANTITY, DataType.INT, entity.getOpeningInventoryQuantity());
        request.addDataParam(AssetChangeItemDBField.OPENING_INVENTORY_PRICE, DataType.BIGDECIMAL, entity.getOpeningInventoryPrice());
        request.addDataParam(AssetChangeItemDBField.OPENING_INVENTORY_AMOUNT, DataType.BIGDECIMAL, entity.getOpeningInventoryAmount());
        request.addDataParam(AssetChangeItemDBField.CURRENT_CHECKIN_QUANTITY, DataType.INT, entity.getCurrentCheckinQuantity());
        request.addDataParam(AssetChangeItemDBField.CURRENT_CHECKIN_PRICE, DataType.BIGDECIMAL, entity.getCurrentCheckinPrice());
        request.addDataParam(AssetChangeItemDBField.CURRENT_CHECKIN_AMOUNT, DataType.BIGDECIMAL, entity.getCurrentCheckinAmount());
        request.addDataParam(AssetChangeItemDBField.ENDING_INVENTORY_QUANTITY, DataType.INT, entity.getEndingInventoryQuantity());
        request.addDataParam(AssetChangeItemDBField.ENDING_INVENTORY_PRICE, DataType.BIGDECIMAL, entity.getEndingInventoryPrice());
        request.addDataParam(AssetChangeItemDBField.ENDING_INVENTORY_AMOUNT, DataType.BIGDECIMAL, entity.getEndingInventoryAmount());
        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setId(autoIncrementId);
    }

    @Override
    public void updateAssetChangeItem(AssetChangeItem entity) {
        SqlerRequest request = new SqlerRequest("updateAssetChangeItemByPrimaryKey");
        request.addDataParam(AssetChangeItemDBField.ID, DataType.INT, entity.getId());

        request.addDataParam(AssetChangeItemDBField.SKU, DataType.STRING, entity.getSku());
        request.addDataParam(AssetChangeItemDBField.CREATE_DATE, DataType.TIMESTAMP, entity.getCreateDate());
        request.addDataParam(AssetChangeItemDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        request.addDataParam(AssetChangeItemDBField.INVENTORY_TYPE, DataType.INT, entity.getInventoryType());
        request.addDataParam(AssetChangeItemDBField.ORDER_TYPE, DataType.INT, entity.getOrderType());
        request.addDataParam(AssetChangeItemDBField.ORDER_NO, DataType.STRING, entity.getOrderNo());
        request.addDataParam(AssetChangeItemDBField.OPENING_INVENTORY_QUANTITY, DataType.INT, entity.getOpeningInventoryQuantity());
        request.addDataParam(AssetChangeItemDBField.OPENING_INVENTORY_PRICE, DataType.BIGDECIMAL, entity.getOpeningInventoryPrice());
        request.addDataParam(AssetChangeItemDBField.OPENING_INVENTORY_AMOUNT, DataType.BIGDECIMAL, entity.getOpeningInventoryAmount());
        request.addDataParam(AssetChangeItemDBField.CURRENT_CHECKIN_QUANTITY, DataType.INT, entity.getCurrentCheckinQuantity());
        request.addDataParam(AssetChangeItemDBField.CURRENT_CHECKIN_PRICE, DataType.BIGDECIMAL, entity.getCurrentCheckinPrice());
        request.addDataParam(AssetChangeItemDBField.CURRENT_CHECKIN_AMOUNT, DataType.BIGDECIMAL, entity.getCurrentCheckinAmount());
        request.addDataParam(AssetChangeItemDBField.ENDING_INVENTORY_QUANTITY, DataType.INT, entity.getEndingInventoryQuantity());
        request.addDataParam(AssetChangeItemDBField.ENDING_INVENTORY_PRICE, DataType.BIGDECIMAL, entity.getEndingInventoryPrice());
        request.addDataParam(AssetChangeItemDBField.ENDING_INVENTORY_AMOUNT, DataType.BIGDECIMAL, entity.getEndingInventoryAmount());
        SqlerTemplate.execute(request);
    }

    @Override
    public void batchCreateAssetChangeItem(List<AssetChangeItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createAssetChangeItem");
            for (AssetChangeItem entity : entityList) {
                request.addBatchDataParam(AssetChangeItemDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(AssetChangeItemDBField.CREATE_DATE, DataType.TIMESTAMP, entity.getCreateDate());
                request.addBatchDataParam(AssetChangeItemDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(AssetChangeItemDBField.INVENTORY_TYPE, DataType.INT, entity.getInventoryType());
                request.addBatchDataParam(AssetChangeItemDBField.ORDER_TYPE, DataType.INT, entity.getOrderType());
                request.addBatchDataParam(AssetChangeItemDBField.ORDER_NO, DataType.STRING, entity.getOrderNo());
                request.addBatchDataParam(AssetChangeItemDBField.OPENING_INVENTORY_QUANTITY, DataType.INT, entity.getOpeningInventoryQuantity());
                request.addBatchDataParam(AssetChangeItemDBField.OPENING_INVENTORY_PRICE, DataType.BIGDECIMAL, entity.getOpeningInventoryPrice());
                request.addBatchDataParam(AssetChangeItemDBField.OPENING_INVENTORY_AMOUNT, DataType.BIGDECIMAL, entity.getOpeningInventoryAmount());
                request.addBatchDataParam(AssetChangeItemDBField.CURRENT_CHECKIN_QUANTITY, DataType.INT, entity.getCurrentCheckinQuantity());
                request.addBatchDataParam(AssetChangeItemDBField.CURRENT_CHECKIN_PRICE, DataType.BIGDECIMAL, entity.getCurrentCheckinPrice());
                request.addBatchDataParam(AssetChangeItemDBField.CURRENT_CHECKIN_AMOUNT, DataType.BIGDECIMAL, entity.getCurrentCheckinAmount());
                request.addBatchDataParam(AssetChangeItemDBField.ENDING_INVENTORY_QUANTITY, DataType.INT, entity.getEndingInventoryQuantity());
                request.addBatchDataParam(AssetChangeItemDBField.ENDING_INVENTORY_PRICE, DataType.BIGDECIMAL, entity.getEndingInventoryPrice());
                request.addBatchDataParam(AssetChangeItemDBField.ENDING_INVENTORY_AMOUNT, DataType.BIGDECIMAL, entity.getEndingInventoryAmount());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void batchUpdateAssetChangeItem(List<AssetChangeItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateAssetChangeItemByPrimaryKey");
            for (AssetChangeItem entity : entityList) {
                request.addBatchDataParam(AssetChangeItemDBField.ID, DataType.INT, entity.getId());

                request.addBatchDataParam(AssetChangeItemDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(AssetChangeItemDBField.CREATE_DATE, DataType.TIMESTAMP, entity.getCreateDate());
                request.addBatchDataParam(AssetChangeItemDBField.LAST_UPDATE_DATE, DataType.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(AssetChangeItemDBField.INVENTORY_TYPE, DataType.INT, entity.getInventoryType());
                request.addBatchDataParam(AssetChangeItemDBField.ORDER_TYPE, DataType.INT, entity.getOrderType());
                request.addBatchDataParam(AssetChangeItemDBField.ORDER_NO, DataType.STRING, entity.getOrderNo());
                request.addBatchDataParam(AssetChangeItemDBField.OPENING_INVENTORY_QUANTITY, DataType.INT, entity.getOpeningInventoryQuantity());
                request.addBatchDataParam(AssetChangeItemDBField.OPENING_INVENTORY_PRICE, DataType.BIGDECIMAL, entity.getOpeningInventoryPrice());
                request.addBatchDataParam(AssetChangeItemDBField.OPENING_INVENTORY_AMOUNT, DataType.BIGDECIMAL, entity.getOpeningInventoryAmount());
                request.addBatchDataParam(AssetChangeItemDBField.CURRENT_CHECKIN_QUANTITY, DataType.INT, entity.getCurrentCheckinQuantity());
                request.addBatchDataParam(AssetChangeItemDBField.CURRENT_CHECKIN_PRICE, DataType.BIGDECIMAL, entity.getCurrentCheckinPrice());
                request.addBatchDataParam(AssetChangeItemDBField.CURRENT_CHECKIN_AMOUNT, DataType.BIGDECIMAL, entity.getCurrentCheckinAmount());
                request.addBatchDataParam(AssetChangeItemDBField.ENDING_INVENTORY_QUANTITY, DataType.INT, entity.getEndingInventoryQuantity());
                request.addBatchDataParam(AssetChangeItemDBField.ENDING_INVENTORY_PRICE, DataType.BIGDECIMAL, entity.getEndingInventoryPrice());
                request.addBatchDataParam(AssetChangeItemDBField.ENDING_INVENTORY_AMOUNT, DataType.BIGDECIMAL, entity.getEndingInventoryAmount());
                request.addBatch();
            }
            SqlerTemplate.batchUpdate(request);
        }
    }

    @Override
    public void deleteAssetChangeItem(Integer primaryKey) {
        SqlerRequest request = new SqlerRequest("deleteAssetChangeItemByPrimaryKey");
        request.addDataParam(AssetChangeItemDBField.ID, DataType.INT, primaryKey);
        SqlerTemplate.execute(request);
    }

    @Override
    public List<AssetChangeItemCalcBean> queryAssetChangeItemCalcBean() {
        SqlerRequest request = new SqlerRequest("queryAssetChangeItemCalcBean");
        return SqlerTemplate.query(request, new AssetChangeItemCalcMapper());
    }

    @Override
    public AssetChangeItem queryPreAssetChangeItem(AssetChangeItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryPreAssetChangeItem");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new AssetChangeItemMapper());
    }

    @Override
    public List<AssetChangeItem> queryFistAndPriceZeroAssetChangeItemGroupBySku() {
        SqlerRequest request = new SqlerRequest("queryFistAndPriceZeroAssetChangeItemGroupBySku");
        return SqlerTemplate.query(request, new AssetChangeItemMapper());
    }

    @Override
    public List<AssetChangeItem> queryNeedCalculateCurrentCheckInPriceItems(String startDate, String endDate) {
        SqlerRequest request = new SqlerRequest("queryNeedCalculateCurrentCheckInPriceItemList");
        request.addDataParam("start_create_time", DataType.STRING, startDate);
        request.addDataParam("end_create_time", DataType.STRING, endDate);
        return SqlerTemplate.query(request, new AssetChangeItemMapper());
    }

    @Override
    public List<AssetChangeItemCalcBean> queryNeedCalculateCurrentCheckInPriceAssetChangeItemCalcBean(String startDate,
            String endDate, List<String> skuList) {
        SqlerRequest request = new SqlerRequest("queryNeedCalculateCurrentCheckInPriceAssetChangeItemCalcBean");
        request.addDataParam("skuList", DataType.STRING, skuList);
        request.addDataParam("start_create_time", DataType.STRING, startDate);
        request.addDataParam("end_create_time", DataType.STRING, endDate);
        return SqlerTemplate.query(request, new AssetChangeItemCalcMapper());
    }

    @Override
    public AssetChangeItemGroup queryAssetChangeItemGroup(AssetChangeItemQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryAssetChangeItemGroup");
        setQueryCondition(request, query);
        if (pager != null) {
            SQLDialect dial = DialectFactory.createDialect(null);

            long start = (long) (pager.getPageNo() - 1) * pager.getPageSize();

            long end = start + pager.getPageSize() - 1L;

            request.addSqlDataParam("LIMIT", dial.queryFirst2Last("", (int) start, (int) end));
        }
        return SqlerTemplate.queryForObject(request, new AssetChangeItemMapper(true, false));
    }

    @Override
    public List<AssetChangeItem> queryDiffAssetInventoryTypeQtyList(AssetChangeItemQueryCondition query, int step) {
        if (StringUtils.isEmpty(query.getFromDate()) || StringUtils.isEmpty(query.getToDate()))
            return new ArrayList<>();
        SqlerRequest request = new SqlerRequest("queryApvQty");
        switch (step) {
            case 1:
                request = new SqlerRequest("queryApvQty");
                break;
            case 2:
                request = new SqlerRequest("queryAsnDeliverQty");
                break;
            case 3:
                request = new SqlerRequest("queryRfoCheckInQty");
                break;
            case 4:
                request = new SqlerRequest("queryDecreaseInventoryTaskQty");
                break;
            case 5:
                request = new SqlerRequest("queryAllocateRecordOutQty");
                break;
            case 6:
                request = new SqlerRequest("queryFbaAllocationOutQty");
                break;
            case 7:
                request = new SqlerRequest("queryWhLendQty");
                break;
            case 8:
                request = new SqlerRequest("queryScrapQty");
                break;
            case 9:
                request = new SqlerRequest("queryBadProdQty");
                break;
            case 10:
                request = new SqlerRequest("queryCheckInUpQty");
                break;
            case 11:
                request = new SqlerRequest("queryFbaAllocationInQty");
                break;
            case 12:
                request = new SqlerRequest("queryPacReturnQty");
                break;
            case 13:
                request = new SqlerRequest("queryShopOrderOutQty");
                break;
            case 14:
                request = new SqlerRequest("queryIncreaseInventoryTaskQty");
                break;
            case 15:
                request = new SqlerRequest("queryAbroadReturnQty");
                break;
            case 16:
                request = new SqlerRequest("queryWhLendCheckInQty");
                break;
            case 17:
                request = new SqlerRequest("queryAllocateRecordInQty");
                break;
            case 18:
                request = new SqlerRequest("queryRfoOutQty");
                break;
            case 19:
                request = new SqlerRequest("queryMergeOutSkuQty");
                break;
            case 20:
                request = new SqlerRequest("queryMergeInSkuQty");
                break;
            case 21:
                request = new SqlerRequest("queryCombineOutSkuQty");
                break;
            case 22:
                request = new SqlerRequest("queryStockAllocationOutQty");
                break;
            case 23:
                request = new SqlerRequest("queryStockAllocationInQty");
                break;
            case 24:
                request = new SqlerRequest("queryNewTransferReturnUpQty");
                break;
            default:
                break;
        }
        request.setReadOnly(true);
        request.addDataParam("fromDate", DataType.STRING, query.getFromDate());
        request.addDataParam("toDate", DataType.STRING, query.getToDate());
        if (StringUtils.isNotBlank(query.getSkuStr())) {
            if (StringUtils.contains(query.getSkuStr(), ",")) {
                List<String> skuList = CommonUtils.splitList(query.getSkuStr(), ",");
                request.addDataParam("skuList", DataType.STRING, skuList);
            }
            else {
                request.addDataParam("skuList", DataType.STRING, query.getSkuStr());
            }
        }
        return SqlerTemplate.query(request, new RowMapper<AssetChangeItem>() {

            @Override
            public AssetChangeItem mapRow(ResultSet rs, int rowNum) throws SQLException {
                AssetChangeItem entity = new AssetChangeItem();
                entity.setSku(rs.getString(WhStockMonitorsDBField.SKU));
                entity.setCreateDate(Timestamp.valueOf(rs.getString("orderDate")));
                entity.setOrderNo(rs.getString("orderNo"));
                entity.setInventoryType(rs.getInt("inventoryType"));
                entity.setOrderType(rs.getInt("orderType"));
                entity.setCurrentCheckinQuantity(rs.getObject("quantity") == null ? 0 : rs.getInt("quantity"));
                entity.setAccountNumber(CheckInWhType.LOCAL.getName());
                entity.setOutAccountNumber(CheckInWhType.LOCAL.getName());
                if (step == 3 || step == 10 || step == 15 || step == 23){
                    entity.setRelationBatchNo(rs.getString("relationBatchNo"));
                }
                // 采购单才计算本期入库单价
                if (step == 10) {
                    entity.setTotalWeight(rs.getObject("totalWeight") == null ? BigDecimal.ZERO : rs.getBigDecimal("totalWeight"));
                    entity.setTotalQty(rs.getObject("totalQty") == null ? 0 : rs.getInt("totalQty"));
                    entity.setCost(rs.getObject("orderCost") == null ? BigDecimal.ZERO : rs.getBigDecimal("orderCost"));
                    entity.setPrice(
                            rs.getObject("orderPrice") == null ? BigDecimal.ZERO : rs.getBigDecimal("orderPrice"));
                    try {
                        calculateCurrentCheckInPrice(entity, entity.getSku());
                    }
                    catch (Exception e) {
                        entity.setCurrentCheckinPrice(entity.getPrice());
                        log.error("计算入库单价失败" + e.getMessage(), e);
                    }
                }
                if (step == 6 || step == 11){
                    Object outWarehouseId = rs.getObject("outWarehouseId");
                    Object inWarehouseId = rs.getObject("inWarehouseId");
                    Object location = rs.getObject("location");

                    if (PickingTaskWarehouseType.OLD.intCode().equals(outWarehouseId)) {
                        entity.setOutAccountNumber(CheckInWhType.LOCAL.getName());
                        entity.setAccountNumber(
                                location == null ? StringRedisUtils.get(Constant.PAC_STOCK_OWNER_USER_ID)
                                        : String.valueOf(location));
                    }
                    else if (PickingTaskWarehouseType.OLD.intCode().equals(inWarehouseId)) {
                        entity.setAccountNumber(CheckInWhType.LOCAL.getName());
                        entity.setOutAccountNumber(
                                location == null ? StringRedisUtils.get(Constant.PAC_STOCK_OWNER_USER_ID)
                                        : String.valueOf(location));

                    }
                }
                if (step == 5 || step == 17) {
                    entity.setAccountNumber(StringUtils.isNotEmpty(rs.getString("accountNumber"))
                            && rs.getString("accountNumber").equals(WhAllocateTypeEnum.LOCAL.getName())
                                    ? CheckInWhType.LOCAL.getName()
                                    : rs.getString("accountNumber"));
                    entity.setOutAccountNumber(StringUtils.isNotEmpty(rs.getString("outAccountNumber"))
                            && rs.getString("outAccountNumber").equals(WhAllocateTypeEnum.LOCAL.getName())
                                    ? CheckInWhType.LOCAL.getName()
                                    : rs.getString("outAccountNumber"));

                    String platform = rs.getString("platform");
                    String outPlatform = rs.getString("outPlatform");
                    entity.setPlatform(platform);
                    entity.setOutPlatform(outPlatform);
                    if (CheckInWhType.LOCAL.getName().equals(entity.getAccountNumber()))
                        entity.setPlatform(outPlatform);
                    if (CheckInWhType.LOCAL.getName().equals(entity.getOutAccountNumber()))
                        entity.setOutPlatform(platform);
                }
                if (step == 1) {
                    entity.setPlatform(rs.getString("platform"));
                }

                if (step == 21) {
                    entity.setSpu(rs.getString("spu"));
                    entity.setSpuQty(rs.getObject("spuQty") == null ? 0 : rs.getInt("spuQty"));
                    try {
                        calculateCombineSkuCheckInPrice(entity);
                    }
                    catch (Exception e) {
                        entity.setCurrentCheckinPrice(entity.getPrice());
                        log.error("计算组合SKU入库单价失败" + e.getMessage(), e);
                    }
                }
                if (step == 24){
                    String warehouseId = rs.getString("warehouse_id");
                    String expressNo = rs.getString("relationBatchNo");
                    String accountNumber = rs.getString("accountNumber");
                    entity.setPlatform(SaleChannel.CHANNEL_SMT);
                    if (StringUtils.isNotBlank(accountNumber))
                        entity.setAccountNumber(accountNumber);
                    getRelationNo(entity,warehouseId, expressNo);
                }
                return entity;
            }
        });
    }
    //新中转仓退件获取关联出库单号
    private void getRelationNo(AssetChangeItem entity, String warehouseId, String expressNo) {
        if (entity == null || StringUtils.isBlank(warehouseId) || StringUtils.isBlank(expressNo))
            return;
        String sql = "";
        if (StringUtils.equals(warehouseId, BatchReturnType.LOCAL_JIT.getCode())) {
            sql = "SELECT apv_no AS apvNo FROM wh_apv WHERE `tracking_number`IN (SELECT sri.origin_outbound_no FROM smt_return_order sr"
                    + " LEFT JOIN smt_return_order_item sri ON sri.return_id = sr.id" + " WHERE sr.express_no = '"
                    + expressNo + "')";
        }
        else if (StringUtils.equals(warehouseId, BatchReturnType.TRANSIT.getCode())) {
            sql = "SELECT fba_no AS apvNo FROM wh_fba_allocation wf"
                    + " LEFT JOIN wh_fba_allocation_item wfi ON wfi.fba_id = wf.id"
                    + " WHERE wfi.`temu_tag_url` IN (SELECT sri.origin_outbound_no FROM smt_return_order sr"
                    + " LEFT JOIN smt_return_order_item sri ON sri.return_id = sr.id" + " WHERE sr.express_no = '"
                    + expressNo + "')";
        }
        else if (StringUtils.equals(warehouseId, BatchReturnType.SHEIN.getCode())) {
            sql = "SELECT fba_no AS apvNo FROM wh_fba_allocation wf"
                    + " WHERE wf.`shipment_id` IN (SELECT sri.order_no FROM shein_return_order sr"
                    + " LEFT JOIN shein_return_order_item sri ON sri.return_id = sr.id" + " WHERE sr.express_no = '"
                    + expressNo + "')";
        }
        else if (StringUtils.equals(warehouseId, BatchReturnType.TEMU.getCode())) {
            sql = "SELECT purchase_sub_order_sn AS apvNo FROM temu_return_package  WHERE package_no = '" + expressNo
                    + "'";
            entity.setPlatform(SaleChannel.CHANNEL_TEMU);
        }
        if (StringUtils.isBlank(sql))
            return;
        List<Map<String, Object>> orderMap = DbTemplateUtils.executeSqlScript(sql);
        if (CollectionUtils.isEmpty(orderMap))
            return;
        String apvNo = orderMap.stream().filter(s -> Objects.nonNull(s.get("apvNo")))
                .map(s -> s.get("apvNo").toString()).distinct().collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(apvNo)){
            entity.setRelationBatchNo(apvNo);
        }
    }
    private void calculateCombineSkuCheckInPrice(AssetChangeItem entity) {
        if (entity == null || StringUtils.isBlank(entity.getSku()) || entity.getCurrentCheckinQuantity() == null
                || entity.getCurrentCheckinQuantity() == 0)
            return;

        // 对于组合SKU的子SKU，需要根据组合SKU的约束来计算价格
        // 约束：combo_sku.price × combo_sku.quantity = Σ(child_sku.price × child_sku.quantity)

        // 获取组合SKU信息
        String spu = entity.getSpu();
        if (StringUtils.isNotBlank(spu)) {
            try {
                // 查询组合SKU的配置信息
                WhCombineSkuQueryCondition query = new WhCombineSkuQueryCondition();
                query.setSpu(spu);
                List<WhCombineSku> combineSkus = whCombineSkuService.queryWhCombineSkus(query, null);

                if (CollectionUtils.isNotEmpty(combineSkus) && CollectionUtils.isNotEmpty(combineSkus.get(0).getItemList())) {
                    WhCombineSku combineSku = combineSkus.get(0);

                    // 计算组合SKU的理论总价值（基于子SKU的采购价格）
                    BigDecimal totalChildValue = BigDecimal.ZERO;
                    Map<String, Integer> childSkuQuantityMap = combineSku.getItemList().stream()
                            .collect(Collectors.toMap(WhCombineSkuItem::getSku, WhCombineSkuItem::getQuantity));

                    for (WhCombineSkuItem item : combineSku.getItemList()) {
                        SingleItemEs childItemEs = singleItemService.getSingleItemBySku(item.getSku());
                        if (childItemEs != null) {
                            BigDecimal childPrice = Optional.ofNullable(childItemEs.getCost()).orElse(BigDecimal.ZERO);
                            BigDecimal childQuantity = BigDecimal.valueOf(item.getQuantity());
                            totalChildValue = totalChildValue.add(childPrice.multiply(childQuantity));
                        }
                    }

                    // 计算当前子SKU在组合中的价值占比
                    Integer currentSkuQuantityInCombo = childSkuQuantityMap.get(entity.getSku());
                    if (currentSkuQuantityInCombo != null && currentSkuQuantityInCombo > 0) {
                        SingleItemEs currentItemEs = singleItemService.getSingleItemBySku(entity.getSku());
                        if (currentItemEs != null) {
                            BigDecimal currentSkuPrice = Optional.ofNullable(currentItemEs.getCost()).orElse(BigDecimal.ZERO);
                            BigDecimal currentSkuValueInCombo = currentSkuPrice.multiply(BigDecimal.valueOf(currentSkuQuantityInCombo));

                            // 按比例分配组合SKU的价值到当前子SKU
                            if (totalChildValue.compareTo(BigDecimal.ZERO) > 0) {
                                BigDecimal ratio = currentSkuValueInCombo.divide(totalChildValue, 4, RoundingMode.HALF_UP);
                                BigDecimal allocatedPrice = currentSkuPrice.multiply(ratio);
                                entity.setCurrentCheckinPrice(allocatedPrice);  // 设置单价
                                entity.setPrice(allocatedPrice.multiply(BigDecimal.valueOf(-entity.getCurrentCheckinQuantity())));  // 设置总价
                                return;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("计算组合SKU价格失败，使用默认逻辑: " + e.getMessage());
            }
        }

        // 默认逻辑：找不到组合SKU配置时，取产品系统当前的采购单价
        SingleItemEs itemEs = singleItemService.getSingleItemBySku(entity.getSku());
        if (itemEs == null)
            return;
        BigDecimal price = Optional.ofNullable(itemEs.getCost()).orElse(BigDecimal.ZERO);
        entity.setCurrentCheckinPrice(price);  // 设置单价
        entity.setPrice(price.multiply(BigDecimal.valueOf(-entity.getCurrentCheckinQuantity())));  // 设置总价
    }

    /**
     * 计算本期入库单价
     *
     * @param entity
     * @param sku
     */
    private void calculateCurrentCheckInPrice(AssetChangeItem entity, String sku) {
        log.info("================ start calculateCurrentCheckInPrice ===============");
        if (StringUtils.isBlank(sku) || entity == null || StringUtils.isEmpty(entity.getRelationBatchNo())) {
            return;
        }
        // 查询入库单采购单sku的入库记录（除去已废弃）
        WhCheckInQueryCondition query = new WhCheckInQueryCondition();
        String statusStr = CheckInStatus.WAITING_QC.intCode() + "," + CheckInStatus.WAITING_UP.intCode() + ","
                + CheckInStatus.UPING.intCode() + "," + CheckInStatus.UPERROR.intCode() + ","
                + CheckInStatus.CONFIRMED.intCode();
        List<Integer> statuses = new ArrayList<>();
        CollectionUtils.addAll(statuses, StringUtils.split(statusStr, ","));
        query.setStatuses(statuses);
        query.setSku(sku);
        query.setPurchaseOrderNo(entity.getRelationBatchNo());
        query.setReadOnly(true);
        List<WhCheckIn> whCheckInList = whCheckInDao.queryWhCheckInList(query, null);

        if (whCheckInList.size() == 0) {
            return;
        }

        BigDecimal purchasePrice = entity.getPrice();// PMS中采购价格

        // 运费占比改成从采购单里取
        PurchaseOrderPercentageQueryCondition percentageQuery = new PurchaseOrderPercentageQueryCondition();
        percentageQuery.setPurchaseOrderNo(entity.getRelationBatchNo());
        percentageQuery.setSku(sku);
        percentageQuery.setReadOnly(true);
        PurchaseOrderPercentage purchaseOrderPercentage = purchaseOrderPercentageDao
                .queryPurchaseOrderPercentage(percentageQuery);

        BigDecimal percentage = BigDecimal.valueOf(0);
        if (purchaseOrderPercentage != null && purchaseOrderPercentage.getPercentage() != null) {
            percentage = purchaseOrderPercentage.getPercentage();
        }

        if (percentage.compareTo(BigDecimal.ZERO) == 0) {
            // 重量占比为0时再重新计算一次
            WhPurchaseOrderQueryCondition queryCondition = new WhPurchaseOrderQueryCondition();
            queryCondition.setPurchaseOrderNo(entity.getRelationBatchNo());
            queryCondition.setReadOnly(true);
            List<WhPurchaseOrder> purchaseOrders = whPurchaseOrderDao.queryWhPurchaseOrderAndItemList(queryCondition,
                    null);
            if (CollectionUtils.isNotEmpty(purchaseOrders)) {
                percentage = PurchaseOrderPercentageUtils.calculatePercentage(purchaseOrders.get(0), sku);
            }
        }

        BigDecimal shippingCost = entity.getCost() == null ? BigDecimal.ZERO : entity.getCost();// 运费
        BigDecimal upQuantity = new BigDecimal(Optional.ofNullable(entity.getTotalQty()).orElse(entity.getCurrentCheckinQuantity()));// 上架数量

        whCheckInList.sort(Comparator.comparing(WhCheckIn::getInId));
        // PMS中采购价格+重量占比(数量占比)×运费/上架数量
        entity.setCurrentCheckinPrice(
                purchasePrice.add(percentage.multiply(shippingCost).divide(upQuantity, 2, BigDecimal.ROUND_HALF_UP)));
        if (whCheckInList.size() > 1
                && !StringUtils.equals(whCheckInList.get(0).getInId().toString(), entity.getOrderNo()))
            // 入库单是采购单内此SKU第n（n>1）次上架，本期入库单件=PMS采购价格
            entity.setCurrentCheckinPrice(purchasePrice);

        log.info("================ calculateCurrentCheckInPrice end ===============");
    }

    @Override
    public List<AssetChangeItem> queryDiffTransferQtyList(AssetChangeItemQueryCondition query,Integer step) {
        if (StringUtils.isEmpty(query.getFromDate()) || StringUtils.isEmpty(query.getToDate()))
            return new ArrayList<>();
        SqlerRequest request = new SqlerRequest("queryTransferCheckInUpQty");
        DrpTurnoverOderType orderType = DrpTurnoverOderType.build(String.valueOf(step));
        switch (orderType) {
            case CHECK_IN:
                request = new SqlerRequest("queryTransferCheckInUpQty");
                break;
            case FBA_OUT:
                request = new SqlerRequest("queryFbaOutUpQty");
                request.addDataParam("purposeHouseNotInList",DataType.STRING, SaleChannel.saleChannels);
                break;
            case FIRST_ORDER_OUT:
                request = new SqlerRequest("queryFbaOutUpQty");
                request.addDataParam("purposeHouseList",DataType.STRING, SaleChannel.saleChannels);
                break;
            case ALLOCATION_OUT:
                request = new SqlerRequest("queryOwnerUserChangeQty");
                break;
            case INVENTORY_ORDER_OUT:
                request = new SqlerRequest("queryTransferInventoryOutQty");
                break;
            case MERGE_SKU:
                request = new SqlerRequest("queryTransferMergeSkuQty");
                break;
            case PDD_OUT:
                request = new SqlerRequest("queryPddOutQty");
                break;
            case RETURN_ORDER:
                request = new SqlerRequest("queryPddReturnUpQty");
                break;
            default:
                break;
        }
        request.setReadOnly(true);
        request.addDataParam("fromDate", DataType.STRING, query.getFromDate());
        request.addDataParam("toDate", DataType.STRING, query.getToDate());
        if (StringUtils.isNotBlank(query.getSkuStr())) {
            if (StringUtils.contains(query.getSkuStr(), ",")) {
                List<String> skuList = CommonUtils.splitList(query.getSkuStr(), ",");
                request.addDataParam("skuList", DataType.STRING, skuList);
            }
            else {
                request.addDataParam("skuList", DataType.STRING, query.getSkuStr());
            }
        }
        return SqlerTemplate.query(request, new RowMapper<AssetChangeItem>() {

            @Override
            public AssetChangeItem mapRow(ResultSet rs, int rowNum) throws SQLException {
                AssetChangeItem entity = new AssetChangeItem();
                entity.setSku(rs.getString(WhStockMonitorsDBField.SKU));
                entity.setCreateDate(Timestamp.valueOf(rs.getString("orderDate")));
                entity.setOrderNo(rs.getString("orderNo"));
                entity.setInventoryType(rs.getInt("inventoryType"));
                entity.setOrderType(rs.getInt("orderType"));
                entity.setAccountNumber(rs.getString("accountNumber"));
                entity.setCurrentCheckinQuantity(rs.getObject("quantity") == null ? 0 : rs.getInt("quantity"));
                entity.setPlatform(rs.getString("platform"));
                if (orderType.equals(DrpTurnoverOderType.CHECK_IN)) {
                    entity.setRelationBatchNo(rs.getString("relationBatchNo"));
                    entity.setTotalWeight(
                            rs.getObject("totalWeight") == null ? BigDecimal.ZERO : rs.getBigDecimal("totalWeight"));
                    entity.setTotalQty(rs.getObject("totalQty") == null ? 0 : rs.getInt("totalQty"));
                    entity.setCost(rs.getObject("orderCost") == null ? BigDecimal.ZERO : rs.getBigDecimal("orderCost"));
                    entity.setPrice(
                            rs.getObject("orderPrice") == null ? BigDecimal.ZERO : rs.getBigDecimal("orderPrice"));
                    try {
                        calculateCurrentCheckInPrice(entity, entity.getSku());
                    }
                    catch (Exception e) {
                        entity.setCurrentCheckinPrice(entity.getPrice());
                        log.error("计算入库单价失败" + e.getMessage(), e);
                    }
                }
                if (orderType.equals(DrpTurnoverOderType.FIRST_ORDER_OUT))
                    entity.setInventoryType(DrpTurnoverOderType.FIRST_ORDER_OUT.intCode());
                if (orderType.equals(DrpTurnoverOderType.FBA_OUT)){
                    entity.setInventoryType(DrpTurnoverOderType.FBA_OUT.intCode());
                    entity.setPlatform(SaleChannel.CHANNEL_AMAZON);
                }
                if (orderType.equals(DrpTurnoverOderType.RETURN_ORDER)){
                    entity.setRelationBatchNo(rs.getString("relationBatchNo"));
                }

                if (orderType.equals(DrpTurnoverOderType.ALLOCATION_OUT)){
                    entity.setOutAccountNumber(rs.getString("outAccountNumber"));
                    entity.setOutPlatform(rs.getString("outPlatform"));
                }
                return entity;
            }
        });
    }
}