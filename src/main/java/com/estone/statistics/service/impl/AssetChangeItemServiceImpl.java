package com.estone.statistics.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.allocation.util.AllocationPushUtil;
import com.estone.common.executors.CommonThread;
import com.estone.common.executors.ExecutorUtils;
import com.estone.common.util.ArithmeticUtils;
import com.estone.common.util.BeanConvertUtils;
import com.estone.common.util.DateUtils;
import com.estone.common.util.PagingUtils;
import com.estone.common.util.model.ApiResult;
import com.estone.elasticsearch.model.SingleItemEs;
import com.estone.elasticsearch.service.IEsSingleItemService;
import com.estone.sku.bean.WhSku;
import com.estone.sku.utils.ProductSkuQueryCondition;
import com.estone.sku.utils.ProductSkuQueryUtils;
import com.estone.statistics.bean.*;
import com.estone.statistics.dao.AssetChangeItemDao;
import com.estone.statistics.domain.WhAssetChangeItemDo;
import com.estone.statistics.enums.AssetInventoryType;
import com.estone.statistics.enums.AssetOrderType;
import com.estone.statistics.enums.DrpTurnoverInventoryType;
import com.estone.statistics.enums.DrpTurnoverOderType;
import com.estone.statistics.service.AssetChangeItemService;
import com.whq.tool.component.Pager;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.exception.DBErrorCode;
import com.whq.tool.sqler.SqlerException;

import lombok.extern.slf4j.Slf4j;

@Service("assetChangeItemService")
@Slf4j
public class AssetChangeItemServiceImpl implements AssetChangeItemService {

    private final static ExecutorService executors = ExecutorUtils.newFixedThreadPool(50, 5 * 60 * 1000);

    @Resource
    private AssetChangeItemDao assetChangeItemDao;

    @Resource
    private IEsSingleItemService singleItemService;

    @Override
    public AssetChangeItem getAssetChangeItem(Integer id) {
        AssetChangeItem assetChangeItem = assetChangeItemDao.queryAssetChangeItem(id);
        return assetChangeItem;
    }

    @Override
    public AssetChangeItem getAssetChangeItemDetail(Integer id) {
        AssetChangeItem assetChangeItem = assetChangeItemDao.queryAssetChangeItem(id);
        // 关联查询
        return assetChangeItem;
    }

    @Override
    public AssetChangeItem queryAssetChangeItem(AssetChangeItemQueryCondition query) {
        Assert.notNull(query, "query is null!");
        AssetChangeItem assetChangeItem = assetChangeItemDao.queryAssetChangeItem(query);
        return assetChangeItem;
    }

    @Override
    public List<AssetChangeItem> queryAllAssetChangeItems() {
        return assetChangeItemDao.queryAssetChangeItemList();
    }

    @Override
    public List<AssetChangeItem> queryAssetChangeItems(AssetChangeItemQueryCondition query, Pager pager) {
        Assert.notNull(query, "query is null!");
        if (pager != null && pager.isQueryCount()) {
            int count = assetChangeItemDao.queryAssetChangeItemCount(query);
            pager.setTotalCount(count);
            if (count == 0) {
                return new ArrayList<AssetChangeItem>();
            }
        }
        List<AssetChangeItem> assetChangeItems = assetChangeItemDao.queryAssetChangeItemList(query, pager);
        return assetChangeItems;
    }

    @Override
    public void createAssetChangeItem(AssetChangeItem assetChangeItem) {
        try {
            assetChangeItemDao.createAssetChangeItem(assetChangeItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchCreateAssetChangeItem(List<AssetChangeItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                assetChangeItemDao.batchCreateAssetChangeItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public void deleteAssetChangeItem(Integer id) {
        try {
            assetChangeItemDao.deleteAssetChangeItem(id);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void updateAssetChangeItem(AssetChangeItem assetChangeItem) {
        try {
            assetChangeItemDao.updateAssetChangeItem(assetChangeItem);
        }
        catch (SqlerException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
    }

    @Override
    public void batchUpdateAssetChangeItem(List<AssetChangeItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            try {
                assetChangeItemDao.batchUpdateAssetChangeItem(entityList);
            }
            catch (SqlerException e) {
                log.error(e.getMessage(), e);
                throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
            }
        }
    }

    @Override
    public List<AssetChangeItemCalcBean> queryAssetChangeItemCalcBean() {
        return assetChangeItemDao.queryAssetChangeItemCalcBean();
    }

    @Override
    public void queryAssetChangeItemsCount(AssetChangeItemQueryCondition query, Pager pager,
            WhAssetChangeItemDo domain) {
        List<AssetChangeItem> assetChangeItems = queryAssetChangeItems(query, pager);
        domain.setWhAssetChangeItems(JSONObject.parseObject(JSONObject.toJSONString(assetChangeItems),
                new TypeReference<List<WhAssetChangeItem>>() {
                }));
        if (Boolean.TRUE.equals(query.getIsTotal())) {
            AssetChangeItemGroup changeItemGroup = assetChangeItemDao.queryAssetChangeItemGroup(query, pager);
            domain.setWhAssetChangeItemGroup(JSONObject.parseObject(JSONObject.toJSONString(changeItemGroup),
                    new TypeReference<WhAssetChangeItemGroup>() {
                    }));
        }
    }

    @Override
    public void batchUpdateAssetChangeItemByCalculate(List<AssetChangeItemCalcBean> calcBeanList) {
        try {
            int total = calcBeanList.size();
            int pageSize = 1000;
            int totalPage = (total + pageSize - 1) / pageSize;
            CountDownLatch countDownLatch = new CountDownLatch(totalPage);
            for (int pageNo = 1; pageNo <= totalPage; pageNo++) {
                Map<String, Object> map = new HashMap<>();
                map.put("pageNo", pageNo);
                map.put("countDownLatch", countDownLatch);
                executors.execute(new CommonThread(map, new Function<Map<String, Object>, Object>() {
                    @Override
                    public Object apply(Map<String, Object> t) {
                        try {
                            List<AssetChangeItemCalcBean> itemList = PagingUtils.paging(calcBeanList,
                                    (int) t.get("pageNo"), pageSize);
                            updateAssetChangeItemByCalculate(itemList);
                        }
                        finally {
                            ((CountDownLatch) map.get("countDownLatch")).countDown();
                        }
                        return null;
                    }
                }));
            }
            countDownLatch.await();
        }
        catch (Exception e) {
            log.error("资产报表统计失败！", e);
        }
    }

    @Override
    public void callFistAndPriceZeroAssetChangeItemGroupBySku() {
        List<AssetChangeItem> itemList = assetChangeItemDao.queryFistAndPriceZeroAssetChangeItemGroupBySku();
        if (CollectionUtils.isNotEmpty(itemList)) {
            List<String> skus = itemList.stream().map(item -> item.getSku()).collect(Collectors.toList());
            ProductSkuQueryCondition productQuery = new ProductSkuQueryCondition();
            productQuery.setArticleNumbers(skus);
            List<WhSku> pmsSkuList = ProductSkuQueryUtils.getSkusByProduct(productQuery);
            if (CollectionUtils.isEmpty(pmsSkuList)) {
                log.error("产品系统未查询需要同步的sku");
                return;
            }
            Map<String, Double> pricMap = pmsSkuList.stream()
                    .collect(Collectors.toMap(WhSku::getSku, pmsSku -> pmsSku.getAveragePurchasePrice()));
            for (AssetChangeItem item : itemList) {
                try {
                    /*
                     * item.setOpeningInventoryQuantity(0);
                     * item.setOpeningInventoryPrice(BigDecimal.ZERO);
                     * item.setOpeningInventoryAmount(BigDecimal.ZERO);
                     */
                    if (item.getOpeningInventoryQuantity() == null) {
                        item.setOpeningInventoryQuantity(0);
                    }
                    if (item.getOpeningInventoryPrice() == null) {
                        item.setOpeningInventoryPrice(BigDecimal.ZERO);
                    }
                    if (item.getOpeningInventoryAmount() == null) {
                        item.setOpeningInventoryAmount(BigDecimal.ZERO);
                    }

                    Double b = pricMap.get(item.getSku()) == null ? 0d : pricMap.get(item.getSku());
                    BigDecimal price = new BigDecimal(String.valueOf(ArithmeticUtils.round(b, 2)));
                    item.setCurrentCheckinPrice(price);
                    if (item.getCurrentCheckinQuantity() == null) {
                        item.setCurrentCheckinQuantity(0);
                    }
                    if (item.getCurrentCheckinQuantity().equals(0)) {
                        item.setCurrentCheckinAmount(BigDecimal.ZERO);
                    }
                    else {
                        item.setCurrentCheckinAmount(item.getCurrentCheckinPrice()
                                .multiply(new BigDecimal(item.getCurrentCheckinQuantity())));
                    }
                    item.setEndingInventoryQuantity(
                            item.getOpeningInventoryQuantity() + item.getCurrentCheckinQuantity());
                    item.setEndingInventoryAmount(item.getOpeningInventoryAmount().add(item.getCurrentCheckinAmount()));
                    item.setEndingInventoryPrice(item.getCurrentCheckinPrice());
                    if (BigDecimal.ZERO.equals(item.getCurrentCheckinPrice())
                            || item.getCurrentCheckinPrice().doubleValue() == 0) {
                        log.warn("SKU: ".concat(item.getSku()).concat(" currentCheckinPrice is 0"));
                    }
                    assetChangeItemDao.updateAssetChangeItem(item);
                }
                catch (Exception e) {
                    log.error("计算第分组第一条SKU当期价格异常！".concat(item.getSku()), e);
                }
            }
            // whAssetChangeItemDao.batchUpdateWhAssetChangeItem(itemList);
        }
    }


    public void updateAssetChangeItemByCalculate(List<AssetChangeItemCalcBean> calcBeanList) {
        for (AssetChangeItemCalcBean sku : calcBeanList) {
            try {
                List<AssetChangeItem> skuItemList = sku.getAssetItemList();
                List<AssetChangeItem> updateItemList = new ArrayList<>();
                for (int i = 0; i < skuItemList.size(); i++) {
                    AssetChangeItem currentIem = skuItemList.get(i);
                    if (i == 0) {
                        AssetChangeItemQueryCondition itemQuery = new AssetChangeItemQueryCondition();
                        itemQuery.setId(currentIem.getId());
                        itemQuery.setSku(currentIem.getSku());
                        AssetChangeItem lastItem = assetChangeItemDao.queryPreAssetChangeItem(itemQuery);
                        updateItemList.add(operationAssetChangeItem(lastItem, currentIem));
                    }
                    else {
                        updateItemList.add(operationAssetChangeItem(skuItemList.get(i - 1), currentIem));
                    }
                }
                assetChangeItemDao.batchUpdateAssetChangeItem(updateItemList);
            }
            catch (Exception e) {
                log.warn("资产明细计算价格异常！sku=" + sku.getSku() + "," + e);
            }
        }
    }

    public AssetChangeItem operationAssetChangeItem(AssetChangeItem lastItem, AssetChangeItem currentItem) {
        /* 第一次入库 */
        if (null == lastItem && (AssetInventoryType.CHECK_IN.intCode().equals(currentItem.getInventoryType())
                || AssetInventoryType.INVENTORY_ALLOCATION.intCode().equals(currentItem.getInventoryType()))) {
            lastItem = new AssetChangeItem();
            lastItem.setEndingInventoryQuantity(0);
            lastItem.setEndingInventoryPrice(BigDecimal.ZERO);
            lastItem.setEndingInventoryAmount(BigDecimal.ZERO);
        }

        /* 期初 */
        if (null == lastItem) {// 计算第一条
            throw new BusinessException("没有初始化第一条数据，资产明细算价失败!");
        }

        currentItem.setOpeningInventoryQuantity(lastItem.getEndingInventoryQuantity());
        currentItem.setOpeningInventoryPrice(lastItem.getEndingInventoryPrice());
        currentItem.setOpeningInventoryAmount(lastItem.getEndingInventoryAmount());

        if (AssetInventoryType.CHECK_IN.intCode().equals(currentItem.getInventoryType())
                && !AssetOrderType.LEND_ORDER.intCode().equals(currentItem.getOrderType())) {
            if (null == currentItem.getCurrentCheckinPrice()) {
                throw new BusinessException("没有初始化采购本期价格，资产明细算价失败!");
            }
        }
        else {
            // 本期入库单价 取 期初入库单价
            currentItem.setCurrentCheckinPrice(currentItem.getOpeningInventoryPrice());
        }
        // 本期入库金额
        currentItem.setCurrentCheckinAmount(
                currentItem.getCurrentCheckinPrice().multiply(new BigDecimal(currentItem.getCurrentCheckinQuantity())));

        /* 期末 */
        currentItem.setEndingInventoryQuantity(
                currentItem.getOpeningInventoryQuantity() + currentItem.getCurrentCheckinQuantity());
        currentItem.setEndingInventoryAmount(
                currentItem.getOpeningInventoryAmount().add(currentItem.getCurrentCheckinAmount()));
        if (currentItem.getEndingInventoryQuantity().equals(0)) {
            currentItem.setEndingInventoryPrice(currentItem.getCurrentCheckinPrice());
        }
        else if (BigDecimal.ZERO.equals(currentItem.getEndingInventoryAmount())) {
            currentItem.setEndingInventoryPrice(currentItem.getCurrentCheckinPrice());
        }
        else {
            currentItem.setEndingInventoryPrice(currentItem.getEndingInventoryAmount()
                    .divide(new BigDecimal(currentItem.getEndingInventoryQuantity()), 2, BigDecimal.ROUND_HALF_UP));
        }
        return currentItem;
    }

    /**
     * 获取不同类型单据的数量集合
     *
     * @param query
     * @return
     */
    @Override
    public List<AssetChangeItem> queryDiffAssetInventoryTypeQtyList(AssetChangeItemQueryCondition query) {
        if (StringUtils.isEmpty(query.getFromDate()) || StringUtils.isEmpty(query.getToDate())) {
            Date toDay = new Date();
            String startDate = DateUtils.dateToString(DateUtils.getYesterdayDate(toDay), DateUtils.DEFAULT_FORMAT);
            String endDate = DateUtils.dateToString(toDay, DateUtils.DEFAULT_FORMAT);
            query.setFromDate(startDate);
            query.setToDate(endDate);
        }
        Map<String,List<AssetChangeItem>> itemMap = new ConcurrentHashMap<>();
        List<AssetChangeItem> assetChangeItemList = new CopyOnWriteArrayList<>();
        itemMap.put("result",assetChangeItemList);

        log.warn("---queryDiffAssetInventoryTypeQtyList start---");
        CountDownLatch countDownLatch = new CountDownLatch(24);
        for (int step = 1; step <= 24; step++) {
            Map<String, Object> map = new HashMap<>();
            map.put("step", step);
            map.put("countDownLatch", countDownLatch);
            executors.execute(new CommonThread(map, new Function<Map<String, Object>, Object>() {

                @Override
                public Object apply(Map<String, Object> t) {
                    try {
                        int pStep = (int) map.get("step");
                        List<AssetChangeItem> list = assetChangeItemDao.queryDiffAssetInventoryTypeQtyList(query,pStep);

                        if (CollectionUtils.isNotEmpty(list)){
                            if (pStep == 23){
                                List<String> orderNoList = list.stream().map(AssetChangeItem::getRelationBatchNo).distinct().collect(Collectors.toList());
                                ApiResult<List<WhDrpTurnoverItme>> apiResult = AllocationPushUtil.getAllocationOutBatchInfo(orderNoList);
                                if (apiResult.isSuccess() && CollectionUtils.isNotEmpty(apiResult.getResult())) {
                                    List<WhDrpTurnoverItme> items = JSONObject.parseObject(JSONObject.toJSONString(apiResult.getResult()),
                                            new TypeReference<List<WhDrpTurnoverItme>>() {
                                            });
                                    Map<String, List<WhDrpTurnoverItme>> listMap = items.stream()
                                            .collect(Collectors
                                                    .groupingBy(o -> (o.getOrderNo() + o.getSku()).toUpperCase()));
                                    list.forEach(item -> {
                                        String key = (item.getRelationBatchNo() + item.getSku()).toUpperCase();
                                        if (CollectionUtils.isEmpty(listMap.get(key))) {
                                            // 找不到的取产品系统当前的采购单价
                                            SingleItemEs itemEs = singleItemService.getSingleItemBySku(item.getSku());
                                            if (itemEs == null)
                                                return;
                                            BigDecimal price = Optional.ofNullable(itemEs.getCost()).orElse(BigDecimal.ZERO);
                                            item.setCurrentCheckinPrice(price);
                                        }
                                        else {
                                            WhDrpTurnoverItme drpTurnoverItem = listMap.get(key).stream()
                                                    .max(Comparator.comparing(WhDrpTurnoverItme::getReportTime)
                                                            .thenComparing(WhDrpTurnoverItme::getId))
                                                    .get();
                                            item.setCurrentCheckinPrice(drpTurnoverItem.getPurchaseCostPrice());
                                            item.setCost(drpTurnoverItem.getPurchaseFreightPrice());
                                            item.setRelationBatchNo(drpTurnoverItem.getBatchNo());
                                        }
                                    });
                                }
                            }
                            if (pStep == 21) {
                                Map<String, List<AssetChangeItem>> listMap = list.stream().collect(Collectors.groupingBy(AssetChangeItem::getSpu));
                                List<AssetChangeItem> spuList = new ArrayList<>();
                                for (Map.Entry<String, List<AssetChangeItem>> entry : listMap.entrySet()) {
                                    AssetChangeItem skuItem = entry.getValue().get(0);
                                    AssetChangeItem spuItem = BeanConvertUtils.convert(skuItem, AssetChangeItem.class);
                                    spuItem.setSku(skuItem.getSpu());
                                    spuItem.setCurrentCheckinQuantity(skuItem.getSpuQty());
                                    double sumPrice = entry.getValue().stream()
                                            .mapToDouble(i -> i.getPrice() == null ? 0 : i.getPrice().doubleValue())
                                            .sum();
                                    if (skuItem.getSpuQty() > 0){
                                        spuItem.setCurrentCheckinPrice(new BigDecimal(sumPrice).divide(BigDecimal.valueOf(skuItem.getSpuQty()),2, RoundingMode.HALF_UP));
                                        spuItem.setCost(spuItem.getCurrentCheckinPrice());
                                    }
                                    spuItem.setInventoryType(DrpTurnoverInventoryType.WAREHOUSE_CHECK_IN.intCode());
                                    spuList.add(spuItem);
                                }
                                list.addAll(spuList);
                            }
                            itemMap.get("result").addAll(list);
                        }
                    }
                    catch (Exception e) {
                        log.error("queryDiffAssetInventoryTypeQtyList 查询异常！", e);
                    }
                    finally {
                        ((CountDownLatch) t.get("countDownLatch")).countDown();
                    }
                    return null;
                }

            }));
        }
        try {
            countDownLatch.await();
        }
        catch (Exception e) {
            throw new BusinessException(DBErrorCode.DB_OPERATION_FAILED, e);
        }
        log.warn("---queryDiffAssetInventoryTypeQtyList end---");

        if (CollectionUtils.isNotEmpty(itemMap.get("result")))
            assetChangeItemList = itemMap.get("result").stream().sorted(Comparator
                    .comparing(AssetChangeItem::getCreateDate).thenComparing(AssetChangeItem::getInventoryType))
                    .collect(Collectors.toList());
        return assetChangeItemList;
    }

    @Override
    public List<AssetChangeItem> queryDiffTransferQtyList(AssetChangeItemQueryCondition query) {
        List<AssetChangeItem> itemList = new ArrayList<>();
        //中转仓上架
        List<AssetChangeItem> checkInList = assetChangeItemDao.queryDiffTransferQtyList(query, DrpTurnoverOderType.CHECK_IN.intCode());
        if (CollectionUtils.isNotEmpty(checkInList))
            itemList.addAll(checkInList);
        //fba出库
        List<AssetChangeItem> fbaOutList = assetChangeItemDao.queryDiffTransferQtyList(query, DrpTurnoverOderType.FBA_OUT.intCode());
        if (CollectionUtils.isNotEmpty(fbaOutList))
            itemList.addAll(fbaOutList);
        //头程出库
        List<AssetChangeItem> firstOutList = assetChangeItemDao.queryDiffTransferQtyList(query, DrpTurnoverOderType.FIRST_ORDER_OUT.intCode());
        if (CollectionUtils.isNotEmpty(firstOutList))
            itemList.addAll(firstOutList);
        //盘点
        List<AssetChangeItem> inventoryOrderOutList = assetChangeItemDao.queryDiffTransferQtyList(query, DrpTurnoverOderType.INVENTORY_ORDER_OUT.intCode());
        if (CollectionUtils.isNotEmpty(inventoryOrderOutList))
            itemList.addAll(inventoryOrderOutList);
        //调拨入库
        List<AssetChangeItem> allocationInList = assetChangeItemDao.queryDiffTransferQtyList(query, DrpTurnoverOderType.ALLOCATION_OUT.intCode());
        if (CollectionUtils.isNotEmpty(allocationInList))
            itemList.addAll(allocationInList);
        //合并
        List<AssetChangeItem> mergeSkuList = assetChangeItemDao.queryDiffTransferQtyList(query, DrpTurnoverOderType.MERGE_SKU.intCode());
        if (CollectionUtils.isNotEmpty(mergeSkuList))
            itemList.addAll(mergeSkuList);

        //拼多多出库
        List<AssetChangeItem> pddOutList = assetChangeItemDao.queryDiffTransferQtyList(query, DrpTurnoverOderType.PDD_OUT.intCode());
        if (CollectionUtils.isNotEmpty(pddOutList))
            itemList.addAll(pddOutList);
        //拼多多退件
        List<AssetChangeItem> pddReturnUpList = assetChangeItemDao.queryDiffTransferQtyList(query, DrpTurnoverOderType.RETURN_ORDER.intCode());
        if (CollectionUtils.isNotEmpty(pddReturnUpList))
            itemList.addAll(pddReturnUpList);
        return itemList;
    }
}