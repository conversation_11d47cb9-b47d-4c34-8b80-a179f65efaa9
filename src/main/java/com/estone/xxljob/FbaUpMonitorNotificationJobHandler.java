package com.estone.xxljob;

import com.estone.transfer.bean.FbaUpMonitor;
import com.estone.transfer.bean.FbaUpMonitorQueryCondition;
import com.estone.transfer.enums.FbaAlertStatus;
import com.estone.transfer.enums.FbaStatus;
import com.estone.transfer.service.FbaUpMonitorService;
import com.estone.xxljob.config.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * FBA货件上架监控通知定时任务
 * 推送中转仓未上架货件的消息给钉钉
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@Component
public class FbaUpMonitorNotificationJobHandler extends AbstractJobHandler {

    FbaUpMonitorNotificationJobHandler() {
        super("FbaUpMonitorNotificationJobHandler");
    }

    @Resource
    private FbaUpMonitorService fbaUpMonitorService;


    @Override
    public ReturnT<String> run(String param) throws Exception {
        try {
            log.info("---invoke FbaUpMonitorNotificationJobHandler start---");

            // 查询超期预警的FBA货件
            FbaUpMonitorQueryCondition query = new FbaUpMonitorQueryCondition();
            query.setFbaStatusList(Arrays.asList(FbaStatus.PARTIAL.getCode(), FbaStatus.RECEIVED.getCode()));
            query.setAlertStatusList(Arrays.asList(FbaAlertStatus.PEAK_ALERT.getCode(), FbaAlertStatus.SEASON_ALERT.getCode(), FbaAlertStatus.OVERTIME.getCode()));
            List<FbaUpMonitor> fbaUpMonitors = fbaUpMonitorService.queryFbaUpMonitors(query, null);

            if (CollectionUtils.isEmpty(fbaUpMonitors)) {
                log.info("没有查询到超期预警的FBA货件，任务结束");
                return ReturnT.SUCCESS;
            }
            fbaUpMonitorService.pushDingTalkNotifications(fbaUpMonitors);
            log.info("---invoke FbaUpMonitorNotificationJobHandler end---");
        } catch (Exception e) {
            log.error("FBA货件上架监控通知任务执行失败", e);
            return ReturnT.FAIL;
        }
        
        return ReturnT.SUCCESS;
    }






} 