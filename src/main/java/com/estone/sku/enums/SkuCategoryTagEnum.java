package com.estone.sku.enums;

/**
 * @Description: SKU分类标签
 * @Author: Yimeil
 * @Date: 2024/7/11 15:24
 * @Version: 1.0.0
 */
public enum SkuCategoryTagEnum {
    TOYS("玩具", "1"),
    MOTHER_AND_BABY("母婴", "3"),
    MBC("孕婴童", "5"),
    HOME_APPLIANCES("家电", "7"),
    OTHER_HOME_APPLIANCES("其他家电", "9"),
    JEWELRY("珠宝", "10"),
    FRAGILE("易碎", "11"),
    AUTO_PARTS_CIRCLE("汽配-圆形E", "13"),
    AUTO_PARTS_SQUARE("汽配-方形e1", "15"),

    ;

    private String code;

    private String name;

    private SkuCategoryTagEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public Integer intCode() {
        return Integer.valueOf(code);
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(String code) {
        SkuCategoryTagEnum[] values = values();
        for (SkuCategoryTagEnum type : values) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }
}
