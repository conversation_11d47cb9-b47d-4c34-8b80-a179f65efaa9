package com.estone.allocation.service;

import com.estone.allocation.bean.dto.LoadingSubmitData;
import com.estone.android.domain.AndroidProductDo;
import com.whq.tool.json.ResponseJson;

/**
 * 调拨装车处理服务接口
 * 
 * <AUTHOR>
 * @version v1.0
 */
public interface WhAllocationHandleService {

    /**
     * 扫描箱号获取调拨单信息
     * 
     * @param domain 扫描参数（包含箱号、送货方式、物流单号等）
     * @return 调拨单信息（包含箱号、调拨单号、装车箱数等）
     */
    ResponseJson scanAllocationBox(AndroidProductDo domain);


    /**
     * 批量装车处理（基于PDA扫描的批量数据）
     *
     * @param submitData 批量装车提交数据
     * @return 装车处理结果
     * <AUTHOR>
     */
    ResponseJson updateAndLoading(LoadingSubmitData submitData);

} 