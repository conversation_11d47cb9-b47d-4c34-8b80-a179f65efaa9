package com.estone.allocation.service.impl;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.estone.allocation.bean.*;
import com.estone.allocation.bean.dto.LoadingBoxInfo;
import com.estone.allocation.bean.dto.LoadingSubmitData;
import com.estone.allocation.dao.WhApvAllocationDao;
import com.estone.allocation.enums.AllocationBoardTypeEnum;
import com.estone.allocation.enums.AllocationLoadStatusEnum;
import com.estone.allocation.enums.AllocationStatusEnum;
import com.estone.allocation.enums.DeliveryMethodEnum;
import com.estone.allocation.service.WhAllocationHandleService;
import com.estone.allocation.service.WhApvAllocationBoardService;
import com.estone.allocation.service.WhApvAllocationService;
import com.estone.allocation.util.AllocationPushUtil;
import com.estone.android.domain.AndroidProductDo;
import com.estone.common.util.RedisConstant;
import com.estone.common.util.StringRedisUtils;
import com.estone.common.util.SystemLogUtils;
import com.whq.tool.component.StatusCode;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.exception.BusinessException;
import com.whq.tool.json.ResponseJson;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 调拨装车处理服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service("whAllocationHandleService")
public class WhAllocationHandleServiceImpl implements WhAllocationHandleService {

    /** Redis Key常量定义 */
    private static final String ALLOCATION_SCAN_KEY_PREFIX = RedisConstant.ALLOCATION_SCAN_KEY_PREFIX;

    /** 扫描记录过期时间：2小时 */
    private static final long SCAN_EXPIRE_TIME_SECONDS = 2L * 60 * 60;

    @Resource
    private WhApvAllocationService whApvAllocationService;

    @Resource
    private WhApvAllocationBoardService whApvAllocationBoardService;

    @Resource
    private WhApvAllocationDao whApvAllocationDao;

    /**
     * 扫描箱号获取调拨单信息
     */
    @Override
    public ResponseJson scanAllocationBox(AndroidProductDo domain) {
        log.info("扫描箱号获取调拨单信息，箱号：{}", domain.getAllocationBoxNo());

        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        try {
            // 1. 参数验证
            String validationResult = validateScanParams(domain);
            if (StringUtils.isNotBlank(validationResult)) {
                response.setMessage(validationResult);
                return response;
            }

            // 2. 箱号验证
            WhApvAllocationBoard box = validateAndGetBox(domain.getAllocationBoxNo());
            if (box == null) {
                response.setMessage("箱号错误：请检查箱号是否可以使用！");
                return response;
            }

            // 3. 查询关联调拨单
            List<WhApvAllocation> allocations = queryAllocationsByBox(box);
            if (CollectionUtils.isEmpty(allocations)) {
                response.setMessage("没有绑定该箱号的调拨单");
                return response;
            }

            // 4. 获取用户信息
            Integer userIdInt = DataContextHolder.getUserId();
            String userId = userIdInt != null ? userIdInt.toString() : "未知用户";
            WhApvAllocation allocation = allocations.get(0);
            String allocationNo = allocation.getAllocationNo();

            // 5. 检查箱号是否已被其他用户扫描
            if (isBoxScannedByOtherUser(allocationNo, domain.getAllocationBoxNo(), userId)) {
                response.setMessage("箱号" + domain.getAllocationBoxNo() + "已被其他用户扫描，请勿重复扫描");
                return response;
            }

            // 6. 计算装车箱数
            LoadingBoxInfo boxInfo = calculateLoadingBoxCount(allocationNo, domain.getAllocationBoxNo());

            // 7. 保存扫描记录
            saveScanRecord(allocationNo, domain, userId);

            // 8. 构建返回数据
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("allocationNo", allocation.getAllocationNo());
            resultData.put("boxNo", boxInfo.getCurrentBoxNo());
            resultData.put("loadingBoxCount", boxInfo.getLoadingBoxCount());

            response.setStatus(StatusCode.SUCCESS);
            response.setBody(resultData);
            response.setMessage("扫描成功");

        }
        catch (Exception e) {
            log.error("扫描箱号失败", e);
            response.setMessage("扫描失败：" + e.getMessage());
        }

        return response;
    }

    /**
     * 批量装车处理（基于PDA扫描的批量数据）
     *
     * @param submitData 批量装车提交数据
     * @return 装车处理结果
     * <AUTHOR>
     */
    @Override
    public ResponseJson updateAndLoading(LoadingSubmitData submitData) {
        log.info("执行批量装车处理，调拨单项目数量：{}", submitData.getItemList() != null ? submitData.getItemList().size() : 0);

        ResponseJson response = new ResponseJson();
        response.setStatus(StatusCode.FAIL);

        // 1. 数据验证
        String validated = validateSubmitData(submitData);
        if (StringUtils.isNotBlank(validated)) {
            response.setMessage(validated);
            return response;
        }

        // 2. 按调拨单分组
        Map<String, List<LoadingSubmitData.AllocationItem>> groupedData = submitData.getItemList().stream()
                .collect(Collectors.groupingBy(LoadingSubmitData.AllocationItem::getAllocationNo));

        // 3. 批量查询调拨单信息
        List<WhApvAllocation> allocations = queryAllocations(new ArrayList<>(groupedData.keySet()));
        if (CollectionUtils.isEmpty(allocations)) {
            response.setMessage("调拨单不存在");
            return response;
        }

        // 4. 状态验证
        String statusValidation = validateAllocationStatus(allocations, groupedData);
        if (StringUtils.isNotBlank(statusValidation)) {
            response.setMessage(statusValidation);
            return response;
        }

        try {
            // 5. 执行批量处理
            BatchProcessResult processResult = batchUpdateAllocationLoading(allocations, groupedData, submitData);

            // 6. 记录操作日志
            logBatchOperation(processResult, submitData);

            //7.推送到目的仓
            AllocationPushUtil.pushStockAllocationData(processResult.getAllocationIdList());

            response.setStatus(StatusCode.SUCCESS);
            response.setMessage("批量装车成功，共处理 " + processResult.getAllocationIdList().size() + " 个调拨单，"
                    + processResult.getBoxNoList().size() + " 个箱号");
            log.info("批量装车完成，调拨单数量：{}，箱号数量：{}", processResult.getAllocationIdList().size(),
                    processResult.getBoxNoList().size());

        } catch (Exception e) {
            log.error("批量装车处理失败：{}", e.getMessage(), e);
            response.setMessage("装车失败：" + e.getMessage());
            return response;
        }

        return response;
    }

    // ================== 私有方法 ==================

    /**
     * 参数验证
     */
    private String validateScanParams(AndroidProductDo domain) {
        if (StringUtils.isBlank(domain.getAllocationBoxNo())) {
            return "请输入箱号";
        }
        if (domain.getDeliveryMethod() == null) {
            return "送货方式不能为空";
        }
        if (DeliveryMethodEnum.LOGISTICS.intCode().equals(domain.getDeliveryMethod())
                && StringUtils.isBlank(domain.getLogisticsNo())) {
            return "送货方式为物流时，物流单号不能为空";
        }
        return null;
    }

    /**
     * 验证提交数据
     */
    private String validateSubmitData(LoadingSubmitData submitData) {
        if (submitData == null) {
            return "提交数据不能为空";
        }
        if (submitData.getDeliveryMethod() == null) {
            return "送货方式不能为空";
        }
        if (DeliveryMethodEnum.LOGISTICS.intCode().equals(submitData.getDeliveryMethod())
                && StringUtils.isBlank(submitData.getLogisticsNo())) {
            return "送货方式为物流时，物流单号不能为空";
        }
        if (CollectionUtils.isEmpty(submitData.getItemList())) {
            return "装车箱号信息不能为空";
        }

        for (LoadingSubmitData.AllocationItem item : submitData.getItemList()) {
            if (StringUtils.isBlank(item.getBoxNo())) {
                return "箱号不能为空";
            }
            if (StringUtils.isBlank(item.getAllocationNo())) {
                return "调拨单号不能为空";
            }
        }
        return null;
    }

    /**
     * 验证箱号并获取箱号信息
     */
    private WhApvAllocationBoard validateAndGetBox(String boxNo) {
        // 箱号格式验证
        Pattern patternBOX = Pattern.compile("([1-3]{1})([X-X]{1})([H-H]{1})([0-9]{4})([0-9]{0,})");
        Matcher matcherBOX = patternBOX.matcher(boxNo);
        if (!matcherBOX.matches()) {
            return null;
        }

        // 查询箱号是否存在
        WhApvAllocationBoardQueryCondition boardQuery = new WhApvAllocationBoardQueryCondition();
        boardQuery.setBoardType(AllocationBoardTypeEnum.BOX.intCode());
        boardQuery.setBoardNo(boxNo);

        return whApvAllocationBoardService.queryWhApvAllocationBoard(boardQuery);
    }

    /**
     * 根据箱号查询关联调拨单
     */
    private List<WhApvAllocation> queryAllocationsByBox(WhApvAllocationBoard box) {
        WhApvAllocationQueryCondition query = new WhApvAllocationQueryCondition();
        query.setBoxNo(box.getBoardNo());
        return whApvAllocationService.queryApvAllocationDetailList(query, null);
    }

    /**
     * 批量查询调拨单
     */
    private List<WhApvAllocation> queryAllocations(List<String> allocationNoList) {
        WhApvAllocationQueryCondition query = new WhApvAllocationQueryCondition();
        query.setAllocationNoList(allocationNoList);
        return whApvAllocationService.queryApvAllocationDetailList(query, null);
    }

    /**
     * 验证调拨单状态（支持分批装车）
     * 
     * @param allocations 调拨单列表
     * @param groupedData 按调拨单分组的装车数据，包含当前要装车的箱号
     * @return 验证失败信息，null表示验证通过
     * <AUTHOR>
     */
    private String validateAllocationStatus(List<WhApvAllocation> allocations, 
            Map<String, List<LoadingSubmitData.AllocationItem>> groupedData) {
        for (WhApvAllocation allocation : allocations) {
            String allocationNo = allocation.getAllocationNo();

            // 1. 验证调拨单状态
            if (!AllocationStatusEnum.WAIT_LOAD.intCode().equals(allocation.getAllocationStatus())) {
                return "调拨单：" + allocationNo + " 状态不是待装车状态";
            }

            // 2. 获取当前要装车的箱号列表
            List<LoadingSubmitData.AllocationItem> currentItems = groupedData.get(allocationNo);
            if (CollectionUtils.isEmpty(currentItems)) {
                continue;
            }
            
            Set<String> currentBoxNos = currentItems.stream()
                    .map(LoadingSubmitData.AllocationItem::getBoxNo)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());

            // 3. 验证当前要装车的箱号对应的SKU是否已装车（支持分批装车）
            for (WhApvAllocationItem item : allocation.getAllocationItems()) {
                // 只检查当前要装车的箱号对应的SKU，支持分批装车场景
                if (currentBoxNos.contains(item.getBoxNo()) 
                        && item.getLoadStatus() != null
                        && Integer.valueOf(AllocationLoadStatusEnum.LOADING.intCode()).equals(item.getLoadStatus())) {
                    return "调拨单：" + allocationNo + " 箱号：" + item.getBoxNo() 
                            + " SKU：" + item.getSku() + " 已装车，不可重复装车";
                }
            }
        }
        return null;
    }

    /**
     * 批量更新调拨装车状态
     */
    private BatchProcessResult batchUpdateAllocationLoading(List<WhApvAllocation> allocations,
            Map<String, List<LoadingSubmitData.AllocationItem>> groupedData, LoadingSubmitData submitData) {
        List<Integer> allocationIdList = new ArrayList<>();
        List<String> allocationNoList = new ArrayList<>();
        List<String> boxNoList = new ArrayList<>();

        // 收集数据
        allocations.forEach(allocation -> {
            List<LoadingSubmitData.AllocationItem> currentItems = groupedData.get(allocation.getAllocationNo());
            if (CollectionUtils.isNotEmpty(currentItems)) {
                List<String> boxList = currentItems.stream().map(LoadingSubmitData.AllocationItem::getBoxNo)
                        .filter(StringUtils::isNotBlank).collect(Collectors.toList());
                boxNoList.addAll(boxList);
            }
            allocationIdList.add(allocation.getAllocationId());
            allocationNoList.add(allocation.getAllocationNo());
        });

        // 批量更新装车状态
        int updateResult = whApvAllocationDao.batchUpdateAllocationByLoad(allocationNoList, boxNoList,
                submitData.getDeliveryMethod(), submitData.getLogisticsNo());

        if (updateResult < 1) {
            throw new BusinessException("批量更新装车状态失败");
        }

        // 批量更新调拨单状态为待签收
        whApvAllocationService.batchUpdateApvAllocationByLoading(allocationIdList);

        return new BatchProcessResult(allocationIdList, allocationNoList, boxNoList);
    }

    /**
     * 记录批量操作日志
     */
    private void logBatchOperation(BatchProcessResult result, LoadingSubmitData submitData) {
        result.getAllocationIdList()
                .forEach(
                        allocationId -> SystemLogUtils.WHAPVALLOCATION
                                .log(allocationId, "批量装车完成",
                                        new String[][] {
                                                { "送货方式",
                                                        DeliveryMethodEnum
                                                                .getNameByCode(submitData.getDeliveryMethod()) },
                                                { "物流单号", StringUtils.defaultString(submitData.getLogisticsNo(), "无") },
                                                { "装车箱数", String.valueOf(result.getBoxNoList().size()) } }));
    }

    /**
     * 检查箱号是否被其他用户扫描
     */
    private boolean isBoxScannedByOtherUser(String allocationNo, String boxNo, String currentUserId) {
        List<Map<String, Object>> scannedBoxes = parseScannedBoxesFromRedis(allocationNo);
        if (CollectionUtils.isEmpty(scannedBoxes)) {
            return false;
        }

        // 检查是否有其他用户扫描了这个箱号
        return scannedBoxes.stream().filter(box -> boxNo.equals(box.get("boxNo")))
                .anyMatch(box -> !currentUserId.equals(box.get("scanUserId")));
    }

    /**
     * 计算装车箱数
     */
    private LoadingBoxInfo calculateLoadingBoxCount(String allocationNo, String currentBoxNo) {
        // 获取所有箱号
        List<String> allBoxNos = getAllBoxNosByAllocationNo(allocationNo);
        
        Set<String> processedBoxNos = new HashSet<>();
        
        // 1. 获取数据库中已装车的箱号
        List<String> loadedBoxNos = getLoadedBoxNosByAllocationNo(allocationNo);
        if (CollectionUtils.isNotEmpty(loadedBoxNos)) {
            processedBoxNos.addAll(loadedBoxNos);
        }
        
        // 2. 获取Redis中已扫描的箱号
        List<Map<String, Object>> scannedBoxes = parseScannedBoxesFromRedis(allocationNo);
        if (CollectionUtils.isNotEmpty(scannedBoxes)) {
            scannedBoxes.stream()
                    .map(box -> (String) box.get("boxNo"))
                    .filter(StringUtils::isNotBlank)
                    .forEach(processedBoxNos::add);
        }
        
        // 3. 添加当前箱号
        processedBoxNos.add(currentBoxNo);

        LoadingBoxInfo boxInfo = new LoadingBoxInfo();
        boxInfo.setCurrentBoxNo(currentBoxNo);
        boxInfo.setLoadingBoxCount(processedBoxNos.size() + "/" + allBoxNos.size());
        return boxInfo;
    }

    /**
     * 获取调拨单中已装车的箱号
     */
    private List<String> getLoadedBoxNosByAllocationNo(String allocationNo) {
        WhApvAllocationQueryCondition query = new WhApvAllocationQueryCondition();
        query.setAllocationNo(allocationNo);
        WhApvAllocation allocation = whApvAllocationService.queryApvAllocationDetail(query);

        if (allocation == null || CollectionUtils.isEmpty(allocation.getAllocationItems())) {
            return new ArrayList<>();
        }

        return allocation.getAllocationItems().stream()
                .filter(item -> item.getLoadStatus() != null 
                        && Integer.valueOf(AllocationLoadStatusEnum.LOADING.intCode()).equals(item.getLoadStatus()))
                .map(WhApvAllocationItem::getBoxNo)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 保存扫描记录
     */
    private void saveScanRecord(String allocationNo, AndroidProductDo domain, String userId) {
        String scanKey = ALLOCATION_SCAN_KEY_PREFIX + allocationNo;

        try {
            // 获取已扫描的箱号列表
            List<Map<String, Object>> scannedBoxes = parseScannedBoxesFromRedis(allocationNo);

            // 更新或新增扫描记录
            updateOrAddScanRecord(scannedBoxes, domain, userId);

            // 构建保存数据
            Map<String, Object> data = new HashMap<>();
            data.put("scannedBoxes", scannedBoxes);
            data.put("lastUpdateTime", System.currentTimeMillis());
            data.put("lastUpdateUser", userId);

            StringRedisUtils.set(scanKey, JSON.toJSONString(data), SCAN_EXPIRE_TIME_SECONDS);
        }
        catch (Exception e) {
            log.error("保存扫描记录失败，调拨单：{}，箱号：{}", allocationNo, domain.getAllocationBoxNo(), e);
        }
    }

    /**
     * 更新或新增扫描记录
     */
    private void updateOrAddScanRecord(List<Map<String, Object>> scannedBoxes, AndroidProductDo domain, String userId) {
        String currentBoxNo = domain.getAllocationBoxNo();

        // 查找是否已存在记录
        Optional<Map<String, Object>> existingRecord = scannedBoxes.stream()
                .filter(box -> currentBoxNo.equals(box.get("boxNo")) && userId.equals(box.get("scanUserId")))
                .findFirst();

        if (existingRecord.isPresent()) {
            // 更新现有记录
            Map<String, Object> record = existingRecord.get();
            record.put("deliveryMethod", domain.getDeliveryMethod());
            record.put("logisticsNo", domain.getLogisticsNo());
            record.put("scanTime", System.currentTimeMillis());
        }
        else {
            // 新增记录
            Map<String, Object> scanRecord = new HashMap<>();
            scanRecord.put("boxNo", currentBoxNo);
            scanRecord.put("deliveryMethod", domain.getDeliveryMethod());
            scanRecord.put("logisticsNo", domain.getLogisticsNo());
            scanRecord.put("scanTime", System.currentTimeMillis());
            scanRecord.put("scanUserId", userId);

            scannedBoxes.add(scanRecord);
        }
    }

    /**
     * 获取调拨单所有箱号
     */
    private List<String> getAllBoxNosByAllocationNo(String allocationNo) {
        WhApvAllocationQueryCondition query = new WhApvAllocationQueryCondition();
        query.setAllocationNo(allocationNo);
        WhApvAllocation allocation = whApvAllocationService.queryApvAllocationDetail(query);

        if (allocation == null || CollectionUtils.isEmpty(allocation.getAllocationItems())) {
            return new ArrayList<>();
        }

        return allocation.getAllocationItems().stream().map(WhApvAllocationItem::getBoxNo)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
    }

    /**
     * 解析Redis中的扫描箱号数据
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> parseScannedBoxesFromRedis(String allocationNo) {
        String scanKey = ALLOCATION_SCAN_KEY_PREFIX + allocationNo;
        String scanData = StringRedisUtils.get(scanKey);

        if (StringUtils.isBlank(scanData)) {
            return new ArrayList<>();
        }

        try {
            Map<String, Object> data = JSON.parseObject(scanData, HashMap.class);
            List<Map<String, Object>> scannedBoxes = (List<Map<String, Object>>) data.get("scannedBoxes");
            return scannedBoxes != null ? scannedBoxes : new ArrayList<>();
        }
        catch (Exception e) {
            log.warn("解析扫描记录失败，调拨单：{}，错误：{}", allocationNo, e.getMessage());
            return new ArrayList<>();
        }
    }





    // ================== 内部类 ==================

    /**
     * 批量处理结果
     * 
     * <AUTHOR>
     */
    @Getter
    @AllArgsConstructor
    private static class BatchProcessResult {
        private final List<Integer> allocationIdList;
        private final List<String> allocationNoList;
        private final List<String> boxNoList;
    }
}