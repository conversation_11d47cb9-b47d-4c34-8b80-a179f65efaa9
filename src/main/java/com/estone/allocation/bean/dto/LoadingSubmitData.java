package com.estone.allocation.bean.dto;

import java.util.List;

import lombok.Data;

/**
 * 装车提交数据
 * 
 * <AUTHOR>
 */
@Data
public class LoadingSubmitData {
    
    /** 送货方式 */
    private Integer deliveryMethod;
    
    /** 物流单号 */
    private String logisticsNo;
    
    /** 装车箱号信息列表 */
    private List<AllocationItem> itemList;
    
    /**
     * 调拨单项目
     */
    @Data
    public static class AllocationItem {
        /** 调拨单号 */
        private String allocationNo;
        
        /** 箱号 */
        private String boxNo;
    }
} 