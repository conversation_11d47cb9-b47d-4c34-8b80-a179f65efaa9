package com.estone.allocation.bean.dto;

import java.util.List;

import lombok.Data;

/**
 * 装车箱数信息DTO
 * 
 * <AUTHOR>
 */
@Data
public class LoadingBoxInfo {
    
    /** 调拨单号 */
    private String allocationNo;
    
    /** 当前箱号 */
    private String currentBoxNo;
    
    /** 已扫描数量 */
    private int scannedCount;
    
    /** 总数量 */
    private int totalCount;
    
    /** 装车比例 */
    private double loadingRatio;
    
    /** 装车箱数信息 (如: 1/3, 2/3) */
    private String loadingBoxCount;
    
    /** 所有箱号列表 */
    private List<String> allBoxNos;
    
    /** 已扫描箱号列表 */
    private List<String> scannedBoxNos;
} 