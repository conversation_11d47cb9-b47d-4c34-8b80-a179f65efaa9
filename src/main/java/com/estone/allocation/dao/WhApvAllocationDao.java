package com.estone.allocation.dao;

import java.util.List;

import com.estone.allocation.bean.AllocationInventory;
import com.estone.allocation.bean.SyncAllocation;
import com.estone.allocation.bean.WhApvAllocation;
import com.estone.allocation.bean.WhApvAllocationItem;
import com.estone.allocation.bean.WhApvAllocationItemQueryCondition;
import com.estone.allocation.bean.WhApvAllocationQueryCondition;
import com.estone.allocation.bean.WhApvAllocationUpItem;
import com.whq.tool.component.Pager;

public interface WhApvAllocationDao {

    // 查询调拨SKU详情
    List<WhApvAllocationItem> queryApvAllocationSkuList(List<String> skuList);

    // 查询调拨单详情
    WhApvAllocationItem queryWhApvAllocationItem(WhApvAllocationItemQueryCondition query);

    List<WhApvAllocationItem> queryWhApvAllocationItemList(WhApvAllocationItemQueryCondition query);

    // 根据条件查询调拨单
    WhApvAllocation queryApvAllocation(WhApvAllocationQueryCondition query);

    // 根据条件查询调拨单
    List<WhApvAllocation> queryApvAllocationList(WhApvAllocationQueryCondition query);

    // 查询调拨单数量
    int queryApvAllocationCount(WhApvAllocationQueryCondition query);

    // 分页查询调拨单
    List<WhApvAllocation> queryApvAllocationList(WhApvAllocationQueryCondition query, Pager pager);

    // 根据条件查询调拨单详情
    WhApvAllocation queryApvAllocationDetail(WhApvAllocationQueryCondition query);

    // 库存调拨单详情数量
    int queryApvAllocationDetailCount(WhApvAllocationQueryCondition query);

    // 分页查询库存调拨单详情
    List<WhApvAllocation> queryApvAllocationDetailList(WhApvAllocationQueryCondition query, Pager pager);

    // 订单调拨单详情数量
    int queryOrderAllocationDetailCount(WhApvAllocationQueryCondition query);

    // 分页查询订单调拨单详情
    List<WhApvAllocation> queryOrderAllocationDetailList(WhApvAllocationQueryCondition query, Pager pager);

    // 分页查询订单调拨单详情(订单调拨装车专用)
    List<WhApvAllocation> queryOrderAllocationDetailSkuList(WhApvAllocationQueryCondition query, Pager pager);

    // 创建调拨单
    int createApvAllocation(WhApvAllocation entity);

    // 删除调拨单详情
    int deleteApvAllocationItem(List<Integer> itemIds);

    // 创建调拨单详情
    int[] batchCreateApvAllocationItem(List<WhApvAllocationItem> entityList);

    // 修改调拨单
    int updateApvAllocation(WhApvAllocation entity);

    // 批量修改调拨单
    int[] batchUpdateApvAllocation(List<WhApvAllocation> entityList);

    // 修改调拨详情
    int[] batchUpdateApvAllocationItem(List<WhApvAllocationItem> entityList);

    // 修改调拨单 拣货/装箱/打板/装车/入库 各环节的信息
    int updateApvAllocationTrunk(WhApvAllocationItem entity);

    // 批量修改调拨单 拣货/装箱/打板/装车/入库 各环节的信息
    int[] batchUpdateApvAllocationTrunk(List<WhApvAllocationItem> entityList);

    // 修改调拨单状态
    int updateApvAllocationStatus(String allocationNo, int status);

    int updateApvAllocationStatusByOldStatus(String allocationNo, int oldStatus, int status);

    // 根据打板号或箱号查询调拨单
    List<WhApvAllocation> queryApvAllocationByBoardNoOrBoxNo(String boardNo, String boxNo);

    // 根据打板号或子任务号查询调拨单
    List<WhApvAllocation> queryOrderAllocationByBoardNoOrSubTaskNo(String boardNo, String subTaskNo);

    // 库存调拨打板
    int updateApvAllocationByBoard(String boardNo, String boxNo);

    // 订单调拨打板
    int updateOrderAllocationByBoard(String boardNo, String subTaskNo);

    // 库存调拨装车
    int updateApvAllocationByLoad(String boxNo, String boardNo);

    // 订单调拨装车
    int updateOrderAllocationByLoad(String subTaskNo, String boardNo);

    // 查询库存调拨是否装车完成
    List<Integer> queryAllocationByLoading(List<Integer> allocationIdList);

    // 查询订单调拨是否装车完成
    List<Integer> queryOrderAllocationByLoading(List<Integer> allocationIdList);

    // 查询调拨单详情列表
    List<WhApvAllocationItem> queryApvAllocationDetails(WhApvAllocationQueryCondition query);

    // 库存调拨->调拨单 已入库 已完成 状态 推送 修改发货仓调拨库存
    List<AllocationInventory> queryAlloCheckInData(List<String> allocationNoList);

    // 查询调拨需求中已完成的调拨单对应的调拨需求的item
    List<AllocationInventory> queryApvItemByFinish();

    // 订单调拨->调拨单 已完成 查询调出仓apv是否已交运，若交运则修改调入仓调拨库存
    List<String> queryAlloApvNosOnLoad(List<String> apvNoList, List<Integer> statusList);

    // 查询调拨单和item
    List<SyncAllocation> querySyncAllocationAndItemList(WhApvAllocationQueryCondition query, Pager pager);

    // 根据主键修改调拨单
    void updateSyncAllocationByPrimaryKey(List<WhApvAllocation> entityList);

    // 根据主键修改调拨单明细item
    void updateSyncAllocationItemByPrimaryKey(List<WhApvAllocationItem> entityList);

    WhApvAllocationItem queryWhApvAllocationItemByPrimaryKey(Integer primaryKey);

    // 查询调拨上架详情数量
    int queryAllocationUpItemCount(WhApvAllocationQueryCondition query);

    // 查询调拨上架详情
    List<WhApvAllocationUpItem> queryAllocationUpItemList(WhApvAllocationQueryCondition query, Pager pager);

    int batchUpdateAllocationByLoad(List<String> allocationNoList, List<String> boxNoList,Integer deliveryMethod,String logisticsNo);

}
