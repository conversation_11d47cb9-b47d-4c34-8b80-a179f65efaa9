package com.estone.allocation.dao.impl;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.estone.allocation.bean.*;
import com.estone.allocation.dao.WhApvAllocationDao;
import com.estone.allocation.dao.mapper.*;
import com.estone.allocation.enums.AllocationLoadStatusEnum;
import com.estone.allocation.enums.AllocationStatusEnum;
import com.estone.allocation.enums.AllocationTakeStatusEnum;
import com.estone.allocation.enums.AllocationTypeEnum;
import com.estone.allocation.util.AllocationPushUtil;
import com.estone.apv.bean.WhApvItem;
import com.estone.common.util.BeanConvertUtils;
import com.estone.common.util.CommonUtils;
import com.estone.common.util.SqlerTemplate;
import com.whq.tool.component.Pager;
import com.whq.tool.context.DataContextHolder;
import com.whq.tool.sqler.SqlerRequest;
import com.whq.tool.sqler.analyzer.DataType;

@Repository("whApvAllocationDao")
public class WhApvAllocationDaoImpl implements WhApvAllocationDao {

    private void setQueryCondition(SqlerRequest request, WhApvAllocationQueryCondition query) {
        if (query == null) {
            return;
        }

        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam("create_start_time", DataType.STRING, query.getCreateStartTime());
        request.addDataParam("create_end_time", DataType.STRING, query.getCreateEndTime());

        String allocationNoStr = query.getAllocationNoStr();
        if (StringUtils.isNotBlank(allocationNoStr)) {
            allocationNoStr = allocationNoStr.trim().replaceAll(" ", "");
            List<String> list = CommonUtils.splitList(allocationNoStr, ",");
            request.addDataParam("allocation_no_list", DataType.STRING, list);
        }

        String skuStr = query.getSkuStr();
        if (StringUtils.isNotBlank(skuStr)) {
            skuStr = skuStr.trim().replaceAll(" ", "");
            List<String> list = CommonUtils.splitList(skuStr, ",");
            request.addDataParam("sku_list", DataType.STRING, list);
        }

        if (CollectionUtils.isNotEmpty(query.getSkus())) {
            request.addDataParam("skus", DataType.STRING, query.getSkus());
        }

        String allocationStatusStr = query.getAllocationStatusStr();

        if (StringUtils.isNotBlank(allocationStatusStr)) {
            allocationStatusStr = allocationStatusStr.trim().replaceAll(" ", "");
            List<String> list = Arrays.asList(allocationStatusStr.split(","));
            request.addDataParam("allocation_status_list", DataType.STRING, list);
        }

        if (CollectionUtils.isNotEmpty(query.getStatusList())) {
            request.addDataParam("allocation_status_list", DataType.STRING, query.getStatusList());
        }

        request.addDataParam("allocation_id_list", DataType.INT, query.getAllocationIds());

        if (CollectionUtils.isNotEmpty(query.getAllocationNoList()))
            request.addDataParam("allocation_no_list", DataType.STRING, query.getAllocationNoList());

        if (CollectionUtils.isNotEmpty(query.getItemIds())) {
            request.addDataParam("allocation_item_id_list", DataType.INT, query.getItemIds());
        }

        if (StringUtils.isNotBlank(query.getBoxNo())) {
            request.addDataParam(WhApvAllocationItemDBField.BOX_NO, DataType.STRING, query.getBoxNo());
        }
        if (StringUtils.isNotBlank(query.getBoardNo())) {
            request.addDataParam(WhApvAllocationItemDBField.BOARD_NO, DataType.STRING, query.getBoardNo());
        }

        request.addDataParam(WhApvAllocationItemDBField.PUT_STATUS, DataType.INT, query.getPutStatus());
        request.addDataParam(WhApvAllocationItemDBField.PUT_BY, DataType.INT, query.getPutBy());

        request.addDataParam(WhApvAllocationDBField.ALLOCATION_ID, DataType.INT, query.getAllocationId());
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_NO, DataType.STRING, query.getAllocationNo());
        request.addDataParam(WhApvAllocationDBField.DELIVERY_WAREHOUSE_ID, DataType.INT,
                query.getDeliveryWarehouseId());
        request.addDataParam(WhApvAllocationDBField.DEST_WAREHOUSE_ID, DataType.INT, query.getDestWarehouseId());
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_TYPE, DataType.INT, query.getAllocationType());
        request.addDataParam(WhApvAllocationDBField.TRANSPORT_TYPE, DataType.INT, query.getTransportType());
        request.addDataParam(WhApvAllocationDBField.FREIGHT, DataType.BIGDECIMAL, query.getFreight());
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_STATUS, DataType.INT, query.getAllocationStatus());
        request.addDataParam(WhApvAllocationDBField.CREATE_BY, DataType.INT, query.getCreateBy());
        request.addDataParam(WhApvAllocationDBField.CREATE_TIME, DataType.TIMESTAMP, query.getCreateTime());
        request.addDataParam(WhApvAllocationDBField.AUDIT_BY, DataType.INT, query.getAuditBy());
        request.addDataParam(WhApvAllocationDBField.AUDIT_TIME, DataType.TIMESTAMP, query.getAuditTime());
        request.addDataParam(WhApvAllocationDBField.CONFIRM_BY, DataType.INT, query.getConfirmBy());
        request.addDataParam(WhApvAllocationDBField.CONFIRM_TIME, DataType.TIMESTAMP, query.getConfirmTime());
        request.addDataParam(WhApvAllocationDBField.UPDATE_BY, DataType.INT, query.getUpdateBy());
        request.addDataParam(WhApvAllocationDBField.UPDATE_TIME, DataType.TIMESTAMP, query.getUpdateTime());
        request.addDataParam(WhApvAllocationDBField.REMARK, DataType.STRING, query.getRemark());
        request.addDataParam(WhApvAllocationDBField.IS_PUSH, DataType.INT, query.getIsPush());
    }

    private void setItemQueryCondition(SqlerRequest request, WhApvAllocationItemQueryCondition query) {
        if (query == null) {
            return;
        }

        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        request.addDataParam(WhApvAllocationItemDBField.ALLOCATION_ID, DataType.INT, query.getAllocationId());
        request.addDataParam(WhApvAllocationItemDBField.ALLOCATION_NO, DataType.STRING, query.getAllocationNo());
        request.addDataParam(WhApvAllocationItemDBField.SKU, DataType.STRING, query.getSku());
        request.addDataParam(WhApvAllocationItemDBField.SKU_NAME, DataType.STRING, query.getSkuName());
        request.addDataParam(WhApvAllocationItemDBField.ALLOCATION_NUM, DataType.INT, query.getAllocationNum());
        request.addDataParam(WhApvAllocationItemDBField.STOCK_ID, DataType.INT, query.getStockId());
        request.addDataParam(WhApvAllocationItemDBField.CREATE_BY, DataType.INT, query.getCreateBy());
        request.addDataParam(WhApvAllocationItemDBField.CREATE_TIME, DataType.TIMESTAMP, query.getCreateTime());
        request.addDataParam(WhApvAllocationItemDBField.PICK_STATUS, DataType.INT, query.getPickStatus());
        request.addDataParam(WhApvAllocationItemDBField.PICK_NUM, DataType.INT, query.getPickNum());
        request.addDataParam(WhApvAllocationItemDBField.PICK_BY, DataType.INT, query.getPickBy());
        request.addDataParam(WhApvAllocationItemDBField.PICK_TIME, DataType.TIMESTAMP, query.getPickTime());
        request.addDataParam(WhApvAllocationItemDBField.HISTORY_PICK_NUM, DataType.INT, query.getHistoryPickNum());
        request.addDataParam(WhApvAllocationItemDBField.BOX_STATUS, DataType.INT, query.getBoxStatus());
        request.addDataParam(WhApvAllocationItemDBField.BOX_NUM, DataType.INT, query.getBoxNum());
        request.addDataParam(WhApvAllocationItemDBField.BOX_NO, DataType.STRING, query.getBoxNo());
        request.addDataParam(WhApvAllocationItemDBField.BOX_BY, DataType.INT, query.getBoxBy());
        request.addDataParam(WhApvAllocationItemDBField.BOX_TIME, DataType.TIMESTAMP, query.getBoxTime());
        request.addDataParam(WhApvAllocationItemDBField.BOARD_STATUS, DataType.INT, query.getBoardStatus());
        request.addDataParam(WhApvAllocationItemDBField.BOARD_NO, DataType.STRING, query.getBoardNo());
        request.addDataParam(WhApvAllocationItemDBField.BOARD_BY, DataType.INT, query.getBoardBy());
        request.addDataParam(WhApvAllocationItemDBField.BOARD_TIME, DataType.TIMESTAMP, query.getBoardTime());
        request.addDataParam(WhApvAllocationItemDBField.LOAD_STATUS, DataType.INT, query.getLoadStatus());
        request.addDataParam(WhApvAllocationItemDBField.LOAD_BY, DataType.INT, query.getLoadBy());
        request.addDataParam(WhApvAllocationItemDBField.LOAD_TIME, DataType.TIMESTAMP, query.getLoadTime());
        request.addDataParam(WhApvAllocationItemDBField.UP_STATUS, DataType.INT, query.getUpStatus());
        request.addDataParam(WhApvAllocationItemDBField.UP_NUM, DataType.INT, query.getUpNum());
        request.addDataParam(WhApvAllocationItemDBField.UP_BY, DataType.INT, query.getUpBy());
        request.addDataParam(WhApvAllocationItemDBField.UP_TIME, DataType.TIMESTAMP, query.getUpTime());
        request.addDataParam(WhApvAllocationItemDBField.PUT_STATUS, DataType.INT, query.getPutStatus());
        request.addDataParam(WhApvAllocationItemDBField.PUT_BY, DataType.INT, query.getPutBy());
        request.addDataParam(WhApvAllocationItemDBField.PUT_TIME, DataType.TIMESTAMP, query.getPutTime());
        request.addDataParam(WhApvAllocationItemDBField.IS_AUDIT, DataType.INT, query.getIsAudit());

        if (CollectionUtils.isNotEmpty(query.getSkuList())) {
            request.addDataParam("allocation_item_sku_list", DataType.STRING, query.getSkuList());
        }
    }

    @Override
    public WhApvAllocationItem queryWhApvAllocationItem(WhApvAllocationItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhApvAllocationItem");
        setItemQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhApvAllocationItemMapper());
    }

    @Override
    public List<WhApvAllocationItem> queryWhApvAllocationItemList(WhApvAllocationItemQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryWhApvAllocationItemList");
        setItemQueryCondition(request, query);
        return SqlerTemplate.query(request, new WhApvAllocationItemMapper());
    }

    @Override
    public List<WhApvAllocationItem> queryApvAllocationSkuList(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return new ArrayList<WhApvAllocationItem>();
        }
        SqlerRequest request = new SqlerRequest("queryApvAllocationSkuList");
        request.addDataParam("sku_list", DataType.STRING, skuList);

        List<Map<String, Object>> list = SqlerTemplate.queryForList(request);
        List<WhApvAllocationItem> allocationSkuList = BeanConvertUtils.convertList(list, WhApvAllocationItem.class);
        return allocationSkuList;
    }

    @Override
    public WhApvAllocation queryApvAllocation(WhApvAllocationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryApvAllocation");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhApvAllocationMapper(false, false));
    }

    @Override
    public List<WhApvAllocation> queryApvAllocationList(WhApvAllocationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryApvAllocation");
        setQueryCondition(request, query);
        return SqlerTemplate.query(request, new WhApvAllocationMapper(false, false));
    }

    @Override
    public int queryApvAllocationCount(WhApvAllocationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryApvAllocationCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhApvAllocation> queryApvAllocationList(WhApvAllocationQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryApvAllocationList");
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhApvAllocationMapper(true, false));
    }

    @Override
    public WhApvAllocation queryApvAllocationDetail(WhApvAllocationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryApvAllocationDetail");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForObject(request, new WhApvAllocationMapper(false, true));
    }

    @Override
    public int queryApvAllocationDetailCount(WhApvAllocationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryApvAllocationDetailCount");
        setQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhApvAllocation> queryApvAllocationDetailList(WhApvAllocationQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryApvAllocationDetailList");
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhApvAllocationMapper(false, true));
    }

    @Override
    public int queryOrderAllocationDetailCount(WhApvAllocationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryOrderAllocationDetailCount");
        if (StringUtils.isNotBlank(query.getSubTaskNo())) {
            request.addDataParam(WhApvAllocationOrderItemDBField.TASK_NO, DataType.STRING, query.getSubTaskNo());
        }
        if (StringUtils.isNotBlank(query.getBoardNo())) {
            request.addDataParam(WhApvAllocationOrderItemDBField.BOARD_NO, DataType.STRING, query.getBoardNo());
        }
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_TYPE, DataType.INT, AllocationTypeEnum.ORDER.intCode());
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhApvAllocation> queryOrderAllocationDetailList(WhApvAllocationQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryOrderAllocationDetailList");
        if (StringUtils.isNotBlank(query.getSubTaskNo())) {
            request.addDataParam(WhApvAllocationOrderItemDBField.TASK_NO, DataType.STRING, query.getSubTaskNo());
        }
        if (StringUtils.isNotBlank(query.getBoardNo())) {
            request.addDataParam(WhApvAllocationOrderItemDBField.BOARD_NO, DataType.STRING, query.getBoardNo());
        }
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        if (query.getAllocationType() != null) {
            request.addDataParam(WhApvAllocationDBField.ALLOCATION_TYPE, DataType.INT, query.getAllocationType());
        } else {
            request.addDataParam(WhApvAllocationDBField.ALLOCATION_TYPE, DataType.INT, AllocationTypeEnum.ORDER.intCode());
        }

        if (query.getIsDiscard().equals(AllocationStatusEnum.DISCARD.intCode())) {
            request.addDataParam("ISNOTDISCARD", DataType.INT, AllocationStatusEnum.DISCARD.intCode());
        }
        request.addDataParam("allocation_id_list", DataType.INT, query.getAllocationIds());

        return SqlerTemplate.query(request, new WhApvAllocationOrderItemAndSkuMapper(true, true));
    }

    // 分页查询订单调拨单详情(订单调拨装车专用)
    @Override
    public List<WhApvAllocation> queryOrderAllocationDetailSkuList(WhApvAllocationQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryOrderAllocationDetailSkuList");
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_TYPE, DataType.INT, AllocationTypeEnum.ORDER.intCode());

        if (query.getIsDiscard().equals(AllocationStatusEnum.DISCARD.intCode())) {
            request.addDataParam("ISNOTDISCARD", DataType.INT, AllocationStatusEnum.DISCARD.intCode());
        }
        request.addDataParam("allocation_id_list", DataType.INT, query.getAllocationIds());

        return SqlerTemplate.query(request, new WhApvAllocationOrderItemAndSkuMapper(false, true));
    }

    @Override
    public int createApvAllocation(WhApvAllocation entity) {
        SqlerRequest request = new SqlerRequest("createApvAllocation");
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_NO, DataType.STRING, entity.getAllocationNo());
        request.addDataParam(WhApvAllocationDBField.DELIVERY_WAREHOUSE_ID, DataType.INT,
                entity.getDeliveryWarehouseId());
        request.addDataParam(WhApvAllocationDBField.DEST_WAREHOUSE_ID, DataType.INT, entity.getDestWarehouseId());
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_TYPE, DataType.INT, entity.getAllocationType());
        request.addDataParam(WhApvAllocationDBField.TRANSPORT_TYPE, DataType.INT, entity.getTransportType());
        request.addDataParam(WhApvAllocationDBField.FREIGHT, DataType.BIGDECIMAL, entity.getFreight());
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_STATUS, DataType.INT, entity.getAllocationStatus());
        request.addDataParam(WhApvAllocationDBField.CREATE_BY, DataType.INT, entity.getCreateBy());
        request.addDataParam(WhApvAllocationDBField.CREATE_TIME, DataType.TIMESTAMP, entity.getCreateTime());
        request.addDataParam(WhApvAllocationDBField.AUDIT_BY, DataType.INT, entity.getAuditBy());
        request.addDataParam(WhApvAllocationDBField.AUDIT_TIME, DataType.TIMESTAMP, entity.getAuditTime());
        request.addDataParam(WhApvAllocationDBField.CONFIRM_BY, DataType.INT, entity.getConfirmBy());
        request.addDataParam(WhApvAllocationDBField.CONFIRM_TIME, DataType.TIMESTAMP, entity.getConfirmTime());
        request.addDataParam(WhApvAllocationDBField.UPDATE_BY, DataType.INT, entity.getUpdateBy());
        request.addDataParam(WhApvAllocationDBField.UPDATE_TIME, DataType.TIMESTAMP, entity.getUpdateTime());
        request.addDataParam(WhApvAllocationDBField.REMARK, DataType.STRING, entity.getRemark());

        Integer autoIncrementId = SqlerTemplate.executeAndReturn(request);
        entity.setAllocationId(autoIncrementId);

        return autoIncrementId;
    }

    @Override
    public int[] batchCreateApvAllocationItem(List<WhApvAllocationItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("createApvAllocationItem");
            for (WhApvAllocationItem entity : entityList) {
                request.addBatchDataParam(WhApvAllocationItemDBField.ALLOCATION_ID, DataType.INT,
                        entity.getAllocationId());
                request.addBatchDataParam(WhApvAllocationItemDBField.ALLOCATION_NO, DataType.STRING,
                        entity.getAllocationNo());
                request.addBatchDataParam(WhApvAllocationItemDBField.SKU, DataType.STRING, entity.getSku());
                request.addBatchDataParam(WhApvAllocationItemDBField.SKU_NAME, DataType.STRING, entity.getSkuName());
                request.addBatchDataParam(WhApvAllocationItemDBField.ALLOCATION_NUM, DataType.INT,entity.getAllocationNum());
                request.addBatchDataParam(WhApvAllocationItemDBField.STOCK_ID, DataType.INT,entity.getStockId());
                request.addBatchDataParam(WhApvAllocationItemDBField.JSON_STR, DataType.STRING,entity.getJsonStr());
                request.addBatchDataParam(WhApvAllocationItemDBField.CREATE_BY, DataType.INT,
                        DataContextHolder.getUserId());
                request.addBatchDataParam(WhApvAllocationItemDBField.CREATE_TIME, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhApvAllocationItemDBField.PICK_STATUS, DataType.INT,
                        entity.getPickStatus() == null ? 0 : entity.getPickStatus());
                request.addBatchDataParam(WhApvAllocationItemDBField.PICK_NUM, DataType.INT,
                        entity.getPickNum() == null ? 0 : entity.getPickNum());
                request.addBatchDataParam(WhApvAllocationItemDBField.PICK_BY, DataType.INT, entity.getPickBy());
                request.addBatchDataParam(WhApvAllocationItemDBField.PICK_TIME, DataType.TIMESTAMP,
                        entity.getPickTime());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOX_STATUS, DataType.INT,
                        entity.getBoxStatus() == null ? 0 : entity.getBoxStatus());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOX_NUM, DataType.INT,
                        entity.getBoxNum() == null ? 0 : entity.getBoxNum());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOX_BY, DataType.INT, entity.getBoxBy());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOX_TIME, DataType.TIMESTAMP, entity.getBoxTime());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOARD_STATUS, DataType.INT,
                        entity.getBoardStatus() == null ? 0 : entity.getBoardStatus());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOARD_NO, DataType.STRING, entity.getBoardNo());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOARD_BY, DataType.INT, entity.getBoardBy());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOARD_TIME, DataType.TIMESTAMP,
                        entity.getBoardTime());
                request.addBatchDataParam(WhApvAllocationItemDBField.LOAD_STATUS, DataType.INT,
                        entity.getLoadStatus() == null ? 0 : entity.getLoadStatus());
                request.addBatchDataParam(WhApvAllocationItemDBField.LOAD_BY, DataType.INT, entity.getLoadBy());
                request.addBatchDataParam(WhApvAllocationItemDBField.LOAD_TIME, DataType.TIMESTAMP,
                        entity.getLoadTime());
                request.addBatchDataParam(WhApvAllocationItemDBField.UP_STATUS, DataType.INT,
                        entity.getUpStatus() == null ? 0 : entity.getUpStatus());
                request.addBatchDataParam(WhApvAllocationItemDBField.UP_NUM, DataType.INT,
                        entity.getUpNum() == null ? 0 : entity.getUpNum());
                request.addBatchDataParam(WhApvAllocationItemDBField.UP_BY, DataType.INT, entity.getUpBy());
                request.addBatchDataParam(WhApvAllocationItemDBField.UP_TIME, DataType.TIMESTAMP, entity.getUpTime());
                request.addBatch();
            }
            return SqlerTemplate.batchUpdate(request);
        }
        return null;
    }

    @Override
    public int deleteApvAllocationItem(List<Integer> itemIds) {
        Assert.notNull(itemIds);
        SqlerRequest request = new SqlerRequest("deleteApvAllocationItem");
        request.addDataParam("allocation_item_id_list", DataType.STRING, itemIds);
        return SqlerTemplate.execute(request);
    }

    @Override
    public int updateApvAllocation(WhApvAllocation entity) {
        SqlerRequest request = new SqlerRequest("updateApvAllocation");
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_ID, DataType.INT, entity.getAllocationId());
        request.addDataParam(WhApvAllocationDBField.DELIVERY_WAREHOUSE_ID, DataType.INT,
                entity.getDeliveryWarehouseId());
        request.addDataParam(WhApvAllocationDBField.DEST_WAREHOUSE_ID, DataType.INT, entity.getDestWarehouseId());
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_TYPE, DataType.INT, entity.getAllocationType());
        request.addDataParam(WhApvAllocationDBField.TRANSPORT_TYPE, DataType.INT, entity.getTransportType());
        request.addDataParam(WhApvAllocationDBField.FREIGHT, DataType.BIGDECIMAL, entity.getFreight());
        request.addDataParam(WhApvAllocationDBField.UPDATE_BY, DataType.INT, DataContextHolder.getUserId());
        request.addDataParam(WhApvAllocationDBField.UPDATE_TIME, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhApvAllocationDBField.AUDIT_BY, DataType.INT, entity.getAuditBy());
        request.addDataParam(WhApvAllocationDBField.AUDIT_TIME, DataType.TIMESTAMP, entity.getAuditTime());
        request.addDataParam(WhApvAllocationDBField.CONFIRM_BY, DataType.INT, entity.getConfirmBy());
        request.addDataParam(WhApvAllocationDBField.CONFIRM_TIME, DataType.TIMESTAMP, entity.getConfirmTime());
        request.addDataParam(WhApvAllocationDBField.REMARK, DataType.STRING, entity.getRemark());
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_STATUS, DataType.INT, entity.getAllocationStatus());
        request.addDataParam(WhApvAllocationDBField.IS_PUSH, DataType.INT, entity.getIsPush());
        if (CollectionUtils.isNotEmpty(entity.getAllocationNoList())) {
            request.addDataParam("allocation_no_list", DataType.STRING, entity.getAllocationNoList());
        }
        return SqlerTemplate.execute(request);
    }

    @Override
    public int[] batchUpdateApvAllocation(List<WhApvAllocation> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateApvAllocation");
            for (WhApvAllocation entity : entityList) {
                request.addBatchDataParam(WhApvAllocationDBField.ALLOCATION_ID, DataType.INT, entity.getAllocationId());
                request.addBatchDataParam(WhApvAllocationDBField.DELIVERY_WAREHOUSE_ID, DataType.INT,
                        entity.getDeliveryWarehouseId());
                request.addBatchDataParam(WhApvAllocationDBField.DEST_WAREHOUSE_ID, DataType.INT,
                        entity.getDestWarehouseId());
                request.addBatchDataParam(WhApvAllocationDBField.ALLOCATION_TYPE, DataType.INT,
                        entity.getAllocationType());
                request.addBatchDataParam(WhApvAllocationDBField.TRANSPORT_TYPE, DataType.INT,
                        entity.getTransportType());
                request.addBatchDataParam(WhApvAllocationDBField.FREIGHT, DataType.BIGDECIMAL, entity.getFreight());
                request.addBatchDataParam(WhApvAllocationDBField.REMARK, DataType.STRING, entity.getRemark());
                request.addBatchDataParam(WhApvAllocationDBField.ALLOCATION_STATUS, DataType.INT,
                        entity.getAllocationStatus());
                request.addBatchDataParam(WhApvAllocationDBField.UPDATE_BY, DataType.INT,
                        DataContextHolder.getUserId());
                request.addBatchDataParam(WhApvAllocationDBField.UPDATE_TIME, DataType.TIMESTAMP,
                        new Timestamp(System.currentTimeMillis()));
                request.addBatchDataParam(WhApvAllocationDBField.AUDIT_BY, DataType.INT, entity.getAuditBy());
                request.addBatchDataParam(WhApvAllocationDBField.AUDIT_TIME, DataType.TIMESTAMP, entity.getAuditTime());
                request.addBatchDataParam(WhApvAllocationDBField.CONFIRM_BY, DataType.INT, entity.getConfirmBy());
                request.addBatchDataParam(WhApvAllocationDBField.CONFIRM_TIME, DataType.TIMESTAMP,
                        entity.getConfirmTime());
                request.addBatch();
            }
            return SqlerTemplate.batchUpdate(request);
        }

        return null;
    }

    @Override
    public int[] batchUpdateApvAllocationItem(List<WhApvAllocationItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateApvAllocationItem");
            for (WhApvAllocationItem entity : entityList) {
                request.addBatchDataParam(WhApvAllocationItemDBField.ALLOCATION_ITEM_ID, DataType.INT,
                        entity.getAllocationItemId());
                request.addBatchDataParam(WhApvAllocationItemDBField.ALLOCATION_NUM, DataType.INT,
                        entity.getAllocationNum());
                request.addBatchDataParam(WhApvAllocationItemDBField.PICK_NUM, DataType.INT, entity.getPickNum());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOX_NUM, DataType.INT, entity.getBoxNum());
                request.addBatchDataParam(WhApvAllocationItemDBField.IS_AUDIT, DataType.BOOLEAN, entity.getIsAudit());
                request.addBatchDataParam(WhApvAllocationItemDBField.HISTORY_PICK_NUM, DataType.INT,
                        entity.getHistoryPickNum());

                request.addBatchDataParam(WhApvAllocationItemDBField.PUT_BY, DataType.INT, entity.getPutBy());
                request.addBatchDataParam(WhApvAllocationItemDBField.PUT_STATUS, DataType.INT, entity.getPutStatus());
                request.addBatchDataParam(WhApvAllocationItemDBField.INVENTORY_STATUS, DataType.INT, entity.getInventoryStatus());
                request.addBatchDataParam(WhApvAllocationItemDBField.PUT_TIME, DataType.TIMESTAMP, entity.getPutTime());
                request.addBatchDataParam(WhApvAllocationItemDBField.STOCK_ID, DataType.INT, entity.getStockId());
                request.addBatchDataParam(WhApvAllocationItemDBField.JSON_STR, DataType.STRING, entity.getJsonStr());
                request.addBatchDataParam(WhApvAllocationItemDBField.DELIVERY_METHOD, DataType.STRING, entity.getDeliveryMethod());
                request.addBatchDataParam(WhApvAllocationItemDBField.LOGISTICS_NO, DataType.STRING, entity.getLogisticsNo());

                request.addBatch();
            }
            return SqlerTemplate.batchUpdate(request);
        }

        return null;
    }

    @Override
    public int updateApvAllocationTrunk(WhApvAllocationItem entity) {
        SqlerRequest request = new SqlerRequest("updateApvAllocationTrunk");
        request.addDataParam(WhApvAllocationItemDBField.ALLOCATION_ITEM_ID, DataType.INT, entity.getAllocationItemId());
        request.addDataParam(WhApvAllocationItemDBField.ALLOCATION_NO, DataType.STRING, entity.getAllocationNo());
        request.addDataParam(WhApvAllocationItemDBField.ALLOCATION_ID, DataType.INT, entity.getAllocationId());
        request.addDataParam(WhApvAllocationItemDBField.SKU, DataType.STRING, entity.getSku());

        // 拣货
        request.addDataParam(WhApvAllocationItemDBField.PICK_STATUS, DataType.INT, entity.getPickStatus());
        request.addDataParam(WhApvAllocationItemDBField.PICK_NUM, DataType.INT, entity.getPickNum());
        request.addDataParam(WhApvAllocationItemDBField.PICK_BY, DataType.INT, entity.getPickBy());
        request.addDataParam(WhApvAllocationItemDBField.PICK_TIME, DataType.TIMESTAMP, entity.getPickTime());

        // 装箱
        request.addDataParam(WhApvAllocationItemDBField.BOX_STATUS, DataType.INT, entity.getBoxStatus());
        request.addDataParam(WhApvAllocationItemDBField.BOX_NUM, DataType.INT, entity.getBoxNum());
        request.addDataParam(WhApvAllocationItemDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
        request.addDataParam(WhApvAllocationItemDBField.BOX_BY, DataType.INT, entity.getBoxBy());
        request.addDataParam(WhApvAllocationItemDBField.BOX_TIME, DataType.TIMESTAMP, entity.getBoxTime());

        // 打板
        request.addDataParam(WhApvAllocationItemDBField.BOARD_STATUS, DataType.INT, entity.getBoardStatus());
        request.addDataParam(WhApvAllocationItemDBField.BOARD_NO, DataType.STRING, entity.getBoardNo());
        request.addDataParam(WhApvAllocationItemDBField.BOARD_BY, DataType.INT, entity.getBoardBy());
        request.addDataParam(WhApvAllocationItemDBField.BOARD_TIME, DataType.TIMESTAMP, entity.getBoardTime());

        // 装车
        request.addDataParam(WhApvAllocationItemDBField.LOAD_STATUS, DataType.INT, entity.getLoadStatus());
        request.addDataParam(WhApvAllocationItemDBField.LOAD_BY, DataType.INT, entity.getLoadBy());
        request.addDataParam(WhApvAllocationItemDBField.LOAD_TIME, DataType.TIMESTAMP, entity.getLoadTime());

        // 入库
        request.addDataParam(WhApvAllocationItemDBField.UP_STATUS, DataType.INT, entity.getUpStatus());
        request.addDataParam(WhApvAllocationItemDBField.UP_NUM, DataType.INT, entity.getUpNum());
        request.addDataParam(WhApvAllocationItemDBField.UP_BY, DataType.INT, entity.getUpBy());
        request.addDataParam(WhApvAllocationItemDBField.UP_TIME, DataType.TIMESTAMP, entity.getUpTime());

        return SqlerTemplate.execute(request);
    }

    @Override
    public int[] batchUpdateApvAllocationTrunk(List<WhApvAllocationItem> entityList) {
        if (entityList != null && !entityList.isEmpty()) {
            SqlerRequest request = new SqlerRequest("updateApvAllocationTrunk");
            for (WhApvAllocationItem entity : entityList) {
                request.addBatchDataParam(WhApvAllocationItemDBField.ALLOCATION_ITEM_ID, DataType.INT,
                        entity.getAllocationItemId());
                request.addBatchDataParam(WhApvAllocationItemDBField.ALLOCATION_NO, DataType.STRING,
                        entity.getAllocationNo());
                request.addBatchDataParam(WhApvAllocationItemDBField.ALLOCATION_ID, DataType.INT,
                        entity.getAllocationId());
                request.addBatchDataParam(WhApvAllocationItemDBField.SKU, DataType.STRING, entity.getSku());

                // 拣货
                request.addBatchDataParam(WhApvAllocationItemDBField.PICK_STATUS, DataType.INT, entity.getPickStatus());
                request.addBatchDataParam(WhApvAllocationItemDBField.PICK_NUM, DataType.INT, entity.getPickNum());
                request.addBatchDataParam(WhApvAllocationItemDBField.PICK_BY, DataType.INT, entity.getPickBy());
                request.addBatchDataParam(WhApvAllocationItemDBField.PICK_TIME, DataType.TIMESTAMP,
                        entity.getPickTime());

                // 装箱
                request.addBatchDataParam(WhApvAllocationItemDBField.INVENTORY_STATUS, DataType.INT, entity.getInventoryStatus());

                request.addBatchDataParam(WhApvAllocationItemDBField.BOX_STATUS, DataType.INT, entity.getBoxStatus());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOX_NUM, DataType.INT, entity.getBoxNum());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOX_NO, DataType.STRING, entity.getBoxNo());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOX_BY, DataType.INT, entity.getBoxBy());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOX_TIME, DataType.TIMESTAMP, entity.getBoxTime());

                // 打板
                request.addBatchDataParam(WhApvAllocationItemDBField.BOARD_STATUS, DataType.INT,
                        entity.getBoardStatus());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOARD_NO, DataType.STRING, entity.getBoardNo());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOARD_BY, DataType.INT, entity.getBoardBy());
                request.addBatchDataParam(WhApvAllocationItemDBField.BOARD_TIME, DataType.TIMESTAMP,
                        entity.getBoardTime());

                // 装车
                request.addBatchDataParam(WhApvAllocationItemDBField.LOAD_STATUS, DataType.INT, entity.getLoadStatus());
                request.addBatchDataParam(WhApvAllocationItemDBField.LOAD_BY, DataType.INT, entity.getLoadBy());
                request.addBatchDataParam(WhApvAllocationItemDBField.LOAD_TIME, DataType.TIMESTAMP,
                        entity.getLoadTime());

                // 入库
                request.addBatchDataParam(WhApvAllocationItemDBField.UP_STATUS, DataType.INT, entity.getUpStatus());
                request.addBatchDataParam(WhApvAllocationItemDBField.UP_NUM, DataType.INT, entity.getUpNum());
                request.addBatchDataParam(WhApvAllocationItemDBField.UP_BY, DataType.INT, entity.getUpBy());
                request.addBatchDataParam(WhApvAllocationItemDBField.UP_TIME, DataType.TIMESTAMP, entity.getUpTime());
                request.addBatch();
            }
            return SqlerTemplate.batchUpdate(request);
        }

        return null;
    }

    @Override
    public int updateApvAllocationStatus(String allocationNo, int status) {
        SqlerRequest request = new SqlerRequest("updateApvAllocationStatus");
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_NO, DataType.STRING, allocationNo);
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_STATUS, DataType.INT, status);
        int updateNum = SqlerTemplate.execute(request);
        // 防止发货仓先改状态导致库存未扣减，上架不推送状态
        if (updateNum > 0 && status != AllocationStatusEnum.SEGMENT_STORAGE.intCode()
                && status != AllocationStatusEnum.ALL_STORAGE.intCode()) {
            // 推送状态到发货仓
            WhApvAllocationQueryCondition queryCondition = new WhApvAllocationQueryCondition();
            queryCondition.setAllocationNoList(Collections.singletonList(allocationNo));
            queryCondition.setAllocationStatus(status);
            AllocationPushUtil.syncApvAllocationStatus(queryCondition);
        }
        return updateNum;
    }

    /**
     * 调拨单状态更新(行锁)
     *
     * @param allocationNo
     * @param oldStatus
     * @param status
     * @return
     */
    @Override
    public int updateApvAllocationStatusByOldStatus(String allocationNo, int oldStatus, int status) {
        SqlerRequest request = new SqlerRequest("updateApvAllocationStatusByOldStatus");
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_NO, DataType.STRING, allocationNo);
        request.addDataParam("oldStatus", DataType.INT, oldStatus);
        request.addDataParam(WhApvAllocationDBField.ALLOCATION_STATUS, DataType.INT, status);

        return SqlerTemplate.execute(request);
    }

    @Override
    public List<WhApvAllocation> queryApvAllocationByBoardNoOrBoxNo(String boardNo, String boxNo) {
        if (StringUtils.isBlank(boardNo) && StringUtils.isBlank(boxNo)) {
            return null;
        }
        SqlerRequest request = new SqlerRequest("queryApvAllocationByBoardNoOrBoxNo");
        request.addDataParam(WhApvAllocationItemDBField.BOARD_NO, DataType.STRING, boardNo);
        request.addDataParam(WhApvAllocationItemDBField.BOX_NO, DataType.STRING, boxNo);
        return SqlerTemplate.query(request, new WhApvAllocationMapper());
    }

    @Override
    public int updateApvAllocationByBoard(String boardNo, String boxNo) {
        Assert.notNull(boardNo);
        Assert.notNull(boxNo);
        SqlerRequest request = new SqlerRequest("updateApvAllocationByBoard");
        request.addBatchDataParam(WhApvAllocationItemDBField.BOARD_STATUS, DataType.INT,
                AllocationTakeStatusEnum.BOARDING.intCode());
        request.addDataParam(WhApvAllocationItemDBField.BOARD_NO, DataType.STRING, boardNo);
        request.addBatchDataParam(WhApvAllocationItemDBField.BOARD_BY, DataType.INT, DataContextHolder.getUserId());
        request.addBatchDataParam(WhApvAllocationItemDBField.BOARD_TIME, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhApvAllocationItemDBField.BOX_NO, DataType.STRING, boxNo);
        return SqlerTemplate.execute(request);
    }

    @Override
    public int updateApvAllocationByLoad(String boxNo, String boardNo) {
        if (StringUtils.isNotBlank(boxNo)) {
            SqlerRequest request = new SqlerRequest("updateApvAllocationByLoad");
            request.addDataParam(WhApvAllocationItemDBField.LOAD_STATUS, DataType.INT,
                    AllocationLoadStatusEnum.LOADING.intCode());
            request.addDataParam(WhApvAllocationItemDBField.LOAD_BY, DataType.INT, DataContextHolder.getUserId());
            request.addDataParam(WhApvAllocationItemDBField.LOAD_TIME, DataType.TIMESTAMP,
                    new Timestamp(System.currentTimeMillis()));
            request.addDataParam(WhApvAllocationItemDBField.BOX_NO, DataType.STRING, boxNo);
            return SqlerTemplate.execute(request);
        }
        if (StringUtils.isNotBlank(boardNo)) {
            SqlerRequest request = new SqlerRequest("updateApvAllocationByLoad");
            request.addDataParam(WhApvAllocationItemDBField.LOAD_STATUS, DataType.INT,
                    AllocationLoadStatusEnum.LOADING.intCode());
            request.addDataParam(WhApvAllocationItemDBField.LOAD_BY, DataType.INT, DataContextHolder.getUserId());
            request.addDataParam(WhApvAllocationItemDBField.LOAD_TIME, DataType.TIMESTAMP,
                    new Timestamp(System.currentTimeMillis()));
            request.addDataParam(WhApvAllocationItemDBField.BOARD_NO, DataType.STRING, boardNo);
            return SqlerTemplate.execute(request);
        }
        return 0;

    }

    @Override
    public List<Integer> queryAllocationByLoading(List<Integer> allocationIdList) {
        SqlerRequest request = new SqlerRequest("queryAllocationByLoading");
        request.addDataParam("allocation_id_list", DataType.INT, allocationIdList);
        return SqlerTemplate.query(request, new RowMapper<Integer>() {
            public Integer mapRow(ResultSet rs, int rowNum) throws SQLException {
                return rs.getInt("allocation_id");
            }
        });
    }

    @Override
    public List<Integer> queryOrderAllocationByLoading(List<Integer> allocationIdList) {
        SqlerRequest request = new SqlerRequest("queryOrderAllocationByLoading");
        request.addDataParam("allocation_id_list", DataType.INT, allocationIdList);
        return SqlerTemplate.query(request, new RowMapper<Integer>() {
            public Integer mapRow(ResultSet rs, int rowNum) throws SQLException {
                return rs.getInt("allocation_id");
            }
        });
    }

    @Override
    public List<WhApvAllocationItem> queryApvAllocationDetails(WhApvAllocationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryApvAllocationDetails");
        if (CollectionUtils.isNotEmpty(query.getItemIds())) {
            request.addDataParam("allocation_item_id_list", DataType.INT, query.getItemIds());
        }
        if (CollectionUtils.isNotEmpty(query.getStatusList())) {
            request.addDataParam("allocation_status_list", DataType.INT, query.getStatusList());
        }
        if (CollectionUtils.isNotEmpty(query.getSkus())) {
            request.addDataParam("sku_list", DataType.STRING, query.getSkus());
        }
        request.addDataParam(WhApvAllocationItemDBField.ALLOCATION_ID, DataType.INT, query.getAllocationId());
        request.addDataParam(WhApvAllocationItemDBField.ALLOCATION_NO, DataType.STRING, query.getAllocationNo());
        request.addDataParam(WhApvAllocationItemDBField.SKU, DataType.STRING, query.getSku());
        return SqlerTemplate.query(request, new WhApvAllocationItemMapper());
    }

    @Override
    public List<AllocationInventory> queryAlloCheckInData(List<String> allocationNoList) {
        if (CollectionUtils.isEmpty(allocationNoList)) {
            return null;
        }
        SqlerRequest request = new SqlerRequest("querySkuInventoryByCheckIn");
        request.addDataParam("allocation_no_list", DataType.STRING, allocationNoList);
        return SqlerTemplate.query(request, new RowMapper<AllocationInventory>() {
            public AllocationInventory mapRow(ResultSet rs, int rowNum) throws SQLException {
                AllocationInventory entity = new AllocationInventory();
                entity.setAllocationNo(rs.getString("allocationNo"));
                entity.setWarehouseId(rs.getInt("warehouseId"));
                entity.setSku(rs.getString("sku"));
                entity.setGoodQuantity(rs.getObject("goodQuantity") == null ? 0 : rs.getInt("goodQuantity"));
                entity.setBadQuantity(rs.getObject("badQuantity") == null ? 0 : rs.getInt("badQuantity"));
                return entity;
            }
        });
    }

    @Override
    public List<AllocationInventory> queryApvItemByFinish() {
        SqlerRequest request = new SqlerRequest("queryApvItemByFinish");
        return SqlerTemplate.query(request, new RowMapper<AllocationInventory>() {
            @SuppressWarnings("unchecked")
            public AllocationInventory mapRow(ResultSet rs, int rowNum) throws SQLException {
                AllocationInventory entity = new AllocationInventory();
                entity.setWarehouseId(rs.getInt("in_warehouse_id"));
                entity.setAllocationNo(rs.getString("allocation_no"));
                entity.setApvNo(rs.getString("apv_no"));
                entity.setApvId(rs.getInt("apv_id"));
                String itemStr = rs.getString("apv_item");
                if (StringUtils.isNotBlank(itemStr)) {
                    List<WhApvItem> apvItemList = new ArrayList<WhApvItem>();
                    Map<String, Integer> skuMap = JSON.parseObject(itemStr, Map.class);
                    for (String sku : skuMap.keySet()) {
                        WhApvItem apvItem = new WhApvItem();
                        apvItem.setSku(sku);
                        apvItem.setSaleQuantity(skuMap.get(sku));
                        apvItemList.add(apvItem);
                    }
                    entity.setApvItemList(apvItemList);
                }
                return entity;
            }
        });
    }

    @Override
    public List<String> queryAlloApvNosOnLoad(List<String> apvNoList, List<Integer> statusList) {
        if (CollectionUtils.isEmpty(apvNoList)) {
            return null;
        }
        SqlerRequest request = new SqlerRequest("queryAlloApvNosOnLoad");
        request.addDataParam("apv_status_list", DataType.INT, statusList);
        request.addDataParam("apv_no_list", DataType.STRING, apvNoList);
        return SqlerTemplate.query(request, new RowMapper<String>() {
            public String mapRow(ResultSet rs, int rowNum) throws SQLException {
                return rs.getString("apv_no");
            }
        });
    }

    @Override
    public List<WhApvAllocation> queryOrderAllocationByBoardNoOrSubTaskNo(String boardNo, String subTaskNo) {
        if (StringUtils.isBlank(boardNo) && StringUtils.isBlank(subTaskNo)) {
            return null;
        }
        SqlerRequest request = new SqlerRequest("queryOrderAllocationByBoardNoOrSubTaskNo");
        request.addDataParam(WhApvAllocationOrderItemDBField.BOARD_NO, DataType.STRING, boardNo);
        request.addDataParam(WhApvAllocationOrderItemDBField.TASK_NO, DataType.STRING, subTaskNo);
        return SqlerTemplate.query(request, new WhApvAllocationMapper());
    }

    @Override
    public int updateOrderAllocationByBoard(String boardNo, String subTaskNo) {
        Assert.notNull(boardNo);
        Assert.notNull(subTaskNo);
        SqlerRequest request = new SqlerRequest("updateOrderAllocationByBoard");
        request.addBatchDataParam(WhApvAllocationOrderItemDBField.BOARD_STATUS, DataType.INT,
                AllocationTakeStatusEnum.BOARDING.intCode());
        request.addDataParam(WhApvAllocationOrderItemDBField.BOARD_NO, DataType.STRING, boardNo);
        request.addBatchDataParam(WhApvAllocationOrderItemDBField.BOARD_BY, DataType.INT,
                DataContextHolder.getUserId());
        request.addBatchDataParam(WhApvAllocationOrderItemDBField.BOARD_TIME, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam(WhApvAllocationOrderItemDBField.TASK_NO, DataType.STRING, subTaskNo);
        request.addBatchDataParam("BEFOR_BOARD_STATUS", DataType.INT, AllocationTakeStatusEnum.NOT_BOARD.intCode());
        return SqlerTemplate.execute(request);
    }

    @Override
    public int updateOrderAllocationByLoad(String subTaskNo, String boardNo) {
        if (StringUtils.isNotBlank(subTaskNo)) {
            SqlerRequest request = new SqlerRequest("updateOrderAllocationByLoad");
            request.addDataParam(WhApvAllocationOrderItemDBField.LOAD_STATUS, DataType.INT,
                    AllocationLoadStatusEnum.LOADING.intCode());
            request.addDataParam(WhApvAllocationOrderItemDBField.LOAD_BY, DataType.INT, DataContextHolder.getUserId());
            request.addDataParam(WhApvAllocationOrderItemDBField.LOAD_TIME, DataType.TIMESTAMP,
                    new Timestamp(System.currentTimeMillis()));
            request.addDataParam(WhApvAllocationOrderItemDBField.TASK_NO, DataType.STRING, subTaskNo);
            request.addDataParam("befor_load_status", DataType.INT, AllocationLoadStatusEnum.NOT_LOAD.intCode());
            return SqlerTemplate.execute(request);
        }
        if (StringUtils.isNotBlank(boardNo)) {
            SqlerRequest request = new SqlerRequest("updateOrderAllocationByLoad");
            request.addDataParam(WhApvAllocationOrderItemDBField.LOAD_STATUS, DataType.INT,
                    AllocationLoadStatusEnum.LOADING.intCode());
            request.addDataParam(WhApvAllocationOrderItemDBField.LOAD_BY, DataType.INT, DataContextHolder.getUserId());
            request.addDataParam(WhApvAllocationOrderItemDBField.LOAD_TIME, DataType.TIMESTAMP,
                    new Timestamp(System.currentTimeMillis()));
            request.addDataParam(WhApvAllocationOrderItemDBField.BOARD_NO, DataType.STRING, boardNo);
            request.addDataParam("befor_load_status", DataType.INT, AllocationLoadStatusEnum.NOT_LOAD.intCode());
            return SqlerTemplate.execute(request);
        }
        return 0;
    }

    @Override
    public List<SyncAllocation> querySyncAllocationAndItemList(WhApvAllocationQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("querySyncAllocationAndItemList");
        setQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new SyncAllocationAndItemMapper());
    }

    @Override
    public void updateSyncAllocationByPrimaryKey(List<WhApvAllocation> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            if (entityList != null && !entityList.isEmpty()) {
                SqlerRequest request = new SqlerRequest("updateSyncAllocationByprimaryKey");
                for (WhApvAllocation entity : entityList) {
                    request.addBatchDataParam(WhApvAllocationDBField.ALLOCATION_ID, DataType.INT,
                            entity.getAllocationId());
                    request.addBatchDataParam(WhApvAllocationDBField.ALLOCATION_STATUS, DataType.INT,
                            entity.getAllocationStatus());
                    request.addBatch();
                }
                SqlerTemplate.batchUpdate(request);
            }
        }
    }

    @Override
    public void updateSyncAllocationItemByPrimaryKey(List<WhApvAllocationItem> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            if (entityList != null && !entityList.isEmpty()) {
                SqlerRequest request = new SqlerRequest("updateSyncAllocationItemByPrimaryKey");
                for (WhApvAllocationItem entity : entityList) {
                    request.addBatchDataParam(WhApvAllocationItemDBField.ALLOCATION_ITEM_ID, DataType.INT,
                            entity.getAllocationItemId());
                    request.addBatchDataParam(WhApvAllocationItemDBField.UP_NUM, DataType.INT, entity.getUpNum());
                    request.addBatchDataParam(WhApvAllocationItemDBField.IS_AUDIT, DataType.BOOLEAN,
                            entity.getIsAudit());
                    request.addBatchDataParam(WhApvAllocationItemDBField.HISTORY_PICK_NUM, DataType.INT,
                            entity.getHistoryPickNum());
                    request.addBatch();
                }
                SqlerTemplate.batchUpdate(request);
            }
        }
    }

    @Override
    public WhApvAllocationItem queryWhApvAllocationItemByPrimaryKey(Integer primaryKey) {
        if (null == primaryKey) {
            return null;
        }
        SqlerRequest request = new SqlerRequest("queryWhApvAllocationItemByPrimaryKey");
        request.addDataParam(WhApvAllocationItemDBField.ALLOCATION_ITEM_ID, DataType.INT, primaryKey);
        return SqlerTemplate.queryForObject(request, new WhApvAllocationItemMapper());
    }

    private void setUpQueryCondition(SqlerRequest request, WhApvAllocationQueryCondition query) {
        // 搜索条件不允许出现空字符
        request.setAllowBlank(false);

        // 调拨单号
        String allocationNoStr = query.getAllocationNoStr();
        if (StringUtils.isNotBlank(allocationNoStr)) {
            allocationNoStr = allocationNoStr.trim().replaceAll(" ", "");
            List<String> list = Arrays.asList(allocationNoStr.split(","));
            request.addDataParam("allocation_no_list", DataType.STRING, list);
        }

        // 调拨单状态
        String allocationStatusStr = query.getAllocationStatusStr();
        if (StringUtils.isNotBlank(allocationStatusStr)) {
            allocationStatusStr = allocationStatusStr.trim().replaceAll(" ", "");
            List<String> list = Arrays.asList(allocationStatusStr.split(","));
            request.addDataParam("allocation_status_list", DataType.STRING, list);
        } else {
            List<String> list = new ArrayList<String>();
            list.add(AllocationStatusEnum.SIGNING.getCode());
            list.add(AllocationStatusEnum.SEGMENT_STORAGE.getCode());
            list.add(AllocationStatusEnum.ALL_STORAGE.getCode());
            request.addDataParam("allocation_status_list", DataType.STRING, list);
        }

        // 箱号
        request.addDataParam("box_no", DataType.STRING, query.getBoxNo());

        // 发货仓
        request.addDataParam("delivery_warehouse_id", DataType.INT, query.getDeliveryWarehouseId());

        // 目的仓
        request.addDataParam("dest_warehouse_id", DataType.INT, query.getDestWarehouseId());

        // 装箱人
        request.addDataParam("box_by", DataType.INT, query.getBoxBy());

        // 装箱时间
        request.addDataParam("box_start_time", DataType.STRING, query.getBoxStartTime());
        request.addDataParam("box_end_time", DataType.STRING, query.getBoxEndTime());

        // 点数入库人
        request.addDataParam("put_by", DataType.INT, query.getPutBy());

        // 点数入库时间
        request.addDataParam("put_start_time", DataType.STRING, query.getPutStartTime());
        request.addDataParam("put_end_time", DataType.STRING, query.getPutEndTime());

        // 上架差异
        Integer upDiffCondition = query.getUpDiffCondition();
        if (null != upDiffCondition && upDiffCondition == 0) {// 无差异
            request.addSqlDataParam("up_diff_condition", "AND allocation_item.allocation_num = checkin.up_quantity");
        }
        if (null != upDiffCondition && upDiffCondition == 1) {// 有差异
            request.addSqlDataParam("up_diff_condition", "AND allocation_item.allocation_num != checkin.up_quantity");
        }

        // SKU
        String skuStr = query.getSkuStr();
        if (StringUtils.isNotBlank(skuStr)) {
            skuStr = skuStr.trim().replaceAll(" ", "");
            List<String> list = Arrays.asList(skuStr.split(","));
            request.addDataParam("sku_list", DataType.STRING, list);
        }
    }

    @Override
    public int queryAllocationUpItemCount(WhApvAllocationQueryCondition query) {
        SqlerRequest request = new SqlerRequest("queryAllocationUpItemCount");
        setUpQueryCondition(request, query);
        return SqlerTemplate.queryForInt(request);
    }

    @Override
    public List<WhApvAllocationUpItem> queryAllocationUpItemList(WhApvAllocationQueryCondition query, Pager pager) {
        SqlerRequest request = new SqlerRequest("queryAllocationUpItemList");
        setUpQueryCondition(request, query);
        if (pager != null) {
            request.addFetch(pager.getPageNo(), pager.getPageSize());
        }
        return SqlerTemplate.query(request, new WhApvAllocationUpItemMapper());
    }
    
    @Override
    public int batchUpdateAllocationByLoad(List<String> allocationNoList, List<String> boxNoList,
            Integer deliveryMethod, String logisticsNo) {
        if (CollectionUtils.isEmpty(allocationNoList) && CollectionUtils.isEmpty(boxNoList))
            return 0;
        SqlerRequest request = new SqlerRequest("batchUpdateAllocationByLoad");
        request.addDataParam(WhApvAllocationItemDBField.LOAD_STATUS, DataType.INT,
                AllocationLoadStatusEnum.LOADING.intCode());
        request.addDataParam(WhApvAllocationItemDBField.LOAD_BY, DataType.INT, DataContextHolder.getUserId());
        request.addDataParam(WhApvAllocationItemDBField.LOAD_TIME, DataType.TIMESTAMP,
                new Timestamp(System.currentTimeMillis()));
        request.addDataParam("box_no_list", DataType.STRING, boxNoList);
        request.addDataParam("allocation_no_list", DataType.STRING, allocationNoList);
        request.addDataParam(WhApvAllocationItemDBField.DELIVERY_METHOD, DataType.INT, deliveryMethod);
        request.addDataParam(WhApvAllocationItemDBField.LOGISTICS_NO, DataType.STRING, logisticsNo);
        return SqlerTemplate.execute(request);
    }
}