<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
    <style type="text/css">
        .modal-body input {
            width: 240px;
        }
        #condition{
            font-size: 18px;
        }
    </style>
</head>
<body>
	<@header method="header" active="15000000" ><#include "/ftl/header.ftl"></@header>
<div id="page" style="background-color: rgb(231, 237, 248)">
    <div class="row">
        <div class="col-md-12" style="padding: 0">
            <ul class="page-breadcrumb breadcrumb" style="background-color: white;">
                <li><a href="#">发货扫描</a>
                <li class="active">中转仓多件包装</li>
            </ul>
        </div>
    </div>
		<#include "/common/pack_bgcolor_selector.html">

    <!-- 内容 -->
    <div class="container-fluid" style="background-color: white;border: none">
        <div class="row">
            <div>
                <div class=" panel layout-panel layout-panel-west" >
                    <div class="panel-tool"></div>
                </div>
                <div >
                    <h3 style="display: inline-block">扫描区域</h3>
                    <input  type="hidden" value="" id="apv-no-now"/>
                    <input  type="hidden" value="" id="sku-now"/>
                    <input  type="hidden" value="" id="ship-service-now"/>

                    <label>拣货任务号/周转筐</label>
                    <input type="text" class="input-mini" name="orderid" id="orderid" onkeypress="if(event.keyCode==13) { inputorderid(this); return false;}"tabindex="4">

                    <label style="margin-left: 10px">SKU</label>
                    <input type="text" class="input-mini"    name="apvid" id="apvid"  onkeypress="if(event.keyCode==13) { inputnext(this); return false;}"tabindex="4">

                    <label style="margin-left: 10px">SKU</label>
                    <input type="text" class="input-mini"    name="sku" id="sku"  onkeypress="if(event.keyCode==13) { inputsku(this, 0); return false;}">

                    <div style="display: inline-block;margin-left: 10px" class="panel-title2" id="panel-title"><h1  style="color:blue;font-size:48px;">成功:<b>0</b></h1></div>
                    <div style="display: inline-block;margin-left: 10px" class="panel-title2" id="panel-floor"></div>
                    <div style="display: inline-block;margin: 0 10px" class="panel-title2" id="panel-title-piece"><h1  style="color:red;font-size:48px;">计数:<b>0</b></h1></div>

                    <!--<button type="button" class="btn red" onclick="rePrint()">
                        <i class="icon-print"></i> 重新打印
                    </button>-->
                    <button type="button" class="btn red" onclick="failPrint()">
                        <i class="icon-print"></i> 打印失败面单
                    </button>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <button  type="button" class="btn red" onclick="openPrintDialog()">打印机配置</button>
                    <span id="check-quantity" style="font-size: 36px;font-weight: 900;color: red;margin-left: 20px"></span>
                </div>

                <div style="height: 500px;" class="col-md-12">
                    <div id="check_scan_datas" class="col-md-6"></div>
                    <div id="print-waybill" class="col-md-6">
                        <div class="row">
                            <div class="col-md-12">
                                <button type="button" id="printBtn" style="display: none;margin-top: 20px; font-size: 20px" class="btn blue" onclick="printBtn()">打印</button>
                                <iframe src="javascript:void(0);" frameborder="0" marginheight="0" marginwidth="0" frameborder="0" scrolling="auto" id="shippingOrderFrame" name="shippingOrderFrame" width="100%" height="500px"></iframe>
                                <iframe src="javascript:void(0);" frameborder="0" marginheight="0" marginwidth="0" frameborder="0" scrolling="auto" id="lanshouFrame" name="lanshouFrame" width="100%" height="500px"></iframe>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="print_tag" style="width:100%;">
                </div>
                <div id="temu_print"  style="width:100%;"></div>
                <div id="print_gpsr_tag" style="display: none;"></div>
            </div>
        </div>
    </div>
	<#include "/common/footer.html">

</div>

<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/web-storage-cache.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/jquery.sound.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/pages/pack-style.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/packing.js?v=${.now?datetime}"></script>
<script type="text/javascript" src="${CONTEXT_PATH }js/print.js?v=${.now?datetime}"></script>

<script type="text/javascript">
    window.onload = function () {
        getPrinterList();
    };


    var uuIdCacheKey = 'CHECK_PACKING_FOR_UUID_CACHE_KEY_SM'+ '_' +  new Date().getTime();
    var cacheKey = "basket_check_success";
    var pieceCacheKey = "basket_check_piece_success";

    //拣货类型 单品多件和 多品多件 快递和FBA 面单不同
    var pickType ;

    var taskNo ;
    var taskType;

    //物流公司 EMS需要特殊模板
    var logisticsCompany;

    // 上一次扫描的发货单
    var lastApvId;
    var jitAsn=false;
    var asnFirst=false;
    var boxMarkUrl;
    var purposeHouse;

    $(document).ready(function(){
        initSkuUuIdStorageCache(uuIdCacheKey);
        pageInit();

        $('#orderid').val('');
        $('#orderid').select().focus();

        var storage = new WebStorageCache();

        if (storage.get(cacheKey)) {
            lastSuc = storage.get(cacheKey);
            $('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+lastSuc+'</b></h1>');
        }
        initPrinter();
        $('#orderid').focus();

    }); // end ready

    // 初始化
    function pageInit() {
        $('#apvid').val('');

        $('#apvid').select().focus();

        $('#sku').val('');
        // 完成之后不清空显示
        if (!completeCheck()){
            $('#check_scan_datas').html('');
        }
    }


    //周转筐 触发
    function inputorderid(obj){

        if(!obj.value || obj.value.replace(/\s/g,'') == ''){
            layer.alert("请输入拣货任务号/周转筐!");
            return ;
        }

        var orderId = obj.value.replace(/\s/g,'');

        orderId = $.trim(orderId);

        var url = CONTEXT_PATH + "fba/packs/box/scan";


        //中转仓单品多件
        var pickType = 52;

        var r= $.ajax({
            url : url,
            data : {box : orderId, pickType : pickType},
            timeout : 100000,
            success : function(response){

                if(response.status == '200'){
                    $('#apvid').select().focus();
                    pickType = response.message;
                    if (response.body && response.body.whPickingTask){
                        var whPickingTask = response.body.whPickingTask;
                        taskNo = whPickingTask.taskNo;
                        taskType = whPickingTask.taskType;
                    }
                    var lessPick = response.location;
                    if (taskType && taskType != 54 && lessPick) {
                        layer.alert("存在拣货数量=0的包裹，请绑定中转仓播种异常周转筐", {closeBtn: 0}, function (index) {
                            layer.close(index);
                            bindStockOut(orderId, lessPick, null, null);
                        });
                    }
                }else{
                    layer.alert(response.message, {closeBtn: 0},function (index) {
                        layer.close(index);
                        $('#orderid').val('');
                        // 找不到订单
                        $('#orderid').select().focus();
                    });

                }
            },
            error:function(){
                layer.alert('扫描失败，请重新扫描');
            }
        });

    }


    // 第一次扫描SKU
    function inputnext(obj){
        if (!jitPrinter || !jitPrinterTag || !jitPrinter75Tag) {
            layer.alert("请先配置打印机",'error');
            return
        }
        var orderid = $("#orderid").val();
        if (orderid == undefined || orderid == ''){
            layer.alert("请先扫描拣货任务号/周转筐",'error');
            return
        }

        if(!obj.value || obj.value.replace(/\s/g,'') == ''){
            layer.alert("请扫描sku!");
            return ;
        }

        var apvId = obj.value.replace(/\s/g,'');
        apvId = $.trim(apvId);

        //兼容SKU编码
        //apvId = getSkuByBarCode(obj);

        var uuid = apvId.trim();
        if(!(apvId.indexOf("=") == -1)){
            var realSku = apvId.split('=')[0];
            $('#apvid').val(realSku);
            apvId = realSku;
        }

        var url = CONTEXT_PATH + "fba/packs/sm/check/sku";


        var r= $.ajax({
            url : url,
            data : {sku : apvId, lastApvId : lastApvId, orderId: orderid, uuid:uuid},
            timeout : 90 * 1000,
            success : function(response){
                $("#check_scan_datas").html(response);

                const errorElement = $(response).find("#scan-error");
                if (response.length > 230 && errorElement.length===0){

                    var errorMsg = $("#check_scan_datas").find("#scan-error").html();
                    if(errorMsg){
                        layer.alert(errorMsg, {closeBtn: 0},function (index) {
                            layer.close(index);
                            $('#apvid').val("");
                            $('#apvid').focus();
                            addWhUniqueSkuLog(uuid, '');
                        });
                        return;
                    }

                    // 第一次扫描初始化
                    initSkuUuIdStorageCache(uuIdCacheKey);
                    //checkSkuUuIdStorageCache(uuIdCacheKey, uuid);
                    // 扫描成功
                    $('#sku').select().focus();

                    isFocus = false;

                    // 自动扫第一个
                    $("#sku").val(apvId);
                    inputsku(document.getElementById("sku"), 1);

                    // 记录最后一次扫描的订单
                    lastApvId = $(response).find("input[name='apvId']").val();

                    var apvNo = $(response).find("input[name='apvNo']").val();

                    logisticsCompany = $(response).find("input[name='logisticsCompany']").val();

                    addWhUniqueSkuLog(uuid, apvNo);

                    //保存apvNo
                    $("#apv-no-now").val(apvNo);

                    // 要核对的数量
                    var quantity = $(response).find("input[name='totalSaleQuantity']").val();
                    $("#check-quantity").text(quantity);

                    audioPlay('success');

                }else {
                    // 找不到订单
                    $('#apvid').select().focus();
                    addWhUniqueSkuLog(uuid, '');
                    audioPlay('error');

                    const errorMessage = errorElement.length ? errorElement.text().trim() : "未匹配到单据，请重试";
                    layer.alert(errorMessage, {
                        closeBtn: false,
                        skin: 'layui-layer-lan'
                    }, function(index) {
                        layer.close(index);
                        $('#apvid').select().focus();
                    });

                }

            },
            error:function(){
                layer.alert('扫描失败，请重新扫描');
                audioPlay('error');
            }
        });
    }

    function printTag(id, sku,skuBarcode) {
        var url = CONTEXT_PATH + "fba/packs/printTag?id=" + id + "&sku=" + sku;
        if (skuBarcode) {
            url = url + "&skuBarcode=" + skuBarcode;
        }
        $.ajax({
            url: url,
            type:"GET",
            data:{id:id},
            success : function(response){
                $('#print_tag').html(response);
                var printUrl = $("input[name='printUrl']").val();
                var purposeHouse = $(response).find("input[name='purposeHouse']").val();
                if (printUrl) {
                    printUrl = window.location.origin + printUrl;
                    var pageLength = "60mm";
                    var pageWidth = "60mm";
                    printPdfCopies(printUrl, jitPrinter75Tag, pageLength, pageWidth,1);
                } else {
                    var map = new Map();
                    $("input[name='base64']").each(function () {
                        var key = $(this).val();
                        var num = map.get(key);
                        if (num === undefined) {
                            num = 0;
                        }
                        num++;
                        map.put(key, num);
                    })
                    map.each(function (k, v) {
                        if(purposeHouse && purposeHouse == 'Shein'){
                            printWeirdPdfCopies(k, jitPrinter75Tag, "70mm", "60mm", "70mm", "30mm", v);
                        }else {
                            printCopies(k, jitPrinterTag, null, v, null);
                        }
                    });
                    // 打印合并后sku标签
                    var mergePdfList = [];
                    $("input[name='base64Sku']").each(function () {
                        var pdf = $(this).val();
                        mergePdfList.push(pdf);
                    });
                    var copies = $("input[name='printCopies']").val();
                    printMergeCopies(mergePdfList,jitPrinter75Tag,copies);
                }
            }
        });
    }

    /**
     * 用于合并打印
     * @param temuCodeUrls 合并打印的pdf列表
     * @param printerTag 打印机
     * @param copies 打印份数
     */
    function printMergeCopies(mergePdfList,printerTag,copies){
        var LODOP = getLodop();
        LODOP.SET_PRINT_PAGESIZE(0, '70mm', '60mm', 'Note'); // 设置纸张大小
        $(mergePdfList).each(function(index,item){
            // 缩放pdf的内容，将2张pdf弄到一个页面上
            if (index == 0){
                LODOP.ADD_PRINT_PDF("1mm","0mm","70mm","20mm", item);
            }
            if (index == 1){
                LODOP.ADD_PRINT_PDF("21.39mm","0mm","70mm","40mm", item);
            }
        });
        LODOP.SET_PRINT_STYLEA(0,"PDFScaleMode",0);
        if (LODOP.SET_PRINTER_INDEX(printerTag)) {
            if(copies === undefined){
                copies = 1;
            }
            LODOP.SET_PRINT_COPIES(copies); // 打印份数
            LODOP.PRINT(); // 静默打印
            // LODOP.PRINT_DESIGN();
        }
    }
    function printCopies(message,printerTag,printer, copies,jitBoxNumber){
        var LODOP = getLodop();
        var printerName;

        if (printer){
            LODOP.SET_PRINT_PAGESIZE(0, '100mm', '100mm', 'Note'); // 设置纸张大小
            LODOP.ADD_PRINT_PDF(0, 0, '100mm', '100mm', message);
            if (jitBoxNumber) {
                LODOP.ADD_PRINT_TEXT(320, 12, 200, 31, jitBoxNumber);
                LODOP.SET_PRINT_STYLEA(0, "FontSize", 35);
            }
            printerName=printer;
        }
        if(printerTag){
            LODOP.SET_PRINT_PAGESIZE(0, '70mm', '30mm', 'Note'); // 设置纸张大小
            LODOP.ADD_PRINT_PDF(0, 0, '70mm', '30mm', message);
            printerName=printerTag;
        }
        if (LODOP.SET_PRINTER_INDEX(printerName)) {
            if(copies === undefined){
                copies = 1;
            }
            LODOP.SET_PRINT_COPIES(copies); // 打印份数
            LODOP.PRINT(); // 静默打印
        }
    }

    // 添加唯一码包装日志
    function addWhUniqueSkuLog(uuid, apvNo) {
        if (apvNo){
            addUuIdStorageCache(uuIdCacheKey, uuid);
        }
        var r = $.ajax({
            type : "get",
            url :CONTEXT_PATH+"apv/packs/addWhUniqueSkuLog" ,
            data : {uuid : uuid, apvNo: apvNo, packType: 13},
            timeout : 100000,
            beforeSend : function() {
            },
            success : function(responese) {

            },
            error : function() {
            }
        });
        addPackExceptionRecord(uuid, apvNo);
    }

    // 新增包装异常记录-未匹配发货单
    function addPackExceptionRecord(uuid, apvNo){
        if(apvNo != undefined && apvNo != ''){
            return;
        }
        $.ajax({
            type: "POST",
            url: CONTEXT_PATH+"apv/packs/createPackExceptionUuidItem",
            data: {uuid: uuid, taskNo: taskNo, scanPage: 7, packExceptionType: 2},
            success: function(response) {
            },
            error:function () {
            }
        });
    }

    // 校验唯一码重复扫描
    function checkScanPackingUniqueSku(sku, uuid) {
        var r = $.ajax({
            type : "get",
            url :CONTEXT_PATH+"fba/packs/checkScanPackingUniqueSku" ,
            data : {uuid : uuid},
            timeout : 100000,
            beforeSend : function() {
                App.blockUI();
            },
            success : function(responese) {
                App.unblockUI();
                if (responese.status == '500') {
                    layer.alert(responese.message, {closeBtn: 0},function (index) {
                        layer.close(index);
                        audioPlay('error');
                        $('#sku').val("");
                        $('#sku').focus();
                    });
                    createPackExceptionUuidItem(uuid, responese.message);
                } else {
                    // 前端缓存校验是否重复扫描
                    if(!checkRetrunUuIdStorageCache(uuIdCacheKey, uuid)){
                        layer.alert("唯一码重复扫描！",{closeBtn: 0}, function (index) {
                            layer.close(index);
                            audioPlay('error');
                            $('#sku').val("");
                            $('#sku').focus();
                        });
                        return;
                    }
                    checkIn(sku, uuid);
                }
            },
            error : function() {
                App.unblockUI();
                layer.alert('扫描失败，请重新扫描', {closeBtn: 0},function (index) {
                    layer.close(index);
                    audioPlay('error');
                    $('#sku').val("");
                    $('#sku').focus();
                });
            }
        });
    }


    //再次扫描SKU
    function inputsku(obj, type){

        var sku = obj.value;
        var uuid = sku.trim();

        //订单Id,切分二维码. 如果存在的话
        if(!(sku.indexOf("=") == -1)){
            var realSku = sku.split('=')[0];
            $('#sku').val(realSku);
            sku = realSku;
        }
        //checkIn(sku, uuid);
        // 第一次不校验
        if (type!=1){
            checkScanPackingUniqueSku(sku, uuid);
        }else {
            checkIn(sku, uuid);
        }
    }

    //核对sku和数量 正确(对应的sku数量增加)
    function checkIn(sku, uuid){
        sku = $.trim(sku.toUpperCase());
        var apvNo = $('#check_scan_datas').find("input[name='apvNo']").val();
        if (!apvNo){
            layer.alert('请先扫描第一个唯一码',{closeBtn: 0},function (index) {
                layer.close(index);
                addWhUniqueSkuLog(uuid, '');
            });
            return;
        }
        if(completeCheck()){
            layer.alert('检查完毕',{closeBtn: 0},function (index) {
                layer.close(index);
                addWhUniqueSkuLog(uuid, '');
            });
            return;
        }
        if ($('[id="check_sku_'+sku+'"]').length==0) {
            sku = "JR" + sku;
            if ($('[id="check_sku_' + sku + '"]').length == 0) {
                clear();
                $('#sku').select().focus();
                layer.alert("SKU不存在或已检查完毕",{closeBtn: 0},function (index) {
                    layer.close(index);
                    addWhUniqueSkuLog(uuid, '');
                    audioPlay('error');
                });
                return;
            }
        }
        debugger;
        jitAsn = ($('#check_scan_datas').find("input[name='jitAsn']").val()) == 'true';
        var apvNo = $('#check_scan_datas').find("input[name='apvNo']").val();
        var apvId = $('#check_scan_datas').find("input[name='apvId']").val();
        boxMarkUrl = $('#check_scan_datas').find("input[name='boxMarkUrl']").val();
        purposeHouse = $('#check_scan_datas').find("input[name='purposeHouse']").val();
        asnFirst = $('#check_scan_datas').find("input[name='asnFirst']").val() == 'true';

        if (purposeHouse && (purposeHouse == 'Shein' || purposeHouse == 'TEMU')){
            asnFirst = false;
        }

        var allCheck = true;
        $('.pack-info-table').each(function () {
            // 未扫描数量
            var check_quantity = parseInt( $(this).find('#check_quantity').text());
            //套装未扫描数量
            var suitCheckQuantity = parseInt($(this).find('#suit_check_quantity').text());
            var suitNum = parseInt($(this).find('#skuSuitNeedNum').val());

            if (check_quantity == 0){
                if (!completeCheck()){
                    allCheck = false;
                    return;
                }
                layer.alert('拿多了?',{closeBtn: 0},function (index) {
                    layer.close(index);
                    clear();
                    audioPlay('error');
                    addWhUniqueSkuLog(uuid, '');
                });
                return false;
            }

            var needQty = parseInt($(this).find('#need-qty').text());
            var orderQty = parseInt($(this).find('#prepare_qty').text());

            var checkQty = check_quantity - 1;
            // 减少数量
            $(this).find('#check_quantity').html(checkQty);

            if (suitNum && (checkQty == 0 || checkQty % suitNum === 0)) {
                // 减少数量
                $(this).find('#suit_check_quantity').html(suitCheckQuantity - 1);
            }
            if (check_quantity > 1) {
                allCheck = false;
                return false;
            }
            // 未扫描数量减少之后是否完成
            if (check_quantity == 1){
                audioPlay('finish');
                var skuBarcode = $(this).find('#skuBarcode').val();
                if (jitAsn) {
                    printJitTag(apvNo, sku, null, skuBarcode);
                } else if (asnFirst) {
                    let temuCodeUrl = $(this).find('#temuCodeUrl-' + sku).val();
                    if (temuCodeUrl) {
                        var tagFrame = "<iframe src='"+temuCodeUrl+"'  width='50%' height='300px'></iframe>";
                        $('#print_tag').append(tagFrame);
                        printPdfCopies(temuCodeUrl, jitPrinter75Tag, "70mm", "60mm", needQty);
                    }
                } else {
                    if (purposeHouse == undefined || purposeHouse != 'TEMU') {
                        printTag(apvId, sku, skuBarcode);
                        //打印JIT货品标签
                        printJitTag(apvNo, sku, null, skuBarcode);
                    }
                }

                if (purposeHouse && purposeHouse == 'Shein') {
                    printLocalGpsrTag(apvNo, null, false);
                }

                if (!completeCheck()) {
                    allCheck = false;
                } else {
                    allCheck = true;
                }
            }
            //播种少拣绑定异常周转筐
            if (purposeHouse && purposeHouse == 'TEMU') {
                if (taskType && taskType != 54 && (needQty == 0 || check_quantity == 1 && needQty < orderQty)) {
                    $('#unPackBindBtn').attr("disabled","disabled");
                    var boxNo = $('#orderid').val();
                    var packageSn = $("#apv-no-now").val();
                    var packQty = needQty - check_quantity + 1;
                    layer.alert("拣货数量小于分配数量，请绑定中转仓播种异常周转筐", {closeBtn: 0}, function (index) {
                        layer.close(index);
                        bindStockOut(boxNo, packageSn, sku,packQty);
                    });
                    allCheck = false;
                }
                $('#sku-now').val(sku);
            }
        });
        addWhUniqueSkuLog(uuid, apvNo);
        $('#sku').val("");
        $('#sku').focus();
        if (!allCheck) return;
        // 检查是否完成
        clear();
    }


    function completeCheck(){
        var allFinish = true;
        $('.pack-info-table').each(function () {
            var checkQuantity = parseInt($(this).find('#check_quantity').text());
            if (checkQuantity != 0) {
                allFinish = false;
                return false;
            }
        });

        return allFinish;
    }

    //清空核对区
    function clear(){
        if (completeCheck()){

            calsf();

            pageInit();
            $('#check-wait-split').css('display','block');
        }else{
            $('#sku').val("");
            $('#sku').focus();
        }
    };

    // 统计扫描成功和失败的数量
    function calsf(){

        var storage = new WebStorageCache();

        var lastSuc = 0;
        if (storage.get(cacheKey)) {
            lastSuc = storage.get(cacheKey);
        }

        var suc = parseInt(1) + lastSuc;

        storage.set(cacheKey, suc, {exp : 5 * 60 * 60});

        $('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+suc+'</b></h1>');

        // 记录后台
        var apvId = $('#check_scan_datas').find("input[name='apvId']").val();

        var quantity = $('#check_scan_datas').find("input[name='totalSaleQuantity']").val();

        var boxNo = $("#orderid").val();


        var url = "${CONTEXT_PATH}fba/packs/sm/check/pass";
        var param = {"apvId" : apvId};
        if (purposeHouse && purposeHouse == 'TEMU') {
            url = "${CONTEXT_PATH}temu/pack/check/pass";
            param = {"apvId": apvId, "taskNo": taskNo};
        }
        // PASS
        $.get(url, param, function(response) {
            if(response.status == '200'){

                // 计件
                calsfPiece(quantity);
                if (purposeHouse && purposeHouse == 'TEMU') {
                    // 绑定唯一码
                    let apvNoNow = $("#apv-no-now").val();
                    if (apvNoNow){
                        bindingApvAndUuIdlist(apvNoNow, uuIdCacheKey);
                    }
                    // 打印
                    var sku = $("#sku-now").val();
                    var packageSn = $('#check_scan_datas').find("input[name='apvNo']").val();
                    $("#temu_print").html('<div class="col-md-3" name="printHtml" id="printHtml"></div>\n' +
                        '                <div class="col-md-3" id="scan_datas"></div>');
                    printXiangMai(packageSn, sku);
                    setTimeout(function () { printFbaNoAndFnSku(packageSn, sku, quantity);
                    }, 500);
                    $("#sku-now").val('');
                } else {
                    printApvNo($("#apv-no-now").val(),apvId);
                }
                if (jitAsn){
                    $('#printBtn').show();
                    // printAsnLanShou(apvId);
                }
                //最后一个扫描完成
                if(response.message == boxNo){
                    $("#orderid").val('');
                    $('#orderid').select().focus();
                }

            } else {
                layer.alert("数据提交失败!请重试。");

            }
        });

    }

    function printBtn() {
        var diglog = dialog({
            title: '仓库拒收提醒',
            width: 420,
            height: 80,
            content: '<h4>'+'打印箱唛后请务必保证在出库前打印揽收单，确保发货单不会被退回'+'<h4>',
            okValue: '知道了,继续打印',
            ok: function () {diglog.show();},
        });
        diglog.show();
    }


    function printAsnLanShou(id) {
        var url = CONTEXT_PATH+"fba/allocation/printAsnLanShou?id=" + id;
        $.get(url, function(data){
            if (data.status == 200) {
                document.getElementById('lanshouFrame').src = data.message;
                printCopies(data.location, null, jitPrinter, 1, null);
            } else {
                customizeLayer(data.message);
            }
        });
    }

    // 计数
    function calsfPiece(quantity){

        if(!quantity) {
            quantity = 1;
        }

        var storage = new WebStorageCache();

        var lastSuc = 0;

        if (storage.get(pieceCacheKey)) {
            lastSuc = storage.get(pieceCacheKey);
            lastSuc = parseInt(lastSuc) + parseInt(quantity);
        } else {
            lastSuc = quantity;
        }

        storage.set(pieceCacheKey, lastSuc , {exp : 5 * 60 * 60});

        $('#panel-title-piece').html('<h1  style="color:red;font-size:48px;">计数:<b>'+lastSuc+'</b></h1>');
    }


    function printApvNo(apvNo,apvId){
        debugger;
        if (apvNo){
            bindingApvAndUuIdlist(apvNo, uuIdCacheKey);
            initSkuUuIdStorageCache(uuIdCacheKey);
        }
        if (asnFirst && boxMarkUrl) {
            document.getElementById('shippingOrderFrame').src = boxMarkUrl;
            printPdfCopies(boxMarkUrl, jitPrinter, "100mm", "100mm", 1);
            return;
        }
        var url = CONTEXT_PATH + "fba/packs/printXiangmai?id="+apvId;
        if (jitAsn) {
            url = CONTEXT_PATH + 'fba/allocation/localPrintXiangmai?id=' + apvId;
        }
        var r= $.ajax({
            url : url,
            timeout : 100000,
            async: false,
            success : function(response){
                if(response.status == '200'){
                    var jitPdfUrl = response.body.jitPdfUrl;
                    if (jitAsn) {
                        jitPdfUrl = response.body.pdfUrl;
                    }
                    if (!jitPdfUrl) {
                        jitPdfUrl = window.location.origin + CONTEXT_PATH + response.message;
                        document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + response.message;
                    } else {
                        document.getElementById('shippingOrderFrame').src = response.message;
                    }
                    var jitBoxNumber = response.location;
                    printCopies(jitPdfUrl,null,jitPrinter,1,jitBoxNumber);
                }else{
                    layer.alert(response.message, {closeBtn: 0}, function (index) {
                        layer.close(index);
                    });
                }
            },
            error:function(){
                layer.alert('扫描失败，请重新扫描');
            }
        });

    }

    var isFocus = false;
    $("body").click(function() {isFocus = false;});
    function focusSku(){
        if (isFocus && !$('#apvid').val()) {
            $('#apvid').focus();
            setTimeout(focusSku, 1000);
        }
    }


    //打印失败面单
    function failPrint(){
        var apvNo = $("#apv-no-now").val();
        if(apvNo){
            document.getElementById('shippingOrderFrame').src = CONTEXT_PATH + "apvs/failPrint?apvNo=" + apvNo;
        }
        $('#apvid').val('');
        $('#apvid').focus();
    }

    function bindBox() {
        $("#bindBoxModal").modal('show');
    }

    function checkWaitSplit(apvId){
        if (!apvId){
            layer.alert("apvId为空",'error');
            return;
        }

        layer.open({
            title:'确认标记拆包',
            content: '确认拆包后请将货物放回周转筐中，在其他单据包装完成后交给对应同事处理'
            ,btn: ['确定拆包', '取消操作']
            ,yes: function(index){
                $.ajax({
                    url: CONTEXT_PATH+"fba/packs/checkWaitSplit",
                    type: "POST",
                    data: {apvId: apvId},
                    success: function(response) {
                        if(response.status == "200"){
                            layer.alert("标记成功", {closeBtn: 0}, function (index) {
                                pageInit();
                                $("#apv-no-now").val('');
                                layer.close(index);
                            });
                        }else {
                            layer.alert("标记失败：" + response.message);
                        }
                    },
                    error:function () {
                        layer.alert("系统异常，标记失败!");
                    }
                });
            }
            ,btn2: function(index){
                layer.close(index);
            }
            ,cancel: function(index){
                layer.close(index);
            }
        });
    }

    function bindStockOut(box, orderNo, sku,packQty) {
        var diglog = dialog({
            title: '绑定中转仓播种异常周转筐',
            width: 350,
            height: 100,
            url: CONTEXT_PATH + "single/batch/scans/binding/stockout",
            okValue: '确定',
            ok: function () {
                var exportWindow = $(this.iframeNode.contentWindow.document.body);

                var submitForm = exportWindow.find("#submit-form");

                var stockoutBox = $(submitForm).find("#stockout-box").val();

                if (!stockoutBox || stockoutBox == '' || stockoutBox.indexOf('BZYC') == -1) {
                    layer.alert("请绑定中转仓播种异常周转筐!");
                    $(submitForm).find("#stockout-box").val('');
                    return false;
                }
                bindingAndFinish(null, stockoutBox, box, orderNo, sku, diglog, packQty);
            },
        });
        diglog.show();
    }

    function bindingAndFinish(boxCayi, stockoutBox, box, orderNo, sku, diglog, packQty) {
        var result;
        $.ajax({
            url : CONTEXT_PATH + "temu/pack/binding/stockout",
            type : 'post',
            async: false,//使用同步的方式,true为异步方式
            data: {
                boxCayi: $.trim(boxCayi),
                stockoutBox: $.trim(stockoutBox),
                box: box,
                orderNo: orderNo,
                sku: sku,
                packQty: packQty
            },//这里使用json对象
            success : function(data){
                if(data.status == 200){
                    layer.alert("绑定成功！");
                    $('#check_scan_datas').html('');
                    $('#sku').val("");
                    $('#sku').select().focus();
                    if (diglog != null){
                        setTimeout(function () {
                            diglog.close().remove();
                        }, 100);
                    }
                } else {
                    layer.alert("绑定失败:"+data.message,function (index){
                        layer.close(index);
                        if (boxCayi){
                            unPackBind();
                        } else {
                            bindStockOut(box, orderNo, sku, packQty);
                        }
                    });
                }
            }
        });
    }

    //播种差异
    function unPackBind() {

        var packageSn = $("#apv-no-now").val();
        var sku = $("#sku").val();
        var box = $('#orderid').val();
        var boxCayi = '';

        var checkQty = parseInt($('#check_quantity').text());
        var pickQty = parseInt($('#need-qty').text());
        var packQty = pickQty - checkQty;
        if (packQty == 0) {
            layer.alert("拣货数量和核对数量相等，不需要绑定播种差异周转筐");
            return;
        }

        var diglog = dialog({
            title: '绑定中转仓播种差异周转筐',
            width: 350,
            height: 100,
            url: CONTEXT_PATH + "single/batch/scans/binding/gridDiff",
            okValue: '确定',
            ok: function () {
                var exportWindow = $(this.iframeNode.contentWindow.document.body);
                var submitForm = exportWindow.find("#submit-form");
                boxCayi = $(submitForm).find("#grid-diff-box").val();

                if (!boxCayi || boxCayi == '' || boxCayi.indexOf('BZCY') == -1) {
                    layer.alert("请绑定中转仓播种差异周转筐!");
                    $(submitForm).find("#grid-diff-box").val('');
                    return false;
                }
                bindingAndFinish(boxCayi, null, box, packageSn, sku, diglog, packQty);
            },
        });
        diglog.show();
    }

    function printXiangMai(packageSn, sku){
        if (!packageSn || !sku) {
            layer.alert("包裹号-SKU为空!");
            return;
        }
        $.ajax({
            url : CONTEXT_PATH + "separateBox/search",
            type:'POST',
            data : {taskNoAndSku : packageSn+"~"+sku},
            success : function(response) {
                var responseHtml = $(response).find("#show_contents").html();
                var errorMsg = $(response).find("#show_contents").find("#scan-error").text();
                if(errorMsg){
                    layer.alert(errorMsg);
                    return;
                }
                $("#scan_datas").html(responseHtml);
                var printHtml = $(response).find("#show_contents").find("#print_content").html();
                if (printHtml) {
                    printHtmCopies(printHtml,jitPrinter,1);
                } else {
                    var boxNumber = $(response).find("#show_contents").find("#box-number").html();
                    if (boxNumber) {
                        var asnPrintUrl = document.querySelector("#printTemuTagUrlFrame").src;
                        doAsnPrint(asnPrintUrl, boxNumber);
                    }
                }
            },
            error : function() {
                layer.alert('打印面单失败!');
            }
        });
    }

    function printFbaNoAndFnSku(orderNo, sku,quantity) {
        if (orderNo && sku) {
            var printPageUrl = CONTEXT_PATH + "skuLabel/printSkuLabel?orderNo=" + orderNo + "&sku=" + sku;
            $.get(printPageUrl, function (data) {
                $("#printHtml").html(data);
                // var printOrderSkuHtml = $("#printHtml").find("#print-item-1").html();
                // if (printOrderSkuHtml){
                // 	printHtmlCopies(printOrderSkuHtml, 1);
                // }
                var printEtHtml = $("#printHtml").find("#dz-print").html();
                if (printEtHtml){
                    setTimeout(function () {
                        etPrint(printEtHtml, quantity);
                    }, 500);
                }
                var skuTag = $("#printHtml").find("#skuTag").val();
                if (skuTag && skuTag.indexOf("宠物") != -1) {
                    setTimeout(function () {
                        printPetTag(null, quantity);
                    }, 500);
                }
                var errorMsg = $("#printHtml").find("#error-msg").html();
                if (errorMsg) {
                    layer.alert(errorMsg, {closeBtn: 0}, function (index) {
                        layer.close(index);
                        $('#sku').select().focus();
                    });
                    return;
                }
                var printHtml = $("#printHtml").find("#print-item-0").html();
                setTimeout(function () {
                    // 从AJAX响应中提取样式
                    var responseStyles = extractStylesFromResponse(data);
                    if (printHtml) {
                        printMergeGpsrLable(null,printHtml, quantity, responseStyles);
                    } else {
                        // 获取#print-item-asn-tag内容，但排除.label-sku部分
                        var $clonedAsn = $("#printHtml").find("#print-item-asn-tag").clone();
                        $clonedAsn.find(".label-sku").remove();
                        printHtml = $clonedAsn.html();
                        var asnPrintUrl = document.querySelector("#printUrlFrame").src;
                        printMergeGpsrLable(asnPrintUrl,printHtml, quantity, responseStyles);
                    }
                }, 1000);
            });
        }
    }
</script>
</body>
</html>