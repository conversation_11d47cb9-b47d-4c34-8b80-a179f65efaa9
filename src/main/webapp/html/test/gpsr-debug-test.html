<!DOCTYPE html>
<html>
<head>
    <title>GPSR缓存调试工具</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { border: 1px solid #ccc; padding: 15px; margin: 10px 0; }
        .debug-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .debug-result { background: #f5f5f5; padding: 10px; margin: 5px 0; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        button { padding: 8px 15px; margin: 5px; cursor: pointer; }
        input[type="text"] { padding: 5px; margin: 5px; width: 200px; }
        pre { background: #f0f0f0; padding: 10px; overflow: auto; }
    </style>
</head>
<body>
    <h1>🔍 GPSR缓存调试工具</h1>
    
    <div class="debug-section">
        <div class="debug-title">📋 基础信息检查</div>
        <button onclick="checkDependencies()">检查依赖</button>
        <button onclick="checkGpsrManager()">检查GPSR管理器</button>
        <div id="basic-info" class="debug-result"></div>
    </div>

    <div class="debug-section">
        <div class="debug-title">🚀 缓存操作测试</div>
        <input type="text" id="test-apv" placeholder="输入发货单号" value="">
        <button onclick="testPreload()">测试预加载</button>
        <button onclick="testPrint()">测试打印</button>
        <button onclick="viewCache()">查看缓存</button>
        <button onclick="clearCache()">清理缓存</button>
        <div id="cache-info" class="debug-result"></div>
    </div>

    <div class="debug-section">
        <div class="debug-title">📊 缓存状态监控</div>
        <button onclick="getCacheStats()">获取缓存统计</button>
        <button onclick="getPerformanceStats()">获取性能统计</button>
        <div id="stats-info" class="debug-result"></div>
    </div>

    <div class="debug-section">
        <div class="debug-title">🩺 健康检查</div>
        <button onclick="healthCheck()">执行健康检查</button>
        <div id="health-info" class="debug-result"></div>
    </div>

    <script src="../js/jquery-1.11.3.min.js"></script>
    <script src="../js/packing.js"></script>
    <script src="../js/gpsr-cache-manager.js"></script>
    
    <script>
        // 全局变量
        var CONTEXT_PATH = '/';
        
        function log(message, type) {
            var className = type || 'info';
            var timestamp = new Date().toLocaleTimeString();
            console.log('[' + timestamp + '] ' + message);
            return '<div class="' + className + '">[' + timestamp + '] ' + message + '</div>';
        }

        function checkDependencies() {
            var result = '';
            
            // 检查jQuery
            if (typeof $ !== 'undefined') {
                result += log('✅ jQuery已加载 (版本: ' + $.fn.jquery + ')', 'success');
            } else {
                result += log('❌ jQuery未加载', 'error');
            }
            
            // 检查packing.js
            if (typeof printGpsrTag === 'function') {
                result += log('✅ printGpsrTag函数存在', 'success');
            } else {
                result += log('❌ printGpsrTag函数不存在', 'error');
            }
            
            // 检查原始函数备份
            if (typeof window._originalPrintGpsrTag === 'function') {
                result += log('✅ 原始printGpsrTag函数已备份', 'success');
            } else {
                result += log('⚠️ 原始printGpsrTag函数未备份', 'warning');
            }
            
            document.getElementById('basic-info').innerHTML = result;
        }

        function checkGpsrManager() {
            var result = '';
            
            if (typeof GpsrCacheManager !== 'undefined') {
                result += log('✅ GpsrCacheManager已加载', 'success');
                
                if (typeof GpsrCacheManager.preloadGpsrTag === 'function') {
                    result += log('✅ preloadGpsrTag方法存在', 'success');
                } else {
                    result += log('❌ preloadGpsrTag方法不存在', 'error');
                }
                
                if (typeof GpsrCacheManager.printGpsrTag === 'function') {
                    result += log('✅ printGpsrTag方法存在', 'success');
                } else {
                    result += log('❌ printGpsrTag方法不存在', 'error');
                }
                
            } else {
                result += log('❌ GpsrCacheManager未加载', 'error');
            }
            
            document.getElementById('basic-info').innerHTML += result;
        }

        function testPreload() {
            var apvNo = document.getElementById('test-apv').value;
            if (!apvNo) {
                document.getElementById('cache-info').innerHTML = log('请输入发货单号', 'error');
                return;
            }
            
            var result = log('开始预加载测试：' + apvNo);
            document.getElementById('cache-info').innerHTML = result;
            
            if (typeof GpsrCacheManager !== 'undefined') {
                GpsrCacheManager.preloadGpsrTag(apvNo)
                    .then(function(response) {
                        var info = '✅ 预加载成功\n';
                        info += '平台：' + (response.platform || '未知') + '\n';
                        info += 'PDF URLs数量：' + (response.pdfUrls ? response.pdfUrls.length : 0) + '\n';
                        info += '内容长度：' + (response.content ? response.content.length : 0) + ' 字符';
                        
                        document.getElementById('cache-info').innerHTML += log(info, 'success');
                    })
                    .catch(function(error) {
                        document.getElementById('cache-info').innerHTML += log('❌ 预加载失败：' + error.message, 'error');
                    });
            } else {
                document.getElementById('cache-info').innerHTML += log('❌ GpsrCacheManager不可用', 'error');
            }
        }

        function testPrint() {
            var apvNo = document.getElementById('test-apv').value;
            if (!apvNo) {
                document.getElementById('cache-info').innerHTML = log('请输入发货单号', 'error');
                return;
            }
            
            var result = log('开始打印测试：' + apvNo);
            document.getElementById('cache-info').innerHTML += result;
            
            if (typeof printGpsrTag === 'function') {
                try {
                    printGpsrTag(apvNo, null);
                    document.getElementById('cache-info').innerHTML += log('✅ 打印函数调用成功', 'success');
                } catch (error) {
                    document.getElementById('cache-info').innerHTML += log('❌ 打印函数调用失败：' + error.message, 'error');
                }
            } else {
                document.getElementById('cache-info').innerHTML += log('❌ printGpsrTag函数不可用', 'error');
            }
        }

        function viewCache() {
            var apvNo = document.getElementById('test-apv').value;
            if (!apvNo) {
                document.getElementById('cache-info').innerHTML = log('请输入发货单号', 'error');
                return;
            }
            
            if (typeof GpsrCacheManager !== 'undefined' && GpsrCacheManager.cache) {
                var cached = GpsrCacheManager.cache.get(apvNo);
                if (cached) {
                    var info = '📦 缓存数据：\n';
                    info += '时间戳：' + new Date(cached.timestamp).toLocaleString() + '\n';
                    info += '平台：' + (cached.platform || '未知') + '\n';
                    info += 'PDF URLs：' + (cached.pdfUrls ? cached.pdfUrls.length : 0) + ' 个\n';
                    info += '内容长度：' + (cached.content ? cached.content.length : 0) + ' 字符\n';
                    
                    if (cached.content) {
                        var $content = $(cached.content);
                        var gpsrElements = $content.find('.gpsr-tag-print').length;
                        info += 'GPSR标签元素：' + gpsrElements + ' 个';
                    }
                    
                    document.getElementById('cache-info').innerHTML = log(info, 'success');
                } else {
                    document.getElementById('cache-info').innerHTML = log('未找到缓存数据：' + apvNo, 'warning');
                }
            } else {
                document.getElementById('cache-info').innerHTML = log('缓存管理器不可用', 'error');
            }
        }

        function clearCache() {
            if (typeof GpsrCacheManager !== 'undefined') {
                GpsrCacheManager.clearGpsrCache();
                document.getElementById('cache-info').innerHTML = log('✅ 缓存已清理', 'success');
            } else {
                document.getElementById('cache-info').innerHTML = log('❌ 缓存管理器不可用', 'error');
            }
        }

        function getCacheStats() {
            if (typeof GpsrCacheManager !== 'undefined') {
                var stats = GpsrCacheManager.getCacheStats();
                var info = '📊 缓存统计：\n';
                info += '总数量：' + stats.totalCount + '\n';
                info += '最大容量：' + stats.maxSize + '\n';
                info += '过期时间：' + (stats.expireTime / 1000 / 60) + ' 分钟\n';
                info += '缓存键：[' + stats.cacheKeys.join(', ') + ']';
                
                document.getElementById('stats-info').innerHTML = log(info, 'success');
            } else {
                document.getElementById('stats-info').innerHTML = log('❌ 缓存管理器不可用', 'error');
            }
        }

        function getPerformanceStats() {
            if (typeof GpsrCacheManager !== 'undefined' && GpsrCacheManager.performance) {
                var metrics = GpsrCacheManager.performance.getStats();
                var info = '⚡ 性能统计：\n';
                
                for (var key in metrics) {
                    var metric = metrics[key];
                    if (metric.duration !== null) {
                        info += key + '：' + metric.duration.toFixed(2) + 'ms\n';
                    }
                }
                
                if (Object.keys(metrics).length === 0) {
                    info += '暂无性能数据';
                }
                
                document.getElementById('stats-info').innerHTML = log(info, 'success');
            } else {
                document.getElementById('stats-info').innerHTML = log('❌ 性能监控不可用', 'error');
            }
        }

        function healthCheck() {
            if (typeof GpsrCacheManager !== 'undefined') {
                var health = GpsrCacheManager.healthCheck();
                var info = '🩺 健康检查结果：\n';
                info += '状态：' + health.status + '\n';
                info += '时间：' + health.timestamp + '\n';
                info += '缓存使用率：' + health.cache.usage + '\n';
                
                info += '依赖检查：\n';
                info += '  jQuery：' + (health.dependencies.jquery ? '✅' : '❌') + '\n';
                info += '  打印控件：' + (health.dependencies.lodop ? '✅' : '❌') + '\n';
                info += '  打印函数：' + (health.dependencies.printFunction ? '✅' : '❌') + '\n';
                
                if (health.issues && health.issues.length > 0) {
                    info += '问题：\n';
                    health.issues.forEach(function(issue) {
                        info += '  ⚠️ ' + issue + '\n';
                    });
                }
                
                var type = health.status === 'healthy' ? 'success' : 'warning';
                document.getElementById('health-info').innerHTML = log(info, type);
            } else {
                document.getElementById('health-info').innerHTML = log('❌ 健康检查不可用', 'error');
            }
        }

        // 页面加载完成后自动检查
        $(document).ready(function() {
            checkDependencies();
            checkGpsrManager();
        });
    </script>
</body>
</html> 