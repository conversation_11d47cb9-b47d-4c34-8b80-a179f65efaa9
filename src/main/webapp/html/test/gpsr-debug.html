<!DOCTYPE html>
<html>
<head>
    <title>GPSR缓存调试工具</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { border: 1px solid #ccc; padding: 15px; margin: 10px 0; }
        .debug-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .debug-result { background: #f5f5f5; padding: 10px; margin: 5px 0; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        button { padding: 8px 15px; margin: 5px; cursor: pointer; }
        input[type="text"] { padding: 5px; margin: 5px; width: 200px; }
        pre { background: #f0f0f0; padding: 10px; overflow: auto; }
    </style>
</head>
<body>
    <h1>🔍 GPSR缓存调试工具</h1>
    
    <div class="debug-section">
        <div class="debug-title">📋 基础信息检查</div>
        <button onclick="checkDependencies()">检查依赖</button>
        <button onclick="checkGpsrManager()">检查GPSR管理器</button>
        <div id="basic-info" class="debug-result"></div>
    </div>

    <div class="debug-section">
        <div class="debug-title">🌐 网络和路径检查</div>
        <button onclick="checkContextPath()">检查CONTEXT_PATH</button>
        <button onclick="testNetworkConnection()">测试网络连接</button>
        <button onclick="manualUrlTest()">手动测试URL</button>
        <div id="network-info" class="debug-result"></div>
    </div>

    <div class="debug-section">
        <div class="debug-title">🚀 缓存操作测试</div>
        <input type="text" id="test-apv" placeholder="输入发货单号" value="">
        <button onclick="testPreload()">测试预加载</button>
        <button onclick="testPrint()">测试打印</button>
        <button onclick="viewCache()">查看缓存</button>
        <div id="cache-info" class="debug-result"></div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="http://*************:8181/wms/js/jquery-1.11.3.min.js"></script>
    <script src="http://*************:8181/wms/js/packing.js"></script>
    <script src="http://*************:8181/wms/js/gpsr-cache-manager.js"></script>
    
    <script>
        var CONTEXT_PATH = '/wms/';  // 根据您的环境调整
        
        function log(message, type) {
            var className = type || 'info';
            var timestamp = new Date().toLocaleTimeString();
            return '<div class="' + className + '">[' + timestamp + '] ' + message + '</div>';
        }

        function checkDependencies() {
            var result = '';
            
            if (typeof $ !== 'undefined') {
                result += log('✅ jQuery已加载', 'success');
            } else {
                result += log('❌ jQuery未加载', 'error');
            }
            
            if (typeof printGpsrTag === 'function') {
                result += log('✅ printGpsrTag函数存在', 'success');
            } else {
                result += log('❌ printGpsrTag函数不存在', 'error');
            }
            
            if (typeof window._originalPrintGpsrTag === 'function') {
                result += log('✅ 原始printGpsrTag函数已备份', 'success');
            } else {
                result += log('⚠️ 原始printGpsrTag函数未备份', 'warning');
            }
            
            document.getElementById('basic-info').innerHTML = result;
        }

        function checkGpsrManager() {
            var result = '';
            
            if (typeof GpsrCacheManager !== 'undefined') {
                result += log('✅ GpsrCacheManager已加载', 'success');
            } else {
                result += log('❌ GpsrCacheManager未加载', 'error');
            }
            
            document.getElementById('basic-info').innerHTML += result;
        }

        function checkContextPath() {
            var result = '';
            result += log('当前CONTEXT_PATH: ' + CONTEXT_PATH, 'info');
            result += log('window.CONTEXT_PATH: ' + (window.CONTEXT_PATH || '未定义'), 'info');
            result += log('当前域名: ' + window.location.origin, 'info');
            result += log('当前路径: ' + window.location.pathname, 'info');
            
            document.getElementById('network-info').innerHTML = result;
        }

        function testNetworkConnection() {
            var result = log('测试网络连接...', 'info');
            document.getElementById('network-info').innerHTML = result;
            
            // 测试基础连接
            $.ajax({
                url: window.location.origin + CONTEXT_PATH + 'apv/packs/printGpsrTag',
                type: 'HEAD',
                timeout: 5000,
                success: function() {
                    document.getElementById('network-info').innerHTML += log('✅ 网络连接正常', 'success');
                },
                error: function(xhr, status, error) {
                    var errorMsg = '❌ 网络连接失败: ' + status + ' - ' + error;
                    if (xhr.status) {
                        errorMsg += ' (HTTP ' + xhr.status + ')';
                    }
                    document.getElementById('network-info').innerHTML += log(errorMsg, 'error');
                }
            });
        }

        function manualUrlTest() {
            var apvNo = document.getElementById('test-apv').value || 'TEST123';
            var url1 = window.location.origin + CONTEXT_PATH + 'apv/packs/printGpsrTag?apvNo=' + apvNo;
            var url2 = window.location.origin + CONTEXT_PATH + 'fba/packs/printGpsrTag?apvNo=' + apvNo;
            
            var result = '';
            result += log('测试URL 1: ' + url1, 'info');
            result += log('测试URL 2: ' + url2, 'info');
            
            document.getElementById('network-info').innerHTML = result;
            
            // 测试第一个URL
            $.ajax({
                url: url1,
                type: 'GET',
                timeout: 10000,
                success: function(response) {
                    var info = '✅ URL1 请求成功，响应长度: ' + response.length;
                    if (response.indexOf('gpsr-tag-print') > -1) {
                        info += '，包含GPSR元素';
                    } else {
                        info += '，不包含GPSR元素';
                    }
                    document.getElementById('network-info').innerHTML += log(info, 'success');
                },
                error: function(xhr, status, error) {
                    var errorMsg = '❌ URL1 请求失败: ' + status + ' - ' + error;
                    if (xhr.status) {
                        errorMsg += ' (HTTP ' + xhr.status + ')';
                    }
                    if (xhr.responseText) {
                        errorMsg += '，响应: ' + xhr.responseText.substring(0, 100);
                    }
                    document.getElementById('network-info').innerHTML += log(errorMsg, 'error');
                }
            });
        }

        function testPreload() {
            var apvNo = document.getElementById('test-apv').value;
            if (!apvNo) {
                document.getElementById('cache-info').innerHTML = log('请输入发货单号', 'error');
                return;
            }
            
            var result = log('开始预加载测试：' + apvNo, 'info');
            result += log('使用CONTEXT_PATH: ' + CONTEXT_PATH, 'info');
            document.getElementById('cache-info').innerHTML = result;
            
            // 设置window.CONTEXT_PATH以确保一致性
            window.CONTEXT_PATH = CONTEXT_PATH;
            
            GpsrCacheManager.preloadGpsrTag(apvNo)
                .then(function(response) {
                    var info = '✅ 预加载成功';
                    if (response.platform) {
                        info += '，平台: ' + response.platform;
                    }
                    if (response.content) {
                        info += '，内容长度: ' + response.content.length;
                    }
                    document.getElementById('cache-info').innerHTML += log(info, 'success');
                })
                .catch(function(error) {
                    var errorMsg = '❌ 预加载失败: ' + error.message;
                    if (error.details) {
                        errorMsg += '\n详细信息: ' + JSON.stringify(error.details, null, 2);
                    }
                    document.getElementById('cache-info').innerHTML += log(errorMsg, 'error');
                });
        }

        function testPrint() {
            var apvNo = document.getElementById('test-apv').value;
            if (!apvNo) {
                document.getElementById('cache-info').innerHTML = log('请输入发货单号', 'error');
                return;
            }
            
            document.getElementById('cache-info').innerHTML += log('开始打印测试：' + apvNo);
            printGpsrTag(apvNo, null);
        }

        function viewCache() {
            var apvNo = document.getElementById('test-apv').value;
            if (!apvNo) {
                document.getElementById('cache-info').innerHTML = log('请输入发货单号', 'error');
                return;
            }
            
            var cached = GpsrCacheManager.cache.get(apvNo);
            if (cached) {
                var info = '📦 缓存存在，内容长度：' + cached.content.length;
                document.getElementById('cache-info').innerHTML = log(info, 'success');
            } else {
                document.getElementById('cache-info').innerHTML = log('未找到缓存：' + apvNo, 'warning');
            }
        }

        $(document).ready(function() {
            checkDependencies();
            checkGpsrManager();
        });
    </script>
</body>
</html> 