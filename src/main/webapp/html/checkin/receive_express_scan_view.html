<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html lang="en" class="no-js">
<head>
<title>易世通达仓库管理系统</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<link href="${CONTEXT_PATH}css/deliver.css" rel="stylesheet">
	<style type="text/css">
.modal-body input {
	width: 240px;
}
.top_bar{
    position:fixed;top:0px;
}
#task-list td {
	vertical-align:middle;
}
.input-medium {
	display: inline-block;
	width: 190px !important;
	float: right;
}
.my-input-label {
	line-height: 30px;
	height: 30px;
	margin-bottom: 20px;
	text-align: right
}
#input_boxNo{
	margin-bottom: 35px !important;
}

#input_boxNo_log{
	margin-bottom: 35px !important;
}
.flags-name{
	color: red;
}
.my-input-label > label {
	margin-right: 5px;
}

#box-receive-item {
	padding: 50px 20px;
	float: right;
	display: none;
}
#box-receive-item .border-gray {
	overflow-y: scroll;
	height: 600px !important;
	overflow-x: hidden;
}

#box-receive-item i{
	margin-left: 10%;
	cursor: pointer;
	font-size: 18px;
}


	</style>
</head>
<!-- BEGIN BODY -->
<body class="page-header-fixed page-full-width">
	<@header method="header" active="10010000"><#include "/ftl/header.ftl"></@header>

	<div id="page" style="background-color: rgb(231, 237, 248)">
		<div class="row">
			<div class="col-md-12" style="padding: 0">
				<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
					<li><a href="#">入库管理</a></li>
					<li class="active">收货扫描</li>
				</ul>
			</div>
		</div>
		<!-- 内容 -->
		<div class="container-fluid" style="background-color: white;border: none">
			<div class="row" style="height: 100%">
				<div class="col-md-2">
					<div class="col-md-12" style="border-right: 1px solid #eee;padding-top: 30px;padding-bottom: 30px;height: 100%">
                        <input type="hidden" id="warehouseId" name="warehouseId" value="${domain.warehouseId}"/>
                        <h3 style="margin-left: 30px;margin-bottom: 30px">扫描单号(快递收货)</h3>
                        <div class="my-input-label">
                            <label>快递公司</label>
							<input style="text-align: left;" class="form-control input-medium" type="text" name="shipping_method" id="shipping_method">
                        </div>
                        <div id="input_boxNo" class="my-input-label">
                            <label>周转框</label>
                            <input type="text" class="form-control input-medium" name="boxNo" id="boxNo" value="" onkeypress="if(event.keyCode==13) { inputBoxNo(this,'receive'); return false;}">
                            <span style="color: red;font-size: 20px;" id="span-boxNo"></span>
                        </div>
						<div class="my-input-label">
                            <label>重量</label>
                            <input type="text" class="form-control input-medium" name="weight" id="weight" value="" onkeypress="if(event.keyCode==13) { inputWeight(this); return false;}">
                            <span style="color: red;font-size: 12px;">≤100KG</span>
                        </div>
                        <div class="my-input-label">
                            <label>件数</label>
                            <input type="text" class="form-control input-medium" name="quantity" id="quantity" value="">
                        </div>
                        <div class="my-input-label">
                            <label>快递单号</label>
                            <input type="text" class="form-control input-medium" name="orderid" id="expressId" value="" onkeypress="if(event.keyCode==13) { inputnext(this); return false;}"tabindex="4">
                        </div>
					</div>
                    <div class="col-md-12" style="border-right: 1px solid #eee;padding-top: 50px;padding-bottom: 30px;height: 100%">
                        <h3 style="margin-left: 30px;margin-bottom: 30px">扫描单号(物流收货)</h3>
						<div class="my-input-label">
							<label>快递公司</label>
							<input style="text-align: left;" class="form-control input-medium" type="text" name="shipping_method" id="shipping-method-log">
						</div>
						<div id="input_boxNo_log" class="my-input-label">
							<label>周转筐号</label>
							<input type="text" class="form-control input-medium" id="boxNo-log" value="" onkeypress="if(event.keyCode==13) { validateBoxNo_log(this); return false;}">
							<span style="color: red;font-size: 20px;" id="span-boxNo-log"></span>
						</div>
                        <div class="my-input-label">
                            <label>物流单号</label>
                            <input type="text" class="form-control input-medium" id="expressId-log" value="" onkeypress="if(event.keyCode==13) { inputnext_log(this); return false;}"tabindex="4">
                        </div>
                        <div class="my-input-label">
                            <label>件数</label>
                            <input type="text" class="form-control input-medium" id="quantity-log" value="" onkeypress="if(event.keyCode==13) { inputQuantity_log(this); return false;}">
                        </div>
                        <div class="my-input-label">
                            <label>重量</label>
                            <input type="text" class="form-control input-medium" id="weight-log" value="" onkeypress="if(event.keyCode==13) { inputWeight_log(this); return false;}">
                            <span style="color: red;font-size: 12px;">≤999KG</span>
                        </div>
                    </div>
				</div>

				<div class="col-md-4" style="padding: 50px 20px">
					<div class="panel-header" style="overflow: hidden;">
						<div class="fl panel-title2" id="panel-title"><h1  style="color:#090;font-size:48px;">成功:<b>0</b></h1></div>
					</div>
					<div>
						<div id="check_scan_datas" class="border-gray p5">

						</div>
					</div>
				</div>
				
				<!-- class="hide" -->
				<iframe  style="width:25%;height:400px;border: 0 none;margin-top: 2px;" name="printHtml" id="printHtml"></iframe>

				<div id="box-receive-item" class="col-md-3">
					<h4>
						<strong>收货周转筐绑定明细
							<span id="refresh-receive-item">
								<i class="icon-refresh"></i>
							</span>
						</strong>
					</h4>
					<div class="border-gray p5">
						<table class="table table-striped table-bordered table-hover table-condensed">
							<thead>
							<tr>
								<th>快递公司</th>
								<th>快递单号</th>
								<th>件数</th>
							</tr>
							</thead>
							<tbody>

							</tbody>
						</table>
					</div>
				</div>
				
			</div><!-- end row -->

		</div>
		<div style="display:none;" id="shippingMethodList">
			<#if domain.shippingMethodList?? >
				[{"id":"", "text":""}<#list domain.shippingMethodList as item>,{"id":"${item}", "text":"${item}"}</#list>]
			</#if>
		</div>
		<#include "/common/footer.html">
	</div>

	<object width="0" height="0" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" id="LODOP_OB">
	  <embed width="0" height="0" type="application/x-print-lodop" id="LODOP_EM">
   	</object>

	<!-- 	打印插件 -->
   	<script type="text/javascript" src="${CONTEXT_PATH}js/LodopFuncs.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/web-storage-cache.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.sound.js?v=${.now?datetime}"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
	<script type="text/javascript" src="${CONTEXT_PATH}js/prompt-message.js"></script>
	<script type="text/javascript">
		var cacheKey = "receive_express_success";
		var pieceCacheKey = "receive_express_piece_success";
		var boxNoCacheKey = "receive_express_scan_currentBoxNo";
		
		jQuery(document).ready(function() {
			
			pageInit();
			
			$("#quantity").blur(function(){
	  			var $this = $(this);
	  			if($this.val() == ''){
	  				$this.val(1);
	  				return;
	  			}
	  			
	  			var reg = /^\+?[1-9][0-9]*$/;
	  			if (!reg.test($this.val())) {
					getErrorInfoAlert("请输入正确的正整数");
	  				$this.val(1);
	  				return;
	  			}
	  		});
			
			
      		var storage = new WebStorageCache();
      		
      		if (storage.get(cacheKey)) {
      			lastSuc = storage.get(cacheKey);
      			$('#panel-title').html('<h1  style="color:blue;font-size:48px;">成功：<b>'+lastSuc+'</b></h1>');
      		}

			//定时监测周转框
			setInterval(function(){
				isMultiBoxNo();
			},3000);
			//onload删除当前页面缓存boxno
			sessionStorage.removeItem(boxNoCacheKey);
	    });
		
		// 初始化
		function pageInit() {
			$('#expressId').val('');
			$('#weight').val('');
			$('#quantity').val(1);
			$('#boxNo').focus();
			$('#check_scan_datas').html('');
			$('#span-boxNo').text('');

            $('#expressId-log').val('');
            $('#weight-log').val('');
            $('#quantity-log').val(1);
            isPrint = false;
		}

		var shippingMethodArr = jQuery.parseJSON($("#shippingMethodList").text());
		$("input[name='shipping_method']").select2({
			data : shippingMethodArr,
			placeholder : "快递公司",
			allowClear : true
		});

        //绑定周转框
        function inputBoxNo(obj,type){
            // 上次扫描的周转框
            var oldBoxNo = $('#span-boxNo').text();

            var warehouseId = $('#warehouseId').val();

            if(warehouseId == null || warehouseId == ''){
				getErrorInfoAlert("获取当前仓库信息失败!");
                $('#boxNo').val('');
                return ;
			}
            var boxNo = obj.value.replace(/\s/g,'');

            if(!obj.value || boxNo == ''){
				getErrorInfoAlert("请输入周转码!");
                $('#boxNo').val('');
                $('#boxNo').focus();
                return ;
            }
            if(boxNo.length < 4){
				getErrorInfoAlert("请输入正确的周转码!");
                return ;
            }

            var boxNo = $.trim(obj.value);

            var start2SH = boxNo.indexOf("2SH");
			var startSH = boxNo.indexOf('SH');
            if(start2SH == -1 && startSH == -1){
				getErrorInfoAlert("此周转筐不是收货周转框!");
                $('#boxNo').val('');
                $('#boxNo').focus();
                return ;
            }
            if((start2SH == 0 && warehouseId != 2) || (startSH == 0 && warehouseId != 3 && warehouseId != 1)){
				getErrorInfoAlert("此周转筐与本仓不匹配!");
                $('#boxNo').val('');
                $('#boxNo').focus();
                return ;
            }

            if(oldBoxNo != '' && boxNo != '' && boxNo != oldBoxNo){
                if(confirm('确认更换周转框吗？更换后扫描的快递单将绑定到新的周转框！')){
                    var params = {
                        boxNo:boxNo,
                        oldBoxNo:oldBoxNo
                    };
                    $.post(CONTEXT_PATH + "expressRecords/trackingNoBindBoxNoAndChangeStatus",params,function (data) {
						if(data.status == '200'){
                            checkBox(boxNo,type,warehouseId);
						}else {
							getErrorInfoAlert(data.message);
                            $('#boxNo').val('');
                            $('#boxNo').focus();
						}
                    });
                }
			}else {
            	checkBox(boxNo,type,warehouseId);
			}


        }

        function checkBox(boxNo,type,warehouseId) {
            $.ajax({
                url : CONTEXT_PATH + "expressRecords/checkBox",
                data : {boxNo : boxNo, type:type, warehouseId:warehouseId},
                success : function(response){
                    if (response.status == '500') {
						getErrorInfoAlert(response.message);
                        $('#boxNo').val("");
                        $('#boxNo').focus();
                        return;
                    } else if (response.status == '200') {
                    	localStorage.setItem(boxNoCacheKey,boxNo);
                    	sessionStorage.setItem(boxNoCacheKey,boxNo);
                        $('#span-boxNo').text(boxNo);
                        $('#boxNo').val("");
                        $('#weight').focus();

                        if (response.message){
                        	refreshReceiveItems(response.message);
						}
                    }
                },
                error:function(){
					getErrorInfoAlert('扫描失败，请重新扫描!');
                }
            });
        }

        $('#refresh-receive-item').on("click",function (){
			refreshReceiveItems(null);
		});


        function refreshReceiveItems(message){
        	$('#box-receive-item').css("display", "block");
        	if (message == null){
				var boxNo = $('#span-boxNo').text();
				$.ajax({
					url : CONTEXT_PATH + "expressRecords/refreshReceiveItems",
					data : {boxNo : boxNo},
					async: false,//使用同步的方式,true为异步方式
					success : function(response){
						if (response.status == '200' && response.message != null) {
							message = response.message;
						}
					}
				});
			}
			if (message){
				var pBox = JSON.parse(message);
				var items = pBox.whBoxItems;
				if (items.length > 0){
					var html = '';
					for (var i=0;i<items.length;i++){
						var shippingCpn = '';
						var trackingNum = '';
						var quantity = '';
						if (items[i].shippingCpn){
							shippingCpn = items[i].shippingCpn;
						}
						if (items[i].trackingNum){
							trackingNum = items[i].trackingNum;
						}
						if (items[i].quantity){
							quantity = items[i].quantity;
						}
						html += "<tr><td>"+shippingCpn+"</td><td>"+trackingNum+"</td><td>"+quantity+"</td></tr>";
					}
					$("#box-receive-item").find("tbody").children().remove();
					$("#box-receive-item").find("tbody").append(html);
				}
			}
		}


        $('#warehouseId').change(function () {
            var warehouseId = $(this).children('option:selected').val();
            var oldBoxNo = $('#span-boxNo').text();
            var start2SH = oldBoxNo.indexOf("2SH");
            var startSH = oldBoxNo.indexOf('SH');

            if((start2SH == 0 && warehouseId != 2) || (startSH == 0 && warehouseId != 1)){
				getErrorInfoAlert("所选仓库与周转框所在仓库不匹配!");
                $('#boxNo').focus();
                return ;
            }

        });

		// 输入电子秤重量
		function inputWeight(obj){
		    var  boxNo =  $('#span-boxNo').text();
		    if(boxNo == ''){
				getErrorInfoAlert("请先输入周转码!");
                $('#boxNo').val('');
                $('#weight').val('');
                $('#boxNo').focus();
                return ;
			}
			if(!obj.value || obj.value.replace(/\s/g,'') == ''){
				getErrorInfoAlert("请称重!");
				$('#weight').val('');
				$('#weight').focus();
				return ;
			}
			
			var weight = obj.value.replace(/\s/g,'');
			var reg = /^[-\+]?\d+(\.\d+)?$/;//Double
  			if (!reg.test(weight)) {
				getErrorInfoAlert("请输入正确的重量");
  				$('#weight').val('');
				$('#weight').focus();
				return ;
  			}
			if(weight > 100){
				getErrorInfoAlert("重量不能大于100KG");
                $('#weight').val('');
                $('#weight').focus();
                return;
			}
			$('#expressId').val('');
			$('#expressId').focus();
		}

		//加载快递单数据，输入快递单号触发
		function inputnext(obj){
			if(!obj.value || obj.value.replace(/\s/g,'') == ''){
				getErrorInfoAlert("请输入快递单号!");
				$('#expressId').val('');
				$('#expressId').focus();
				return ;
			}
			var expressId = obj.value.replace(/\s/g,'').trim();
			if(expressId.length < 7 || (!(expressId.indexOf("CG") == -1))){
				getErrorInfoAlert("请输入正确的快递单号!");
				$('#expressId').val('');
				$('#expressId').focus();
				return ;
			}
			var weight = $("#weight").val().trim();
			if (weight == '') {
				getErrorInfoAlert("请称重!");
				$('#weight').val('');
				$('#weight').focus();
				return ;
			}else {
				var reg = /^[-\+]?\d+(\.\d+)?$/;//Double
	  			if (!reg.test(weight)) {
					getErrorInfoAlert("请输入正确的重量");
	  				$('#weight').val('');
					$('#weight').focus();
					return ;
	  			}

                if(weight > 100){
					getErrorInfoAlert("重量不能大于100KG");
                    $('#weight').val('');
                    $('#weight').focus();
                    return;
                }
			}
			submitTable(expressId, weight);
		}

		// 提交数据
		function submitTable(expressId, weight){
			debugger;
			var warehouseId = $("#warehouseId").val();
			if(warehouseId == null || warehouseId==''){
				getErrorInfoAlert("请选择仓库!");
				return ;
			}
			var boxNo = $('#span-boxNo').text();
			if(boxNo == ''){
				getErrorInfoAlert('周转框不能为空！');
			    return;
			}
			var quantity = $('#quantity').val();
	  		if(quantity == ''){
				getErrorInfoAlert("包裹数量不能为空!");
	  			return;
	  		}
	  		var reg = /^\+?[1-9][0-9]*$/;
	  		if (!reg.test(quantity)) {
				getErrorInfoAlert("包裹数量必须正整数!");
	  			return;
	  		}
			if(quantity > 100){
				getErrorInfoAlert("包裹数量不能大于100!");
				$('#quantity').val('');
				$('#quantity').focus();
				return;
			}

	  		var shippingCpn = $('#shipping_method').val();

			// 获取快递单相关入库单
 			 var r= $.ajax({
				 url: CONTEXT_PATH + "checkin/scans/query/express?query.shippingCpn=" + shippingCpn,
				data : {expressId : expressId, warehouseId: warehouseId, weight: weight, quantity: quantity,boxNo:boxNo},
	            timeout : 30000,
	            beforeSend : function() {
					App.blockUI();
				},
	            success : function(response){
	            	App.unblockUI();
	            	if (response.match("^\{(.+:.+,*){1,}\}$")) {
	            		var jsonObject = eval("(" + response + ")");
	            		$.error(jsonObject.message);
	            		return;
	            	}
	            	
	            	var responseHtml = $(response).find("#check_scan_datas").html();
	            	$("#check_scan_datas").html(responseHtml);


	            	if($("#check_scan_datas").find(".purchase-order").length > 0){

                        var flagNames = $("#check_scan_datas").find(".flags-name").text();

                        var containsJerry = false;
                        $("#check_scan_datas").find("[name='whCheckIn.whCheckInItem.sku']").each(function () {
							if($(this).val().indexOf("JR") == 0){
								containsJerry = true;
								return false;
							}
							//return containsJerry;
                        });
						var delayTime = 0;
                        if(containsJerry){
                            audioPlay("JERRY");
                            delayTime = 1000;
						}
						var flagsName = '';
						if ((flagNames && flagNames.indexOf('不贴标入库') != -1)) {
							setTimeout('audioPlay("success")');
							layer.alert('该包裹属于批发订单-不贴标业务，已从当前周转筐中解绑，请使用PDA绑定批发订单类型周转筐进行后续业务', function (index) {
								layer.close(index);
							});

						} else if ((flagNames && flagNames.indexOf('普通采购单') != -1) || !flagNames) {
							setTimeout('audioPlay("success")', delayTime);
						}
                        else if(flagNames){
							if (flagNames.indexOf('特急') != -1 && $("#check_scan_datas").find("#hasShopifyTJSku").val() == 'true') {
								dialog({
									title: '警告',
									content: 'shopify全检加急入库',
									width: 200,
									top: 0,
									okValue: '确认',
									ok: function () {}
								}).showModal();
							}else if (flagNames.indexOf('特急') != -1) {
                                flagsName += "特急,";
                                setTimeout('audioPlay("PO_TJ")', delayTime);
                            }
                            if (flagNames.indexOf('FBA') != -1) {
                                flagsName += "FBA,";
                                setTimeout('audioPlay("PO_FBA")', delayTime);
                            }
                            if (flagNames.indexOf('虚拟仓') != -1) {
                                flagsName += "虚拟仓,";
                                setTimeout('audioPlay("PO_VM")', delayTime);
                            }
                            if(flagsName){
								getErrorInfoAlert(flagsName);
							}
                        }

	            		calsf();
	            		
	            		// 防止重复扫
				        setTimeout(removeDisabled, 1000);
	            		
	            		//$.successTip("扫描成功");
	            		
	            		// 打印标签
	            		var purchaseInfo = $("#check_scan_datas").find(".purchase-info").text();
	            		if(purchaseInfo.indexOf('CG') != -1) {
	            			
	            			var serial =new Date().Format('yyyyMMdd');
	            			
	            			// 扩展到4位
	            			var piece = calsfPiece();
	            			if(piece < 10) {
	            				serial = serial + '000' + piece;
	            			} else if(piece < 100) {
	            				serial = serial + '00' + piece;
	            			} else if(piece < 1000) {
	            				serial = serial + '0' + piece;
	            			}
	            		}
	            		
	            	} else {
	            		audioPlay("error");

						var errorMsg = $("#check_scan_datas").find("#errorMsg").text();
						if (errorMsg && (errorMsg.indexOf("特急包裹") >= 0 || errorMsg.indexOf("普通包裹") >= 0)) {
							layer.open({
								content: errorMsg,
								yes: function (index, layero) {
									layer.close(index);
								},
								cancel: function (index, layero) {
									layer.close(index);
								}
							});
						} else {
							if ($('#isRejectOrder').val() && $('#isRejectOrder').val() == 1) {
								setTimeout('audioPlay("REJECT")', 1000);
							}
							getErrorInfoAlert('绑定失败！');
						}
	            	}
	            	// 若有子单号, 打印子单号
	            	// expressId : expressId, quantity: quantity
	            	if (quantity>1) {
	            		// iframe跳转打印页面
						var printPageUrl = '${CONTEXT_PATH}expressRecords/printItemExpressIds?';
						$('#printHtml').attr('src', printPageUrl + "&" + "expressId="+expressId+"&quantity="+quantity);
	            		//自动打印
			            setTimeout(IframeOnloadPrint, 100);
					}
	            	
	            	$('#expressId').val('');
	    			$('#weight').val('');
	    			$('#quantity').val(1);
	    			$('#weight').focus();
					refreshReceiveItems(null);
	             },
	             error:function(){
	            	 App.unblockUI();
	            	 $('#expressId').val("");
		             $('#expressId').focus();
					 getErrorInfoAlert('扫描失败，请重新扫描');
	             }
	        }); 
		}

		var isPrint = false;

        // 验证物流收货周转筐号
        function validateBoxNo_log(obj) {
            if(!obj.value || obj.value.replace(/\s/g,'') == ''){
                getErrorInfoAlert("请输入周转筐号!");
                $('#boxNo-log').val('');
                $('#boxNo-log').focus();
                return;
            }
            var boxNo = obj.value.replace(/\s/g,'').trim();
            
            $.ajax({
                url: CONTEXT_PATH + "checkin/scans/validateBox",
                type: "POST",
                data: {boxNo: boxNo},
                success: function (response) {
                    if (response.status == '200') {
                        $('#span-boxNo-log').text(boxNo);
                        $('#expressId-log').val('');
                        $('#expressId-log').focus();
                    } else {
                        getErrorInfoAlert(response.message);
                        $('#boxNo-log').val('');
                        $('#span-boxNo-log').text('');
                        $('#boxNo-log').focus();
                    }
                },
                error: function() {
                    getErrorInfoAlert('验证周转筐号失败，请重试');
                    $('#boxNo-log').val('');
                    $('#span-boxNo-log').text('');
                    $('#boxNo-log').focus();
                }
            });
        }

        //加载快递单数据，输入快递单号触发
        function inputnext_log(obj){
			$('#box-receive-item').css("display", "none");

            
            if(!obj.value || obj.value.replace(/\s/g,'') == ''){
				getErrorInfoAlert("请输入快递单号!");
                $('#expressId-log').val('');
                $('#expressId-log').focus();
                return ;
            }
            var expressId = obj.value.replace(/\s/g,'').trim();
            if(expressId.length < 7 || (!(expressId.indexOf("CG") == -1))){
				getErrorInfoAlert("请输入正确的快递单号!");
                $('#expressId-log').val('');
                $('#expressId-log').focus();
                return ;
            }

			$.ajax({
				url: CONTEXT_PATH + "checkin/scans/check/express",
				data: {expressId: expressId},
				success: function (response) {
					if (response.status == '200') {
						if (response.message) {
							layer.alert(response.message, function (index) {
								audioPlay("PO_TJ");
								layer.close(index);
								if (isPrint) {
									// 已经打印
									$('#quantity-log').val('1');
									$('#weight-log').val('');
									$('#weight-log').focus();
								} else {
									// 未打印
									$('#quantity-log').val('');
									$('#quantity-log').focus();
								}
							});
						} else if (isPrint) {
							// 已经打印
							$('#quantity-log').val('1');
							$('#weight-log').val('');
							$('#weight-log').focus();
						} else {
							// 未打印
							$('#quantity-log').val('');
							$('#quantity-log').focus();
						}
					} else {
						audioPlay("error");
						layer.alert(response.message, function (index) {
							layer.close(index);
							$('#expressId-log').val('');
							$('#expressId-log').focus();
						});
						return false;
					}
				}
			});

        }

        function getExpressId() {
            var expressId = $('#expressId-log').val();
            if(!expressId || expressId.replace(/\s/g,'') == ''){
				getErrorInfoAlert("请输入快递单号!");
                $('#expressId').val('');
                $('#expressId').focus();
                return false;
            }
            expressId = expressId.replace(/\s/g,'').trim();
            if(expressId.length < 7 || (!(expressId.indexOf("CG") == -1))){
				getErrorInfoAlert("请输入正确的快递单号!");
                $('#expressId-log').val('');
                $('#expressId-log').focus();
                return false;
            }
            return expressId;
        }

		function inputQuantity_log(obj) {
			debugger;
            var quantity = obj.value;
            if(quantity == ''){
				getErrorInfoAlert("包裹数量不能为空!");
                return;
            }
            var reg = /^\+?[1-9][0-9]*$/;
            if (!reg.test(quantity)) {
				getErrorInfoAlert("包裹数量必须正整数!");
                return;
            }
			if(quantity > 200){
				getErrorInfoAlert("包裹数量不能大于200!");
				$('#quantity-log').val('');
				$('#quantity-log').focus();
				return;
			}
            // 数量大于1打印子单号
            if (quantity > 1) {
                // iframe跳转打印页面
                var printPageUrl = '${CONTEXT_PATH}expressRecords/printItemExpressIds?';
                var expressId = getExpressId();
                $('#printHtml').attr('src', printPageUrl + "&" + "expressId="+expressId+"&quantity="+quantity+"&isLog=true");
                //自动打印
                setTimeout(IframeOnloadPrint, 100);
                $("#check_scan_datas").html("<div class=\"alert alert-danger\"><h3>已生成并打印子单号，请扫描子单号依次称重</h3></div>");
                isPrint = true;
                $('#expressId-log').val('');
                $('#expressId-log').focus();
            } else {
                $('#weight-log').val('');
                $('#weight-log').focus();
            }
        }


        // 输入电子秤重量
        function inputWeight_log(obj){
            if(!obj.value || obj.value.replace(/\s/g,'') == ''){
				getErrorInfoAlert("请称重!");
                $('#weight-log').val('');
                $('#weight-log').focus();
                return ;
            }
            var weight = obj.value.replace(/\s/g,'');
            var reg = /^[-\+]?\d+(\.\d+)?$/;//Double
            if (!reg.test(weight)) {
				getErrorInfoAlert("请输入正确的重量");
                $('#weight-log').val('');
                $('#weight-log').focus();
                return ;
            }
            if(weight > 1000){
				getErrorInfoAlert("重量不能大于1000KG");
                $('#weight-log').val('');
                $('#weight-log').focus();
                return;
            }
            submitTable_log(weight);
        }

        // 提交数据
        function submitTable_log(weight){
            var quantity = $('#quantity-log').val();
            var expressId = getExpressId();
            if (!expressId) {
                return;
            }
            
            // 检查周转筐号
            var boxNo = $('#span-boxNo-log').text();
            
            if(quantity == ''){
				getErrorInfoAlert("包裹数量不能为空!");
                return;
            }
            var reg = /^\+?[1-9][0-9]*$/;
            if (!reg.test(quantity)) {
				getErrorInfoAlert("包裹数量必须正整数!");
                return;
            }
            if(quantity != 1){
				getErrorInfoAlert("子包裹数量必须为1!");
                return;
            }
			var shippingCpn = $('#shipping-method-log').val();
            // 获取快递单相关入库单
            var r= $.ajax({
                url : CONTEXT_PATH + "checkin/scans/query/express/log?query.shippingCpn=" + shippingCpn,
                data : {expressId : expressId, weight: weight, quantity: quantity, boxNo: boxNo},
                timeout : 30000,
                beforeSend : function() {
                    App.blockUI();
                },
                success : function(response){
                    App.unblockUI();
                    if (response.match("^\{(.+:.+,*){1,}\}$")) {
                        var jsonObject = eval("(" + response + ")");
                        $.error(jsonObject.message);
                        return;
                    }

                    var responseHtml = $(response).find("#check_scan_datas").html();
                    $("#check_scan_datas").html(responseHtml);

                    if($("#check_scan_datas").find(".purchase-order").length > 0){
                        var flagNames = $("#check_scan_datas").find(".flags-name").text();
                        var containsJerry = false;
                        $("#check_scan_datas").find("[name='whCheckIn.whCheckInItem.sku']").each(function () {
                            if($(this).val().indexOf("JR") == 0){
                                containsJerry = true;
                                return false;
                            }
                        });
                        var delayTime = 0;
                        if(containsJerry){
                            audioPlay("JERRY");
                            delayTime = 1000;
                        }
                        var flagsName = '';

						if ((flagNames && flagNames.indexOf('不贴标入库') != -1)) {
							setTimeout('audioPlay("success")');
							layer.alert('该包裹属于批发订单-不贴标业务', function (index) {
								layer.close(index);
							});

						} else if ((flagNames && flagNames.indexOf('普通采购单') != -1) || !flagNames) {
							setTimeout('audioPlay("success")', delayTime);
						}
                        else if(flagNames){
							if (flagNames.indexOf('特急') != -1 && $("#check_scan_datas").find("#hasShopifyTJSku").val()== 'true') {
								dialog({
									title: '警告',
									content: 'shopify全检加急入库',
									width: 200,
									top: 0,
									okValue: '确认',
									ok: function () {}
								}).showModal();
							}else if (flagNames.indexOf('特急') != -1) {
                                flagsName += "特急,";
                                setTimeout('audioPlay("PO_TJ")', delayTime);
                            }
                            if (flagNames.indexOf('FBA') != -1) {
                                flagsName += "FBA,";
                                setTimeout('audioPlay("PO_FBA")', delayTime);
                            }
                            if (flagNames.indexOf('虚拟仓') != -1) {
                                flagsName += "虚拟仓,";
                                setTimeout('audioPlay("PO_VM")', delayTime);
                            }
                            if(flagsName && flagsName.indexOf("特急") == -1){
								getErrorInfoAlert(flagsName);
                            }
                        }

                        calsf();
                        // 防止重复扫
                        setTimeout(function () {
                            $("#expressId-log").removeAttr("disabled");
                            $('#expressId-log').val("");
                            $('#weight-log').val('');
                            $('#quantity-log').val(1);
                            $('#expressId-log').focus();
                        }, 1000);
                    } else {
                        audioPlay("error");
                        if($('#isRejectOrder').val() && $('#isRejectOrder').val() == 1){
                            setTimeout('audioPlay("REJECT")', 1000);
                        }
						getErrorInfoAlert('绑定失败！');
                    }
					$('#expressId-log').val('');
					$('#weight-log').val('');
					$('#quantity-log').val(1);
					$('#expressId-log').focus();
                },
                error:function(){
                    App.unblockUI();
                    $('#expressId-log').val("");
                    $('#weight-log').val('');
                    $('#quantity-log').val(1);
                    $('#expressId-log').focus();
					getErrorInfoAlert('扫描失败，请重新扫描');
                }
            });
        }
		
		function removeDisabled() {
			$("#expressId").removeAttr("disabled");
			
			$('#expressId').val("");
			$('#weight').val('');
			$('#weight').focus();
        	//$('#expressId').focus();
		}

		// 统计扫描成功和失败的数量
	    function calsf(){
			
      		var storage = new WebStorageCache();
      		
      		// 一开始没有缓冲，所以这次数量算1
      		var lastSuc = 0;
      		
      		if (storage.get(cacheKey)) {
      			lastSuc = storage.get(cacheKey);
      			lastSuc ++;
      		} else {
      			lastSuc = 1;
      		}
      		
      		storage.set(cacheKey, lastSuc , {exp : 5 * 60 * 60});
      		
	    	$('#panel-title').html('<h1 style="color:blue;font-size:48px;">成功：<b>'+lastSuc+'</b></h1>');
	    }
		
	 	// 计数
		function calsfPiece(){
			
      		var storage = new WebStorageCache();
      		
      		var lastSuc = 0;
      		
      		if (storage.get(pieceCacheKey)) {
      			lastSuc = storage.get(pieceCacheKey);
      			lastSuc = lastSuc + 1;
      		} else {
      			lastSuc = 1;
      		}
      		
      		storage.set(pieceCacheKey, lastSuc , {exp : 5 * 60 * 60});
      		
      		return lastSuc;
	    }
	 	
		Date.prototype.Format = function (fmt) { //author: meizz
	        var o = {
	            "M+": this.getMonth() + 1, //月份 
	            "d+": this.getDate(), //日 
	            "h+": this.getHours(), //小时 
	            "m+": this.getMinutes(), //分 
	            "s+": this.getSeconds(), //秒 
	            "q+": Math.floor((this.getMonth() + 3) / 3), //季度 
	            "S": this.getMilliseconds() //毫秒 
	        };
	        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
	        for (var k in o)
	        if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
	        return fmt;
	    }
	 	
		/** 打印 **/
		var LODOP; //声明为全局变量
		function myPrint() {
			App.unblockUI();
			//先判断 内页中是否有 打印 方法 有的话直接调用
			try{
				if(typeof(eval(window.frames["printHtml"].myPrint))=='function'){
					return window.frames["printHtml"].myPrint();
				}
			}catch(e){
			}
		};
		
		function GetIframeInnerHtml(objIFrame) {
			var iFrameHTML = objIFrame.document.getElementById('print_content').innerHTML;
			return iFrameHTML;
		}
		
		// 这里Go
		var printed = false;
		function IframeOnloadPrint(){
			var iframe=document.getElementById("printHtml");
			// 加载完iframe后执行
			if (iframe.attachEvent){
			    iframe.attachEvent("onload", function(){
			    	printed = true;
				    myPrint();
			    });
			} else {
			    iframe.onload = function(){
			    	printed = true;
			    	setTimeout(myPrint, 2000);/*延时2秒打印*/
			    	return;
			    };
			}
			
			printed = false;
		}
		
		function isMultiBoxNo() {
			var localBoxNo = localStorage.getItem(boxNoCacheKey);
			var sessionBoxNo = sessionStorage.getItem(boxNoCacheKey);
			if(null != localBoxNo && null != sessionBoxNo){
				if(localBoxNo != sessionBoxNo){
					layer.alert("请勿操作多个周转框！");
					setTimeout(function() {
						location.reload();
					},1000);
				}
			}
		}
    </script>
	<!-- END JAVASCRIPTS -->
</body>
<!-- END BODY -->
</html>