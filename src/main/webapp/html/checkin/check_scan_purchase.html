<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
</head>

<body>
<div>
	<div id="check_scan_datas" class="border-gray p5">

		<#if !domain.purchaseOrders>
				<#if false == domain.hasCheckIn>
					<#if !domain.itemExpressIdsStr>
						<div class="alert alert-danger">
							<h3 class="message-info" style="font-size:48px;">${domain.messageInfo}</h3>
						</div>
					</#if>
					<#if domain.itemExpressIdsStr??>
						<div class="alert alert-danger">
							<div class="alert alert-danger">
								<h3 class="message-info"  style="font-size:48px;">${domain.messageInfo}</h3>
							</div>
							 <#if domain.query?? && domain.query.trackingNumbers??>
                                <input type="hidden" id="returnItemExpressIdsStr" name="returnItemExpressIdsStr" value="${domain.query.trackingNumbers?join(',')}"/>
                                <#else>
                                <input type="hidden" id="returnItemExpressIdsStr" name="returnItemExpressIdsStr" value=""/>
							</#if>
							<input type="hidden" id="itemExpressIdsStr" name="itemExpressIdsStr" value="${domain.itemExpressIdsStr}"></input>
							<input type="hidden" id="expressQuantity" name="expressQuantity" value="${domain.expressQuantity}"></input>
							<input type="hidden" id="purchaseOrderNo" name="purchaseOrderNo" value="${domain.purchaseOrderNo}"/>
							<input type="hidden" id="recordCount" name="recordCount" value="${domain.recordCount}"/>
							<input type="hidden" id="scanExpressIdsStr" name="scanExpressIdsStr" value="${domain.scanExpressIdsStr}"/>
							<span style="color: red;font-size: 20px;" class="span-expressQuantity">总数（包含主单号）：${domain.expressQuantity}</span>
							<br />
							<span style="color: red;font-size: 20px;" class="span-itemExpressIdsStr">已扫单号：${domain.itemExpressIdsStr}</span>
						</div>
					</#if>
				</#if>
				<#if true == domain.hasCheckIn>
					<div class="alert alert-danger">
						<h3 class="message-info"  style="font-size:48px;">该采购单已入库！</h3>
					</div>
				</#if>
			<#else>
				<button type="button" class="btn  blue" id="finish-checkIn">完成点数</button>
				<div><input type="hidden" id="hasShopifyTJSku" value="${domain.hasShopifyTJSku}"></div>
				<#list domain.purchaseOrders as purchaseOrder >
					<form class="form-horizontal submit-form mb20" name="submitForm" id="submit-form-${purchaseOrder_index }" action="${CONTEXT_PATH}checkin/scans/checkIn" method="POST">
						<div id="common-param-${purchaseOrder_index }">
							<!--  采购单ID-->
								<!-- <input type="hidden" name="whCheckIn.purchaseOrder.orderId" value="${purchaseOrder.orderId }"> -->
                            <span style="display: none" id="purchaseOrderArrayStr">${domain.purchaseOrderArrayStr}</span>
							<!--  采购单号-->
							<input type="hidden" name="whCheckIn.purchaseOrderNo" value="${purchaseOrder.purchaseOrderNo}"></input>
							<!--  快递单号-->
							<input type="hidden" name="whCheckIn.trackingNumber" value="${(!purchaseOrder.trackingNumber)?string(purchaseOrder.expressId,purchaseOrder.trackingNumber)}"></input>
							<!--  入库类型-->
							<input type="hidden" name="whCheckIn.checkInType" value="1">
                            <!-- 运费-->
							<input type="hidden" name="whCheckIn.shippingCost" value="${purchaseOrder.shippingCost}">
                            <!-- 总重量-->
							<input type="hidden" name="whCheckIn.totalWeight" value="${purchaseOrder.totalWeight}">
							<!--  仓库ID-->
							<input type="hidden" name="whCheckIn.warehouseId" value="${purchaseOrder.warehouseId}">
							<!--  标签-->
							<input type="hidden" name="whCheckIn.flags" value="${purchaseOrder.flagEnStr}">
							<#if domain.query?? && domain.query.trackingNumbers??>
                                <input type="hidden" name="returnItemExpressIdsStr" value="${domain.query.trackingNumbers?join(',')}"/>
                                <#else>
                                <input type="hidden" name="returnItemExpressIdsStr" value="${purchaseOrder.trackingNumber}"/>
							</#if>
						</div>
						<!--  采购单号-->
						<#if (purchaseOrder.logisticsMark)??>
							<h3 class="flags-name">此包裹含有补发配件，拆箱时请注意！</h3>
						</#if>
						<h3>采购单号：${purchaseOrder.purchaseOrderNo } &nbsp; |&nbsp; 快递单号：${purchaseOrder.expressId}  &nbsp; | &nbsp; 采购员：[${purchaseOrder.purchaseUserName}]  &nbsp; 采购单类型
							<#if purchaseOrder.purchaseOrderNo?contains("NCGHW")>
								<span class="flags-name">[中转仓入库]</span>
							<#else >
								<span class="flags-name">${purchaseOrder.flagsName}</span>
							</#if>
							<#if (purchaseOrder.specialType)?? && purchaseOrder.specialType == 2009>
								<span class="flags-name">[贵重物品]</span>
							</#if>
							<#if purchaseOrder.flagsName?? && purchaseOrder.flagsName?contains("不贴标入库")>
								<span class="flags-name" style="font-weight: bold;">[不贴标业务]</span>
							</#if>
						</h3>
						<h3>
							<#if purchaseOrder.hasShopifyTJSku?? && '' != purchaseOrder.hasShopifyTJSku >
								<span class="flags-name">${purchaseOrder.hasShopifyTJSku} shopify全检加急入库</span>
							</#if>
						</h3>
						<table	class="table table-bordered table-condensed purchase-order" id="table-purchase-order-${purchaseOrder_index }">
							<colgroup>
								<col width="80" />
								<col width="140" />
								<col />
								<col />
								<col width="50"/>
								<col />
								<col />
								<col />
								<col width="50"/>
							</colgroup>
							<thead>
							<tr class="">
								<th>图片</th>
								<th>SKU</th>
								<th>商品名称</th>
								<th>质检</th>
								<th>已入库数量</th>
								<th>采购数量-取消在途</th>
								<th>良品数量</th>
								<th>来货包装属性</th>
								<th>不良品类型</th>
								<th>异常数量</th>
								<th>异常周转码</th>
								<th>异常备注</th>
								<th>操作</th>
							</tr>
							</thead>
							<tbody>
							<#list purchaseOrder.purchaseOrderItems as item >
								<#if domain.whCheckInException?? && item.sku == domain.whCheckInException.sku && domain.whCheckInException.handleWay==3 && (domain.whCheckInException.status == 4 || domain.whCheckInException.status==13 )>
									<tr id="update-image-tab-${purchaseOrder_index }-${item_index}" style="height: 23px;">
										<td colspan="13" style="height: 23px;">
											<span class="flags-name"  style="color: red;padding: 15px;font-size: large;font-weight: bold;">SKU已有"来货与图不符"异常，目前开发正等待带样换图中，请不要重复提交相同类型的异常单。</span><td>
									</tr>
								</#if>
								<tr class="purchase-tr" id="purchase-tr-${purchaseOrder_index }-${item_index}">
									<td>
										<#if (item.whSku.whSkuExtend.qcImageUrl)?? && item.whSku.whSkuExtend.qcImageUrl!=''>
											<img class="sku-image" onclick="getSkuImage('${item.sku}')" alt="产品缩略图" border="0" width="80px" height="74px" src="${item.whSku.whSkuExtend.qcImageUrl}"/>
										<#else>
											<img class="sku-image" onclick="getSkuImage('${item.sku}')" alt="产品缩略图" border="0" width="80px" height="74px" src="${item.whSku.imageUrl }"/>
										</#if>
									</td>
									<td>
										<a href="${CONTEXT_PATH}skus/skuDetail?sku=${item.sku }" target="_blank">${item.sku }</a>
										<input type="hidden" name="purchaseOrderItem.cancelHandleStatus" value="${item.cancelHandleStatus}">
										<input type="hidden" name="whCheckIn.whCheckInItem.sku" value="${item.sku}">
										<input type="hidden" name="whCheckIn.whCheckInItem.supplierId" value="${purchaseOrder.supplierId}">
										<input type="hidden" name="whCheckIn.whCheckInItem.purchasePrice" value="${item.price}">
										<input type="hidden" name="whCheckIn.whCheckInItem.purchaseQuantity" value="${item.quantity}">
										<#if item.neededQuantity gt 0 >
											<input type="hidden" name="whCheckIn.whCheckInItem.firstOrderType" value="9">
										<#else >
											<input type="hidden" name="whCheckIn.whCheckInItem.firstOrderType" value="${item.firstOrderType}">
										</#if>
										<input type="hidden" name="whCheckIn.whCheckInItem.isSkipQc" value="${item.isSkipQc?string}">
										<input type="hidden" name="whCheckIn.whCheckInItem.isFreeCheck" value="${item.isFreeCheck?string}" id="itemFreeCheck">
										<input type="hidden" name="whCheckIn.whCheckInException.boxNo" value="">
										<input type="hidden" name="whCheckIn.whCheckInException.userName" value="${purchaseOrder.purchaseUser}">
										<!--  标签-->
										<input type="hidden" name="whCheckIn.whCheckInItem.hasShopifyTJSku" value="${item.hasShopifyTJSku}">
										<input type="hidden" name="whCheckIn.whCheckInItem.checkInProcess" value="${item.whSku.checkInProcess}">
										<input type="hidden" name="specialType" value="${item.whSku.specialType}">

										<input type="hidden" name="whCheckIn.afterSaleQty" value="${item.afterSaleQty}">
										<input type="hidden" name="whCheckIn.vendorName" value="${purchaseOrder.vendorName}">
										<input type="hidden" name="whCheckIn.supplierId" value="${purchaseOrder.vendorCode}">
										<#if (item.whSku.specialType??) && (item.whSku.specialType == 2009)>
                                        	</br><strong style="color: red">贵重物品</strong>
										</#if>
                                        </br><span>${item.whSku.skuBarCode}</span>
										</br>
										</br><span style="color: red;">采购返回库位：${item.location}</span>
										<#if (item.whSku.locationNumber??) && ('WSW' == item.whSku.locationNumber)>
											</br>
											</br>
											<span class="" style="color: red;font-size: 14px;font-weight: 600">请先分配或同步库位</span>
										</#if>
									</td>
									<td style="width: 12%">
										<dl>
											<dd id="product-name-${item.skuId}">产品名称：<span style="color: red;font-size: large;font-weight: bold;">${item.whSku.name}</span></dd>
											<dd id="product-isPackingAgain-${item.skuId}">是否加工装袋：
                                                <#if !(item.whSku.floorLocation??) || item.whSku.floorLocation == 0>
                                                    <span style="color: red;">否</span>
                                                <#else >
                                                    <span style="color: red;">
                                                        ${util('enumName', 'com.estone.sku.enums.ProcessType', item.whSku.floorLocation)}
                                                    </span>
                                                </#if>
											</dd>
											<dd id="product-weigh-${item.skuId}">净重：<span style="color: red;font-size: large;font-weight: bold;">${item.whSku.weight}g</span></dd>
<#--											<dd id="product-checkInProcess-${item.skuId}">来货是否加工：-->
<#--												<#if item.firstOrderType == 1 || item.firstOrderType == 2>-->
<#--													<select name="item.whSku.checkInProcess" value="${item.whSku.checkInProcess}" style="color: red;width: 70px" onchange="updateSkuCheckInProcess(this,'${item.sku}')">-->
<#--														<option value=""></option>-->
<#--														<option value=0 <#if item.whSku.checkInProcess==0>selected="selected"</#if> >否</option>-->
<#--														<option value=1 <#if item.whSku.checkInProcess==1>selected="selected"</#if> >是</option>-->
<#--													</select>-->
<#--												<#else >-->
<#--													<select name="item.whSku.checkInProcess" value="${item.whSku.checkInProcess}" style="color: red;width: 70px" disabled="disabled">-->
<#--														<option value=""></option>-->
<#--														<option value=0 <#if item.whSku.checkInProcess==0>selected="selected"</#if> >否</option>-->
<#--														<option value=1 <#if item.whSku.checkInProcess==1>selected="selected"</#if> >是</option>-->
<#--													</select>-->
<#--												</#if>-->
<#--											</dd>-->

											<dd id="product-packageAttribute-${item.skuId}">包装属性：
												<span style="font-size: 14px;color: red;font-weight: bold;font-weight: 600">${item.whSku.whSkuExtend.packageAttribute }</span>
											</dd>
<#--											<dd id="product-useOlderPackage-${item.skuId}">产品标识：-->
<#--												<span style="color: red;">${item.productFlag}</span>-->
<#--											</dd>-->
											<dd id="product-size-${item.skuId}">标准尺寸：
												<span style="color: red;">${item.whSku.contain}</span>
											</dd>
										</dl>
									</td>
									<td style="width: 10%;">
                                        <dl>
                                            <dd id="product-skuDecs-${item.skuId}">产品系统质检备注：
                                                <span style="font-size: 14px;color: red;font-weight: bold;font-weight: 600">${item.skuQcCategoryDesc}</span>
                                            </dd>
                                            <dd id="product-skuFeature-${item.skuId}">仓库质检备注：
                                                <span style="font-size: 14px;color: red;font-weight: bold;font-weight: 600">${item.whSku.feature }</span>
                                            </dd>
<#--											<dd id="product-packingMemo-${item.skuId}">包装备注：-->
<#--												<span style="font-size: 14px;color: red;font-weight: bold;font-weight: 600">${item.packingMemo }</span>-->
<#--											</dd>-->
                                            <dd id="product-tagList-${item.skuId}">仓库标签：
                                                <span style="font-size: 14px;color: red;font-weight: bold;font-weight: 600">${item.whSku.whSkuExtend.tags }</span>
                                            </dd>
											<dd id="product-tagList-${item.skuId}">分类路径：
												<span style="font-size: 14px;font-weight: bold;font-weight: 600">${item.whSku.categoryPath }</span>
											</dd>
											<dd id="product-tagList-${item.skuId}">耗材匹配：
												<#if item.matchMaterials??>
													<#list item.matchMaterials as material>
														<span style="color:green">
															${material.materialArticleNumber}
														</span>
														<span style="color:green;margin-left: 10px;">${material.name}</span>
														&nbsp;&nbsp;&nbsp;&nbsp;
													</#list>
												</#if>
											</dd>
                                        </dl>
                                    </td>
									<td style="text-align: center;color: red;font-size: 20px;">
										<!--  已入库（已经确认状态的入库数+未确认入库数量）-->
										<span class="check_in_quantity">${item.undefinedInQuantity+item.inedQuantity }</span>
									</td>
									<td style="text-align: center;">
										<span class="purchase-quantity-minus-revoked" style="font-size: larger">${item.quantity - item.revokedQuantity}</span>
										<span class="purchase-quantity" style="display: none">${item.quantity}</span>
										<!--已经确认状态的入库数-->
										<span class="instocked-quantity" style="display: none">${item.inedQuantity }</span>
										<!--  未确认入库数量-->
										<span class="undefined-quantity" style="display: none">${item.undefinedInQuantity }</span>
										<!--  取消在途数量-->
										<span class="revoked-quantity" style="display: none">${item.revokedQuantity }</span>
										<!--  待取消在途数量-->
										<#if item.waitHandlerQ?? && item.waitHandlerQ gt 0>
											<br/><br/><span style="color: red;">待取消: </span>
											<span class="waitHandlerQ" style="color: red">${item.waitHandlerQ }</span>
											<#else>
											<span class="waitHandlerQ" style="display: none">0</span>
										</#if>
											<span class="waitHandlerQEx" style="display: none">0</span>
										<!--  入库异常数量-->
										<span class="dbexception-quantity" style="display: none">${item.exceptionQuantity }</span>
									</td>
									<td>
										<input number="true" min="0" digits="true"  required="true" name="whCheckIn.whCheckInItem.quantity" class=" form-control input-xsmall instock-quantity" value="">
											<!-- 打印预览时 需要的参数 -->
										<div id="hidden-div-${purchaseOrder_index }-${item_index}">
											<input type="hidden" name="i" value="">
											<input type="hidden" name="s" value="${item.sku}">
											<input type="hidden" name="l" value="${item.whSku.locationNumber}">
											<input type="hidden" name="q" value="0" class="whCheckInItems-quantity">
											<input type="hidden" name="n" value="${item.whSku.name}">
										</div>
										<!--<#if domain.whCheckInException?? && item.sku == domain.whCheckInException.sku && domain.whCheckInException.handleWay==3 && (domain.whCheckInException.status == 4 || domain.whCheckInException.status==13)>
											<br/><br/><span style="color: red;font-weight: bold;font-weight: 600;font-size: 14px">最多入: </span>
											<span class="allowHandlerQ" style="color: red;font-weight: bold;font-weight: 600;font-size: 14px">${domain.whCheckInException.confirmQuantity}</span>
										</#if>-->
										<#if item.revokedQuantity gt 0 >
											<span class="" style="color: red;font-size: 16px;">有取消在途${item.revokedQuantity}个，请联系采购</span>
										</#if>
										<#if item.neededQuantity gt 0 >
											<span class="needed-quantity" style="color: red;font-size: 16px;" class="needed-quantity">需分批入库
												${item.neededQuantity}
											</span>
											<span class="match-quantity" style="display: none">${item.neededQuantity }</span>
										<#else>
											<span class="match-quantity" style="display: none">0</span>
										</#if>

										<#if item.isFreeCheck=true>
											<#if domain.exemptionQcConfiguration?? && domain.exemptionQcConfiguration.checkInSkuNum??>
												<span  style="color: red;font-size: 16px;" class="isFreeCheck">入库良品数≤${domain.exemptionQcConfiguration.checkInSkuNum}时免QC</span>
											<#else>
												<span  style="color: red;font-size: 16px;" class="isFreeCheck">免QC</span>
											</#if>
										</#if>
										<button type="button" class="btn btn-default" id="excess-btn-${purchaseOrder_index }-${item_index}" 
											onclick="showExcessModule(${purchaseOrder_index}, ${item_index}, '${item.sku}', '${purchaseOrder.purchaseOrderNo}');"
											style="margin-top: 10px;display:none;">
											<i class="icon-plus"></i>多货
										</button>
									</td>
									<td>
										<input class="form-control" name="whCheckIn.whCheckInItem.checkInPackageAttr"
											   id="item-check-in-package-attr-${purchaseOrder_index }-${item_index}"
											   onchange="changeExceptionType(this)" type="text" value="">
									</td>
									<td>
										<input class="form-control" name="whCheckIn.whCheckInException.exceptionType"
											   id="item-exception-type-${purchaseOrder_index }-${item_index}"
											   onchange="changeExceptionType(this)"
											   type="text" value="">
									</td>
									<td>
										<input number="true" min="0" digits="true"  required="true" name="whCheckIn.whCheckInException.quantity" class=" form-control input-xsmall exception-quantity" value="">
									</td>
									<td>
										<input type="text" class="form-control" name="exBoxNo" id="exBoxNo-${purchaseOrder_index }-${item_index}" style="display: block"
											   value="" onkeypress="if(event.keyCode==13) { checkBox(this, ${purchaseOrder_index }, ${item_index},'exception'); return false;}"tabindex="4">
										<span style="color: red;font-size: 20px;" class="span-boxNo"></span>
									</td>
									<td class="exception-comment-td">
										<input id="exceptionComment" type="text" class="form-control" name="whCheckIn.whCheckInException.exceptionComment" class=" form-control" value="">
                                        <#if domain.whCheckInException?? && item.sku == domain.whCheckInException.sku>
                                            <dl>
                                                <dd><span style="color: red;">异常单处理入库</span></dd>
                                                <dd><span style="color: red;">异常确认数量：${domain.whCheckInException.confirmQuantity}</span></dd>
                                                <dd><span style="color: red;">异常员：${util('name',domain.whCheckInException.exceptionUser)}</span></dd>
                                                <dd><span style="color: red;">备注信息：${domain.whCheckInException.exceptionComment}</span></dd>
                                            </dl>

                                        </#if>
										<button type="button" class="btn btn-xs btn-info" style="display: none;margin-top: 5px;" onclick="clickComment1(this)">非保质期SKU</button>
										<button type="button" class="btn btn-xs btn-info" style="display: none;margin-top: 5px;" onclick="clickComment2(this)">临近/已过保质期</button>
									</td>
									<td style="text-align: center;">
										<button type="button" class="btn btn-default" id="single-build-btn-${purchaseOrder_index }-${item_index}" onclick="buildInStock(${purchaseOrder_index }, ${item_index}, false);">
											<i class="icon-plus"></i>提交
										</button>
                                        <#if item.whSku.skuBarCode?? || item.existSkuUniqueSegment>
                                            <button type="button" style="margin-top: 10px;" class="btn btn-default" id="print-one-uuid-${purchaseOrder_index }-${item_index}" onclick="buildInStock(${purchaseOrder_index }, ${item_index}, true);">
                                                不打码<br>提交
                                            </button>
                                        </#if>
										<div name="restricted-storage" style="margin-top: 20px;">
											<span style="color: red;"></span>
											<input type="hidden" name="whCheckIn.restrictedStorage" value="0"/>
											<input type="hidden" name="purchaseNo" value="${purchaseOrder.purchaseOrderNo}">
											<input type="hidden" name="whCheckIn.days" value="">
											<input type="hidden" name="whCheckIn.proDate" value="">
											<input type="hidden" name="whCheckIn.expDate" value="">
											<input type="hidden" name="whCheckIn.expWaitId" value="">
											<input type="hidden" name="whCheckIn.waitOffsetQty" value="">
											<input type="hidden" name="whCheckIn.expFlag" value="">
										</div>
									</td>
								</tr>
								<tr style="font-size: larger;" id="special-tag-${purchaseOrder_index }-${item_index}">
<#--									<td colspan="9" style="padding: 15px;">-->
<#--										<#if item.matchMaterials??>-->
<#--											<dd>-->
<#--												入库封口袋/背心袋匹配：建议使用-->
<#--												<#list item.matchMaterials as material>-->
<#--													<#if material_index != 0>或</#if>-->
<#--													<span style="color:green;margin: 0px 20px;">${material.materialArticleNumber}<span-->
<#--																style="margin-left: 20px;">${material.name}</span></span>-->
<#--												</#list>-->
<#--											</dd>-->
<#--										<#else >-->
<#--											<dd>入库封口袋/背心袋匹配：无</dd>-->
<#--										</#if>-->
<#--									</td>-->
									<#if item.whSku.specialTag??>
									<th colspan="13" style="text-align: center;line-height:53px">
										<span style="color: red;font-size: 20px;">${item.whSku.specialTag}</span>
									</th>
									<#else >
									<td colspan="13" style="padding: 15px;"></td>
									</#if>
								</tr>
								<#if item.whSku.expSku == true>
									<tr class="purchase-tr" id="purchase-exp-${purchaseOrder_index }-${item_index}">
										<td style="padding: 20px;" colspan="13">
											<#include "/checkin/expSettingContain.html">
											<span class="expCheckMsg" style="color: red;font-size: 16px;"></span>
										</td>
									</tr>
								</#if>

								<!-- 多货处理模块 -->
								<tr class="purchase-tr" id="purchase-excess-${purchaseOrder_index }-${item_index}" style="display:none;">
									<td style="padding: 20px;" colspan="13">
										<#assign parentIndex = purchaseOrder_index>
										<#assign itemIndex = item_index>
										<#include "/checkin/moreGoodsSettingContain.html">
									</td>
								</tr>
								<!-- 服装类业务html -->
                                <#include "/checkin/clothingSizeContain.html">
                                <#include "/checkin/exceptionCountContain.html">
                                <#include "/checkin/recentExceptionsContain.html">
								<tr class="separator-row" id="separator-row-${purchaseOrder_index }-${item_index}" >
									<td colspan="13" style="height: 40px; background-color: white; border-left: hidden;border-right: hidden;"></td>
								</tr>
							</#list>
							</tbody>
						</table>
					</form>
				</#list>
		</#if>
		<div id="images-div" class="container-fluid category-view display-none">
			<div class="" style='position:relative'>
				<div id="div_image" class="">
					<img id="display-image" src="" alt="" style="width: 800px;height: 800px" />
				</div>
				<button type="button" style="text-align: center;position:absolute;top:50%" class="btn btn-default" id="" onclick="lastImage();">
					<
				</button>
				<button type="button" style="text-align: center;position:absolute;top:50%;right:0" class="btn btn-default" id="" onclick="nextImage();">
					>
				</button>
			</div>
		</div>
		<!-- 服装类业务script -->
		<#include "/checkin/checinClothingSizeContainScript.html">
		<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/jquery.form.js"></script>
		<script type="text/javascript" src="${CONTEXT_PATH}js/sku/expSettingCheckIn.js?v=${.now?datetime}"></script>
		<script type="text/javascript">
            // 异常类型
            //var exceptionTypeArray = jQuery.parseJSON($("#exceptionTypes").text());
            var exceptionTypeArray = ${domain.exceptionTypes};
            // Filter out exception type ID 12 for non-special purchase orders
            var purchaseOrderNo = $("input[name='whCheckIn.purchaseOrderNo']").val();
            if (purchaseOrderNo && 
                !purchaseOrderNo.startsWith('NCGYP') && 
                !purchaseOrderNo.startsWith('NCGZS') && 
                !purchaseOrderNo.startsWith('NCGCW') && 
                !purchaseOrderNo.startsWith('NCGOYP') && 
                !purchaseOrderNo.startsWith('NCGHH') &&
                !purchaseOrderNo.startsWith('NCGSC') &&
                !purchaseOrderNo.startsWith('NCGSW') &&
                !purchaseOrderNo.startsWith('NCGHC')) {
                exceptionTypeArray = exceptionTypeArray.filter(function(item) {
                    return item.id != 12;
                });
            }
            $("input[name='whCheckIn.whCheckInException.exceptionType']").select2({
                data : exceptionTypeArray,
                placeholder : "异常类型",
                multiple: true,
                allowClear : true
            });
			var checkInPackageAttrArray = ${domain.checkInPackageAttrs};
			$("input[name='whCheckIn.whCheckInItem.checkInPackageAttr']").select2({
				data : checkInPackageAttrArray,
				placeholder : "来货包装属性",
				multiple: false,
				allowClear : true
			});
            $(document).ready(function(){
                // 检查每个SKU行是否需要显示多货按钮
               /* $('.purchase-tr').each(function() {
                    var $tr = $(this);
                    var purchaseQuantity = parseInt($tr.find('.purchase-quantity-minus-revoked').text() || 0);
                    var checkInQuantity = parseInt($tr.find('.check_in_quantity').text() || 0);
                    
                    // 获取当前行的索引
                    var trId = $tr.attr('id');
                    var matches = trId.match(/purchase-tr-(\d+)-(\d+)/);
                    if (matches) {
                        var parentIndex = matches[1];
                        var itemIndex = matches[2];
                        
                        // 如果采购数量减去取消在途等于已入库数量，显示多货按钮
                        if (purchaseQuantity === checkInQuantity) {
                            $("#excess-btn-" + parentIndex + "-" + itemIndex).show();
                        }
                    }
                });*/
                
                // 保证(入库数量 + 已入库数量 + 已入库数量)小于等于采购数量
                $(".instock-quantity").blur(function(){
                    var $this = $(this);
                    if($this.val() == ''){
                        $this.val("");
                        return;
                    }

                    var reg = /^\+?[1-9][0-9]*$/;
                    if (!reg.test($this.val())) {
                        $this.val("");
						layer.alert("请输入正确的正整数");
                        return;
                    }
					debugger;

                    // 当前入库数量
                    var currentInstockQuantity = parseInt($this.val());

                    // 所在的行
                    var currentTr = $this.closest(".purchase-tr");

                    // 已入库数量
                    var instockedQuantity = parseInt(currentTr.find(".instocked-quantity").text());

                    //从采购单生成入库单的数量（入库单未确认入库数）
                    var undefinedInQuantity = parseInt(currentTr.find(".undefined-quantity").text());

                    // 取消在途数量
                    var revokedQuantity = parseInt(currentTr.find(".revoked-quantity").text());
                    // 待取消在途
                    var waitHandlerQ = parseInt(currentTr.find(".waitHandlerQ").text());

                    // 采购数量
                    var purchaseQuantity = parseInt(currentTr.find(".purchase-quantity").text());

                    //当前入库数+未确认入库数量+已入库数量+取消在途数量 >采购数量
                    if((currentInstockQuantity + undefinedInQuantity + revokedQuantity + instockedQuantity + waitHandlerQ) > purchaseQuantity) {
                        if((purchaseQuantity - undefinedInQuantity-instockedQuantity-revokedQuantity - waitHandlerQ)<0){
                            $this.val("");
                        } else{
                            $this.val("");
                        }
                    }
                    // 同时修改打印SKU的数量
                    $this.next().find(".whCheckInItems-quantity").val($this.val());
                });

                // 异常数量失去焦点校验
                $(".exception-quantity").blur(function(){
                    var $this = $(this);
                    if($this.val() == ''){
                        $this.val("");
                        return;
                    }
                    var reg = /^\+?[1-9][0-9]*$/;
                    if (!reg.test($this.val())) {
                        $this.val("");
						layer.alert("请输入正确的正整数");
                        return;
                    }
                    var currentExceptionQuantity = parseInt($this.val()== "" ? 0 : $this.val());
                    // 所在的行
                    var currentTr = $this.closest(".purchase-tr");
                    var cancelHandleStatus = currentTr.find("[name='purchaseOrderItem.cancelHandleStatus']").val();
                    // 待取消在途
                    var waitHandlerQ = 0;
                    if(cancelHandleStatus == 'wait_Handler' || cancelHandleStatus == 'part_Handler'){
                        //待发处理状态  "wait_Handler","待处理","part_Handler","部分处理","already_Handler","已处理"
                        waitHandlerQ = parseInt(currentTr.find(".waitHandlerQ").text());
                    }
                    // 待取消在途异常
                    var waitHandlerQEx = parseInt(currentTr.find(".waitHandlerQEx").text());
                    var exceptionType = currentTr.find("[name='whCheckIn.whCheckInException.exceptionType']").val();
                    if(exceptionType == '26' && currentExceptionQuantity > 0 && (waitHandlerQEx + currentExceptionQuantity) > waitHandlerQ){
                        $this.val("");
                        return;
                    }
                });

                var images = null;
                var currentImageIndex = null;
            }); // end ready

            // 校验周转码
            function checkBox(obj, parentIndex, index,type){

                if(!obj.value || obj.value.replace(/\s/g,'') == ''){
					layer.alert("请输入周转码!");
                    return ;
                }
                var boxNo = obj.value.replace(/\s/g,'');
                if(boxNo.length < 4){
					layer.alert("请输入正确的周转码!");
                    return ;
                }
                var flag = hasUsedBoxNo(boxNo);
                if(flag){
					layer.alert("该周转码已锁定，请重新输入!");
                    return ;
                }

                if(obj.id == $("#boxNo-"+ parentIndex + "-" + index).attr("id")){
                    $("#boxNo-"+ parentIndex + "-" + index).val('');// 清空中转码
                }
                if(obj.id == $("#exBoxNo-"+ parentIndex + "-" + index).attr("id")){
                    $("#exBoxNo-"+ parentIndex + "-" + index).val('');// 清空中转码
                }

                if(boxNo==""){
					layer.alert("请输入周转码！");
                    return false;
                } else{
                    // 验证有效性
                    $.ajax({
                        url : CONTEXT_PATH + "checkin/scans/createCheckIn/checkBox",
                        data : {boxNo : boxNo, type:type},
                        success : function(json){
                            if (json.status == '500') {
                                layer.alert(json.message);
                            } else if (json.status == '200') {
                                //buildInStock(parentIndex, index, boxNo);
                                if(obj.id == $("#exBoxNo-"+ parentIndex + "-" + index).attr("id")){
                                    $("#purchase-tr-" + parentIndex + "-" + index).find("[name='whCheckIn.whCheckInException.boxNo']").val(boxNo);// 塞入异常中转码
                                    $("#exBoxNo-"+ parentIndex + "-" + index).next(".span-boxNo").text(boxNo);// 塞入异常中转码
                                }

                            }
                        },
                        error:function(){
                            layer.alert('校验失败，请重新扫描周转码!');
                        }
                    });
                }

            }

            // 单个生成入库单
            function buildInStock(parentIndex, index, printOneUuid) {
                // 当前行
                var currentTr = $("#purchase-tr-" + parentIndex + "-" + index);
                var valuableProduct = currentTr.find("[name='specialType']").val();
				debugger;
                var cancelHandleStatus = currentTr.find("[name='purchaseOrderItem.cancelHandleStatus']").val();
                /*if(cancelHandleStatus == 'wait_Handler' || cancelHandleStatus == 'part_Handler'){
                    //待发处理状态  "wait_Handler","待处理","part_Handler","部分处理","already_Handler","已处理"
                    alert("当前采购单SKU还有采购未处理的取消在途任务，请联系采购处理后再入库", 'error');
                    return;
				}*/

                // 当前入库数量
                var currentInstockQuantity = parseInt(currentTr.find('.instock-quantity').val() == '' ? 0 : currentTr.find('.instock-quantity').val());

                //当前入库异常数量
                var currentExceptionQuantity = parseInt(currentTr.find(".exception-quantity").val() == "" ? 0 : currentTr.find(".exception-quantity").val());

                // 已入库数量
                var instockedQuantity = parseInt(currentTr.find(".instocked-quantity").text());

                //从采购单生成入库单的数量（入库单未确认入库数）
                var undefinedInQuantity = parseInt(currentTr.find(".undefined-quantity").text());
                // 取消在途数量
                var revokedQuantity = parseInt(currentTr.find(".revoked-quantity").text());
                // 待取消在途
                var waitHandlerQ = 0;
                if(cancelHandleStatus == 'wait_Handler' || cancelHandleStatus == 'part_Handler'){
                    //待发处理状态  "wait_Handler","待处理","part_Handler","部分处理","already_Handler","已处理"
                    waitHandlerQ = parseInt(currentTr.find(".waitHandlerQ").text());
                }
                // 待取消在途异常
                var waitHandlerQEx = parseInt(currentTr.find(".waitHandlerQEx").text());

                // 采购数量
                var purchaseQuantity = parseInt(currentTr.find(".purchase-quantity").text());

				//分批入库数量
				var matchQuantity = parseInt(currentTr.find(".match-quantity").text());

                //入库异常数量
                //var exceptionQuantity = parseInt(currentTr.find(".dbexception-quantity").text());

                //判断是否已校验异常中转码
                var exBoxNo = $("#purchase-tr-" + parentIndex + "-" + index).find("[name='whCheckIn.whCheckInException.boxNo']").val();

                var exceptionType = $("#item-exception-type-" + parentIndex + "-" + index).val();

                //当前入库数+未确认入库数量+已入库数量+取消在途数量+入库异常数量+当前入库异常数量 + 待取消
                var count = currentInstockQuantity + undefinedInQuantity + revokedQuantity + instockedQuantity + waitHandlerQ;

                var reg = /^\+?[1-9][0-9]*$/;
                if (currentInstockQuantity > 0 && !reg.test(currentInstockQuantity)) {
                    layer.alert("良品数量输入错误，请输入正确的正整数");
                    return;
                }
				debugger;
				if (matchQuantity>0 &&  currentInstockQuantity > matchQuantity){
					layer.alert("请确认实物数量后按照提示的分批入库数量先提交第一次入库单，其余数量再进行第二次填写提交");
					return;
				}
				debugger;
				// 最多可入数量
				/*var allowHandlerQuantity = currentInstockQuantity;
				var allowHandlerQText = currentTr.find(".allowHandlerQ").text();
                if (allowHandlerQText != "") {
                    allowHandlerQuantity = parseInt(allowHandlerQText);
                }*/
				if( currentExceptionQuantity >0 && !reg.test(currentExceptionQuantity)){
					layer.alert("异常数量输入错误，请输入正确的正整数");
					return;
				}
				/*if (allowHandlerQuantity != null &&  currentInstockQuantity+currentExceptionQuantity > allowHandlerQuantity){
					layer.alert("本次入库良品数量不能大于最多可入数量");
					return;
				}*/


				var allowHandFlag = 0;
			/*	debugger;
				// 最多可入数量
				var allowHandlerQuantity = currentInstockQuantity;
				var allowHandlerQText = currentTr.find(".allowHandlerQ").text();
				var allowHandFlag = 0;
				if (allowHandlerQText != "") {
					allowHandlerQuantity = parseInt(allowHandlerQText);
					if (currentInstockQuantity+currentExceptionQuantity < (allowHandlerQuantity)) {
						allowHandFlag = 1;
					}
				}*/

                //良品数量 不能大于入库数量
                if( count > purchaseQuantity) {
					layer.alert("当前入库良品数量不能大于采购数量！");
                    return;
                }

                //点数异常类型为 11.少货 时，不需要绑定异常周转筐
                if(exceptionType != "" && exceptionType != 11 && exBoxNo==""){
					layer.alert("请先绑定异常周转码！");
                    return;
                }
                if((currentExceptionQuantity > 0 || exBoxNo != '' || $('#exceptionComment').val() != '') && exceptionType == ''){
					layer.alert("请输入异常类型！");
                    return;
                }
                if(currentInstockQuantity <= 0 && currentExceptionQuantity <= 0){
					layer.alert("良品数量或不良品数量为空，请重新输入！");
                    return;
                }

				if(exceptionType == '26' && currentExceptionQuantity > 0 && (waitHandlerQEx + currentExceptionQuantity) > waitHandlerQ){
					layer.alert("待取消异常数量不能大于待取消数量，请重新输入！");
					return;
				}

                // 生成入库单
				var receiveBoxNo = $('#span-boxNo').text();
                var arrayTrackingNo = $('#returnTrackingNos').val();
                var trackingNumbers = $('#trackingNumbers').val();
                var oldExpressId = $('#oldExpressId').val();

                // if(arrayTrackingNo != ''){
                //     arrayTrackingNo = arrayTrackingNo.substring(1,arrayTrackingNo.length-1);
				// }
                var trackingNo = $("#common-param-" + parentIndex ).find("[name='whCheckIn.trackingNumber']").val();
				// 设置来货是否加工属性
				var val=currentTr.find("[name='item.whSku.checkInProcess']").find("option:selected").val();//查询value值
				currentTr.find("[name='whCheckIn.whCheckInItem.checkInProcess']").val(val);

				var params = $("#common-param-" + parentIndex + ", #purchase-tr-" + parentIndex + "-" + index).regionSerialize()
					+"&receiveBoxNo="+receiveBoxNo+"&trackingNumbers="+trackingNumbers+"&arrayTrackingNo="+arrayTrackingNo+"&purchaseQuantity="+purchaseQuantity;

                if(oldExpressId && oldExpressId.indexOf("RKYC") != -1){
                    $("#common-param-" + parentIndex ).find("[name='whCheckIn.checkInType']").val('7');
                    params = $("#common-param-" + parentIndex + ", #purchase-tr-" + parentIndex + "-" + index).regionSerialize()
                            +"&receiveBoxNo="+receiveBoxNo+"&trackingNumbers="+trackingNumbers+"&arrayTrackingNo="+arrayTrackingNo
                            + "&whCheckIn.exceptionBoxNo="+oldExpressId+"&purchaseQuantity="+purchaseQuantity;
                }
                // 校验服装数据
				if(!chekclothingData(parentIndex, index)){
					return false;
				}else {
				    var clothingTa = $("#clothing-tab-" + parentIndex + "-" + index);
				    if(clothingTa != null){
                        params = params + "&" + $(clothingTa).regionSerialize();
					}
				}

				// var expParam = $("#single-build-btn-" + parentIndex + "-" + index).parent().find("div[name='restricted-storage']").regionSerialize();
				// params = params + "&" + expParam;


				if (valuableProduct && valuableProduct == 2009) {
					if (confirm("SKU【" + currentTr.find("[name='whCheckIn.whCheckInItem.sku']").val() + "】为贵重物品，请确认是否提交?")) {
						doBuild(currentTr, parentIndex, index, printOneUuid, arrayTrackingNo, trackingNo, exceptionType, currentExceptionQuantity, params,matchQuantity,currentInstockQuantity,allowHandFlag)
					}
				} else {
					doBuild(currentTr, parentIndex, index, printOneUuid, arrayTrackingNo, trackingNo, exceptionType, currentExceptionQuantity, params,matchQuantity,currentInstockQuantity,allowHandFlag);
				}
			}

			function doBuild(currentTr, parentIndex, index, printOneUuid, arrayTrackingNo, trackingNo, exceptionType, currentExceptionQuantity, params,matchQuantity,stockQuantity,allowHandFlag) {
				$.ajax({
					url: "${CONTEXT_PATH}checkin/scans/checkIn",
					type: "POST",
					dataType: "json",
					data: params,
					error: function () {
						App.unblockUI();
						layer.alert("系统内部出错，请稍后重试。");
					},
					beforeSend: function () {
						App.blockUI();
					},
					success: function (data) {
						App.unblockUI();
						if (data.exceptionCode && data.exceptionCode == '50000000') {
							$.error(data.message);
							return;
						}
						if (data.status == 500) {
							layer.alert('点数入库失败！错误信息：' + data.message);
							return;
						}
						debugger;
						// 设置入库单ID
						if (data.body.inStocks && data.body.inStocks.length > 0) {
							if (arrayTrackingNo != '') {
								arrayTrackingNo = arrayTrackingNo.split(",");
							}
							if (arrayTrackingNo != '' && arrayTrackingNo.length > 0) {
								$.each(arrayTrackingNo, function (i, value) {
									$("#" + escapeJquery(value)).remove();
									if (value.split('').length > 7) {
										rowHtml = "<p class='already-send' id='" + value + "'>" + value.substr(0, 3) + '****' + value.substr(7, value.split('').length) + "</p>";
									} else {
										rowHtml = "<p class='already-send' id='" + value + "'>" + value.substr(0, 3) + '****' + "</p>";
									}
									$(".trackingNo-row").append(rowHtml);
								});
							} else {
								$("#" + trackingNo).remove();
								if (trackingNo.split('').length > 7) {
									rowHtml = "<p class='already-send' id='" + trackingNo + "'>" + trackingNo.substr(0, 3) + '****' + trackingNo.substr(7, trackingNo.split('').length) + "</p>";
								} else {
									rowHtml = "<p class='already-send' id='" + trackingNo + "'>" + trackingNo.substr(0, 3) + '****' + "</p>";
								}
								$(".trackingNo-row").append(rowHtml);
							}
							/*$("#"+trackingNo).css('color','red');
                            $("#"+trackingNo).attr('class','already-send');*/

							$("#purchase-tr-" + parentIndex + "-" + index).find('.span-boxNo').text('');// 清空中转码
							$("#purchase-tr-" + parentIndex + "-" + index).find("[name='whCheckIn.whCheckInException.quantity']").val('');
							$("#purchase-tr-" + parentIndex + "-" + index).find("[name='whCheckIn.whCheckInException.exceptionComment']").val('');
							$("#purchase-tr-" + parentIndex + "-" + index).find("[name='whCheckIn.whCheckInException.exceptionType']").val('');
							$("#purchase-tr-" + parentIndex + "-" + index).find("[name='whCheckIn.whCheckInException.boxNo']").val('');
							$("#purchase-tr-" + parentIndex + "-" + index).find("[name='whCheckIn.whCheckInItem.checkInPackageAttr']").val('');
							$("#s2id_item-exception-type-" + parentIndex + "-" + index).find("li.select2-search-choice").remove();
							// calsf();// 计算成功数
							var inStock = data.body.inStocks[0];
							$("#hidden-div-" + parentIndex + "-" + index).find("[name='i']").val(inStock.inId);
							$("#hidden-div-" + parentIndex + "-" + index).find("[name='l']").val(inStock.locationNumber);

							if (inStock.successCount != undefined && inStock.successCount != '') {
								$('#panel-title').html('<h1>成功：<b>'+inStock.successCount+'</b></h1>');
							}

							var shelfStraightHair = data.body.isShelfStraightHair;

							//是否免检
							var freeCheck = data.body.isFreeCheck;

							var noLabel = data.body.noLabel;

							// 计件
							calsfPiece(inStock.totalInQuantity);
							debugger;
							// iframe跳转打印页面
							var printPageUrl = '${CONTEXT_PATH}checkins/qr?noLabel=' + noLabel;

							if (printOneUuid) {
								$("#hidden-div-" + parentIndex + "-" + index).find(".whCheckInItems-quantity").val(1);
							}

							var expParam = $("#single-build-btn-" + parentIndex + "-" + index).parent().find("div[name='restricted-storage']").regionSerialize();

							var purchaseOrderNo = $("input[name='whCheckIn.purchaseOrderNo']").regionSerialize();
							var afterSaleQty = $("#purchase-tr-" + parentIndex + "-" + index).find("input[name='whCheckIn.afterSaleQty']").regionSerialize();

							debugger;
							$('#printHtml').attr('src', printPageUrl + "&" + expParam + "&" + $("#hidden-div-" + parentIndex + "-" + index).regionSerialize() + "&" + purchaseOrderNo + "&" + afterSaleQty + "&whCheckIn.shelfStraightHair=" + shelfStraightHair + "&whCheckIn.freeCheck=" + freeCheck);

							currentTr.css('background-color', '#AFEEEE');// 成功后改变背景颜色
							$('#oldExpressIdFinished').val('true');//标记当前快递已完成

							//自动打印
							setTimeout(IframeOnloadPrint, 100);
							if (shelfStraightHair){
								layer.alert("当前入库单不进行上架，由包装员直接领取，请将打印的提示标签放在周转筐中明显的位置");
							}
							if (freeCheck){
								layer.alert("当前入库单免检");
							}
						/*	debugger;
							if (allowHandFlag == 1) {
								layer.alert("已成功提交入库单，未入库数量请继续提交入库或者异常，如果不继续提交请将剩余货品放回异常周转筐中！");
							}*/
							// 清空中间区域
							currentTr.find('.instock-quantity').each(function () {

								var $this = $(this);

								// 当前入库数量
								var currentInstockQuantity = parseInt($this.val() == '' ? 0 : $this.val());

								// 已入库数量
								var instockedQuantity = parseInt(currentTr.find(".instocked-quantity").text());

								//从采购单生成入库单的数量（入库单未确认入库数）
								var undefinedInQuantity = parseInt(currentTr.find(".undefined-quantity").text());

								// 取消在途数量
								var revokedQuantity = parseInt(currentTr.find(".revoked-quantity").text());

								// 待取消在途
								var waitHandlerQ = parseInt(currentTr.find(".waitHandlerQ").text());
								// 待取消在途异常
								var waitHandlerQEx = parseInt(currentTr.find(".waitHandlerQEx").text());

								var purchaseQuantity = parseInt(currentTr.find(".purchase-quantity").text());// 采购数量

								//需要分批入库数量
								var matchQuantity = parseInt(currentTr.find(".match-quantity").text());
								if (shelfStraightHair){
									var neededQuantity=matchQuantity - currentInstockQuantity>=0?matchQuantity - currentInstockQuantity:0;
									currentTr.find(".match-quantity").html(neededQuantity);
									currentTr.find(".needed-quantity").html('需分批入库'+neededQuantity);
									debugger;
									if (neededQuantity==0){
										currentTr.find(".needed-quantity").html('');
										$("#purchase-tr-" + parentIndex + "-" + index).find("input[name='whCheckIn.whCheckInItem.firstOrderType']").val('');
									}
								}

								//当前入库数+未确认入库数量+已入库数量 +取消在途>采购数量
								var waitHandlerQException = 0;
								if (exceptionType == '26' && currentExceptionQuantity > 0) {
									//waitHandlerQException = currentExceptionQuantity;
									waitHandlerQEx = waitHandlerQEx + currentExceptionQuantity
									currentTr.find(".waitHandlerQEx").html(waitHandlerQEx);
								}
								debugger;
								/*var allowHandlerQText = currentTr.find(".allowHandlerQ").text();
								if (allowHandlerQText != "") {
									allowHandlerQuantity = parseInt(allowHandlerQText);
									currentTr.find(".allowHandlerQ").html(allowHandlerQuantity - currentExceptionQuantity - stockQuantity);
								}*/

								if ((currentInstockQuantity + undefinedInQuantity + instockedQuantity + revokedQuantity + waitHandlerQEx) >= purchaseQuantity) {
									$("#single-build-btn-" + parentIndex + "-" + index).remove();
									$("#print-one-uuid-" + parentIndex + "-" + index).remove();
								}

								// 未确认,当前入库数量数量改变
								currentTr.find(".undefined-quantity").html(undefinedInQuantity + currentInstockQuantity);
								currentTr.find(".check_in_quantity").html(undefinedInQuantity + currentInstockQuantity + instockedQuantity);
								$this.val('');
								$("#hidden-div-" + parentIndex + "-" + index).find(".whCheckInItems-quantity").val(0);
								$("#purchase-exp-" + parentIndex + "-" + index).find('input').val('');
								currentTr.find("div[name='restricted-storage']").find('input:not([name="purchaseNo"])').val('');
								currentTr.find("div[name='restricted-storage']").find("input[name='whCheckIn.restrictedStorage']").val(0);
								$("#purchase-exp-"+parentIndex+"-"+index).find("span[class='expCheckMsg']").text("");
								// 显示多货按钮
								var purchaseOrderNo = $("#common-param-" + parentIndex).find("[name='whCheckIn.purchaseOrderNo']").val();
								if (currentInstockQuantity > 0 &&
								    !purchaseOrderNo.startsWith('NCGYP') && 
								    !purchaseOrderNo.startsWith('NCGZS') && 
								    !purchaseOrderNo.startsWith('NCGCW') && 
								    !purchaseOrderNo.startsWith('NCGOYP') && 
								    !purchaseOrderNo.startsWith('NCGHH') &&
								    !purchaseOrderNo.startsWith('NCGSC') &&
								    !purchaseOrderNo.startsWith('NCGSW') &&
								    !purchaseOrderNo.startsWith('NCGHC')) {
									$("#excess-btn-" + parentIndex + "-" + index).show();
								}
							}); //end each
							debugger;
							if (!matchQuantity || matchQuantity <= 0) {
								const $tbody = $("#table-purchase-order-"+parentIndex).find('tbody')[0];
								const selector = "#update-image-tab-"+parentIndex+"-"+index +","+
										"#purchase-tr-"+parentIndex+"-"+index +","+
										"#special-tag-"+parentIndex+"-"+index+","+
										"#purchase-exp-"+parentIndex+"-"+index+","+
										"#purchase-excess-"+parentIndex+"-"+index+","+
										"#clothing-tab-"+parentIndex+"-"+index+","+
										"#count-tab-"+parentIndex+"-"+index+","+
										"#recent-exception-"+parentIndex+"-"+index+","+
										"#separator-row-"+parentIndex+"-"+index;
								const $rowsToMove = $(selector);
								$rowsToMove.detach().appendTo($tbody);

								$rowsToMove.css('background-color', '#B6CEA3');
								
								// 更新所有相关元素的ID和属性中的索引值
								$rowsToMove.each(function() {
									var $row = $(this);
									var oldId = $row.attr('id');
									if (oldId) {
										// 提取旧的索引
										var idMatch = oldId.match(/-(\\d+)-(\\d+)/);
										if (!idMatch) return;
										var oldParentIndex = idMatch[1];
										var oldItemIndex = idMatch[2];

										// 更新行ID
										var newId = oldId.replace(/-(\\d+)-(\\d+)/, '-' + parentIndex + '-' + index);
										$row.attr('id', newId);

										// 更新行内所有相关元素的ID和属性
										$row.find('[id*=\"-' + oldParentIndex + '-' + oldItemIndex + '\"]').each(function() {
											var $elem = $(this);
											var elemId = $elem.attr('id');
											if (elemId) {
												var newElemId = elemId.replace(/-(\\d+)-(\\d+)/, '-' + parentIndex + '-' + index);
												$elem.attr('id', newElemId);
											}
										});

										// 更新onclick事件中的索引值
										$row.find('[onclick*=\"' + oldParentIndex + ',' + oldItemIndex + '\"]').each(function() {
											var $elem = $(this);
											var onclick = $elem.attr('onclick');
											if (onclick) {
												var newOnclick = onclick.replace(/(\\d+),\\s*(\\d+)/g, parentIndex + ',' + index);
												$elem.attr('onclick', newOnclick);
											}
										});
									}
								});
							}


						} else {
							layer.alert("生成入库单失败，请重新扫描后再试！错误信息: " + data.message);
						}  // end length

						$('#expressId').val("");
						$('#expressId').focus();
					}
				}); // end ajax
			}


            $('#finish-checkIn').on('click',function () {
                var purchaseOrderArrayStr = $('#purchaseOrderArrayStr').text();
				var returnTrackingNos = $('#returnTrackingNos').val().replace(/\s/g,'');
				var receiveBoxNo = $('#span-boxNo').text();
				var expressId = $('#oldExpressId').val();
                if(purchaseOrderArrayStr && purchaseOrderArrayStr != ''){
                    $.ajax({
                        url : CONTEXT_PATH + "checkin/scans/batchCreateWhCheckInException",
                        type:"POST",
						data: {
							purchaseOrderArrayStr: purchaseOrderArrayStr,
							intersection: returnTrackingNos,
							boxNo: receiveBoxNo,
							expressId: expressId
						},
                        success : function(data){
                            if(data.status == 200){
								//清除免QC标记
								$("input[name='whCheckIn.whCheckInItem.isFreeCheck']").each(function () {
									$(this).val(false);
								});
								//清除带 span class="isFreeCheck" 的值
								$("span[class='isFreeCheck']").each(function () {
									$(this).text('');
								});
								finishFlag = true;
                                layer.alert("成功！");
                            } else {
                                layer.alert("批量生成异常失败," + data.message);
                            }
                        },
                        error:function(){
                            layer.alert('批量生成异常失败!');
                        }
                    });
                }else {
                    layer.alert("没有相关采购单！");
				}
            });
            // 保存备注
            function saveRemark( parentIndex, index) {
                var remark = $("#item-remark-" + parentIndex + "-" + index).val();
                if(remark != "") {
                    $("#item-preremark-" + parentIndex + "-" + index).text(remark);
                    $("#item-remark-" + parentIndex + "-" + index).val("");
                }
            }

            function getSkuImage(sku){
                dialog({
                    title: sku+'相关图片',
					url: CONTEXT_PATH + "skus/getSkuImgs?sku="+sku,
					width: 800,
					height: 680,
					top: 0
				}).showModal();
				$('.ui-dialog').css('left', '250px');

            }

            function hasUsedBoxNo(inputBoxNo){
                var flag = false;
                var boxNoSpans = $('.span-boxNo');
                for (var i = 0; i < boxNoSpans.length; i++) {
                    var boxNoSpan = boxNoSpans[i];
                    var boxNo = $(boxNoSpan).text();
                    if(boxNo !=null && boxNo!='' && boxNo == inputBoxNo){
                        flag = true;
                        break;
                    }
                }
                return flag;
            }

            function showHistoryExceptionList(exceptionType,parentIndex,index) {
                var sku = $("#purchase-tr-" + parentIndex + "-" + index).find("[name = 'whCheckIn.whCheckInItem.sku']").val();
                $.ajax({
                    url : "${CONTEXT_PATH}checkInException/getRecentThreeExceptionsByExceptionType?sku="+sku+"&exceptionType="+exceptionType,
                    type: "GET",
                    dataType : "json",
                    error : function() {
                        App.unblockUI();
                        layer.alert("系统内部出错，请稍后重试。");
                    },
                    beforeSend : function() {
                        App.blockUI();
                    },
                    success: function(data) {
                        App.unblockUI();
                        if(data.body.recentThreeExceptions){
                                var html = "";
                            for (var i = 0; i < data.body.recentThreeExceptions.length; i++ ){
                                var exception = data.body.recentThreeExceptions[i];
                                var firstEditHandleComment = '';
                                var lastPurchaseHandleComment = '';
                                var exceptionHandleWayName = '';
                                var image = '';
                                var creationDate = '';
                                if(exception.firstEditHandleComment){
                                    firstEditHandleComment = exception.firstEditHandleComment;
                                }
                                if(exception.lastPurchaseHandleComment){
                                    lastPurchaseHandleComment = exception.lastPurchaseHandleComment;
                                }
                                if(exception.exceptionHandleWayName){
                                    exceptionHandleWayName = exception.exceptionHandleWayName;
                                }
                                if(exception.images){
                                    image = exception.images[0];
                                }
                                if(exception.creationDate){
                                    creationDate = new Date(exception.creationDate).format("yyyy-MM-dd hh:mm:ss");
                                }
                                html += "<tr>"
                                        +"<td>" + exception.id + "</td>"
                                        +"<td>" + creationDate + "</td>"
                                        +"<td>" + exception.exceptionTypeName + "</td>"
                                        +"<td>" + exception.exceptionStatusName + "</td>"
                                        +"<td>" + firstEditHandleComment + "</td>"
                                        +"<td>" + lastPurchaseHandleComment + "</td>"
                                        +"<td>" + exceptionHandleWayName + "</td>"
                                        +"<td> <input type='hidden' id ='img-"+exception.id+"' value='"+exception.images+"'/>"
                                        + "<img onclick='getExceptionImages("+exception.id+")'  border = '0' width='65px' height='65px' src='"+image+"'></td>"
                                        +"</tr>";
                            }
                            $("#recent-exception-" + parentIndex + "-" + index).find("tbody").html(html);
                            $("#recent-exception-" + parentIndex + "-" + index).css("display","table-row");
                        }
                    }
                });// end ajax
            }

            function expand(obj,parentIndex,index){
                if ($(obj).hasClass('expand')) {
                    $(obj).text('【查看】');
                    $("#count-tab-" + parentIndex + "-" + index).find('table').css("display","none");
                    $("#recent-exception-" + parentIndex + "-" + index).css("display","none");
                    $(obj).removeClass('expand');
                } else {
                    $(obj).text('【收起】');
                    $(obj).addClass('expand');
                    $("#count-tab-" + parentIndex + "-" + index).find('table').css("display","table");
                }
            }

            function getExceptionImages(exceptionId){
                var  img = $("#img-"+exceptionId).val();
                var images = null;
                if(img!=null && img !=''){
                    images = img.split(",");
                }
                dialog({
                    title: '相关异常图片',
                    url: CONTEXT_PATH + "checkInException/getExceptionImages?exceptionImages="+images,
                    width: 800,
                    height: 680,
                    top: 0
                }).showModal();
            }
			// 提交修改来货是否加工
            function updateSkuCheckInProcess(ziji,sku) {
				var val=$(ziji).find("option:selected").val();//查询value值
				$.ajax({
					url : CONTEXT_PATH + "skus/updateCheckInProcess",
					type:"GET",
					data : {sku : sku,checkInProcess:val},
					success : function(data){
						if(data.status == 200){
							layer.alert("成功！");
						} else {
							layer.alert("修改来货是否加工失败," + data.message);
						}
					},
					error:function(){
						layer.alert('修改来货是否加工失败!');
					}
				});
			}

			// 选择异常类型
			function changeExceptionType(obj) {
				var objDev = $(obj).parent().parent();
				var exceptionType = $(obj).val();
				if (exceptionType && exceptionType.indexOf('29') >= 0) {
					$(objDev).find('.exception-comment-td').find('button').css("display", "block");
				} else {
					$(objDev).find('.exception-comment-td').find('button').css("display", "none");
				}
			}

			function clickComment1(obj) {
				$(obj).parent().find("input[id='exceptionComment']").val($(obj).text());
			}

			function clickComment2(obj) {
				$(obj).parent().find("input[id='exceptionComment']").val($(obj).text());
			}

			function showExcessModule(parentIndex, itemIndex, sku, purchaseOrderNo) {
				var rowId = "#purchase-excess-" + parentIndex + "-" + itemIndex;
				$(rowId).toggle();
				// 设置sku到多货处理模块的隐藏input
				$("#currentExcessSku-" + parentIndex + "-" + itemIndex).val(sku);
				$("#currentExcessPurchaseOrderNo-" + parentIndex + "-" + itemIndex).val(purchaseOrderNo);
				// 判断"计算入库单"按钮是否显示
				//var calcBtn = $("#calc-instock-btn-" + parentIndex + "-" + itemIndex);
				//if (calcBtn.css('display') !== 'none') {
					$(rowId).css('background-color', '#f8f8f8');
				//} else {
				//	$(rowId).css('background-color', '');
				//}
			}

			// 在 script 标签内添加提交按钮的点击事件处理
			function buildExcessInStock(parentIndex, itemIndex) {
				debugger;
				
				// 检查是否有异常周转框需要校验
				var $excessTable = $('#excess-table-wrap-' + parentIndex + '-' + itemIndex);
				var $exceptionBoxInput = $excessTable.find('.excess-exception-boxno');
				
				if ($exceptionBoxInput.length > 0 && !$exceptionBoxInput.prop('disabled')) {
					// 如果异常周转框存在且未禁用，需要校验
					var boxNoValue = $exceptionBoxInput.val();
					if (!boxNoValue || boxNoValue.replace(/\s/g,'') == '') {
						layer.alert("请输入异常周转码!");
						return;
					}
					
					var boxNo = boxNoValue.replace(/\s/g,'');
					if (boxNo.length < 4) {
						layer.alert("请输入正确的异常周转码!");
						return;
					}
					
					// 检查是否已被使用
					var flag = hasUsedBoxNo(boxNo);
					if (flag) {
						layer.alert("该周转码已锁定，请重新输入!");
						return;
					}
					
					// 验证有效性 - 同步验证
					var isValidBox = false;
					$.ajax({
						url : CONTEXT_PATH + "checkin/scans/createCheckIn/checkBox",
						data : {boxNo : boxNo, type:'exception'},
						async: false, // 使用同步请求，确保校验完成后才继续
						success : function(json){
							if (json.status == '500') {
								layer.alert(json.message);
								$exceptionBoxInput.val('');
								return;
							} else if (json.status == '200') {
								// 校验成功，可以继续提交
								isValidBox = true;
							}
						},
						error:function(){
							layer.alert('校验失败，请重新扫描周转码!');
							$exceptionBoxInput.val('');
							return;
						}
					});
					
					// 如果校验失败，停止提交
					if (!isValidBox) {
						return;
					}
				}

				// 获取多货数量
				var excessQuantity = $('#excessQuantity-' + parentIndex + '-' + itemIndex).val();
				if(!excessQuantity || excessQuantity <= 0) {
					layer.alert('请输入有效的多货数量');
					return;
				}

				// 获取表格中的多货匹配数据
				var excessList = [];
				// 获取多货异常数量和异常周转框的值
				var excessExceptionQuantity = parseInt($('#excess-table-body-' + parentIndex + '-' + itemIndex + ' input[name="excessExceptionQuantity"]').val() || 0);
				var excessExceptionBoxNo = $('#excess-table-body-' + parentIndex + '-' + itemIndex + ' input[name="excessExceptionBoxNo"]').val() || '';
				
				// 计算实际多货数量：如果有异常数量，需要从原始多货数量中减去异常数量
				if (excessExceptionQuantity > 0) {
					excessQuantity = excessQuantity - excessExceptionQuantity;
					if (excessQuantity < 0) {
						layer.alert('异常数量不能大于多货数量');
						return;
					}
				}

				// 获取第一行的SKU和多货数量(因为使用了rowspan)
				var firstRow = $('#excess-table-body-' + parentIndex + '-' + itemIndex + ' tr:first');
				if (firstRow.length > 0) {
					// 正确的列索引：第一行包含rowspan列
					// td[0]=图片, td[1]=SKU, td[2]=多货数量, td[3]=采购单号, td[4]=采购员, td[5]=采购数量, td[6]=匹配良品数量, td[7]=处理方式
					var sku = firstRow.find('td').eq(1).text().trim();  // SKU在第2列
					var excessQuantityVal = firstRow.find('td').eq(2).text().trim();  // 多货数量在第3列
					var firstRowExcess = {
						sku: sku,
						purchaseOrderNo: firstRow.find('td').eq(3).text().trim(),  // 采购单号在第4列
						purchaseUser: firstRow.find('input[name="purchaseUser"]').val(),
						purchaseQuantity: parseInt(firstRow.find('td').eq(5).text().trim()) || 0,  // 采购数量在第6列
						excessQuantity: parseInt(excessQuantityVal) || 0,
						matchedQuantity: parseInt(firstRow.find('td').eq(6).text().trim()) || 0,  // 匹配良品数量在第7列
						processingMethod: firstRow.find('input[name="processingMethod"]').val(),
						// 多货异常信息
						excessExceptionQuantity: excessExceptionQuantity,
						excessExceptionBoxNo: excessExceptionBoxNo,
						// Add the 5 new fields
						weight: parseFloat(firstRow.find('input[name="weight"]').val()) || 0,
						shippingCost: parseFloat(firstRow.find('input[name="shippingCost"]').val()) || 0,
						trackingNumber: firstRow.find('input[name="trackingNumber"]').val() || '',
						supplierId: firstRow.find('input[name="supplierId"]').val() || '',
						vendorName: firstRow.find('input[name="vendorName"]').val() || ''
					};
					excessList.push(firstRowExcess);
					// 处理其他行数据 - 其他行没有rowspan列，所以列索引不同
					// td[0]=采购单号, td[1]=采购员, td[2]=采购数量, td[3]=匹配良品数量, td[4]=处理方式
					$('#excess-table-body-' + parentIndex + '-' + itemIndex + ' tr:not(:first)').each(function() {
						var row = $(this);
						var excess = {
							sku: sku,  // 使用第一行的SKU
							purchaseOrderNo: row.find('td').eq(0).text().trim(),  // 采购单号在第1列
							purchaseUser: row.find('input[name="purchaseUser"]').val(),
							purchaseQuantity: parseInt(row.find('td').eq(2).text().trim()) || 0,  // 采购数量在第3列
							excessQuantity: parseInt(excessQuantityVal) || 0,  // 使用第一行的多货数量
							matchedQuantity: parseInt(row.find('td').eq(3).text().trim()) || 0,  // 匹配良品数量在第4列
							processingMethod: row.find('input[name="processingMethod"]').val(),
							// 多货异常信息 - 只在第一行有输入框，其他行共享
							excessExceptionQuantity: excessExceptionQuantity,
							excessExceptionBoxNo: excessExceptionBoxNo,
							// Add the 5 new fields
							weight: parseFloat(row.find('input[name="weight"]').val()) || 0,
							shippingCost: parseFloat(row.find('input[name="shippingCost"]').val()) || 0,
							trackingNumber: row.find('input[name="trackingNumber"]').val() || '',
							supplierId: row.find('input[name="supplierId"]').val() || '',
							vendorName: row.find('input[name="vendorName"]').val() || ''
						};
						excessList.push(excess);
					});
				}

				// 获取入库明细行
				var purchaseTr = $('#purchase-tr-' + parentIndex + '-' + itemIndex);
				// 获取保质期模块
				var purchaseExp = $('#purchase-exp-' + parentIndex + '-' + itemIndex);
				// 获取多货模块
				var purchaseExcess = $('#purchase-excess-' + parentIndex + '-' + itemIndex);
				// 获取hidden-div
				var hiddenDiv = $('#hidden-div-' + parentIndex + '-' + itemIndex);

				// 收集表单数据
				var params = {};
				// 主要入库数据
				params["whCheckIn.purchaseOrderNo"] = $('#currentExcessPurchaseOrderNo-' + parentIndex + '-' + itemIndex).val();
				params["whCheckIn.trackingNumber"] = purchaseTr.find("[name='whCheckIn.trackingNumber']").val();
				params["whCheckIn.warehouseId"] = purchaseTr.find("[name='whCheckIn.warehouseId']").val();
				params["whCheckIn.checkInType"] = 1;
				params["whCheckIn.shippingCost"] = purchaseTr.find("[name='whCheckIn.shippingCost']").val();
				params["whCheckIn.totalWeight"] = purchaseTr.find("[name='whCheckIn.totalWeight']").val();
				// 添加原始入库单ID
				params["whCheckIn.originalInId"] = hiddenDiv.find("[name='i']").val();
				// 移除NO_LABEL标签
				var flags = purchaseTr.find("[name='whCheckIn.flags']").val();
				if(flags) {
					flags = flags.replace('NO_LABEL', '').replace(/,,/g, ',').replace(/^,|,$/g, '');
				}
				params["whCheckIn.flags"] = flags;
				// 添加多货列表数据
				excessList.forEach(function(excess, idx) {
					params["whCheckIn.whCheckInExcessList[" + idx + "].sku"] = excess.sku;
					params["whCheckIn.whCheckInExcessList[" + idx + "].purchaseOrderNo"] = excess.purchaseOrderNo;
					params["whCheckIn.whCheckInExcessList[" + idx + "].purchaseUser"] = excess.purchaseUser;
					params["whCheckIn.whCheckInExcessList[" + idx + "].purchaseQuantity"] = excess.purchaseQuantity;
					params["whCheckIn.whCheckInExcessList[" + idx + "].excessQuantity"] = excess.excessQuantity;
					params["whCheckIn.whCheckInExcessList[" + idx + "].processingMethod"] = excess.processingMethod;
					params["whCheckIn.whCheckInExcessList[" + idx + "].matchedQuantity"] = excess.matchedQuantity;
					// 多货异常信息
					params["whCheckIn.whCheckInExcessList[" + idx + "].differenceQuantity"] = excess.excessExceptionQuantity;
					params["whCheckIn.whCheckInExcessList[" + idx + "].excessExceptionBoxNo"] = excess.excessExceptionBoxNo;
					// Add the 5 new fields using the same pattern as other fields
					params["whCheckIn.whCheckInExcessList[" + idx + "].weight"] = excess.weight;
					params["whCheckIn.whCheckInExcessList[" + idx + "].shippingCost"] = excess.shippingCost;
					params["whCheckIn.whCheckInExcessList[" + idx + "].trackingNumber"] = excess.trackingNumber;
					params["whCheckIn.whCheckInExcessList[" + idx + "].supplierId"] = excess.supplierId;
					params["whCheckIn.whCheckInExcessList[" + idx + "].vendorName"] = excess.vendorName;
				});
				// 入库商品数据
				params["whCheckIn.whCheckInItem.sku"] = $('#currentExcessSku-' + parentIndex + '-' + itemIndex).val();
				params["whCheckIn.whCheckInItem.supplierId"] = purchaseTr.find("[name='whCheckIn.whCheckInItem.supplierId']").val();
				params["whCheckIn.whCheckInItem.purchasePrice"] = purchaseTr.find("[name='whCheckIn.whCheckInItem.purchasePrice']").val();
				params["whCheckIn.whCheckInItem.purchaseQuantity"] = purchaseTr.find("[name='whCheckIn.whCheckInItem.purchaseQuantity']").val();
				params["whCheckIn.whCheckInItem.quantity"] = excessQuantity;
				params["whCheckIn.whCheckInItem.checkInPackageAttr"] = purchaseTr.find("[name='whCheckIn.whCheckInItem.checkInPackageAttr']").val();
				params["whCheckIn.whCheckInItem.isSkipQc"] = purchaseTr.find("[name='whCheckIn.whCheckInItem.isSkipQc']").val();
				params["whCheckIn.whCheckInItem.isFreeCheck"] = false;
				params["whCheckIn.whCheckInItem.firstOrderType"] = purchaseTr.find("[name='whCheckIn.whCheckInItem.firstOrderType']").val();
				params["whCheckIn.whCheckInItem.hasShopifyTJSku"] = purchaseTr.find("[name='whCheckIn.whCheckInItem.hasShopifyTJSku']").val();
				params["whCheckIn.whCheckInItem.checkInProcess"] = purchaseTr.find("[name='whCheckIn.whCheckInItem.checkInProcess']").val();
				
				// 多货异常信息
				if(excessExceptionQuantity > 0) {
					params["whCheckIn.whCheckInException.quantity"] = excessExceptionQuantity;
				}
				if(excessExceptionBoxNo && excessExceptionBoxNo.trim() !== '') {
					params["whCheckIn.whCheckInException.boxNo"] = excessExceptionBoxNo.trim();
				}
				// 特殊类型和售后数量
				params["specialType"] = purchaseTr.find("[name='specialType']").val();
				params["whCheckIn.afterSaleQty"] = purchaseTr.find("[name='whCheckIn.afterSaleQty']").val();
				// 供应商信息
				params["whCheckIn.vendorName"] = purchaseTr.find("[name='whCheckIn.vendorName']").val();
				params["whCheckIn.supplierId"] = purchaseTr.find("[name='whCheckIn.supplierId']").val();
				// 保质期相关数据
				params["whCheckIn.days"] = purchaseExp.find("[name='days']").val();
				params["whCheckIn.proDate"] = purchaseExp.find("[name='proDate']").val();
				params["whCheckIn.expDate"] = purchaseExp.find("[name='expDate']").val();
				params["whCheckIn.expWaitId"] = purchaseExp.find("[name='whCheckIn.expWaitId']").val();
				params["whCheckIn.waitOffsetQty"] = purchaseExp.find("[name='whCheckIn.waitOffsetQty']").val();
				params["whCheckIn.expFlag"] = purchaseExp.find("[name='whCheckIn.expFlag']").val();
				// 其他必要参数
				params["receiveBoxNo"] = $('#span-boxNo').text();
				params["trackingNumbers"] = $('#trackingNumbers').val();
				params["arrayTrackingNo"] = $('#returnTrackingNos').val();
				params["purchaseQuantity"] = parseInt(purchaseTr.find('.purchase-quantity').text());
				// 提交入库
				$.ajax({
					url: CONTEXT_PATH + "checkin/scans/checkIn",
					type: "POST",
					dataType: "json",
					data: params,
					beforeSend: function() {
						App.blockUI();
					},
					success: function(data) {
						App.unblockUI();
						if(data.status == 500) {
							layer.alert('多货入库失败！错误信息：' + data.message);
							return;
						}
						if(data.body && data.body.inStocks && data.body.inStocks.length > 0) {
							var inStock = data.body.inStocks[0];
							var days = inStock.days;
							var proDate = inStock.proDate;
							var expDate = inStock.expDate;
							var afterSaleQty = inStock.afterSaleQty;
							// 清空输入框和表格
							$('#excessQuantity-' + parentIndex + '-' + itemIndex).val('');
							$('#currentExcessSku-' + parentIndex + '-' + itemIndex).val('');
							$('#currentExcessPurchaseOrderNo-' + parentIndex + '-' + itemIndex).val('');
							// 隐藏按钮
							$('#calc-instock-btn-' + parentIndex + '-' + itemIndex).hide();
							$('#submit-more-goods-' + parentIndex + '-' + itemIndex).hide();
							// 设置多货模块表格背景色
							$('#excess-btn-' + parentIndex + '-' + itemIndex).hide();
							purchaseExcess.css('background-color', '#AFEEEE');
							
							// 禁用多货模块中的异常周转框
							$('#excess-table-wrap-' + parentIndex + '-' + itemIndex).find('.excess-exception-boxno').prop('disabled', true);
							// 禁用多货数量输入框
							$('#excessQuantity-' + parentIndex + '-' + itemIndex).prop('disabled', true);
							// 禁用取消多货录入按钮
							$('#cancel-more-goods-' + parentIndex + '-' + itemIndex).prop('disabled', true);
							// 设置打印所需参数
							$('#hidden-div-' + parentIndex + '-' + itemIndex).find("[name='i']").val(inStock.inId);
							$('#hidden-div-' + parentIndex + '-' + itemIndex).find("[name='l']").val(inStock.locationNumber);
							$('#hidden-div-' + parentIndex + '-' + itemIndex).find("[name='q']").val(excessQuantity);
							if (inStock.successCount != undefined && inStock.successCount != '') {
								$('#panel-title').html('<h1>成功：<b>'+inStock.successCount+'</b></h1>');
							}
							var shelfStraightHair = false;
							var freeCheck = false;
							var noLabel = false;
							// 计件
							calsfPiece(excessQuantity);
							// iframe跳转打印页面
							var printPageUrl = CONTEXT_PATH + 'checkins/qr?noLabel=' + noLabel;
							debugger;
							// 获取保质期参数
							//var expParam = $('#single-build-btn-' + parentIndex + '-' + itemIndex).parent().find("div[name='restricted-storage']").regionSerialize();
							// 获取采购单号和售后数量
							var purchaseOrderNo = $("input[name='whCheckIn.purchaseOrderNo']").regionSerialize();
							//var afterSaleQty = $('#purchase-tr-' + parentIndex + '-' + itemIndex).find("input[name='inStock.afterSaleQty']").regionSerialize();
							// 设置打印URL
							/*$('#printHtml').attr('src', printPageUrl + "&" + expParam + "&" +
								$('#hidden-div-' + parentIndex + '-' + itemIndex).regionSerialize() + "&" + 
								purchaseOrderNo + "&" + afterSaleQty +
								"&whCheckIn.shelfStraightHair=" + shelfStraightHair +
								"&whCheckIn.freeCheck=" + freeCheck);*/
							if (days == null || days == '' || days == undefined) {
								$('#printHtml').attr('src', printPageUrl + "&" +
										$('#hidden-div-' + parentIndex + '-' + itemIndex).regionSerialize() + "&" +
										purchaseOrderNo  +
										"&inStock.shelfStraightHair=" + shelfStraightHair +
										"&inStock.freeCheck=" + freeCheck);
							} else {
								$('#printHtml').attr('src', printPageUrl + "&whCheckIn.days="+days+"&whCheckIn.proDate="+proDate+"&whCheckIn.expDate="+expDate+
										"&whCheckIn.afterSaleQty=" +afterSaleQty+ "&" +
										$('#hidden-div-' + parentIndex + '-' + itemIndex).regionSerialize() + "&" +
										purchaseOrderNo  +
										"&inStock.shelfStraightHair=" + shelfStraightHair +
										"&inStock.freeCheck=" + freeCheck);
							}
							// 自动打印
							setTimeout(IframeOnloadPrint, 100);
							layer.alert('多货入库成功！');
						} else {
							layer.alert("生成入库单失败，请重新提交！错误信息: " + data.message);
						}
					},
					error: function() {
						App.unblockUI();
						layer.alert("系统内部出错，请稍后重试。");
					}
				});
			};
		</script>
	</div>
</div>
</body>
</html>