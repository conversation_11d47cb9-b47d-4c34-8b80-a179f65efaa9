<div style="margin: 10px 0; display: flex; align-items: center; justify-content: space-between;">
    <div>
        <label style="margin-right: 8px;">多货数量</label>
        <input class="form-control" type="number" name="excessQuantity" id="excessQuantity-${parentIndex}-${itemIndex}" value="" min="1" style="width: 120px; display: inline-block; margin-right: 8px;">
        <input type="hidden" name="currentExcessSku" id="currentExcessSku-${parentIndex}-${itemIndex}" class="current-excess-sku" value="">
        <input type="hidden" name="currentExcessPurchaseOrderNo" id="currentExcessPurchaseOrderNo-${parentIndex}-${itemIndex}" class="current-excess-purchase-order-no" value="">
        <button type="button" class="btn btn-default" id="calc-instock-btn-${parentIndex}-${itemIndex}" onclick="calcInStock(${parentIndex}, ${itemIndex});">
            计算入库单
        </button>
        <span style="color: red; font-size: 16px; margin-left: 10px;">点击计算入库单计算成功后，不可再取消多货录入</span>
    </div>
    <button type="button" class="btn btn-default" id="cancel-more-goods-${parentIndex}-${itemIndex}">
        取消多货录入
    </button>
</div>
<!-- loading 提示 -->
<div id="excess-loading-${parentIndex}-${itemIndex}" style="display:none; margin: 20px 0; color: #444; font-size: 16px;">
    <img src="${CONTEXT_PATH}images/favicon.png" style="width:24px;vertical-align:middle;margin-right:8px;">正在计算多货匹配采购单，请不要刷新页面
</div>
<!-- 表格部分默认隐藏 -->
<div id="excess-table-wrap-${parentIndex}-${itemIndex}" style="margin-top: 10px; display:none;">
    <table style="width: 100%;text-align: center;font-size: 13px;" class="table table-bordered table-condensed">
        <thead>
        <tr>
            <th rowspan="2" style="vertical-align: middle;">图片</th>
            <th rowspan="2" style="vertical-align: middle;">SKU</th>
            <th rowspan="2" style="vertical-align: middle;">多货数量</th>
            <th>采购单号</th>
            <th>采购员</th>
            <th>采购数量</th>
            <th>匹配良品数量</th>
            <th>处理方式</th>
            <th rowspan="2" style="vertical-align: middle;">多货异常数量</th>
            <th rowspan="2" style="vertical-align: middle;">异常周转框</th>
            <th rowspan="2" style="vertical-align: middle;">操作</th>
        </tr>
        </thead>
        <tbody id="excess-table-body-${parentIndex}-${itemIndex}">
        <!-- 动态填充 -->
        </tbody>
    </table>
</div>

<script>

    $(document).on('click', '#cancel-more-goods-${parentIndex}-${itemIndex}', function() {
        // 隐藏整个多货处理模块
        $(this).closest('.purchase-tr').hide();
        // 清空输入值
        $('#currentExcessSku-${parentIndex}-${itemIndex}').val('');
        $('#currentExcessPurchaseOrderNo-${parentIndex}-${itemIndex}').val('');
        $('#excessQuantity-${parentIndex}-${itemIndex}').val('');
        // 隐藏表格和loading
        $('#excess-table-wrap-${parentIndex}-${itemIndex}').hide();
        $('#excess-loading-${parentIndex}-${itemIndex}').hide();
        // 清空表格内容
        $('#excess-table-body-${parentIndex}-${itemIndex}').html('');
    });

    // 多货异常数量不可编辑，已删除相关校验逻辑

    // 校验多货异常周转码
    function checkExcessBox(obj, parentIndex, itemIndex){
        if(!obj.value || obj.value.replace(/\s/g,'') == ''){
            layer.alert("请输入异常周转码!");
            return ;
        }
        var boxNo = obj.value.replace(/\s/g,'');
        if(boxNo.length < 4){
            layer.alert("请输入正确的异常周转码!");
            return ;
        }
        
        // 检查是否已被使用
        var flag = hasUsedBoxNo(boxNo);
        if(flag){
            layer.alert("该周转码已锁定，请重新输入!");
            return ;
        }

        if(boxNo==""){
            layer.alert("请输入异常周转码！");
            return false;
        } else{
            // 验证有效性 - 参考原有的checkBox函数调用方式
            $.ajax({
                url : CONTEXT_PATH + "checkin/scans/createCheckIn/checkBox",
                data : {boxNo : boxNo, type:'exception'},
                success : function(json){
                    if (json.status == '500') {
                        layer.alert(json.message);
                        $(obj).val('');
                    } else if (json.status == '200') {
                        // 校验成功，保留输入值
                        layer.msg('周转码校验成功', {icon: 1});
                    }
                },
                error:function(){
                    layer.alert('校验失败，请重新扫描周转码!');
                    $(obj).val('');
                }
            });
        }
    }

    function calcInStock(parentIndex, itemIndex) {
        // 获取当前多货处理模块所在的行
        //var purchaseExcess = $(this).closest('.purchase-tr');
        // var excessId = purchaseExcess.attr('id');
        //var matches = excessId.match(/purchase-excess-(\d+)-(\d+)/);
        //var parentIndex = matches[1];
        //var itemIndex = matches[2];
        debugger;
        // 隐藏计算入库单按钮
        $('#calc-instock-btn-' + parentIndex + '-' + itemIndex).hide();
        // 使用索引获取对应的值
        var sku = $("#purchase-tr-" + parentIndex + "-" + itemIndex).find("[name='whCheckIn.whCheckInItem.sku']").val();
        var purchaseOrderNo = $("#common-param-" + parentIndex).find("[name='whCheckIn.purchaseOrderNo']").val();
        var quantity = $("#excessQuantity-" + parentIndex + "-" + itemIndex).val();

        // 获取当前SKU的图片信息
        var $currentRow = $("#purchase-tr-" + parentIndex + "-" + itemIndex);
        var $skuImage = $currentRow.find('.sku-image');
        var currentImageUrl = '';

        if ($skuImage.length > 0) {
            currentImageUrl = $skuImage.attr('src');
        }

        if (!sku || !purchaseOrderNo || !quantity) {
            layer.alert('请填写完整的多货SKU、采购单号和多货数量');
            return;
        }
        // 隐藏表格，显示loading
        $('#excess-table-wrap-' + parentIndex + '-' + itemIndex).hide();
        $('#excess-loading-' + parentIndex + '-' + itemIndex).show();
        // 隐藏取消按钮
        $('#cancel-more-goods-' + parentIndex + '-' + itemIndex).hide();
        // 发起请求
        $.ajax({
            url: CONTEXT_PATH + 'checkin/scans/matchExcess',
            type: 'POST',
            data: {
                currentExcessSku: sku,
                currentExcessPurchaseOrderNo: purchaseOrderNo,
                excessQuantity: quantity
            },
            dataType: 'json',
            success: function(res) {
                $('#excess-loading-' + parentIndex + '-' + itemIndex).hide();
                // 成功后不显示取消按钮
                if(res.status == 200 && res.body && res.body.data && res.body.data.length > 0) {
                    var html = '';
                    var len = res.body.data.length;
                    res.body.data.forEach(function(row, idx) {
                        html += '<tr>';
                        if(idx === 0) {
                            // 图片列 - 直接使用当前SKU页面的图片
                            html += '<td rowspan="' + len + '" style="vertical-align: middle;">';
                            html += '<img class="sku-image" onclick="getSkuImage(\'' + (row.sku||'') + '\')" alt="产品缩略图" border="0" width="80px" height="74px" src="' + currentImageUrl + '"/>';
                            html += '</td>';
                            html += '<td rowspan="' + len + '" style="vertical-align: middle;">'+(row.sku||'')+'</td>';
                            html += '<td rowspan="' + len + '" style="vertical-align: middle;">'+(row.excessQuantity||'')+'</td>';
                        }
                        // 如果是仅异常情况，则只显示SKU、多货数量和异常信息
                        html += '<td style="vertical-align: middle;">'+(row.purchaseOrderNo||'')+'</td>';
                        html += '<td style="vertical-align: middle;">'+(row.purchaseUserName||'')+'<input type="hidden" name="purchaseUser" value="'+(row.purchaseUser||'')+'"></td>';
                        html += '<td style="vertical-align: middle;">'+(row.purchaseQuantity||'')+'</td>';
                        html += '<td style="vertical-align: middle;">'+(row.matchedQuantity||'')+'</td>';
                        html += '<td style="vertical-align: middle;">'+(row.processingMethodName||'')+'<input type="hidden" name="processingMethod" value="'+(row.processingMethod||'')+'"></td>';

                        if(idx === 0) {
                            // 多货异常数量列
                            html += '<td rowspan="' + len + '" style="vertical-align: middle;">';
                            if (row.differenceQuantity != null  && row.differenceQuantity != '' && row.differenceQuantity != 0)  {
                                // 有异常数据时，显示异常数量并设为只读
                                html += '<input number="true" min="0" digits="true" name="excessExceptionQuantity" class="form-control input-xsmall excess-exception-quantity" value="' + row.differenceQuantity + '" style="width:80px;" readonly>';
                            } else {
                                // 无异常数据时，保持为空且只读
                                html += '<input number="true" min="0" digits="true" name="excessExceptionQuantity" class="form-control input-xsmall excess-exception-quantity" value="" style="width:80px;" readonly>';
                            }
                            html += '</td>';
                            
                            // 异常周转框列
                            html += '<td rowspan="' + len + '" style="vertical-align: middle;">';
                                                         if (row.differenceQuantity != null  && row.differenceQuantity != '' && row.differenceQuantity != 0) {
                                 // 允许输入异常周转框，并添加按键校验
                                 html += '<input type="text" class="form-control excess-exception-boxno" name="excessExceptionBoxNo" style="width:100px;" value="" onkeypress="if(event.keyCode==13) { checkExcessBox(this, \'' + parentIndex + '\', \'' + itemIndex + '\'); return false;}" tabindex="4">';
                             } else {
                                 // 禁止输入异常周转框
                                 html += '<input type="text" class="form-control excess-exception-boxno" name="excessExceptionBoxNo" style="width:100px;" value="" disabled>';
                             }
                            html += '</td>';
                        }
                        // Add hidden inputs for the new fields
                        html += '<input type="hidden" name="weight" value="'+(row.weight||'')+'">';
                        html += '<input type="hidden" name="shippingCost" value="'+(row.shippingCost||'')+'">';
                        html += '<input type="hidden" name="trackingNumber" value="'+(row.trackingNumber||'')+'">';
                        html += '<input type="hidden" name="supplierId" value="'+(row.supplierId||'')+'">';
                        html += '<input type="hidden" name="vendorName" value="'+(row.vendorName||'')+'">';
                        // 添加differenceQuantity隐藏字段
                        html += '<input type="hidden" name="differenceQuantity" value="'+(row.differenceQuantity||'')+'">';
                        
                                                 if(idx === 0) {
                             html += '<td rowspan="' + len + '" style="vertical-align: middle;">' +
                                 '<button type="button" class="btn btn-default" onclick="buildExcessInStock(\'' + parentIndex + '\', \'' + itemIndex + '\');" id="submit-more-goods-' + parentIndex + '-' + itemIndex + '"><i class="icon-plus"></i>提交</button>' +
                                 '</td>';
                         }
                        html += '</tr>';
                    });
                    $('#excess-table-body-' + parentIndex + '-' + itemIndex).html(html);
                    $('#excess-table-wrap-' + parentIndex + '-' + itemIndex).show();
                    $('#calc-instock-btn-' + parentIndex + '-' + itemIndex).hide();
                } else {
                    $('#excess-table-body-' + parentIndex + '-' + itemIndex).html('');
                    $('#excess-table-wrap-' + parentIndex + '-' + itemIndex).show();
                    var msg = res.message || '未匹配到多货采购单';
                    layer.alert(msg);
                    // 失败时恢复取消按钮
                    $('#cancel-more-goods-' + parentIndex + '-' + itemIndex).show();
                    // 计算入库单按钮
                    $('#calc-instock-btn-' + parentIndex + '-' + itemIndex).show();
                }
            },
            error: function(xhr) {
                $('#excess-loading-' + parentIndex + '-' + itemIndex).hide();
                // 接口失败时重新显示取消按钮
                $('#cancel-more-goods-' + parentIndex + '-' + itemIndex).show();
                // 计算入库单按钮
                $('#calc-instock-btn-' + parentIndex + '-' + itemIndex).show();
                var msg = '多货匹配采购单失败';
                if(xhr.responseJSON && xhr.responseJSON.message) {
                    msg = xhr.responseJSON.message;
                }
                layer.alert(msg);
            }
        });
    };
</script>