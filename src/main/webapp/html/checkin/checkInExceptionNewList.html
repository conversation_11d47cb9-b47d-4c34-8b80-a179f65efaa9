<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
	<title>易世通达仓库管理系统</title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<#include "/common/include.html">
	<style type="text/css">
		.modal-body input {
			width: 240px;
		}
		.return-model{
			padding: 12px;
		}

		.exp-btn {
			word-wrap: break-word !important;
			word-break: break-all !important;
			white-space: normal !important;
		}

		/* SKU和图片合并列的样式 */
		.sku-image-cell {
			text-align: center !important;
			vertical-align: middle !important;
			padding: 8px 4px !important;
		}

		.sku-image-cell .sku-link {
			display: block;
			margin-bottom: 8px;
			font-weight: bold;
			color: #337ab7;
			text-decoration: none;
			font-size: 12px;
			line-height: 1.2;
		}

		.sku-image-cell .sku-link:hover {
			color: #23527c;
			text-decoration: underline;
		}

		.sku-image-cell .image-container {
			text-align: center;
		}

		.sku-image-cell img {
			max-width: 100%;
			height: 55px;
			border: 1px solid #ddd;
			border-radius: 3px;
		}

		.sku-image-cell .no-image {
			color: #999;
			font-size: 11px;
			font-style: italic;
		}
	</style>
</head>
<body>
<@header method="header" active="10040000"><#include "/ftl/header.ftl"></@header>

<div id="page" style="background-color: rgb(231, 237, 248)">
	<div class="row">
		<div class="col-md-12" style="padding: 0">
			<ul class="page-breadcrumb breadcrumb" style="background-color: white;">
				<li><a href="#">入库查询</a></li>
				<li class="active">新入库异常单查询</li>
			</ul>
		</div>
	</div>

	<!-- 内容 -->
	<div class="container-fluid" style="background-color: white;border: none">
		<div class="row">
			<div class="col-md-12">
				<#assign query = domain.query>
				<form action="${CONTEXT_PATH}checkInException/newExce/search"
						   class="form-horizontal form-bordered form-row-stripped"
						   method="post" modelAttribute="domain" name="checkInExceptionForm" id ="domain">
					<!-- 分页信息 -->
					<input id="page-no" type="hidden" name="page.pageNo" value="1">
					<input id="page-size" type="hidden" name="page.pageSize" value="${domain.page.pageSize}">

					<div class="form-body">
						<div class="form-group">
							<label class="control-label col-md-1">添加时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromCreateDate" placeholder="" readonly="readonly" value="${query.fromCreateDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toCreateDate" placeholder="" readonly="readonly" value="${query.toCreateDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
							<label class="control-label col-md-1">异常类型</label>
							<div class="col-md-3">
								<input class="form-control" name="query.exceptionTypes" type="text"
									   value="<#if (query.exceptionTypes)!>${query.exceptionTypes?join(',')}<#else ></#if>">
							</div>
							<label class="control-label col-md-1">处理方式</label>
							<div class="col-md-3">
								<input class="form-control" name="query.handleWays" type="text"
                                       value="<#if (query.handleWays)!>${query.handleWays?join(',')}<#else ></#if>">
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-md-1">IDs</label>
							<div class="col-md-3">
								<input class="form-control" id="queryIds" name="query.ids" type="text" placeholder="请输入异常编号" value="<#if (query.ids)!>${query.ids?join(',')}<#else ></#if>">
							</div>
							<label class="control-label col-md-1">状态</label>
							<div class="col-md-3">
								<input class="form-control" name="query.statusList" type="text"
									   value="<#if (query.statusList)!>${query.statusList?join(',')}<#else ></#if>">
							</div>
							<label class="control-label col-md-1">采购单号</label>
							<div class="col-md-3">
								<input class="form-control" name="query.purchaseOrderNo" type="text" placeholder="请输入采购单号" value="${query.purchaseOrderNo}">
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-md-1">周转码</label>
							<div class="col-md-3">
								<input class="form-control" name="query.boxNo" type="text" placeholder="请输入周转码" value="${query.boxNo}">
							</div>
                            <label class="control-label col-md-1">异常来源</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.exceptionFroms" type="text"
									   value="<#if (query.exceptionFroms)!>${query.exceptionFroms?join(',')}<#else ></#if>">
                            </div>
                            <label class="control-label col-md-1">快递单号</label>
                            <div class="col-md-3">
                                <input class="form-control" type="text" name="query.trackingNumber" placeholder="请输入快递单号" value="${query.trackingNumber }">
                            </div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">SKU</label>
							<div class="col-md-3">
								<input class="form-control" type="text" id="uuidSku" name="query.sku" placeholder="请输入SKU" onkeypress="if(event.keyCode==13) { inputSku(this); return false;}" value="${query.sku }">
							</div>
                            <label class="control-label col-md-1">采购员</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.purchaseUserList" type="text" value="<#if (query.purchaseUserList)!>${query.purchaseUserList?join(',')}<#else ></#if>">
                            </div>
                            <label class="control-label col-md-1">异常员</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.exceptionUser" type="text" value="${query.exceptionUser}">
                            </div>
						</div>
                        <div class="form-group">
                            <label class="control-label col-md-1">采购处理时间</label>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromPurchaseHandleDate" placeholder="" readonly="readonly" value="${query.fromPurchaseHandleDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toPurchaseHandleDate" placeholder="" readonly="readonly" value="${query.toPurchaseHandleDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
                            <label class="control-label col-md-1">完成时间</label>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromFinishDate" placeholder="" readonly="readonly" value="${query.fromFinishDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toFinishDate" placeholder="" readonly="readonly" value="${query.toFinishDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
                            <label class="control-label col-md-1">异常处理时间</label>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromExceptionHandleDate" placeholder="" readonly="readonly" value="${query.fromExceptionHandleDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toExceptionHandleDate" placeholder="" readonly="readonly" value="${query.toExceptionHandleDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
                        </div>
						<div class="form-group">
							<label class="control-label col-md-1">是否首单</label>
							<div class="col-md-3">
								<input class="form-control" name="query.firstOrderType" type="text" value="${query.firstOrderType}">
							</div>

                            <label class="control-label col-md-1">新/建单采购单号</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.newPurchaseOrderNo" type="text" placeholder="请输入新/建单采购单号" value="${query.newPurchaseOrderNo}">
                            </div>
							<label class="control-label col-md-1">异常次数</label>
							<div class="col-md-3">
								<input class="form-control" name="query.exTimes" number="true" placeholder="请输入异常次数" value="${query.exTimes}">
							</div>
						</div>
						<div class="form-group">
							<label class="control-label col-md-1">库位</label>
							<div class="col-md-3">
								<input class="form-control" type="text" id="locationNumber" name="query.locationNumber" placeholder="请输入库位" value="${query.locationNumber }">
							</div>
							<label class="control-label col-md-1">收货周转筐</label>
							<div class="col-md-3">
								<input class="form-control" type="text" name="query.receiveBoxNo" placeholder="请输入收货周转筐" value="${query.receiveBoxNo }">
							</div>
							<label class="control-label col-md-1">待入库时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromWaitCheckInDate" placeholder="" readonly="readonly" value="${query.fromWaitCheckInDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toWaitCheckInDate" placeholder="" readonly="readonly" value="${query.toWaitCheckInDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
						</div>
                        <div class="form-group">
                            <label class="control-label col-md-1">入库中时间</label>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromDoingCheckInDate" placeholder="" readonly="readonly" value="${query.fromDoingCheckInDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toDoingCheckInDate" placeholder="" readonly="readonly" value="${query.toDoingCheckInDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
							<label class="control-label col-md-1">标签</label>
							<div class="col-md-3 input-group">
								<select class="form-control" name="query.tags" value="${query.tags}" id="apv-flag-id">
									<option value="all" ${(query.tags == 'all')?string('selected', '')}>全部</option>
									<option value="2001" ${(query.tags == '2001')?string('selected', '')}>换图缺货订单</option>
									<option value="2023" ${(query.tags == '2023')?string('selected', '')}>服装类</option>
								</select>
							</div>
                            <label class="control-label col-md-1">待质控处理时间</label>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromStartQCHandleDate" placeholder="" readonly="readonly" value="${query.fromStartQCHandleDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toStartQCHandleDate" placeholder="" readonly="readonly" value="${query.toStartQCHandleDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-1">质控处理完成时间</label>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <input class="form-control Wdate" type="text" name="query.fromCompletedQCHandleDate" placeholder="" readonly="readonly" value="${query.fromCompletedQCHandleDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                    <span class="input-group-addon">到</span>
                                    <input class="form-control Wdate" type="text" name="query.toCompletedQCHandleDate" placeholder="" readonly="readonly" value="${query.toCompletedQCHandleDate }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
                                </div>
                            </div>
                            <label class="control-label col-md-1">异常处理中</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.exceptionHandled" type="text" value="${query.exceptionHandled}">
                            </div>
                            <label class="control-label col-md-1">标记状态</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.markStatus" type="text" value="${query.markStatus}">
                            </div>
                        </div>
                        <div class="form-group">
							<label class="control-label col-md-1">废弃员</label>
							<div class="col-md-3">
								<input class="form-control" name="query.discardedUser" type="text" value="${query.discardedUser}">
							</div>
							<label class="control-label col-md-1">完成员</label>
							<div class="col-md-3">
								<input class="form-control" name="query.finishUser" type="text" value="${query.finishUser}">
							</div>
							<label class="control-label col-md-1">采购单类型</label>
							<div class="col-md-3">
								<input class="form-control" name="query.orderFlag" type="text" value="${query.orderFlag}">
							</div>
                        </div>
						<div class="form-group">
							<label class="control-label col-md-1">SPU</label>
							<div class="col-md-3">
								<input class="form-control" name="query.spu" placeholder="请输入spu,多个请按英文逗号分隔" type="text" value="${query.spu}">
							</div>
							<label class="control-label col-md-1">创建人</label>
							<div class="col-md-3">
								<input class="form-control" name="query.createdBy" type="text" value="${query.createdBy}">
							</div>
							<label class="control-label col-md-1">标记时间</label>
							<div class="col-md-3">
								<div class="input-group">
									<input class="form-control Wdate" type="text" name="query.fromMarkTime" placeholder="" readonly="readonly" value="${query.fromMarkTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 00:00:00',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
									<span class="input-group-addon">到</span>
									<input class="form-control Wdate" type="text" name="query.toMarkTime" placeholder="" readonly="readonly" value="${query.toMarkTime }" onfocus="WdatePicker({startDate:'%y-%M-%d 23:59:59',dateFmt:'yyyy-MM-dd HH:mm:ss',alwaysUseStartDate:true})">
								</div>
							</div>
						</div>
                        <div class="form-group">
                            <label class="control-label col-md-1">标记原因</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.markReason" type="text" value="${query.markReason}">
                            </div>
                            <label class="control-label col-md-1">标记人员</label>
                            <div class="col-md-3">
                                <input class="form-control" name="query.markUserId" type="text" value="${query.markUserId}">
                            </div>
                        </div>
					</div>
					<div>
						<div class="pull-left" style="margin-bottom: 10px;">
							<@header method="auth" authCode="NEW_CHECKIN_EXCEPTION_ORDER_BATCH_DISCARD">
								<button type="button" class="btn btn-default" onclick="batchScrap()">
									批量废弃
								</button>
							</@header>
                            <@header method="auth" authCode="NEW_CHECKIN_EXCEPTION_ORDER_REMARK_FINISHED_BY_MANAGER">
							<button type="button" class="btn btn-default" onclick="markFinished()">
								标记完成(主管)
							</button>
                            </@header>
                            <@header method="auth" authCode="NEW_CHECKIN_EXCEPTION_ORDER_REMARK_FINISHED">
							<button type="button" class="btn btn-default" onclick="markFinished2()">
								标记完成
							</button>
                            </@header>
							<@header method="auth" authCode="NEW_CHECKIN_EXCEPTION_ORDER_BATCH_SUBMIT">
								<button type="button" class="btn btn-default" onclick="batchSendException()">
                                    批量提交
                                </button>
							</@header>
							<@header method="auth" authCode="NEW_CHECKIN_EXCEPTION_ORDER_SYNC_TO_PURCHASE_SYSTEM">
								<button type="button" class="btn btn-default" onclick="batchReSendExceptionToPms()">
									同步异常信息到采购系统
								</button>
							</@header>
                            <@header method="auth" authCode="NEW_CHECKIN_EXCEPTION_ORDER_PRINT_ORDER_LIST">
							<button type="button" class="btn btn-default" onclick="printCarryItems()">
								打印带货清单
							</button>
                            </@header>
                            <@header method="auth" authCode="NEW_CHECKIN_EXCEPTION_ORDER_BATCH_PRINT_TAG">
							<button type="button" class="btn btn-default" onclick="bathPrintCarryExceptions()">
								批量打印带货标签
							</button>
                            </@header>
							<@header method="auth" authCode="NEW_CHECKIN_EXCEPTION_ORDER_ADD_EXCEPTION">
								<button type="button" class="btn btn-default" onclick="location.href='${CONTEXT_PATH}checkInException/newEdit?id='">
									添加异常
								</button>
							</@header>
							<@header method="auth" authCode="NEW_CHECKIN_EXCEPTION_ORDER_MARK_CONFIG">
								<button type="button" class="btn btn-default" onclick="openMarkReasonConfig()">
									标记原因配置
								</button>
							</@header>
							<@header method="auth" authCode="NEW_CHECKIN_EXCEPTION_ORDER_BATCH_MARK">
								<button type="button" class="btn btn-default" onclick="batchMark()">
									批量标记
								</button>
							</@header>
                            <@header method="auth" authCode="NEW_CHECKIN_EXCEPTION_ORDER_DOWNLOAD">
							<button type="button" class="btn btn-default" onclick="download()">
								导出
							</button>
                            </@header>
                            <@header method="auth" authCode="NEW_CHECKIN_EXCEPTION_ORDER_BATCH_WAIT_IN">
							<button type="button" class="btn btn-default" onclick="batchWaitCheckIn()">
								批量待入库
							</button>
                            </@header>
                            <@header method="auth" authCode="NEW_CHECKIN_EXCEPTION_ORDER_MODIFY_LOCATION">
							<button type="button" class="btn btn-default" onclick="batchUpdateLocation()">
								修改库位
							</button>
                            </@header>
						</div>
						<div class="col-md-offset-10" style="text-align: right">
							<button type="button" class="btn btn-default" onclick="formReset(this)">
								<i class="icon-refresh"></i> 重置
							</button>
							<button type="submit" class="btn blue">
								<i class="icon-search"></i> 查询
							</button>
						</div>
					</div>
				</form>
			</div>
			<br/>
		</div>

		<div class="row">
			<div id="fixedDiv" class="col-md-12">
				<table class="table table-striped table-bordered table-hover table-condensed" id="fixedTab">
					<colgroup>
						<col width="5%" />
						<col width="8%" />
						<col width="5%" />
						<col width="4%" />
						<col width="4%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="3%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
						<col width="8%" />
					</colgroup>
					<thead>
						<tr>
							<th><input type="checkbox"  id="check-alls" name="checkAll">全选</th>
							<th>SKU/图片</th>
							<th>周转码</th>
							<th>库位</th>
							<th>异常数量</th>
							<th>确认数量<br/><span style="color: red">处理数量</span></th>
							<th>异常次数<br/><span style="color: red">沟通次数</span></th>
							<th>异常来源</th>
							<th>异常类型</th>
							<th>处理结果</th>
							<th>状态<br/><span style="color: red">总库位库存</span></th>
							<th>标签</th>
							<th>收货周转框</th>
							<th>时效</th>
							<th>创建人</th>
							<th>入库员</th>
							<th>采购员</th>
							<th>异常员</th>
							<th>废弃员</th>
							<th>完成员</th>
							<th>添加时间</th>
							<th>推送时间</th>
							<th>待入库时间</th>
                            <th>入库中时间</th>
                            <th>标记原因<br/><span style="color: red">标记时间</span></th>
							<th>操作项</th>
						</tr>
					</thead>
				</table>
			</div>
			<div class="col-md-12" id="task-list-warp">
				<!-- 内容 -->
				<table class="table table-striped table-bordered table-hover table-condensed" id="task-list">
					<colgroup>
						<col width="5%" />
						<col width="8%" />
						<col width="5%" />
						<col width="4%" />
						<col width="4%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="3%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
						<col width="5%" />
                        <col width="5%" />
                        <col width="5%" />
						<col width="8%" />
					</colgroup>
					<thead>
					<tr>
						<th><input type="checkbox"  id="check-all" name="checkAll">全选</th>
						<th>SKU/图片</th>
						<th>周转码</th>
						<th>库位</th>
						<th>异常数量</th>
						<th>确认数量<br/><span style="color: red">处理数量</span></th>
						<th>异常次数<br/><span style="color: red">沟通次数</span></th>
						<th>异常来源</th>
						<th>异常类型</th>
						<th>处理结果</th>
						<th>状态<br/><span style="color: red">总库位库存</span></th>
						<th>标签</th>
						<th>收货周转框</th>
						<th>时效</th>
						<th>创建人</th>
						<th>入库员</th>
						<th>采购员</th>
						<th>异常员</th>
						<th>废弃员</th>
						<th>完成员</th>
						<th>添加时间</th>
						<th>推送时间</th>
						<th>待入库时间</th>
                        <th>入库中时间</th>
                        <th>标记原因<br/><span style="color: red">标记时间</span></th>
						<th>操作项</th>
					</tr>
					</thead>
					<tbody>
						<#list domain.whCheckInExceptions as whCheckInException>
							<tr>
								<td  colspan="26">
									<p align="left">
										<input type="checkbox" value="${whCheckInException.id}" class="${whCheckInException.id}" name="batchNos" onchange="checkChange(this)">
										<b>快递单号:${whCheckInException.trackingNumber}</b>&nbsp
										<b>采购单号:${whCheckInException.purchaseOrderNo}</b>&nbsp&nbsp
										<b>采购单类型:${whCheckInException.flagName}</b>&nbsp
										<button id="expandFn-${whCheckInException.id}" value = "${whCheckInException.id}" type="button" class="btn btn-info btn-xs" onclick="details(this)">
											收起
										</button>
									</p>
								</td>
							</tr>
							<tr class = "tr-${whCheckInException.id}">
								<td>
									<input type="checkbox" value="${whCheckInException.id}" name="ids" class="${whCheckInException.id}">
									${whCheckInException.id}
									<#if whCheckInException.firstOrderTypeString == '普通'>
										<br>${whCheckInException.firstOrderTypeString}
									<#else >
										<br><span style="color: red">${whCheckInException.firstOrderTypeString}</span>
									</#if>
								</td>
								<td class="sku-image-cell">
									<a href="${CONTEXT_PATH}checkInException/newEdit?id=${whCheckInException.id}" class="sku-link">
										${whCheckInException.sku}
									</a>
									<div class="image-container">
										<#if whCheckInException.images??>
											<img alt="${whCheckInException.sku}" src="${whCheckInException.images[0]}">
										<#else>
											<span class="no-image">无图片</span>
										</#if>
									</div>
								</td>
								<td>${whCheckInException.boxNo}</td>
								<td>${whCheckInException.locationNumber}</td>
								<td>${whCheckInException.quantity}</td>
								<td>${whCheckInException.confirmQuantity}
									<#if whCheckInException.handledQuantity??>
										<br/><span style="color: red">${whCheckInException.handledQuantity}</span>
									</#if>
								</td>

								<td>
									${whCheckInException.exTimes}
									<br/>
									<span style="color: red">
										<#if !(whCheckInException.wmsHandleTimes)??>0<#else >${whCheckInException.wmsHandleTimes}</#if>
									</span>
								</td>
								<td>${whCheckInException.exceptionFromName}</td>
								<td>${whCheckInException.exceptionTypeName}</td>
								<td>
									<#if whCheckInException.handleWay != 9 >
										${whCheckInException.exceptionHandleWayName}
										<#if whCheckInException.exceptionHandleWayName == '建单入库' || whCheckInException.exceptionHandleWayName == '入库' && whCheckInException.exceptionTypeName == '跟单来'>
											<br/>
											<font color="#ff0033">${whCheckInException.newPurchaseOrderNo}</font>
										</#if>
									</#if>
									<#if (whCheckInException.handleWay == 9) >
										<button type="button" class="btn btn-info btn-xs exp-btn" onclick="toReturnOrderList(${whCheckInException.id})">
											${whCheckInException.exceptionHandleWayName}
										</button>
									</#if>
									<#if (whCheckInException.exceptionFrom == 4) && (whCheckInException.returnInformationJson??) && !(whCheckInException.returnInformationJson=='') >
										<button type="button" class="btn btn-info btn-xs exp-btn" onclick="showUnScanUuId(${whCheckInException.id})">
											QC全检未扫描的唯一码
										</button>
									</#if>
									<div id="returnInformation_${whCheckInException.id}" class="category-view display-none">
										<div class="col-md-8">
											<textarea style="color: red;font-size: 15px;" rows="7" cols="53" name="" readonly>${whCheckInException.returnInformationJson}</textarea>
										</div>
									</div>
								</td>
								<td>
									<span id="${whCheckInException.id}_status">${whCheckInException.exceptionStatusName}</span>
									<#if whCheckInException.skuLocationQty??>
										<br/><span style="color: red">${whCheckInException.skuLocationQty}</span>
									</#if>
								</td>
								<td><span style="color: #008000">
										${util('enumName', 'com.estone.checkin.enums.CheckExceptionTagTypeEnum', whCheckInException.tags)}
									</span>
								</td>
								<td>${whCheckInException.receiveBoxNo}</td>
								<td><#if whCheckInException.aging?? >${whCheckInException.aging }</#if></td>
								<td>${util('name',whCheckInException.createdBy)}</td>
								<td>${util('name',whCheckInException.checkInUser)}</td>
								<td>${whCheckInException.purchaseUserName}</td>
								<td>${util('name',whCheckInException.exceptionUser)}</td>
								<td>${util('name',whCheckInException.discardedUser)}</td>
								<td>${util('name',whCheckInException.finishUser)}</td>
								<td>${whCheckInException.creationDate }</td>
								<td>${whCheckInException.purchaseHandleDate }</td>
								<td>${whCheckInException.waitCheckInDate }</td>
								<td>${whCheckInException.doingCheckInDate }</td>
								<td>
									<#if whCheckInException.markReason??>
										${whCheckInException.markReason}
										<#if whCheckInException.markTime??>
											<br/><span style="color: red">${whCheckInException.markTime?string('yyyy-MM-dd HH:mm')}</span>
										</#if>
									<#else>
										-
									</#if>
								</td>
								<td>
									<button type="button" class="btn btn-info btn-xs" onclick="viewLogs(${whCheckInException.id})" style="margin: 5px">日志</button>
									<#if whCheckInException.status != 32 && whCheckInException.status != 5 && whCheckInException.status != 9 && whCheckInException.status != 11 && whCheckInException.status != 6 && whCheckInException.exceptionFrom != 4>
									<button type="button" class="btn btn-info btn-xs" onclick="editFunc(this)" url="${CONTEXT_PATH}checkInException/newEdit?id=${whCheckInException.id}">编辑</button>
									</#if>
									<#if whCheckInException.markReason??>
									<button type="button" class="btn btn-warning btn-xs" onclick="cancelMark(${whCheckInException.id})" style="margin: 5px">取消标记</button>
									<#else>
									<button type="button" class="btn btn-success btn-xs" onclick="markException(${whCheckInException.id})" style="margin: 5px">标记</button>
									</#if>
									<#if whCheckInException.exceptionFrom != 4>
									<button type="button" class="btn btn-info btn-xs" onclick="printExceptionTag(${whCheckInException.id})" style="margin: 5px">打印标签</button>
									</#if>
									<button type="button" class="btn btn-info btn-xs" onclick="viewLog(${whCheckInException.id}, 'checkInExceptionTag')" style="margin: 5px">标签打印日志</button>
								</td>
							</tr>
						</#list>
					</tbody>
				</table>
				<!-- 内容end -->
			</div>
		</div>
		<div id="fixed-bottom">
			<div id="pager"></div>
		</div>
	</div>
	<#include "/common/footer.html">
</div>
	<div style="display:none;" id="purchase-user">
		[{"id":"", "text":""}<#list domain.purchaseUsers as purchaseUser>,{"id":"${purchaseUser.id}", "text":"${purchaseUser.userName} - ${purchaseUser.employeeName}"}</#list>]
	</div>
	<div>
		<iframe style="width:260px;height:270px;border: 0 none; display: none;" name="exceptionTagPrintHtml" id="exceptionTagPrintHtml"></iframe>
	</div>

<script type="text/javascript" src="${CONTEXT_PATH}js/datepicker/WdatePicker.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.region.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/table-thead-fixed.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/assets/plugins/jquery.serializejson.min.js"></script>
<script type="text/javascript" src="${CONTEXT_PATH}js/prompt-message.js"></script>
<script type="text/javascript">

	function details(tag){
		var batchNo = $(tag).val();
		if ($(tag).hasClass('expand')) {
			$(tag).text('收起');
			//$('#div-' + batchNo).slideDown(200);
			$('.tr-' + batchNo).show();
		} else {
			$(tag).text('展开');
			//$('#div-' + batchNo).slideUp(200);
			$('.tr-' + batchNo).hide();
		}
		$(tag).toggleClass('expand');
		event.preventDefault();
	}

	/*$('#expandFn').on('click', function (event) {

	});*/

	function checkChange(tar) {
		var f = $(tar).is(":checked");
		var batchNo = $(tar).val();
		$("." + batchNo).attr('checked',f);
	}

	// 首单
	$("input[name='query.firstOrderType']").select2({
		data : [{"id":"0", "text":"普通"},{"id":"1", "text":"新品首单"},{"id":"2", "text":"供应商首单"}],
		placeholder : "是否首单",
		allowClear : true
	});

    // 异常类型

    //var exceptionTypeArray = jQuery.parseJSON($("#exceptionTypes").text());
	var exceptionTypeArray = ${domain.exceptionTypes}
    $("input[name='query.exceptionTypes']").select2({
        data : exceptionTypeArray,
        placeholder : "异常类型",
        multiple: true,
        allowClear : true
    });
    var purchaseUserArry = jQuery.parseJSON($('#purchase-user').text());
    $("input[name='query.purchaseUserList']").select2({
        data : purchaseUserArry,
        placeholder : "采购员",
		multiple: true,
        allowClear : true
    });
	// 创建人 =采购入库、入库质检、入库异常单页面权限
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=10020100,10030100,10040400", function(json){
		if (json) {
			$("input[name='query.createdBy']").select2({
				data : json,
				placeholder : "创建人",
				allowClear : true
			});
		} else {
			$("input[name='query.createdBy']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});

	// 异常员 =采购入库、入库质检、入库异常单页面权限
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=10020100,10030100,10040400", function(json){
		if (json) {
			$("input[name='query.exceptionUser']").select2({
				data : json,
				placeholder : "异常员",
				allowClear : true
			});
			$("input[name='query.markUserId']").select2({
				data : json,
				placeholder : "标记人员",
				allowClear : true
			});
		} else {
			$("input[name='query.exceptionUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
			$("input[name='query.markUserId']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});
	// 废弃员 =采购入库、入库质检、入库异常单页面权限
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=10020100,10030100,10040400", function(json){
		if (json) {
			$("input[name='query.discardedUser']").select2({
				data : json,
				placeholder : "废弃员",
				allowClear : true
			});
		} else {
			$("input[name='query.discardedUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});
	// 废弃员 =采购入库、入库质检、入库异常单页面权限
	$.getJSON(CONTEXT_PATH + "system/saleusers/userByPermissionCode?permissionCode=10020100,10030100,10040400", function(json){
		if (json) {
			$("input[name='query.finishUser']").select2({
				data : json,
				placeholder : "完成员",
				allowClear : true
			});
		} else {
			$("input[name='query.finishUser']").attr("placeholder", "该角色没有相关人员!").attr("readonly", true);
		}
	});

	var orderFlagArray = ${domain.orderFlags}
	$("input[name='query.orderFlag']").select2({
		data : orderFlagArray,
		placeholder : "标签",
		multiple: false,
		allowClear : true
	});


    // 用于拦截IDs所输入字符，只允许为英文逗号和数字以及空格
    $("input[id='queryIds']").on("input",function (val) {
        var value = val.target.value;
        const regex = /^\s*\d+(?:\s*,\s*\d+)*\s*$/;
        if (!isBlank(value) && !regex.test(value)){
            alert("ID只允许输入数字和英文逗号以及空格",'error');
        }
    });
    // 用于校准IDs所输入字符,使其末尾不存在多余的逗号以及去除对应的空格
    $("input[id='queryIds']").on("blur",function (val) {
        var value = val.target.value;
        var regex = /^,$/;
        if (isBlank(value)){
            return;
        }
        value = value.replaceAll(" ","");
        var character  = value.slice(-1);
        if (regex.test(character)){
            val.target.value = value.slice(0,-1);
        }
    });
    // 用于判断字符串是否为空
    function isBlank(str){
        if (str == null || str == undefined || str == "" || str.length == 0){
            return true;
        }
        return false;
    }

	// 处理方式
	var handleWays = ${domain.handleWays}
    $("input[name='query.handleWays']").select2({
        data : handleWays,
        placeholder : "处理方式",
        multiple: true,
        allowClear : true
    });
	// 状态
	var statuses = ${domain.statuses}
    $("input[name='query.statusList']").select2({
        data : statuses,
        placeholder : "状态",
        multiple: true,
        allowClear : true
    });

    // 异常处理中
    $("input[name='query.exceptionHandled']").select2({
        data : [{id:"true",text:"是"}],
        placeholder : "异常处理中",
        multiple: false,
        allowClear : true
    });

    // 标记状态
    $("input[name='query.markStatus']").select2({
        data : [{id:"marked",text:"已标记"},{id:"unmarked",text:"未标记"}],
        placeholder : "标记状态",
        multiple: false,
        allowClear : true
    });

    // 标记原因
    var markReasons = ${domain.markReasons}
    $("input[name='query.markReason']").select2({
        data : markReasons,
        placeholder : "标记原因",
        multiple: false,
        allowClear : true
    });

	// 来源
	var exceptionFroms = ${domain.exceptionFroms}
    $("input[name='query.exceptionFroms']").select2({
        data : exceptionFroms,
        placeholder : "来源",
        multiple: true,
        allowClear : true
    });

    // 分页
    var total = "${domain.page.totalCount}";
	var pageNo = "${domain.page.pageNo}";
	var pageSize = "${domain.page.pageSize}";
	$("#pager").initPager({ total : total, pageNo : pageNo, pageSize : pageSize});

    var heights = $("body").height();
	if(heights>910){
        $("#fixed-bottom").addClass("fixed-bottom-height")
	}

	// 全选
	var checkAll = $("input[name='checkAll']");

	// 子选项
	var itemIds = $("input[name='ids']");
	checkAll.change(
	  function () {
		  itemIds.prop("checked", $(this).prop("checked"));
		  itemIds.each(function(){
			  var f = $(this).is(":checked");
				var checkClass = $(this).prop("class");
				$("." + checkClass).each(function(){
					$(this).prop("checked",f);
				})
		  })
	  }
	);
	// 获取选中的入库单
	function getCheckedIds() {
		var outIds = $("input[name='ids']:checked");
        var checkIds = "";
        for (var i = 0; i < outIds.length; i++) {
            var outId = outIds[i].value;
            if (i == 0) {
                checkIds += outId;
            } else {
                checkIds += "," + outId;
            }
        }
        return checkIds;
	}
    //日志
    function viewLogs(id, type) {
        var diglog = dialog({
            title: id + ' 入库异常日志',
            width: 800,
            url: CONTEXT_PATH + "checkInExceptionHandle/showLogs?relevanceId=" + id,
            cancelValue: '取消',
            cancel: function () {}
        });
        diglog.show();
    }

    //标记完成
    function markFinished(ids) {
        if(getCheckedIds().length == 0) {
			getErrorInfoAlert("请选择要操作的入库异常单");
            return;
        }
		$.ajax({
			url: CONTEXT_PATH + "checkInException/batchUpdateExceptionToFinished",
			type: "GET",
			data: {ids:getCheckedIds()},
			success: function(data){
				if (data.message) {
					getErrorInfoAlert(data.message);
				} else {
					alert("成功");
				}
				setTimeout(function () {
					window.location.reload();
				}, 2000);
			}
		});
    }

	//标记完成
	function markFinished2(ids) {
		if(getCheckedIds().length == 0) {
			getErrorInfoAlert("请选择要操作的入库异常单");
			return;
		}
		$.ajax({
			url: CONTEXT_PATH + "checkInException/batchUpdateExceptionToFinished2",
			type: "GET",
			data: {ids:getCheckedIds()},
			success: function(data){
				if (data.message) {
					getErrorInfoAlert(data.message);
				} else {
					alert("成功");
				}
				setTimeout(function () {
					window.location.reload();
				}, 2000);
			}
		});
	}

	//废弃
	function batchScrap() {
	    var ids = getCheckedIds();
		if(ids.length == 0) {
			getErrorInfoAlert("请选择要操作的入库异常单");
			return;
		}

		// 用于存储草稿状态的单的ID
		var draftIdArray = [];
		$.each(ids.split(","),function(index,item){
		    var statusId = item+"_status";
		    var status = $("#"+statusId).text();
		    if (status == '草稿'){
                draftIdArray.push(item);
            }
        });

		if (draftIdArray.length == 0){
            $.ajax({
                url: CONTEXT_PATH + "checkInException/batchScrapException",
                type: "GET",
                data: {ids:ids},
                success: function(data){
                    if (data.message) {
                        layer.alert(data.message, {closeBtn: 0}, function (index) {
                            layer.close(index);
                            window.location.reload();
                        });
                    } else {
                        layer.alert("成功", {closeBtn: 0}, function (index) {
                            layer.close(index);
                            window.location.reload();
                        });
                    }

                }
            });
        }else{
            //草稿状态所填写的备注
            var remark = '';
            dialog({
                title: '存在草稿状态的订单，需填写备注项',
                width: 250,
                height: 50,
                content: "<div>备注:<input type= 'text' id='remarkContent' style='width: 218px'/></div>",
                okValue: '确定',
                ok: function () {
                    remark = $("#remarkContent").val().trim();
                    if (!remark || remark == '' || remark == undefined){
                        layer.alert("备注必须填写!","error");
                        return;
                    }
                    $.ajax({
                        url: CONTEXT_PATH + "checkInException/batchScrapException",
                        type: "GET",
                        data: {ids:ids,remark:remark},
                        success: function(data){
                            if (data.message) {
                                layer.alert(data.message, {closeBtn: 0}, function (index) {
                                    layer.close(index);
                                    window.location.reload();
                                });
                            } else {
                                layer.alert("成功", {closeBtn: 0}, function (index) {
                                    layer.close(index);
                                    window.location.reload();
                                });
                            }

                        }
                    });
                },
                cancelValue: '取消操作',
                cancel: function () {
                }
            }).showModal();
        }

	}

	function printExceptionTag(id) {
		var printPageUrl = CONTEXT_PATH + "checkInException/printExceptionTag?id=" + id;
		$('#exceptionTagPrintHtml').attr('src', printPageUrl);
		//自动打印
		setTimeout(IframeOnloadPrint, 100);

	}

	function IframeOnloadPrint(){
		var iframe=document.getElementById("exceptionTagPrintHtml");
		// 加载完iframe后执行
		if (iframe.attachEvent){
			iframe.attachEvent("onload", function(){
				myPrint();
			});
		} else {
			iframe.onload = function(){
				setTimeout(myPrint, 500);/*延时0.5秒打印*/
				return;
			};
		}
	}

	/** 打印 **/
	var LODOP; //声明为全局变量
	function myPrint() {
		App.unblockUI();

		//先判断 内页中是否有 打印 方法 有的话直接调用
		try{
			if(typeof(eval(window.frames["exceptionTagPrintHtml"].myPrint))=='function'){
				return window.frames["exceptionTagPrintHtml"].myPrint();
			}
		}catch(e){
		}

		try{
			CreatePrintPage();
			LODOP.PRINT();
		}
		catch(e){

		}
	};

    function batchSendException() {
        if(getCheckedIds().length == 0) {
			getErrorInfoAlert("请选择要操作的入库异常单");
            return;
        }
        $.ajax({
            url: CONTEXT_PATH + "checkInException/batchUpdateExceptionToPurchasePending",
            type: "POST",
            data: {ids:getCheckedIds()},
            success: function(data){
                if(data.status == '200'){
                    alert("成功");
                }else {
					getErrorInfoAlert(data.message);
                }
                setTimeout(function () {
                    window.location.reload();
                }, 2000);
            }
        });
    }

	function batchWaitCheckIn() {
		if(getCheckedIds().length == 0) {
			getErrorInfoAlert("请选择要操作的入库异常单");
			return;
		}
		$.ajax({
			url: CONTEXT_PATH + "checkInException/batchUpdateToWaitCheckIn",
			type: "POST",
			data: {ids:getCheckedIds()},
			success: function(data){
				if (data.status == '200') {
					if (!data.message) {
						alert("成功");
						setTimeout(function () {
							window.location.reload();
						}, 2000);
					}
				}
				if (data.message) {
					layer.open({
						content: data.message,
						yes: function(){
							window.location.reload();
						},
						cancel: function(){
							window.location.reload();
						}
					});
				}
			}
		});
	}

	function batchUpdateLocation() {
		if(getCheckedIds().length == 0) {
			getErrorInfoAlert("请选择要操作的入库异常单");
			return;
		}

		var html = "<div style='margin-top: 20px;'>" +
				   "	<label class='control-label col-md-3'>新库位号：</label>" +
				   "	<div class='col-md-7'>" +
				   "		<input class='form-control' id='newLocation' type='text' value=''>" +
				   "	</div>" +
				   "	<br><br><label class='control-label col-md-3'></label>" +
				   "	<div class='col-md-7'>留空保存时，会将异常单从库位上下架</div>" +
				   "</div>";

		layer.open({
			type: 1,
			skin: 'layui-layer-rim', //加上边框
			title: '修改上架库位',
			area: ['500px', '200px'], //宽高
			shadeClose: false, //开启遮罩关闭
			btn: ['确定'],
			content: html,
			yes: function (index, layero) {
				var newLocation = layero.find("input[id='newLocation']").val();
				doUpdateLocation(index,newLocation,getCheckedIds());
			},
			end: function (index) {
				layer.close(index);
				location.reload();
			}
		});
	}

	function doUpdateLocation(index, newLocation, ids) {
		$.ajax({
			url: CONTEXT_PATH + "checkInException/batchUpdateLocation",
			type: "POST",
			data: {ids: ids, location: newLocation},
			success: function (data) {
				if (data.status == '200') {
					if (!data.message) {
						layer.alert('成功', function (index) {
							layer.close(index);
							location.reload();
						});
					}
				}
				if (data.message) {
					layer.open({
						content: data.message,
						end: function (index) {
							layer.close(index);
							location.reload();
						}
					});
				}
			}
		});
	}

    function showUnScanUuId(id) {
        var diglog = dialog({
            title: 'QC全检未扫描的唯一码！',
            width: 500,
            //height:100,
            content: $('#returnInformation_'+id),
            onshow: function() {},
            okValue: '确定',
            ok: function() {
                setTimeout(function() {
                    diglog.close().remove();
                }, 100);
                return false;
            },
            cancelValue: '关闭',
            cancel: function() {}
        }).showModal();
    }

    //同步异常信息到采购系统
    function batchReSendExceptionToPms(ids) {
        if(getCheckedIds().length == 0) {
			getErrorInfoAlert("请选择要同步的入库异常单");
            return;
        }
        window.location.href = CONTEXT_PATH + "checkInException/batchReSendExceptionToPms?isNew=true&ids=" + getCheckedIds();
    }

    // 打印带货清单
    function printCarryItems() {
        if(getCheckedIds().length == 0) {
			getErrorInfoAlert("请选择要打印异常单");
            return;
        }
        window.open(CONTEXT_PATH + "checkInException/printCarryItems?ids=" + getCheckedIds());
    }

    // 批量打印带货标签
    function bathPrintCarryExceptions() {
        if(getCheckedIds().length == 0) {
			getErrorInfoAlert("请选择要批量打印异常单");
            return;
        }
        
        // 使用layer弹窗显示选择项
        layer.open({
            type: 1,
            title: '选择带货类型',
            area: ['400px', '300px'],
            content: '<div style="padding: 20px;">' +
                     '<div style="margin-bottom: 15px;">' +
                     '<label><input type="radio" name="carryType" value="headquarters_purchase" checked> 总部采购带货</label>' +
                     '</div>' +
                     '<div style="margin-bottom: 15px;">' +
                     '<label><input type="radio" name="carryType" value="headquarters_development"> 总部开发带货</label>' +
                     '</div>' +
                     '<div style="margin-bottom: 15px;">' +
                     '<label><input type="radio" name="carryType" value="followup_purchase"> 跟单采购带货</label>' +
                     '</div>' +
                     '</div>',
            btn: ['确定', '取消'],
            yes: function(index, layero) {
                var selectedType = layero.find('input[name="carryType"]:checked').val();
                window.open(CONTEXT_PATH + "checkInException/bathPrintCarryExceptions?ids=" + getCheckedIds() + "&carryType=" + selectedType);
                layer.close(index);
            }
        });
    }

	// 导出
	function download() {
		var params = $('#domain').serialize();
		var ids = $("input[name='ids']:checked");
		debugger;
		if (ids.length > 0) {
			params = params + "&" +ids.serialize();
		} else {
			params = params ;
		}
		$.post(CONTEXT_PATH + "checkInException/download", params, function(data){
			if (data.status == 200) {
				if (data.message==null || data.message==''){
					layer.alert('成功',function (index) {
						layer.close(index);
						diglog.close().remove();
						location.reload();
					});
				}else{
					customizeLayer(data.message);
				}
			} else {
				customizeLayer(data.message);
			}
		});
	}


	// 超过500条不能用GET请求
	function downloadByPost(){
		var ids = getCheckedIds();
		var url= CONTEXT_PATH + "checkInException/download";
		var tempForm = document.createElement("form");
		tempForm.id="tempForm";
		tempForm.method="post";
		tempForm.action=url;
		tempForm.target="blank";
		var hideInput = document.createElement("input");
		hideInput.type="hidden";
		hideInput.name="ids";
		hideInput.value= ids;
		tempForm.appendChild(hideInput);
		if (tempForm.attachEvent) {  // IE
			tempForm.attachEvent("onsubmit",function(){ window.open('about:blank','blank'); });
		} else if (tempForm.addEventListener) {  // DOM Level 2 standard
			tempForm.addEventListener("onsubmit",function(){ window.open('about:blank','blank'); });
		}
		document.body.appendChild(tempForm);
		if (document.createEvent) { // DOM Level 2 standard
			evt = document.createEvent("MouseEvents");
			evt.initMouseEvent("submit", true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
			tempForm.dispatchEvent(evt);
		} else if (tempForm.fireEvent) { // IE
			tempForm.fireEvent('onsubmit');
		}
		//必须手动的触发
		tempForm.submit();
		document.body.removeChild(tempForm);
	}

	/*编辑保存查询条件*/
    function editFunc($this){
    	redirectWithSearch($this.getAttribute("url"), $("#domain"), "checkinexception_search");
    }

	window.onload=function(){
		var pageinfo = sessionStorage.getItem("searchPageinfo");
		if(null != pageinfo){
			searchReload("checkinexception_search", $("#domain"));
		}
	};
	/*编辑保存查询条件*/
	function toReturnOrderList(id) {
        var action = document.checkInExceptionForm.action;
        var target = document.checkInExceptionForm.target;
        var method = document.checkInExceptionForm.method;
        document.checkInExceptionForm.action= CONTEXT_PATH + "returnPurchaseOrder/search?query.exceptionIds="+id;
        document.checkInExceptionForm.target="_blank";
        document.checkInExceptionForm.method="POST";
        document.checkInExceptionForm.submit();
        document.checkInExceptionForm.target=target;
        document.checkInExceptionForm.action=action;
        document.checkInExceptionForm.method=method;
    }

    // 单个异常单标记
    function markException(exceptionId) {
        // 先获取异常单信息
        $.ajax({
            url: CONTEXT_PATH + "checkInException/getExceptionInfo/" + exceptionId,
            type: "GET",
            success: function(data) {
                if (data.success) {
                    showMarkDialog(data.result);
                } else {
                    layer.alert("获取异常单信息失败：" + (data.errorMsg || "未知错误"));
                }
            },
            error: function() {
                layer.alert("获取异常单信息失败，请检查网络连接");
            }
        });
    }

    // 显示标记对话框
    function showMarkDialog(exception) {
        var html = '<div style="padding: 20px;">';
        html += '<h4 style="margin-bottom: 15px;">异常单信息：</h4>';
        html += '<div style="background-color: #f9f9f9; padding: 10px; margin-bottom: 15px; border-radius: 4px;">';
        html += '  <div style="margin-bottom: 5px;"><strong>快递单号：</strong>' + (exception.trackingNumber || '-') + '</div>';
        html += '  <div style="margin-bottom: 5px;"><strong>采购单号：</strong>' + (exception.purchaseOrderNo || '-') + '</div>';
        html += '  <div style="margin-bottom: 5px;"><strong>SKU：</strong>' + (exception.sku || '-') + '</div>';
        html += '  <div><strong>异常类型：</strong>' + (exception.exceptionTypeName || '-') + '</div>';
        html += '</div>';
        html += '<div style="margin-bottom: 15px;">';
        html += '  <label style="display: block; margin-bottom: 5px;"><strong>标记原因：</strong></label>';
        html += '  <select id="markReasonSelect" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">';
        html += '    <option value="">请选择标记原因</option>';
        // 动态添加标记原因选项
        for (var i = 0; i < markReasons.length; i++) {
            html += '    <option value="' + markReasons[i].id + '">' + markReasons[i].text + '</option>';
        }
        html += '    <option value="custom">自定义原因</option>';
        html += '  </select>';
        html += '</div>';
        html += '<div id="customReasonDiv" style="margin-bottom: 15px; display: none;">';
        html += '  <label style="display: block; margin-bottom: 5px;"><strong>自定义原因：</strong></label>';
        html += '  <input type="text" id="customReason" placeholder="请输入自定义标记原因" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" maxlength="100">';
        html += '</div>';
        html += '<div>';
        html += '  <label style="display: block; margin-bottom: 5px;"><strong>备注信息：</strong></label>';
        html += '  <textarea id="markRemark" placeholder="请输入备注信息" style="width: 100%; height: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;" maxlength="500"></textarea>';
        html += '</div>';
        html += '</div>';

        layer.open({
            type: 1,
            title: '标记异常单',
            area: ['500px', '600px'],
            content: html,
            btn: ['确认标记', '取消'],
            yes: function(index, layero) {
                var reasonSelect = layero.find('#markReasonSelect').val();
                var customReason = layero.find('#customReason').val().trim();
                var remark = layero.find('#markRemark').val().trim();
                
                var reason = '';
                if (reasonSelect === 'custom') {
                    if (!customReason) {
                        layer.alert("请输入自定义标记原因");
                        return false;
                    }
                    reason = customReason;
                } else {
                    if (!reasonSelect) {
                        layer.alert("请选择标记原因");
                        return false;
                    }
                    reason = reasonSelect;
                }
                
                // 执行标记
                doMark([exception.id], reason, remark, index);
                return false;
            },
            success: function(layero) {
                // 绑定原因选择变化事件
                layero.find('#markReasonSelect').change(function() {
                    if ($(this).val() === 'custom') {
                        layero.find('#customReasonDiv').show();
                    } else {
                        layero.find('#customReasonDiv').hide();
                    }
                });
            }
        });
    }

    // 批量标记
    function batchMark() {
        var checkedIds = getCheckedIds();
        if (!checkedIds || checkedIds.length === 0) {
            layer.alert("请选择要标记的异常单");
            return;
        }

        var html = '<div style="padding: 20px;">';
        html += '<div style="margin-bottom: 15px;">';
        html += '  <div style="color: #666;">已选择 <span style="color: #ff6600; font-weight: bold;">' + checkedIds.split(',').length + '</span> 个异常单进行批量标记</div>';
        html += '</div>';
        html += '<div style="margin-bottom: 15px;">';
        html += '  <label style="display: block; margin-bottom: 5px;"><strong>标记原因：</strong></label>';
        html += '  <select id="batchMarkReasonSelect" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">';
        html += '    <option value="">请选择标记原因</option>';
        // 动态添加标记原因选项
        for (var i = 0; i < markReasons.length; i++) {
            html += '    <option value="' + markReasons[i].id + '">' + markReasons[i].text + '</option>';
        }
        html += '    <option value="custom">自定义原因</option>';
        html += '  </select>';
        html += '</div>';
        html += '<div id="batchCustomReasonDiv" style="margin-bottom: 15px; display: none;">';
        html += '  <label style="display: block; margin-bottom: 5px;"><strong>自定义原因：</strong></label>';
        html += '  <input type="text" id="batchCustomReason" placeholder="请输入自定义标记原因" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" maxlength="100">';
        html += '</div>';
        html += '<div>';
        html += '  <label style="display: block; margin-bottom: 5px;"><strong>备注信息：</strong></label>';
        html += '  <textarea id="batchMarkRemark" placeholder="请输入备注信息" style="width: 100%; height: 80px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;" maxlength="500"></textarea>';
        html += '</div>';
        html += '</div>';

        layer.open({
            type: 1,
            title: '批量标记异常单',
            area: ['500px', '500px'],
            content: html,
            btn: ['确认标记', '取消'],
            yes: function(index, layero) {
                var reasonSelect = layero.find('#batchMarkReasonSelect').val();
                var customReason = layero.find('#batchCustomReason').val().trim();
                var remark = layero.find('#batchMarkRemark').val().trim();
                
                var reason = '';
                if (reasonSelect === 'custom') {
                    if (!customReason) {
                        layer.alert("请输入自定义标记原因");
                        return false;
                    }
                    reason = customReason;
                } else {
                    if (!reasonSelect) {
                        layer.alert("请选择标记原因");
                        return false;
                    }
                    reason = reasonSelect;
                }
                
                // 执行批量标记
                doMark(checkedIds.split(','), reason, remark, index);
                return false;
            },
            success: function(layero) {
                // 绑定原因选择变化事件
                layero.find('#batchMarkReasonSelect').change(function() {
                    if ($(this).val() === 'custom') {
                        layero.find('#batchCustomReasonDiv').show();
                    } else {
                        layero.find('#batchCustomReasonDiv').hide();
                    }
                });
            }
        });
    }

    // 执行标记操作
    function doMark(exceptionIds, reason, remark, dialogIndex) {
        $.ajax({
            url: CONTEXT_PATH + "checkInException/markExceptions",
            type: "POST",
            data: {
                ids: exceptionIds.join ? exceptionIds.join(',') : exceptionIds,
                markReason: reason,
                markRemark: remark
            },
            success: function(data) {
                if (data.success) {
                    layer.close(dialogIndex);
                    layer.msg("标记成功", { icon: 1, time: 1500 });
                    window.location.reload();
                } else {
                    layer.alert("标记失败：" + (data.message || "未知错误"));
                }
            },
            error: function() {
                layer.alert("标记失败，请检查网络连接");
            }
        });
    }

    // 取消标记
    function cancelMark(exceptionId) {
        layer.confirm('确定要取消此异常单的标记吗？', {
            btn: ['确定', '取消']
        }, function(index) {
            $.ajax({
                url: CONTEXT_PATH + "checkInException/cancelMark",
                type: "POST",
                data: { id: exceptionId },
                success: function(data) {
                    if (data.success) {
                        layer.close(index);
                        layer.msg("取消标记成功", { icon: 1, time: 1500 });
                        window.location.reload();
                    } else {
                        layer.alert("取消标记失败：" + (data.message || "未知错误"));
                    }
                },
                error: function() {
                    layer.alert("取消标记失败，请检查网络连接");
                }
            });
        });
    }

    // 打开标记原因配置弹窗
    function openMarkReasonConfig() {
        // 先加载现有原因列表
        $.ajax({
            url: CONTEXT_PATH + "exceptionMarkConfig/getAvailableReasons",
            type: "POST",
            dataType: "json",
            success: function(data) {
                if (data.success === true) {
                    showMarkReasonDialog(data.result || []);
                } else {
                    layer.alert("加载原因列表失败：" + (data.errorMsg || "未知错误"));
                }
            },
            error: function() {
                layer.alert("加载原因列表失败，请检查网络连接");
            }
        });
    }

    // 显示标记原因配置对话框
    function showMarkReasonDialog(reasons) {
        var html = '<div style="padding: 20px;">';
        
        // 现有原因列表
        html += '<div style="margin-bottom: 20px;">';
        html += '<h4 style="margin-bottom: 10px;">现有标记原因：</h4>';
        html += '<div id="reasonsList" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background-color: #f9f9f9;">';
        if (reasons.length === 0) {
            html += '<div style="color: #999; text-align: center;">暂无原因</div>';
        } else {
            for (var i = 0; i < reasons.length; i++) {
                html += '<div class="reason-item" data-reason="' + reasons[i] + '" style="padding: 8px 0; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">';
                html += '  <span style="flex: 1;">' + reasons[i] + '</span>';
                html += '  <button type="button" class="btn btn-danger btn-xs remove-reason-btn" data-reason="' + reasons[i] + '" style="margin-left: 10px; padding: 2px 6px;">删除</button>';
                html += '</div>';
            }
        }
        html += '</div>';
        html += '</div>';
        
        // 添加新原因
        html += '<div>';
        html += '<h4 style="margin-bottom: 10px;">添加新原因：</h4>';
        html += '<input type="text" id="newReasonInput" placeholder="请输入标记原因名称（如：商品变形）" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;" maxlength="100">';
        html += '</div>';
        html += '</div>';

        layer.open({
            type: 1,
            title: '标记原因配置',
            area: ['550px', '650px'],
            content: html,
            btn: ['添加原因', '关闭'],
            yes: function(index, layero) {
                var name = layero.find('#newReasonInput').val().trim();
                
                if (!name) {
                    layer.alert("请输入原因名称");
                    return false;
                }
                
                if (name.length > 100) {
                    layer.alert("原因名称不能超过100个字符");
                    return false;
                }
                
                // 执行添加
                $.ajax({
                    url: CONTEXT_PATH + "exceptionMarkConfig/addCustomReason",
                    type: "POST",
                    data: { reasonName: name },
                    dataType: "json",
                    success: function (data) {
                        if (data.success === true) {
                            layer.close(index);
                            layer.msg("添加成功", { icon: 1, time: 1500 });
                            window.location.reload();
                        } else {
                            layer.alert("添加失败：" + (data.errorMsg || "未知错误"));
                        }
                    },
                    error: function() {
                        layer.alert("添加失败，请检查网络连接");
                    }
                });
                return false;
            },
            btn2: function(index) {
                layer.close(index);
            },
            success: function(layero) {
                // 绑定删除按钮事件
                layero.find('.remove-reason-btn').click(function() {
                    var reasonName = $(this).data('reason');
                    var currentItem = $(this).closest('.reason-item');
                    
                    layer.confirm('确定要删除标记原因"' + reasonName + '"吗？', {
                        btn: ['确定', '取消']
                    }, function(confirmIndex) {
                        // 执行删除
                        $.ajax({
                            url: CONTEXT_PATH + "exceptionMarkConfig/removeCustomReason",
                            type: "POST",
                            data: { reasonName: reasonName },
                            dataType: "json",
                            success: function (data) {
                                if (data.success === true) {
                                    layer.close(confirmIndex);
                                    layer.msg("删除成功", { icon: 1, time: 1500 });
                                    // 从界面上移除该项
                                    currentItem.fadeOut(300, function() {
                                        $(this).remove();
                                        // 检查是否还有原因，如果没有显示暂无原因
                                        if (layero.find('.reason-item').length === 0) {
                                            layero.find('#reasonsList').html('<div style="color: #999; text-align: center;">暂无原因</div>');
                                        }
                                    });
                                } else {
                                    layer.alert("删除失败：" + (data.errorMsg || "未知错误"));
                                }
                            },
                            error: function() {
                                layer.alert("删除失败，请检查网络连接");
                            }
                        });
                    });
                });
            }
        });
    }
</script>
</body>
</html>