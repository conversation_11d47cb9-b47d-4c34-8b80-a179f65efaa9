<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html>
<head>
    <title>易世通达仓库管理系统</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <#include "/common/include.html">
</head>
<body class="page-header-fixed page-full-width">
<div id="order-log-area" style="max-height: 405px;overflow: auto;text-align: center">
    <table class="table table-condensed table-bordered  table-striped">
        <thead>
        <tr>
            <th>时间</th>
            <th>处理备注</th>
            <th>处理方式</th>
            <th>处理结果状态</th>
            <th>处理数量</th>
            <th>操作人</th>
        </tr>
        </thead>
        <tbody>
        <#list domain.whCheckInExceptionHandles as log>
            <tr>
                <td>${log.creationDate}</td>
                <td>
                    <#if !log.handleComment>
                        无
                        <#else>
                        ${log.handleComment}
                    </#if>
                </td>
                <td>
                    <#if !log.handleWay>
                        无
                    <#elseif log.handleWay == 99 || log.handleWay == 100>
                        异常标记
                    <#else>
                        ${log.exceptionHandleWayName}
                    </#if>
                </td>
                <td>
                    <#if log.handleWay == 99>
                        已标记
                    <#elseif log.handleWay == 100>
                        取消标记
                    <#elseif !log.status>
                        无
                    <#else>
                        ${log.exceptionStatusName}
                    </#if>
                </td>
                <td>${log.quantity}</td>
                <td>
                    <#if !log.createUserName>
                        ${util('name',log.createdBy)}
                        <#else>
                        ${log.createUserName}
                    </#if>
                </td>
            </tr>
        </#list>
        <#if !domain.whCheckInExceptionHandles >
            <tr class="tc">
                <td colspan="99" align="center">没有记录</td>
            </tr>
        </#if>
        </tbody>
    </table>
</div>
</body>
<!-- END BODY -->
</html>