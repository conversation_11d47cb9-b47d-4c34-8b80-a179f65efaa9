package com.estone.oversea;

import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.apv.domain.WhApvMoreProductsDo;
import com.estone.apv.service.WhApvService;
import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.enums.PickingTaskType;
import com.estone.warehouse.service.WhBoxService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 海外仓与中转仓逻辑对比验证测试
 * 
 * 测试目标：确保海外仓头程单拆分后的包装和播种逻辑与中转仓逻辑完全一致
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class OverseasTransferComparisonTest {

    @Mock
    private WhApvService whApvService;
    
    @Mock
    private WhBoxService whBoxService;

    private static final String GRID_SCAN_VIEW = "transfer/transfer_grid_scan_view";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 对比测试1: 单品单件包装逻辑一致性验证
     * 
     * 验证海外仓单品任务使用TRANSFER_SINGLESINGLE与纯中转仓TRANSFER_SINGLESINGLE的包装逻辑一致
     */
    @Test
    public void testSingleSinglePackingLogicConsistency() {
        // 准备SS类型的APV测试数据
        List<WhApv> ssApvs = Arrays.asList(
            createMockApv("APV_SS_001", "SS"),
            createMockApv("APV_SS_002", "SS")
        );
        when(whApvService.queryByCondition(any(WhApvQueryCondition.class))).thenReturn(ssApvs);

        // 海外仓单品任务使用TRANSFER_SINGLESINGLE包装
        WhApvQueryCondition overseasCondition = new WhApvQueryCondition();
        overseasCondition.setTaskType(PickingTaskType.ASN_FIRST_SINGLE.intCode());
        overseasCondition.setPickType("TRANSFER_SINGLESINGLE");
        overseasCondition.setApvType("SS");
        List<WhApv> overseasResult = whApvService.queryByCondition(overseasCondition);

        // 纯中转仓TRANSFER_SINGLESINGLE包装
        WhApvQueryCondition transferCondition = new WhApvQueryCondition();
        transferCondition.setTaskType(PickingTaskType.TRANSFER_SINGLESINGLE.intCode());
        transferCondition.setPickType("TRANSFER_SINGLESINGLE");
        transferCondition.setApvType("SS");
        List<WhApv> transferResult = whApvService.queryByCondition(transferCondition);

        // 验证结果一致性
        assertEquals("海外仓和中转仓单品单件包装应返回相同数量的APV", 
            overseasResult.size(), transferResult.size());
        
        // 验证APV类型过滤一致性
        for (int i = 0; i < overseasResult.size(); i++) {
            assertEquals("APV类型应该一致", 
                overseasResult.get(i).getApvType(), transferResult.get(i).getApvType());
            assertEquals("应该都是SS类型", "SS", overseasResult.get(i).getApvType());
        }
    }

    /**
     * 对比测试2: 单品多件包装逻辑一致性验证
     * 
     * 验证海外仓单品任务使用TRANSFER_SINGLEMULTIPLE与纯中转仓TRANSFER_SINGLEMULTIPLE的包装逻辑一致
     */
    @Test
    public void testSingleMultiplePackingLogicConsistency() {
        // 准备SM类型的APV测试数据
        List<WhApv> smApvs = Arrays.asList(
            createMockApv("APV_SM_001", "SM"),
            createMockApv("APV_SM_002", "SM"),
            createMockApv("APV_SM_003", "SM")
        );
        when(whApvService.queryByCondition(any(WhApvQueryCondition.class))).thenReturn(smApvs);

        // 海外仓单品任务使用TRANSFER_SINGLEMULTIPLE包装
        WhApvQueryCondition overseasCondition = new WhApvQueryCondition();
        overseasCondition.setTaskType(PickingTaskType.ASN_FIRST_SINGLE.intCode());
        overseasCondition.setPickType("TRANSFER_SINGLEMULTIPLE");
        overseasCondition.setApvType("SM");
        List<WhApv> overseasResult = whApvService.queryByCondition(overseasCondition);

        // 纯中转仓TRANSFER_SINGLEMULTIPLE包装
        WhApvQueryCondition transferCondition = new WhApvQueryCondition();
        transferCondition.setTaskType(PickingTaskType.TRANSFER_SINGLEMULTIPLE.intCode());
        transferCondition.setPickType("TRANSFER_SINGLEMULTIPLE");
        transferCondition.setApvType("SM");
        List<WhApv> transferResult = whApvService.queryByCondition(transferCondition);

        // 验证结果一致性
        assertEquals("海外仓和中转仓单品多件包装应返回相同数量的APV", 
            overseasResult.size(), transferResult.size());
        
        for (int i = 0; i < overseasResult.size(); i++) {
            assertEquals("APV类型应该一致", 
                overseasResult.get(i).getApvType(), transferResult.get(i).getApvType());
            assertEquals("应该都是SM类型", "SM", overseasResult.get(i).getApvType());
        }
    }

    /**
     * 对比测试3: 多品多件包装逻辑一致性验证
     * 
     * 验证海外仓多品任务使用TRANSFER_MULTIPLEMULTIPLE与纯中转仓TRANSFER_MULTIPLEMULTIPLE的包装逻辑一致
     */
    @Test
    public void testMultipleMultiplePackingLogicConsistency() {
        // 准备MM类型的APV测试数据
        List<WhApv> mmApvs = Arrays.asList(
            createMockApv("APV_MM_001", "MM"),
            createMockApv("APV_MM_002", "MM")
        );
        when(whApvService.queryByCondition(any(WhApvQueryCondition.class))).thenReturn(mmApvs);

        // 海外仓多品任务使用TRANSFER_MULTIPLEMULTIPLE包装
        WhApvQueryCondition overseasCondition = new WhApvQueryCondition();
        overseasCondition.setTaskType(PickingTaskType.ASN_FIRST_MULTIPLE.intCode());
        overseasCondition.setPickType("TRANSFER_MULTIPLEMULTIPLE");
        overseasCondition.setApvType("MM");
        List<WhApv> overseasResult = whApvService.queryByCondition(overseasCondition);

        // 纯中转仓TRANSFER_MULTIPLEMULTIPLE包装
        WhApvQueryCondition transferCondition = new WhApvQueryCondition();
        transferCondition.setTaskType(PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode());
        transferCondition.setPickType("TRANSFER_MULTIPLEMULTIPLE");
        transferCondition.setApvType("MM");
        List<WhApv> transferResult = whApvService.queryByCondition(transferCondition);

        // 验证结果一致性
        assertEquals("海外仓和中转仓多品多件包装应返回相同数量的APV", 
            overseasResult.size(), transferResult.size());
        
        for (int i = 0; i < overseasResult.size(); i++) {
            assertEquals("APV类型应该一致", 
                overseasResult.get(i).getApvType(), transferResult.get(i).getApvType());
            assertEquals("应该都是MM类型", "MM", overseasResult.get(i).getApvType());
        }
    }

    /**
     * 对比测试4: 播种逻辑路由一致性验证
     * 
     * 验证海外仓和中转仓任务在播种时的路由逻辑正确性
     */
    @Test
    public void testSowingRoutingLogicConsistency() {
        // 测试海外仓任务在海外仓播种页面的处理
        WhApvMoreProductsDo overseasDomain = new WhApvMoreProductsDo();
        WhPickingTask overseasTask = createOverseasTask(PickingTaskType.ASN_FIRST_SINGLE);
        String overseasResult = simulateOverseasSowingLogic(overseasDomain, overseasTask);
        
        // 海外仓任务应该能正常进入播种流程
        assertNotEquals("海外仓任务应该能在海外仓播种页面正常处理", GRID_SCAN_VIEW, overseasResult);
        
        // 测试中转仓任务在海外仓播种页面的处理
        WhApvMoreProductsDo transferInOverseasDomain = new WhApvMoreProductsDo();
        WhPickingTask transferTask = createTransferTask(PickingTaskType.TRANSFER_SINGLESINGLE);
        String transferInOverseasResult = simulateOverseasSowingLogic(transferInOverseasDomain, transferTask);
        
        // 中转仓任务应该被拒绝，提示使用中转仓播种
        assertEquals("中转仓任务应该被拒绝在海外仓播种页面", GRID_SCAN_VIEW, transferInOverseasResult);
        assertTrue("应该提示使用中转仓播种", 
            transferInOverseasDomain.getErrorMsg().contains("中转仓拣货类型，请使用中转仓播种"));
        
        // 测试海外仓任务在中转仓播种页面的处理
        WhApvMoreProductsDo overseasInTransferDomain = new WhApvMoreProductsDo();
        String overseasInTransferResult = simulateTransferSowingLogic(overseasInTransferDomain, overseasTask);
        
        // ASN_FIRST_SINGLE应该被提示不需要播种
        assertEquals("ASN_FIRST_SINGLE任务应该被告知不需要播种", GRID_SCAN_VIEW, overseasInTransferResult);
        assertTrue("应该提示不需要播种", 
            overseasInTransferDomain.getErrorMsg().contains("不需要播种"));
    }

    /**
     * 对比测试5: APV类型过滤严格性对比
     * 
     * 验证海外仓和中转仓对APV类型过滤的严格程度一致
     */
    @Test
    public void testApvTypeFilteringStrictnessConsistency() {
        // 创建混合类型的APV数据进行过滤测试
        List<WhApv> mixedApvs = Arrays.asList(
            createMockApv("APV_SS_001", "SS"),
            createMockApv("APV_SM_001", "SM"),
            createMockApv("APV_MM_001", "MM")
        );
        
        // 测试只返回特定类型的过滤效果
        when(whApvService.queryByCondition(any(WhApvQueryCondition.class)))
            .thenAnswer(invocation -> {
                WhApvQueryCondition condition = invocation.getArgument(0);
                String apvType = condition.getApvType();
                return mixedApvs.stream()
                    .filter(apv -> apv.getApvType().equals(apvType))
                    .collect(java.util.stream.Collectors.toList());
            });

        // 海外仓SS类型过滤
        WhApvQueryCondition overseasSsCondition = new WhApvQueryCondition();
        overseasSsCondition.setTaskType(PickingTaskType.ASN_FIRST_SINGLE.intCode());
        overseasSsCondition.setApvType("SS");
        List<WhApv> overseasSsResult = whApvService.queryByCondition(overseasSsCondition);

        // 中转仓SS类型过滤
        WhApvQueryCondition transferSsCondition = new WhApvQueryCondition();
        transferSsCondition.setTaskType(PickingTaskType.TRANSFER_SINGLESINGLE.intCode());
        transferSsCondition.setApvType("SS");
        List<WhApv> transferSsResult = whApvService.queryByCondition(transferSsCondition);

        // 验证过滤结果一致性
        assertEquals("SS类型过滤结果数量应该一致", overseasSsResult.size(), transferSsResult.size());
        assertEquals("海外仓应该只返回1个SS类型APV", 1, overseasSsResult.size());
        assertEquals("中转仓应该只返回1个SS类型APV", 1, transferSsResult.size());
        assertEquals("都应该是SS类型", "SS", overseasSsResult.get(0).getApvType());
        assertEquals("都应该是SS类型", "SS", transferSsResult.get(0).getApvType());
    }

    /**
     * 对比测试6: 任务类型验证逻辑一致性
     * 
     * 验证任务类型验证的逻辑在海外仓和中转仓中是否一致
     */
    @Test
    public void testTaskTypeValidationConsistency() {
        // 测试任务类型分类的一致性
        
        // 验证海外仓任务类型归类
        assertTrue("ASN_FIRST_SINGLE应该属于中转仓任务类型集合", 
            PickingTaskType.getTransferIntCode().contains(PickingTaskType.ASN_FIRST_SINGLE.intCode()));
        assertTrue("ASN_FIRST_MULTIPLE应该属于中转仓任务类型集合", 
            PickingTaskType.getTransferIntCode().contains(PickingTaskType.ASN_FIRST_MULTIPLE.intCode()));
        
        // 验证中转仓任务类型归类
        assertTrue("TRANSFER_SINGLESINGLE应该属于中转仓任务类型集合", 
            PickingTaskType.getTransferIntCode().contains(PickingTaskType.TRANSFER_SINGLESINGLE.intCode()));
        assertTrue("TRANSFER_MULTIPLEMULTIPLE应该属于中转仓任务类型集合", 
            PickingTaskType.getTransferIntCode().contains(PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode()));
        
        // 验证JIT任务类型不被误判
        assertFalse("JIT任务不应该被归为中转仓任务", 
            PickingTaskType.getTransferIntCode().contains(PickingTaskType.JIT_SINGLESINGLE.intCode()));
    }

    /**
     * 对比测试7: 查询性能一致性
     * 
     * 验证海外仓和中转仓的查询性能基本一致
     */
    @Test
    public void testQueryPerformanceConsistency() {
        // 准备大量测试数据
        List<WhApv> largeApvList = createLargeApvList(500);
        when(whApvService.queryByCondition(any(WhApvQueryCondition.class))).thenReturn(largeApvList);

        // 测试海外仓查询性能
        long overseasStartTime = System.currentTimeMillis();
        WhApvQueryCondition overseasCondition = new WhApvQueryCondition();
        overseasCondition.setTaskType(PickingTaskType.ASN_FIRST_SINGLE.intCode());
        overseasCondition.setApvType("SS");
        whApvService.queryByCondition(overseasCondition);
        long overseasTime = System.currentTimeMillis() - overseasStartTime;

        // 测试中转仓查询性能
        long transferStartTime = System.currentTimeMillis();
        WhApvQueryCondition transferCondition = new WhApvQueryCondition();
        transferCondition.setTaskType(PickingTaskType.TRANSFER_SINGLESINGLE.intCode());
        transferCondition.setApvType("SS");
        whApvService.queryByCondition(transferCondition);
        long transferTime = System.currentTimeMillis() - transferStartTime;

        // 验证性能差异在合理范围内（差异不超过20%）
        double performanceDiff = Math.abs(overseasTime - transferTime) / (double) Math.max(overseasTime, transferTime);
        assertTrue("海外仓和中转仓查询性能差异应该在20%以内", performanceDiff < 0.2);
    }

    /**
     * 对比测试8: 边界条件处理一致性
     * 
     * 验证海外仓和中转仓对边界条件的处理逻辑一致
     */
    @Test
    public void testBoundaryConditionHandlingConsistency() {
        // 测试空结果处理
        when(whApvService.queryByCondition(any(WhApvQueryCondition.class)))
            .thenReturn(Arrays.asList());

        WhApvQueryCondition overseasEmptyCondition = new WhApvQueryCondition();
        overseasEmptyCondition.setTaskType(PickingTaskType.ASN_FIRST_SINGLE.intCode());
        overseasEmptyCondition.setApvType("INVALID");
        List<WhApv> overseasEmptyResult = whApvService.queryByCondition(overseasEmptyCondition);

        WhApvQueryCondition transferEmptyCondition = new WhApvQueryCondition();
        transferEmptyCondition.setTaskType(PickingTaskType.TRANSFER_SINGLESINGLE.intCode());
        transferEmptyCondition.setApvType("INVALID");
        List<WhApv> transferEmptyResult = whApvService.queryByCondition(transferEmptyCondition);

        // 验证空结果处理一致性
        assertEquals("海外仓和中转仓对空结果的处理应该一致", 
            overseasEmptyResult.size(), transferEmptyResult.size());
        assertEquals("都应该返回空列表", 0, overseasEmptyResult.size());
        assertEquals("都应该返回空列表", 0, transferEmptyResult.size());
    }

    // 辅助方法

    private WhApv createMockApv(String apvNo, String apvType) {
        WhApv apv = new WhApv();
        apv.setApvNo(apvNo);
        apv.setApvType(apvType);
        apv.setPlatform("Amazon");
        apv.setCountry("US");
        apv.setApvStatus(1);
        return apv;
    }

    private WhPickingTask createOverseasTask(PickingTaskType taskType) {
        WhPickingTask task = new WhPickingTask();
        task.setTaskNo("OVERSEAS_TASK_001");
        task.setTaskType(taskType.intCode());
        task.setWarehouseType(2); // 海外仓
        task.setIsAsn(taskType.intCode());
        return task;
    }

    private WhPickingTask createTransferTask(PickingTaskType taskType) {
        WhPickingTask task = new WhPickingTask();
        task.setTaskNo("TRANSFER_TASK_001");
        task.setTaskType(taskType.intCode());
        task.setWarehouseType(1); // 中转仓
        task.setIsAsn(PickingTaskType.ASN_PREPARE.intCode());
        return task;
    }

    private String simulateOverseasSowingLogic(WhApvMoreProductsDo domain, WhPickingTask whPickingTask) {
        try {
            domain.setTaskType(whPickingTask.getTaskType());
            domain.setWarehouseType(whPickingTask.getWarehouseType());
            domain.setIsAsn(whPickingTask.getIsAsn());

            // 模拟海外仓播种页面的验证逻辑
            boolean isAsn = whPickingTask.getIsAsn() != null
                    && PickingTaskType.getTransferIntCode().contains(whPickingTask.getIsAsn());

            if (isAsn || PickingTaskType.getJITIntCode().contains(whPickingTask.getTaskType())
                    || PickingTaskType.getJitAsnIntCode().contains(whPickingTask.getTaskType())) {
                domain.setErrorMsg("中转仓拣货类型，请使用中转仓播种！");
                return GRID_SCAN_VIEW;
            }

            return "success";
        } catch (Exception e) {
            domain.setErrorMsg(e.getMessage());
            return GRID_SCAN_VIEW;
        }
    }

    private String simulateTransferSowingLogic(WhApvMoreProductsDo domain, WhPickingTask whPickingTask) {
        try {
            // 模拟中转仓播种页面的验证逻辑
            if (PickingTaskType.ASN_FIRST_SINGLE.intCode().equals(whPickingTask.getTaskType())) {
                domain.setErrorMsg(PickingTaskType.ASN_FIRST_SINGLE.getName() + "拣货类型，不需要播种！");
                return GRID_SCAN_VIEW;
            }

            boolean isAsn = whPickingTask.getIsAsn() != null
                    && PickingTaskType.getTransferIntCode().contains(whPickingTask.getIsAsn());
            
            if (whPickingTask.getTaskType() == null || !isAsn
                    && !PickingTaskType.getJITIntCode().contains(whPickingTask.getTaskType())
                            && !PickingTaskType.getJitAsnIntCode().contains(whPickingTask.getTaskType())) {
                domain.setErrorMsg("扫描的非中转仓或者仓发任务 ！");
                return GRID_SCAN_VIEW;
            }
            
            return "success";
        } catch (Exception e) {
            domain.setErrorMsg(e.getMessage());
            return GRID_SCAN_VIEW;
        }
    }

    private List<WhApv> createLargeApvList(int size) {
        List<WhApv> apvList = new java.util.ArrayList<>();
        for (int i = 0; i < size; i++) {
            apvList.add(createMockApv("APV_" + String.format("%03d", i), "SS"));
        }
        return apvList;
    }
}