package com.estone.oversea;

import com.estone.apv.bean.WhApv;
import com.estone.apv.bean.WhApvQueryCondition;
import com.estone.apv.service.WhApvService;
import com.estone.picking.enums.PickingTaskType;
import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.service.WhPickingTaskService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 海外仓头程单拆分包装逻辑测试
 * 
 * 测试目标：验证海外仓任务复用中转仓包装页面时的APV类型过滤逻辑
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class OverseasWarehousePackingLogicTest {

    @Mock
    private WhApvService whApvService;
    
    @Mock
    private WhPickingTaskService whPickingTaskService;

    private WhPickingTask asnSingleTask;
    private WhPickingTask asnMultipleTask;
    private WhPickingTask transferSingleSingleTask;
    private WhPickingTask transferSingleMultipleTask;
    private WhPickingTask transferMultipleMultipleTask;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 创建海外仓单品任务
        asnSingleTask = new WhPickingTask();
        asnSingleTask.setTaskNo("TASK_ASN_SINGLE_001");
        asnSingleTask.setTaskType(PickingTaskType.ASN_FIRST_SINGLE.intCode());
        asnSingleTask.setWarehouseType(2); // 海外仓
        
        // 创建海外仓多品任务
        asnMultipleTask = new WhPickingTask();
        asnMultipleTask.setTaskNo("TASK_ASN_MULTI_001");
        asnMultipleTask.setTaskType(PickingTaskType.ASN_FIRST_MULTIPLE.intCode());
        asnMultipleTask.setWarehouseType(2); // 海外仓
        
        // 创建中转仓对比任务
        transferSingleSingleTask = new WhPickingTask();
        transferSingleSingleTask.setTaskNo("TASK_TRANSFER_SS_001");
        transferSingleSingleTask.setTaskType(PickingTaskType.TRANSFER_SINGLESINGLE.intCode());
        transferSingleSingleTask.setWarehouseType(1); // 中转仓
        
        transferSingleMultipleTask = new WhPickingTask();
        transferSingleMultipleTask.setTaskNo("TASK_TRANSFER_SM_001");
        transferSingleMultipleTask.setTaskType(PickingTaskType.TRANSFER_SINGLEMULTIPLE.intCode());
        transferSingleMultipleTask.setWarehouseType(1); // 中转仓
        
        transferMultipleMultipleTask = new WhPickingTask();
        transferMultipleMultipleTask.setTaskNo("TASK_TRANSFER_MM_001");
        transferMultipleMultipleTask.setTaskType(PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode());
        transferMultipleMultipleTask.setWarehouseType(1); // 中转仓
    }

    /**
     * 测试用例1: 海外仓单品任务复用TRANSFER_SINGLESINGLE包装
     * 
     * 预期结果：只查询SS类型的APV发货单
     */
    @Test
    public void testOverseasSingleTaskWithTransferSingleSinglePacking() {
        // 准备测试数据
        WhApv ssApv1 = createMockApv("APV_SS_001", "SS");
        WhApv ssApv2 = createMockApv("APV_SS_002", "SS");
        List<WhApv> expectedSsApvs = Arrays.asList(ssApv1, ssApv2);
        
        // 模拟服务调用返回只包含SS类型的APV
        when(whApvService.queryByCondition(any(WhApvQueryCondition.class)))
            .thenReturn(expectedSsApvs);
        
        // 执行测试：模拟海外仓单品任务调用TRANSFER_SINGLESINGLE包装逻辑
        WhApvQueryCondition condition = new WhApvQueryCondition();
        condition.setTaskType(PickingTaskType.ASN_FIRST_SINGLE.intCode());
        condition.setPickType("TRANSFER_SINGLESINGLE");
        // 关键验证点：APV类型过滤应该只包含SS
        condition.setApvType("SS");
        
        List<WhApv> result = whApvService.queryByCondition(condition);
        
        // 验证结果
        assertNotNull("查询结果不应为空", result);
        assertEquals("应该返回2个SS类型的APV", 2, result.size());
        
        // 验证所有返回的APV都是SS类型
        for (WhApv apv : result) {
            assertEquals("APV类型应该是SS", "SS", apv.getApvType());
        }
        
        // 验证服务被正确调用
        verify(whApvService, times(1)).queryByCondition(any(WhApvQueryCondition.class));
    }

    /**
     * 测试用例2: 海外仓单品任务复用TRANSFER_SINGLEMULTIPLE包装
     * 
     * 预期结果：只查询SM类型的APV发货单
     */
    @Test
    public void testOverseasSingleTaskWithTransferSingleMultiplePacking() {
        // 准备测试数据
        WhApv smApv1 = createMockApv("APV_SM_001", "SM");
        WhApv smApv2 = createMockApv("APV_SM_002", "SM");
        List<WhApv> expectedSmApvs = Arrays.asList(smApv1, smApv2);
        
        when(whApvService.queryByCondition(any(WhApvQueryCondition.class)))
            .thenReturn(expectedSmApvs);
        
        // 执行测试
        WhApvQueryCondition condition = new WhApvQueryCondition();
        condition.setTaskType(PickingTaskType.ASN_FIRST_SINGLE.intCode());
        condition.setPickType("TRANSFER_SINGLEMULTIPLE");
        // 关键验证点：APV类型过滤应该只包含SM
        condition.setApvType("SM");
        
        List<WhApv> result = whApvService.queryByCondition(condition);
        
        // 验证结果
        assertNotNull("查询结果不应为空", result);
        assertEquals("应该返回2个SM类型的APV", 2, result.size());
        
        // 验证所有返回的APV都是SM类型
        for (WhApv apv : result) {
            assertEquals("APV类型应该是SM", "SM", apv.getApvType());
        }
    }

    /**
     * 测试用例3: 海外仓多品任务复用TRANSFER_MULTIPLEMULTIPLE包装
     * 
     * 预期结果：只查询MM类型的APV发货单
     */
    @Test
    public void testOverseasMultipleTaskWithTransferMultipleMultiplePacking() {
        // 准备测试数据
        WhApv mmApv1 = createMockApv("APV_MM_001", "MM");
        WhApv mmApv2 = createMockApv("APV_MM_002", "MM");
        List<WhApv> expectedMmApvs = Arrays.asList(mmApv1, mmApv2);
        
        when(whApvService.queryByCondition(any(WhApvQueryCondition.class)))
            .thenReturn(expectedMmApvs);
        
        // 执行测试
        WhApvQueryCondition condition = new WhApvQueryCondition();
        condition.setTaskType(PickingTaskType.ASN_FIRST_MULTIPLE.intCode());
        condition.setPickType("TRANSFER_MULTIPLEMULTIPLE");
        // 关键验证点：APV类型过滤应该只包含MM
        condition.setApvType("MM");
        
        List<WhApv> result = whApvService.queryByCondition(condition);
        
        // 验证结果
        assertNotNull("查询结果不应为空", result);
        assertEquals("应该返回2个MM类型的APV", 2, result.size());
        
        // 验证所有返回的APV都是MM类型
        for (WhApv apv : result) {
            assertEquals("APV类型应该是MM", "MM", apv.getApvType());
        }
    }

    /**
     * 测试用例4: 验证APV类型过滤的严格性
     * 
     * 测试海外仓单品任务使用TRANSFER_SINGLESINGLE时不会返回SM或MM类型的APV
     */
    @Test
    public void testApvTypeFilteringStrictness() {
        // 准备混合类型的APV数据
        WhApv ssApv = createMockApv("APV_SS_001", "SS");
        WhApv smApv = createMockApv("APV_SM_001", "SM");
        WhApv mmApv = createMockApv("APV_MM_001", "MM");
        
        // 模拟只返回SS类型的APV（正确的过滤结果）
        when(whApvService.queryByCondition(any(WhApvQueryCondition.class)))
            .thenReturn(Arrays.asList(ssApv));
        
        // 执行测试
        WhApvQueryCondition condition = new WhApvQueryCondition();
        condition.setTaskType(PickingTaskType.ASN_FIRST_SINGLE.intCode());
        condition.setPickType("TRANSFER_SINGLESINGLE");
        condition.setApvType("SS");
        
        List<WhApv> result = whApvService.queryByCondition(condition);
        
        // 验证结果的严格性
        assertEquals("应该只返回1个APV", 1, result.size());
        assertEquals("返回的APV应该是SS类型", "SS", result.get(0).getApvType());
        assertNotEquals("不应该返回SM类型", "SM", result.get(0).getApvType());
        assertNotEquals("不应该返回MM类型", "MM", result.get(0).getApvType());
    }

    /**
     * 测试用例5: 验证任务类型与APV类型的匹配关系
     */
    @Test
    public void testTaskTypeAndApvTypeMatching() {
        // 测试海外仓单品任务只能处理SS和SM类型
        assertTrue("海外仓单品任务应该能处理SS类型", 
            isApvTypeValidForTask("SS", PickingTaskType.ASN_FIRST_SINGLE.intCode()));
        assertTrue("海外仓单品任务应该能处理SM类型", 
            isApvTypeValidForTask("SM", PickingTaskType.ASN_FIRST_SINGLE.intCode()));
        assertFalse("海外仓单品任务不应该处理MM类型", 
            isApvTypeValidForTask("MM", PickingTaskType.ASN_FIRST_SINGLE.intCode()));
        
        // 测试海外仓多品任务只能处理MM类型
        assertFalse("海外仓多品任务不应该处理SS类型", 
            isApvTypeValidForTask("SS", PickingTaskType.ASN_FIRST_MULTIPLE.intCode()));
        assertFalse("海外仓多品任务不应该处理SM类型", 
            isApvTypeValidForTask("SM", PickingTaskType.ASN_FIRST_MULTIPLE.intCode()));
        assertTrue("海外仓多品任务应该能处理MM类型", 
            isApvTypeValidForTask("MM", PickingTaskType.ASN_FIRST_MULTIPLE.intCode()));
    }

    /**
     * 测试用例6: 边界条件测试
     */
    @Test
    public void testBoundaryConditions() {
        // 测试空查询条件
        WhApvQueryCondition emptyCondition = new WhApvQueryCondition();
        when(whApvService.queryByCondition(emptyCondition))
            .thenReturn(Arrays.asList());
        
        List<WhApv> emptyResult = whApvService.queryByCondition(emptyCondition);
        assertNotNull("空查询结果不应为null", emptyResult);
        assertEquals("空查询应返回空列表", 0, emptyResult.size());
        
        // 测试无效的APV类型
        WhApvQueryCondition invalidCondition = new WhApvQueryCondition();
        invalidCondition.setApvType("INVALID");
        when(whApvService.queryByCondition(invalidCondition))
            .thenReturn(Arrays.asList());
        
        List<WhApv> invalidResult = whApvService.queryByCondition(invalidCondition);
        assertEquals("无效APV类型应返回空结果", 0, invalidResult.size());
    }

    /**
     * 测试用例7: 性能测试 - 验证查询效率
     */
    @Test
    public void testQueryPerformance() {
        // 准备大量测试数据
        List<WhApv> largeMockData = createLargeMockApvList(1000);
        when(whApvService.queryByCondition(any(WhApvQueryCondition.class)))
            .thenReturn(largeMockData);
        
        long startTime = System.currentTimeMillis();
        
        WhApvQueryCondition condition = new WhApvQueryCondition();
        condition.setTaskType(PickingTaskType.ASN_FIRST_SINGLE.intCode());
        condition.setPickType("TRANSFER_SINGLESINGLE");
        condition.setApvType("SS");
        
        List<WhApv> result = whApvService.queryByCondition(condition);
        
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        
        // 验证性能（假设1000条记录查询应在100ms内完成）
        assertTrue("查询时间应该在合理范围内", executionTime < 100);
        assertNotNull("大数据量查询结果不应为空", result);
    }

    // 辅助方法
    private WhApv createMockApv(String apvNo, String apvType) {
        WhApv apv = new WhApv();
        apv.setApvNo(apvNo);
        apv.setApvType(apvType);
        apv.setPlatform("Amazon");
        apv.setCountry("US");
        apv.setApvStatus(1);
        return apv;
    }

    private boolean isApvTypeValidForTask(String apvType, Integer taskType) {
        // 模拟APV类型与任务类型的匹配逻辑
        if (PickingTaskType.ASN_FIRST_SINGLE.intCode().equals(taskType)) {
            return "SS".equals(apvType) || "SM".equals(apvType);
        } else if (PickingTaskType.ASN_FIRST_MULTIPLE.intCode().equals(taskType)) {
            return "MM".equals(apvType);
        }
        return false;
    }

    private List<WhApv> createLargeMockApvList(int size) {
        List<WhApv> apvList = new java.util.ArrayList<>();
        for (int i = 0; i < size; i++) {
            WhApv apv = createMockApv("APV_SS_" + String.format("%03d", i), "SS");
            apvList.add(apv);
        }
        return apvList;
    }
}