package com.estone.oversea;

import com.estone.apv.bean.WhApvGrid;
import com.estone.apv.bean.WhApvGridQueryCondition;
import com.estone.apv.domain.WhApvMoreProductsDo;
import com.estone.apv.service.WhApvGridService;
import com.estone.picking.bean.WhPickingTask;
import com.estone.picking.enums.PickingTaskType;
import com.estone.warehouse.bean.WhBox;
import com.estone.warehouse.service.WhBoxService;
import com.whq.tool.component.StatusCode;
import com.whq.tool.json.ResponseJson;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 海外仓头程单拆分播种逻辑测试
 * 
 * 测试目标：验证海外仓任务的播种APV类型验证逻辑
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class OverseasWarehouseSowingLogicTest {

    @Mock
    private WhBoxService whBoxService;
    
    @Mock
    private WhApvGridService whApvGridService;

    private WhPickingTask asnSingleTask;
    private WhPickingTask asnMultipleTask;
    private WhPickingTask transferSingleSingleTask;
    private WhPickingTask transferMultipleMultipleTask;

    private final String GRID_SCAN_VIEW = "transfer/transfer_grid_scan_view";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 创建海外仓单品任务
        asnSingleTask = new WhPickingTask();
        asnSingleTask.setTaskNo("TASK_ASN_SINGLE_001");
        asnSingleTask.setTaskType(PickingTaskType.ASN_FIRST_SINGLE.intCode());
        asnSingleTask.setWarehouseType(2); // 海外仓
        asnSingleTask.setIsAsn(PickingTaskType.ASN_FIRST_SINGLE.intCode());
        
        // 创建海外仓多品任务
        asnMultipleTask = new WhPickingTask();
        asnMultipleTask.setTaskNo("TASK_ASN_MULTI_001");
        asnMultipleTask.setTaskType(PickingTaskType.ASN_FIRST_MULTIPLE.intCode());
        asnMultipleTask.setWarehouseType(2); // 海外仓
        asnMultipleTask.setIsAsn(PickingTaskType.ASN_FIRST_MULTIPLE.intCode());
        
        // 创建中转仓对比任务
        transferSingleSingleTask = new WhPickingTask();
        transferSingleSingleTask.setTaskNo("TASK_TRANSFER_SS_001");
        transferSingleSingleTask.setTaskType(PickingTaskType.TRANSFER_SINGLESINGLE.intCode());
        transferSingleSingleTask.setWarehouseType(1); // 中转仓
        transferSingleSingleTask.setIsAsn(PickingTaskType.ASN_PREPARE.intCode());
        
        transferMultipleMultipleTask = new WhPickingTask();
        transferMultipleMultipleTask.setTaskNo("TASK_TRANSFER_MM_001");
        transferMultipleMultipleTask.setTaskType(PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode());
        transferMultipleMultipleTask.setWarehouseType(1); // 中转仓
        transferMultipleMultipleTask.setIsAsn(PickingTaskType.ASN_PREPARE.intCode());
    }

    /**
     * 测试用例1: 海外仓单品任务播种验证
     * 
     * 预期结果：ASN_FIRST_SINGLE任务应该在海外仓播种页面处理，不被误判为中转仓任务
     */
    @Test
    public void testOverseasSingleTaskSowingValidation() {
        // 准备测试数据 - 模拟扫描周转筐
        String boxNo = "BOX_ASN_SINGLE_001";
        WhApvMoreProductsDo domain = new WhApvMoreProductsDo();
        
        // 模拟周转筐扫描成功返回
        ResponseJson mockResponse = new ResponseJson();
        mockResponse.setStatus(StatusCode.SUCCESS);
        mockResponse.setMessage("TASK_ASN_SINGLE_001"); // 任务号
        mockResponse.setLocation("23"); // ASN_FIRST_SINGLE的代码
        
        Map<String, Object> body = new HashMap<>();
        body.put("whPickingTask", asnSingleTask);
        mockResponse.setBody(body);
        
        when(whBoxService.checkPackBoxNoScan(boxNo, null)).thenReturn(mockResponse);
        
        // 模拟播种逻辑处理
        String result = simulateGridScanBoxLogic(domain, 1, boxNo);
        
        // 验证结果
        // 海外仓单品任务应该正常进入播种流程，不应该被拒绝
        assertNotEquals("海外仓单品任务不应该被拒绝进入播种", GRID_SCAN_VIEW, result);
        
        // 验证domain对象设置正确
        assertEquals("任务类型应该正确设置", PickingTaskType.ASN_FIRST_SINGLE.intCode(), domain.getTaskType());
        assertEquals("仓库类型应该是海外仓", Integer.valueOf(2), domain.getWarehouseType());
        assertEquals("IsAsn应该正确设置", PickingTaskType.ASN_FIRST_SINGLE.intCode(), domain.getIsAsn());
    }

    /**
     * 测试用例2: 海外仓多品任务播种验证
     * 
     * 预期结果：ASN_FIRST_MULTIPLE任务应该正常进入播种流程
     */
    @Test
    public void testOverseasMultipleTaskSowingValidation() {
        String boxNo = "BOX_ASN_MULTI_001";
        WhApvMoreProductsDo domain = new WhApvMoreProductsDo();
        
        ResponseJson mockResponse = new ResponseJson();
        mockResponse.setStatus(StatusCode.SUCCESS);
        mockResponse.setMessage("TASK_ASN_MULTI_001");
        mockResponse.setLocation("24"); // ASN_FIRST_MULTIPLE的代码
        
        Map<String, Object> body = new HashMap<>();
        body.put("whPickingTask", asnMultipleTask);
        mockResponse.setBody(body);
        
        when(whBoxService.checkPackBoxNoScan(boxNo, null)).thenReturn(mockResponse);
        
        String result = simulateGridScanBoxLogic(domain, 1, boxNo);
        
        // 验证结果
        assertNotEquals("海外仓多品任务不应该被拒绝进入播种", GRID_SCAN_VIEW, result);
        assertEquals("任务类型应该正确设置", PickingTaskType.ASN_FIRST_MULTIPLE.intCode(), domain.getTaskType());
        assertEquals("仓库类型应该是海外仓", Integer.valueOf(2), domain.getWarehouseType());
    }

    /**
     * 测试用例3: 中转仓任务在海外仓播种页面的验证
     * 
     * 预期结果：中转仓任务应该被拒绝，提示使用中转仓播种
     */
    @Test
    public void testTransferTaskRejectionInOverseasSowing() {
        String boxNo = "BOX_TRANSFER_SS_001";
        WhApvMoreProductsDo domain = new WhApvMoreProductsDo();
        
        ResponseJson mockResponse = new ResponseJson();
        mockResponse.setStatus(StatusCode.SUCCESS);
        mockResponse.setMessage("TASK_TRANSFER_SS_001");
        mockResponse.setLocation("51"); // TRANSFER_SINGLESINGLE的代码
        
        Map<String, Object> body = new HashMap<>();
        body.put("whPickingTask", transferSingleSingleTask);
        mockResponse.setBody(body);
        
        when(whBoxService.checkPackBoxNoScan(boxNo, null)).thenReturn(mockResponse);
        
        String result = simulateGridScanBoxLogic(domain, 1, boxNo);
        
        // 验证结果 - 中转仓任务应该被拒绝
        assertEquals("中转仓任务应该被拒绝", GRID_SCAN_VIEW, result);
        assertTrue("应该设置错误信息提示使用中转仓播种", 
            domain.getErrorMsg() != null && domain.getErrorMsg().contains("中转仓拣货类型，请使用中转仓播种"));
    }

    /**
     * 测试用例4: 海外仓单品任务播种时APV类型验证
     * 
     * 验证只有SS和SM类型的APV能在单品任务中播种
     */
    @Test
    public void testOverseasSingleTaskApvTypeValidation() {
        // 测试SS类型APV在单品任务中的验证
        assertTrue("SS类型APV应该能在海外仓单品任务中播种", 
            validateApvTypeForSowing("SS", PickingTaskType.ASN_FIRST_SINGLE.intCode()));
        
        // 测试SM类型APV在单品任务中的验证
        assertTrue("SM类型APV应该能在海外仓单品任务中播种", 
            validateApvTypeForSowing("SM", PickingTaskType.ASN_FIRST_SINGLE.intCode()));
        
        // 测试MM类型APV在单品任务中的验证
        assertFalse("MM类型APV不应该能在海外仓单品任务中播种", 
            validateApvTypeForSowing("MM", PickingTaskType.ASN_FIRST_SINGLE.intCode()));
    }

    /**
     * 测试用例5: 海外仓多品任务播种时APV类型验证
     * 
     * 验证只有MM类型的APV能在多品任务中播种
     */
    @Test
    public void testOverseasMultipleTaskApvTypeValidation() {
        // 测试SS类型APV在多品任务中的验证
        assertFalse("SS类型APV不应该能在海外仓多品任务中播种", 
            validateApvTypeForSowing("SS", PickingTaskType.ASN_FIRST_MULTIPLE.intCode()));
        
        // 测试SM类型APV在多品任务中的验证
        assertFalse("SM类型APV不应该能在海外仓多品任务中播种", 
            validateApvTypeForSowing("SM", PickingTaskType.ASN_FIRST_MULTIPLE.intCode()));
        
        // 测试MM类型APV在多品任务中的验证
        assertTrue("MM类型APV应该能在海外仓多品任务中播种", 
            validateApvTypeForSowing("MM", PickingTaskType.ASN_FIRST_MULTIPLE.intCode()));
    }

    /**
     * 测试用例6: 播种格子分配逻辑验证
     */
    @Test
    public void testSowingGridAllocationLogic() {
        // 创建模拟的播种格子数据
        List<WhApvGrid> mockGrids = Arrays.asList(
            createMockGrid("GRID_001", "APV_SS_001", "SS"),
            createMockGrid("GRID_002", "APV_SM_001", "SM")
        );
        
        when(whApvGridService.queryByCondition(any(WhApvGridQueryCondition.class)))
            .thenReturn(mockGrids);
        
        // 验证格子分配
        WhApvGridQueryCondition condition = new WhApvGridQueryCondition();
        condition.setTaskNo("TASK_ASN_SINGLE_001");
        
        List<WhApvGrid> result = whApvGridService.queryByCondition(condition);
        
        assertNotNull("播种格子查询结果不应为空", result);
        assertEquals("应该返回2个播种格子", 2, result.size());
        
        // 验证格子中的APV类型都是单品类型
        for (WhApvGrid grid : result) {
            String apvNo = grid.getApvNo();
            assertTrue("播种格子中的APV应该是单品类型", 
                apvNo.contains("SS") || apvNo.contains("SM"));
        }
    }

    /**
     * 测试用例7: ASN_FIRST_SINGLE任务不需要播种的验证（中转仓逻辑）
     */
    @Test
    public void testAsnFirstSingleNoSowingRequired() {
        // 模拟中转仓播种控制器的逻辑
        WhApvMoreProductsDo domain = new WhApvMoreProductsDo();
        
        // 模拟ASN_FIRST_SINGLE任务在中转仓播种页面的处理
        String result = ********************************(domain, asnSingleTask);
        
        // 验证结果 - 应该被拒绝并提示不需要播种
        assertEquals("ASN_FIRST_SINGLE任务应该被拒绝进入中转仓播种", GRID_SCAN_VIEW, result);
        assertTrue("应该提示不需要播种", 
            domain.getErrorMsg() != null && domain.getErrorMsg().contains("不需要播种"));
    }

    /**
     * 测试用例8: 异常情况处理
     */
    @Test
    public void testExceptionHandling() {
        WhApvMoreProductsDo domain = new WhApvMoreProductsDo();
        
        // 测试扫描失败的情况
        ResponseJson failResponse = new ResponseJson();
        failResponse.setStatus(StatusCode.FAIL);
        failResponse.setMessage("周转筐不存在");
        
        when(whBoxService.checkPackBoxNoScan(anyString(), any())).thenReturn(failResponse);
        
        String result = simulateGridScanBoxLogic(domain, 1, "INVALID_BOX");
        
        assertEquals("扫描失败应该返回错误页面", GRID_SCAN_VIEW, result);
        assertEquals("应该设置正确的错误信息", "周转筐不存在", domain.getErrorMsg());
    }

    /**
     * 测试用例9: 边界条件测试
     */
    @Test
    public void testBoundaryConditions() {
        // 测试空任务号
        WhApvMoreProductsDo domain = new WhApvMoreProductsDo();
        ResponseJson emptyTaskResponse = new ResponseJson();
        emptyTaskResponse.setStatus(StatusCode.SUCCESS);
        emptyTaskResponse.setMessage(""); // 空任务号
        emptyTaskResponse.setLocation("23");
        
        when(whBoxService.checkPackBoxNoScan(anyString(), any())).thenReturn(emptyTaskResponse);
        
        String result = simulateGridScanBoxLogic(domain, 1, "BOX_001");
        
        assertEquals("空任务号应该返回错误页面", GRID_SCAN_VIEW, result);
        assertTrue("应该提示任务号为空", 
            domain.getErrorMsg() != null && domain.getErrorMsg().contains("拣货任务号"));
    }

    // 辅助方法

    /**
     * 模拟海外仓播种页面的扫描逻辑（基于WhApvGridScanController.scanBox）
     */
    private String simulateGridScanBoxLogic(WhApvMoreProductsDo domain, Integer gridLocation, String box) {
        ResponseJson rsp = whBoxService.checkPackBoxNoScan(box, null);
        
        if (StatusCode.FAIL.equals(rsp.getStatus())) {
            domain.setErrorMsg(rsp.getMessage());
            return GRID_SCAN_VIEW;
        }
        
        String taskNo = rsp.getMessage();
        if (taskNo == null || taskNo.isEmpty() || rsp.getLocation() == null || rsp.getLocation().isEmpty()) {
            domain.setErrorMsg("拣货任务号 或者拣货类型为空！");
            return GRID_SCAN_VIEW;
        }
        
        domain.setTaskType(Integer.valueOf(rsp.getLocation()));
        
        try {
            WhPickingTask whPickingTask = (WhPickingTask) rsp.getBody().get("whPickingTask");
            Integer warehouseType = whPickingTask.getWarehouseType();
            domain.setWarehouseType(warehouseType);
            domain.setIsAsn(whPickingTask.getIsAsn());
            domain.setWaybillType(whPickingTask.getWaybillType());

            // 模拟海外仓播种页面的验证逻辑
            boolean isAsn = whPickingTask.getIsAsn() != null
                    && PickingTaskType.getTransferIntCode().contains(whPickingTask.getIsAsn());

            if (isAsn || PickingTaskType.getJITIntCode().contains(whPickingTask.getTaskType())
                    || PickingTaskType.getJitAsnIntCode().contains(whPickingTask.getTaskType())) {
                domain.setErrorMsg("中转仓拣货类型，请使用中转仓播种！");
                return GRID_SCAN_VIEW;
            }
            
            // 正常情况下继续播种流程
            domain.setSerialNumber(taskNo);
            return "success"; // 模拟成功进入播种流程
            
        } catch (Exception e) {
            domain.setErrorMsg(e.getMessage());
            return GRID_SCAN_VIEW;
        }
    }

    /**
     * 模拟中转仓播种页面的扫描逻辑（基于TransferGridController.scanBox）
     */
    private String ********************************(WhApvMoreProductsDo domain, WhPickingTask whPickingTask) {
        try {
            // 模拟中转仓播种页面对ASN_FIRST_SINGLE的处理
            if (PickingTaskType.ASN_FIRST_SINGLE.intCode().equals(whPickingTask.getTaskType())) {
                domain.setErrorMsg(PickingTaskType.ASN_FIRST_SINGLE.getName() + "拣货类型，不需要播种！");
                return GRID_SCAN_VIEW;
            }

            boolean isAsn = whPickingTask.getIsAsn() != null
                    && PickingTaskType.getTransferIntCode().contains(whPickingTask.getIsAsn());
            
            if (whPickingTask.getTaskType() == null || !isAsn
                    && !PickingTaskType.getJITIntCode().contains(whPickingTask.getTaskType())
                            && !PickingTaskType.getJitAsnIntCode().contains(whPickingTask.getTaskType())) {
                domain.setErrorMsg("扫描的非中转仓或者仓发任务 ！");
                return GRID_SCAN_VIEW;
            }
            
            return "success";
        } catch (Exception e) {
            domain.setErrorMsg(e.getMessage());
            return GRID_SCAN_VIEW;
        }
    }

    /**
     * 验证APV类型是否适合在指定任务中播种
     */
    private boolean validateApvTypeForSowing(String apvType, Integer taskType) {
        if (PickingTaskType.ASN_FIRST_SINGLE.intCode().equals(taskType)) {
            // 海外仓单品任务只允许SS和SM类型
            return "SS".equals(apvType) || "SM".equals(apvType);
        } else if (PickingTaskType.ASN_FIRST_MULTIPLE.intCode().equals(taskType)) {
            // 海外仓多品任务只允许MM类型
            return "MM".equals(apvType);
        }
        return false;
    }

    private WhApvGrid createMockGrid(String gridNo, String apvNo, String apvType) {
        WhApvGrid grid = new WhApvGrid();
        grid.setGridNo(gridNo);
        grid.setApvNo(apvNo);
        grid.setGridStatus(1);
        return grid;
    }
}