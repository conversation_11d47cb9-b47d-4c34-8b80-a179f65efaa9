# 海外仓头程单APV类型处理修正总结

## 修正概述

本次修正主要解决了海外仓头程单包装功能中由于页面复用导致的APV类型处理错误问题。

## 问题分析

### 原始问题
在海外仓头程单包装功能的`ssBoxScan`方法中，由于复用了中转仓的包装页面，`pickType`参数传递的实际上是中转仓的任务类型，而不是海外仓的任务类型。

### 错误的实现
```java
// 错误：假设pickType是海外仓任务类型
if (PickingTaskType.ASN_FIRST_SINGLE.intCode().equals(pickType)){
    // 海外仓单品：只包含SS和SM类型
    queryCondition.setApvTypeList(Arrays.asList("SS", "SM"));
}
if (PickingTaskType.ASN_FIRST_MULTIPLE.intCode().equals(pickType)){
    // 海外仓多品：包含MM类型
    queryCondition.setApvType("MM");
}
```

### 问题根因
- **页面复用**: 海外仓头程单复用中转仓包装页面
- **参数传递**: `pickType`实际传递的是中转仓任务类型（`TRANSFER_SINGLESINGLE`、`TRANSFER_SINGLEMULTIPLE`、`TRANSFER_MULTIPLEMULTIPLE`）
- **逻辑错误**: 判断海外仓任务类型的条件永远不会被触发

## 修正方案

### 1. 移除错误的海外仓APV类型设置逻辑

**修正前**:
```java
// 海外仓头程单根据APV类型区分包装逻辑
if (PickingTaskType.ASN_FIRST_SINGLE.intCode().equals(pickType)){
    queryCondition.setApvTypeList(Arrays.asList("SS", "SM"));
}
if (PickingTaskType.ASN_FIRST_MULTIPLE.intCode().equals(pickType)){
    queryCondition.setApvType("MM");
}
```

**修正后**:
```java
// 移除了错误的海外仓APV类型设置逻辑
// 保持原有的中转仓逻辑
if (PickingTaskType.TRANSFER_SINGLESINGLE.intCode().equals(pickType)){
    queryCondition.setApvType("SS");
}
if (PickingTaskType.TRANSFER_SINGLEMULTIPLE.intCode().equals(pickType)){
    queryCondition.setApvType("SM");
}
if (PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode().equals(pickType)){
    queryCondition.setApvType("MM");
}
```

### 2. 修正多件包装逻辑

**修正前**:
```java
// 根据任务类型设置APV类型过滤条件
WhPickingTask task = whPickingTaskService.getWhPickingTaskByTaskNo(taskNo);
if (task != null) {
    if (PickingTaskType.TRANSFER_SINGLEMULTIPLE.intCode().equals(task.getTaskType())) {
        queryCondition.setApvType("SM");
    } else if (PickingTaskType.ASN_FIRST_SINGLE.intCode().equals(task.getTaskType())) {
        queryCondition.setApvType("SM");
    } else if (PickingTaskType.ASN_FIRST_MULTIPLE.intCode().equals(task.getTaskType())) {
        queryCondition.setApvType("SM");
    } else {
        queryCondition.setApvType("SM");
    }
}
```

**修正后**:
```java
// 简化为直接设置SM类型
queryCondition.setApvType("SM");
```

### 3. 修正多品包装逻辑

**修正前**:
```java
// 根据任务类型设置APV类型过滤条件
if (whPickingTask != null) {
    if (PickingTaskType.TRANSFER_MULTIPLEMULTIPLE.intCode().equals(whPickingTask.getTaskType())) {
        fbaAllocationQueryCondition.setApvType("MM");
    } else if (PickingTaskType.ASN_FIRST_MULTIPLE.intCode().equals(whPickingTask.getTaskType())) {
        fbaAllocationQueryCondition.setApvType("MM");
    } else {
        fbaAllocationQueryCondition.setApvType("MM");
    }
} else {
    fbaAllocationQueryCondition.setApvType("MM");
}
```

**修正后**:
```java
// 简化为直接设置MM类型
fbaAllocationQueryCondition.setApvType("MM");
```

## 保留的功能

### 1. APV类型列表支持基础设施
- `WhFbaAllocationQueryCondition.apvTypeList` 属性
- DAO层对apvTypeList的支持
- SQL映射文件中的apvTypeList查询条件

这些基础设施被保留是因为在其他地方有正确的使用，例如：

```java
// WhFbaAllocationController中的正确使用
case "ALL_SINGLE":
    // 查询所有单品类型（SS/SM）
    query.setApvType(ApvTypeEnum.SS.getCode()+","+ApvTypeEnum.SM.getCode());
    break;
```

### 2. 播种逻辑中的APV类型验证
播种逻辑中添加的APV类型验证功能被保留，因为它确实能够提供额外的业务逻辑验证。

## 页面复用的正确映射关系

| 海外仓实际任务类型 | 发货单APV类型 | 复用的中转仓页面类型 | 传递的pickType |
|------------------|--------------|-------------------|---------------|
| ASN_FIRST_SINGLE | SS | 中转仓单品单件页面 | TRANSFER_SINGLESINGLE |
| ASN_FIRST_SINGLE | SM | 中转仓单品多件页面 | TRANSFER_SINGLEMULTIPLE |
| ASN_FIRST_MULTIPLE | MM | 中转仓多品多件页面 | TRANSFER_MULTIPLEMULTIPLE |

## 修正效果

1. **解决了页面复用导致的参数传递问题**
2. **恢复了正确的中转仓包装逻辑**
3. **保持了系统的兼容性和稳定性**
4. **确保了拼多多包装功能的正常运行**

## 测试验证

更新了测试用例，移除了不再适用的测试逻辑，添加了页面复用映射关系的验证测试。

## 总结

通过这次修正，我们：
- 正确理解了页面复用的业务场景
- 移除了错误的APV类型设置逻辑
- 保持了系统的向后兼容性
- 确保了海外仓和中转仓包装功能的正常运行
