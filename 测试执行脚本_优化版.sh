#!/bin/bash

# 海外仓头程单拆分测试执行脚本 - 优化版
# 专门处理Maven依赖问题，提供多种执行策略

echo "=========================================="
echo "海外仓头程单拆分包装和播种逻辑测试套件 (优化版)"
echo "=========================================="

# 设置测试环境变量
export MAVEN_OPTS="-Xmx2g -XX:MaxPermSize=512m"

# 设置JAVA_HOME，如果预设路径不存在则自动检测
PRESET_JAVA_HOME="/usr/lib/jvm/java-11-openjdk-amd64"
if [ -d "$PRESET_JAVA_HOME" ]; then
    export JAVA_HOME="$PRESET_JAVA_HOME"
else
    # 自动检测JAVA_HOME
    if command -v java > /dev/null 2>&1; then
        JAVA_PATH=$(readlink -f $(which java))
        export JAVA_HOME="${JAVA_PATH%/bin/java}"
        echo "自动检测到JAVA_HOME: $JAVA_HOME"
    fi
fi

# 检查Maven是否可用
if ! command -v mvn &> /dev/null; then
    echo "错误: Maven 未安装或不在PATH中"
    exit 1
fi

# 检查并显示Java环境状态
if [ -n "$JAVA_HOME" ] && [ -d "$JAVA_HOME" ]; then
    echo "✓ JAVA_HOME 正确设置: $JAVA_HOME"
else
    echo "⚠ JAVA_HOME 未设置或路径不存在，使用系统默认Java"
fi

echo "当前工作目录: $(pwd)"
echo "Java版本:"
java -version
echo ""

# 检查Maven配置
echo "Maven配置信息:"
echo "- Maven版本: $(mvn -v | head -1)"
echo "- 本地仓库: $(mvn help:evaluate -Dexpression=settings.localRepository -q -DforceStdout 2>/dev/null || echo '默认路径')"
echo ""

# 策略1: 尝试正常编译
echo "步骤1: 尝试编译项目..."
mvn clean compile -DskipTests -q
COMPILE_RESULT=$?

if [ $COMPILE_RESULT -eq 0 ]; then
    echo "✓ 项目编译成功"
    COMPILATION_SUCCESS=1
else
    echo "❌ 项目编译失败，分析依赖问题..."
    
    # 分析缺失的依赖
    echo ""
    echo "分析缺失的依赖包:"
    mvn dependency:resolve 2>&1 | grep "Could not resolve dependencies" -A 10 | head -15
    
    echo ""
    echo "⚠ 检测到私有依赖包缺失，尝试其他策略..."
    COMPILATION_SUCCESS=0
fi

echo ""

# 策略2: 编译测试代码（不依赖主代码）
echo "步骤2: 编译测试代码..."
mvn test-compile -DskipTests
TEST_COMPILE_RESULT=$?

if [ $TEST_COMPILE_RESULT -eq 0 ]; then
    echo "✓ 测试代码编译成功"
    TEST_COMPILATION_SUCCESS=1
else
    echo "❌ 测试代码编译失败"
    TEST_COMPILATION_SUCCESS=0
fi

echo ""

# 策略3: 检查已编译的测试类
echo "步骤3: 检查已编译的测试类..."
if [ -d "target/test-classes" ]; then
    TEST_CLASSES_COUNT=$(find target/test-classes -name "*.class" | wc -l)
    echo "✓ 发现 $TEST_CLASSES_COUNT 个已编译的测试类"
    
    # 查找海外仓测试类
    OVERSEAS_TEST_CLASSES=$(find target/test-classes -name "*OverseasWarehouse*Test.class" | wc -l)
    if [ $OVERSEAS_TEST_CLASSES -gt 0 ]; then
        echo "✓ 发现 $OVERSEAS_TEST_CLASSES 个海外仓测试类"
        HAS_OVERSEAS_TESTS=1
    else
        echo "⚠ 未发现已编译的海外仓测试类"
        HAS_OVERSEAS_TESTS=0
    fi
else
    echo "❌ 未发现测试编译目录"
    HAS_OVERSEAS_TESTS=0
fi

echo ""

# 决定执行策略
if [ $TEST_COMPILATION_SUCCESS -eq 1 ] || [ $HAS_OVERSEAS_TESTS -eq 1 ]; then
    echo "=========================================="
    echo "执行测试 - 使用可用的测试类"
    echo "=========================================="
    
    # 运行包装逻辑测试
    echo "步骤4: 运行包装逻辑测试..."
    mvn test -Dtest=OverseasWarehousePackingLogicTest -DfailIfNoTests=false
    if [ $? -eq 0 ]; then
        echo "✓ 包装逻辑测试通过"
        PACKING_TEST_FAILED=0
    else
        echo "❌ 包装逻辑测试失败"
        PACKING_TEST_FAILED=1
    fi
    echo ""

    # 运行播种逻辑测试
    echo "步骤5: 运行播种逻辑测试..."
    mvn test -Dtest=OverseasWarehouseSowingLogicTest -DfailIfNoTests=false
    if [ $? -eq 0 ]; then
        echo "✓ 播种逻辑测试通过"
        SOWING_TEST_FAILED=0
    else
        echo "❌ 播种逻辑测试失败"
        SOWING_TEST_FAILED=1
    fi
    echo ""

    # 运行对比验证测试
    echo "步骤6: 运行对比验证测试..."
    mvn test -Dtest=OverseasTransferComparisonTest -DfailIfNoTests=false
    if [ $? -eq 0 ]; then
        echo "✓ 对比验证测试通过"
        COMPARISON_TEST_FAILED=0
    else
        echo "❌ 对比验证测试失败"
        COMPARISON_TEST_FAILED=1
    fi
    echo ""

    # 运行完整测试套件
    echo "步骤7: 运行完整测试套件..."
    mvn test -Dtest=OverseasWarehouseTestSuite -DfailIfNoTests=false
    if [ $? -eq 0 ]; then
        echo "✓ 完整测试套件执行成功"
        SUITE_TEST_FAILED=0
    else
        echo "❌ 完整测试套件执行失败"
        SUITE_TEST_FAILED=1
    fi
    echo ""

else
    echo "=========================================="
    echo "无法执行测试 - 依赖问题阻止测试运行"
    echo "=========================================="
    
    echo "问题诊断:"
    echo "1. 项目编译: $([ $COMPILATION_SUCCESS -eq 1 ] && echo '✓ 成功' || echo '❌ 失败')"
    echo "2. 测试编译: $([ $TEST_COMPILATION_SUCCESS -eq 1 ] && echo '✓ 成功' || echo '❌ 失败')"
    echo "3. 已编译测试: $([ $HAS_OVERSEAS_TESTS -eq 1 ] && echo '✓ 存在' || echo '❌ 不存在')"
    echo ""
    
    echo "建议解决方案:"
    echo "1. 检查私有Maven仓库连接: http://192.168.3.191:8082/repository/public/"
    echo "2. 联系系统管理员恢复私有仓库服务"
    echo "3. 或者手动安装缺失的依赖包到本地仓库"
    echo "4. 考虑使用Docker环境运行测试"
    echo ""
    
    # 设置所有测试为失败状态
    PACKING_TEST_FAILED=1
    SOWING_TEST_FAILED=1
    COMPARISON_TEST_FAILED=1
    SUITE_TEST_FAILED=1
fi

# 生成测试报告
echo "步骤8: 生成测试报告..."
mvn surefire-report:report -DfailIfNoTests=false
if [ $? -eq 0 ]; then
    echo "✓ 测试报告生成成功，位置: target/site/surefire-report.html"
else
    echo "⚠ 测试报告生成失败，但不影响测试结果"
fi
echo ""

# 测试结果汇总
echo "=========================================="
echo "测试结果汇总"
echo "=========================================="

TOTAL_FAILED=0

if [ $PACKING_TEST_FAILED -eq 0 ]; then
    echo "✓ 包装逻辑测试: 通过"
else
    echo "❌ 包装逻辑测试: 失败"
    TOTAL_FAILED=$((TOTAL_FAILED + 1))
fi

if [ $SOWING_TEST_FAILED -eq 0 ]; then
    echo "✓ 播种逻辑测试: 通过"
else
    echo "❌ 播种逻辑测试: 失败"
    TOTAL_FAILED=$((TOTAL_FAILED + 1))
fi

if [ $COMPARISON_TEST_FAILED -eq 0 ]; then
    echo "✓ 对比验证测试: 通过"
else
    echo "❌ 对比验证测试: 失败"
    TOTAL_FAILED=$((TOTAL_FAILED + 1))
fi

if [ $SUITE_TEST_FAILED -eq 0 ]; then
    echo "✓ 完整测试套件: 通过"
else
    echo "❌ 完整测试套件: 失败"
    TOTAL_FAILED=$((TOTAL_FAILED + 1))
fi

echo ""
echo "环境信息:"
echo "- 编译状态: $([ $COMPILATION_SUCCESS -eq 1 ] && echo '✓ 成功' || echo '❌ 失败 (依赖问题)')"
echo "- 测试编译: $([ $TEST_COMPILATION_SUCCESS -eq 1 ] && echo '✓ 成功' || echo '❌ 失败')"
echo "- Java版本: $(java -version 2>&1 | head -1)"
echo "- Maven版本: $(mvn -v | head -1)"

echo ""
echo "=========================================="

if [ $TOTAL_FAILED -eq 0 ]; then
    echo "🎉 所有测试通过！海外仓头程单拆分逻辑验证成功"
    echo ""
    echo "验证结果："
    echo "- 海外仓单品任务包装逻辑与中转仓逻辑一致 ✓"
    echo "- 海外仓多品任务包装逻辑与中转仓逻辑一致 ✓"
    echo "- 海外仓播种APV类型验证正确 ✓"
    echo "- 页面复用不会产生业务逻辑冲突 ✓"
    exit 0
else
    echo "❌ 有 $TOTAL_FAILED 个测试模块失败"
    echo ""
    if [ $COMPILATION_SUCCESS -eq 0 ]; then
        echo "主要问题：Maven依赖解析失败"
        echo "建议优先解决依赖问题："
        echo "1. 检查私有Maven仓库服务状态"
        echo "2. 验证网络连接到 192.168.3.191:8082"
        echo "3. 检查Maven settings.xml配置"
        echo "4. 考虑离线安装缺失的私有依赖包"
    else
        echo "请检查以下问题："
        echo "1. APV类型过滤逻辑是否正确实现"
        echo "2. 任务类型验证是否按照设计执行"
        echo "3. 页面复用参数传递是否正确"
        echo "4. 播种逻辑是否与中转仓保持一致"
    fi
    echo ""
    echo "查看详细错误信息："
    echo "- 查看控制台输出"
    echo "- 检查 target/surefire-reports/ 目录下的测试报告"
    echo "- 查看 target/site/surefire-report.html（如果生成成功）"
    exit 1
fi 