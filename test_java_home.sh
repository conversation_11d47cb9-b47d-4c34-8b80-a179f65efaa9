#!/bin/bash

echo "=========================================="
echo "JAVA_HOME 测试脚本"
echo "=========================================="

# 模拟测试脚本中的JAVA_HOME设置
export JAVA_HOME="/usr/lib/jvm/java-11-openjdk-amd64"

echo "设置的JAVA_HOME: $JAVA_HOME"

# 检查Java环境（与原脚本相同的逻辑）
if [ ! -d "$JAVA_HOME" ]; then
    echo "警告: JAVA_HOME 未正确设置，使用系统默认Java"
else
    echo "✓ JAVA_HOME 设置正确"
fi

echo ""
echo "Java版本:"
java -version

echo ""
echo "JAVA_HOME/bin/java:"
if [ -f "$JAVA_HOME/bin/java" ]; then
    echo "✓ $JAVA_HOME/bin/java 存在"
    $JAVA_HOME/bin/java -version
else
    echo "❌ $JAVA_HOME/bin/java 不存在"
fi 