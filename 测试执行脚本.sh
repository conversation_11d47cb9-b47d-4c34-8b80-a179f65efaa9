#!/bin/bash

# 海外仓头程单拆分测试执行脚本
# 用于运行完整的测试套件

echo "=========================================="
echo "海外仓头程单拆分包装和播种逻辑测试套件"
echo "=========================================="

# 设置测试环境变量
export MAVEN_OPTS="-Xmx2g -XX:MaxPermSize=512m"

# 设置JAVA_HOME，如果预设路径不存在则自动检测
PRESET_JAVA_HOME="/usr/lib/jvm/java-11-openjdk-amd64"
if [ -d "$PRESET_JAVA_HOME" ]; then
    export JAVA_HOME="$PRESET_JAVA_HOME"
else
    # 自动检测JAVA_HOME
    if command -v java > /dev/null 2>&1; then
        JAVA_PATH=$(readlink -f $(which java))
        export JAVA_HOME="${JAVA_PATH%/bin/java}"
        echo "自动检测到JAVA_HOME: $JAVA_HOME"
    fi
fi

# 检查Maven是否可用
if ! command -v mvn &> /dev/null; then
    echo "错误: Maven 未安装或不在PATH中"
    exit 1
fi

# 检查并显示Java环境状态
if [ -n "$JAVA_HOME" ] && [ -d "$JAVA_HOME" ]; then
    echo "✓ JAVA_HOME 正确设置: $JAVA_HOME"
else
    echo "⚠ JAVA_HOME 未设置或路径不存在，使用系统默认Java"
fi

echo "当前工作目录: $(pwd)"
echo "Java版本:"
java -version
echo ""

# 编译项目
echo "步骤1: 编译项目..."
mvn clean compile -DskipTests
if [ $? -ne 0 ]; then
    echo "错误: 项目编译失败"
    exit 1
fi
echo "✓ 编译成功"
echo ""

# 编译测试代码
echo "步骤2: 编译测试代码..."
mvn test-compile
if [ $? -ne 0 ]; then
    echo "错误: 测试代码编译失败"
    exit 1
fi
echo "✓ 测试代码编译成功"
echo ""

# 运行包装逻辑测试
echo "步骤3: 运行包装逻辑测试..."
mvn test -Dtest=OverseasWarehousePackingLogicTest
if [ $? -ne 0 ]; then
    echo "❌ 包装逻辑测试失败"
    PACKING_TEST_FAILED=1
else
    echo "✓ 包装逻辑测试通过"
    PACKING_TEST_FAILED=0
fi
echo ""

# 运行播种逻辑测试
echo "步骤4: 运行播种逻辑测试..."
mvn test -Dtest=OverseasWarehouseSowingLogicTest
if [ $? -ne 0 ]; then
    echo "❌ 播种逻辑测试失败"
    SOWING_TEST_FAILED=1
else
    echo "✓ 播种逻辑测试通过"
    SOWING_TEST_FAILED=0
fi
echo ""

# 运行对比验证测试
echo "步骤5: 运行对比验证测试..."
mvn test -Dtest=OverseasTransferComparisonTest
if [ $? -ne 0 ]; then
    echo "❌ 对比验证测试失败"
    COMPARISON_TEST_FAILED=1
else
    echo "✓ 对比验证测试通过"
    COMPARISON_TEST_FAILED=0
fi
echo ""

# 运行完整测试套件
echo "步骤6: 运行完整测试套件..."
mvn test -Dtest=OverseasWarehouseTestSuite
if [ $? -ne 0 ]; then
    echo "❌ 完整测试套件执行失败"
    SUITE_TEST_FAILED=1
else
    echo "✓ 完整测试套件执行成功"
    SUITE_TEST_FAILED=0
fi
echo ""

# 生成测试报告
echo "步骤7: 生成测试报告..."
mvn surefire-report:report
if [ $? -eq 0 ]; then
    echo "✓ 测试报告生成成功，位置: target/site/surefire-report.html"
else
    echo "⚠ 测试报告生成失败，但不影响测试结果"
fi
echo ""

# 测试结果汇总
echo "=========================================="
echo "测试结果汇总"
echo "=========================================="

TOTAL_FAILED=0

if [ $PACKING_TEST_FAILED -eq 0 ]; then
    echo "✓ 包装逻辑测试: 通过"
else
    echo "❌ 包装逻辑测试: 失败"
    TOTAL_FAILED=$((TOTAL_FAILED + 1))
fi

if [ $SOWING_TEST_FAILED -eq 0 ]; then
    echo "✓ 播种逻辑测试: 通过"
else
    echo "❌ 播种逻辑测试: 失败"
    TOTAL_FAILED=$((TOTAL_FAILED + 1))
fi

if [ $COMPARISON_TEST_FAILED -eq 0 ]; then
    echo "✓ 对比验证测试: 通过"
else
    echo "❌ 对比验证测试: 失败"
    TOTAL_FAILED=$((TOTAL_FAILED + 1))
fi

if [ $SUITE_TEST_FAILED -eq 0 ]; then
    echo "✓ 完整测试套件: 通过"
else
    echo "❌ 完整测试套件: 失败"
    TOTAL_FAILED=$((TOTAL_FAILED + 1))
fi

echo ""
echo "=========================================="

if [ $TOTAL_FAILED -eq 0 ]; then
    echo "🎉 所有测试通过！海外仓头程单拆分逻辑验证成功"
    echo ""
    echo "验证结果："
    echo "- 海外仓单品任务包装逻辑与中转仓逻辑一致 ✓"
    echo "- 海外仓多品任务包装逻辑与中转仓逻辑一致 ✓"
    echo "- 海外仓播种APV类型验证正确 ✓"
    echo "- 页面复用不会产生业务逻辑冲突 ✓"
    exit 0
else
    echo "❌ 有 $TOTAL_FAILED 个测试模块失败"
    echo ""
    echo "请检查以下问题："
    echo "1. APV类型过滤逻辑是否正确实现"
    echo "2. 任务类型验证是否按照设计执行"
    echo "3. 页面复用参数传递是否正确"
    echo "4. 播种逻辑是否与中转仓保持一致"
    echo ""
    echo "查看详细错误信息："
    echo "- 查看控制台输出"
    echo "- 检查 target/surefire-reports/ 目录下的测试报告"
    echo "- 查看 target/site/surefire-report.html（如果生成成功）"
    exit 1
fi