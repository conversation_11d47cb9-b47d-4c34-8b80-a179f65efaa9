#!/bin/bash

# 海外仓头程单拆分测试执行脚本 - WSL优化版
# 使用Windows Maven仓库和配置

echo "=========================================="
echo "海外仓头程单拆分包装和播种逻辑测试套件 (WSL优化版)"
echo "=========================================="

# 设置测试环境变量
export MAVEN_OPTS="-Xmx2g -XX:MaxPermSize=512m"

# 设置JAVA_HOME（WSL环境）
PRESET_JAVA_HOME="/usr/lib/jvm/java-11-openjdk-amd64"
if [ -d "$PRESET_JAVA_HOME" ]; then
    export JAVA_HOME="$PRESET_JAVA_HOME"
    echo "✓ JAVA_HOME: $JAVA_HOME"
else
    echo "❌ JAVA_HOME设置失败"
    exit 1
fi

echo "当前工作目录: $(pwd)"
echo "Java版本:"
java -version
echo ""

echo "Maven配置信息:"
echo "- Maven版本: $(mvn -v | head -1)"
echo "- 本地仓库: /mnt/d/Maven/maven-repository (Windows仓库)"
echo "- 私有仓库: http://192.168.3.191:8082/repository/public/"
echo ""

# 验证关键依赖包存在
echo "验证私有依赖包:"
if [ -f "/mnt/d/Maven/maven-repository/com/global/iop/iop-api-sdk/1.3.5-ae/iop-api-sdk-1.3.5-ae.jar" ]; then
    echo "✓ com.global.iop:iop-api-sdk:1.3.5-ae"
else
    echo "❌ com.global.iop:iop-api-sdk:1.3.5-ae"
fi

if [ -f "/mnt/d/Maven/maven-repository/com/estone/tools/0.1.8/tools-0.1.8.jar" ]; then
    echo "✓ com.estone:tools:0.1.8"
else
    echo "❌ com.estone:tools:0.1.8"
fi

if [ -f "/mnt/d/Maven/maven-repository/com/estone/mybatis/plugin/pagination/1.2.0/pagination-1.2.0.jar" ]; then
    echo "✓ com.estone.mybatis.plugin:pagination:1.2.0"
else
    echo "❌ com.estone.mybatis.plugin:pagination:1.2.0"
fi
echo ""

# 步骤1: 编译项目
echo "步骤1: 编译项目..."
mvn clean compile -DskipTests -q
COMPILE_RESULT=$?

if [ $COMPILE_RESULT -eq 0 ]; then
    echo "✓ 项目编译成功"
    COMPILATION_SUCCESS=1
else
    echo "❌ 项目编译失败，错误代码: $COMPILE_RESULT"
    echo "尝试显示详细错误信息:"
    mvn clean compile -DskipTests | tail -20
    COMPILATION_SUCCESS=0
fi
echo ""

# 步骤2: 编译测试代码
echo "步骤2: 编译测试代码..."
mvn test-compile -q
TEST_COMPILE_RESULT=$?

if [ $TEST_COMPILE_RESULT -eq 0 ]; then
    echo "✓ 测试代码编译成功"
    TEST_COMPILATION_SUCCESS=1
else
    echo "❌ 测试代码编译失败，错误代码: $TEST_COMPILE_RESULT"
    TEST_COMPILATION_SUCCESS=0
fi
echo ""

# 检查测试类是否存在
echo "步骤3: 检查测试类..."
if [ -f "test/java/com/estone/oversea/OverseasWarehousePackingLogicTest.java" ]; then
    echo "✓ 找到包装逻辑测试源文件"
    HAS_TEST_SOURCE=1
else
    echo "❌ 未找到包装逻辑测试源文件"
    HAS_TEST_SOURCE=0
fi

if [ -d "target/test-classes" ]; then
    TEST_CLASSES_COUNT=$(find target/test-classes -name "*OverseasWarehouse*Test.class" | wc -l)
    if [ $TEST_CLASSES_COUNT -gt 0 ]; then
        echo "✓ 找到 $TEST_CLASSES_COUNT 个已编译的海外仓测试类"
        HAS_COMPILED_TESTS=1
    else
        echo "⚠ 未找到已编译的海外仓测试类"
        HAS_COMPILED_TESTS=0
    fi
else
    echo "❌ 未找到测试编译目录"
    HAS_COMPILED_TESTS=0
fi
echo ""

# 执行测试
if [ $TEST_COMPILATION_SUCCESS -eq 1 ] && [ $HAS_COMPILED_TESTS -eq 1 ]; then
    echo "=========================================="
    echo "执行测试套件"
    echo "=========================================="
    
    # 运行包装逻辑测试
    echo "步骤4: 运行包装逻辑测试..."
    mvn test -Dtest=OverseasWarehousePackingLogicTest -DfailIfNoTests=false
    if [ $? -eq 0 ]; then
        echo "✓ 包装逻辑测试通过"
        PACKING_TEST_FAILED=0
    else
        echo "❌ 包装逻辑测试失败"
        PACKING_TEST_FAILED=1
    fi
    echo ""

    # 运行播种逻辑测试
    echo "步骤5: 运行播种逻辑测试..."
    mvn test -Dtest=OverseasWarehouseSowingLogicTest -DfailIfNoTests=false
    if [ $? -eq 0 ]; then
        echo "✓ 播种逻辑测试通过"
        SOWING_TEST_FAILED=0
    else
        echo "❌ 播种逻辑测试失败"
        SOWING_TEST_FAILED=1
    fi
    echo ""

    # 运行对比验证测试
    echo "步骤6: 运行对比验证测试..."
    mvn test -Dtest=OverseasTransferComparisonTest -DfailIfNoTests=false
    if [ $? -eq 0 ]; then
        echo "✓ 对比验证测试通过"
        COMPARISON_TEST_FAILED=0
    else
        echo "❌ 对比验证测试失败"
        COMPARISON_TEST_FAILED=1
    fi
    echo ""

    # 运行完整测试套件
    echo "步骤7: 运行完整测试套件..."
    mvn test -Dtest=OverseasWarehouseTestSuite -DfailIfNoTests=false
    if [ $? -eq 0 ]; then
        echo "✓ 完整测试套件执行成功"
        SUITE_TEST_FAILED=0
    else
        echo "❌ 完整测试套件执行失败"
        SUITE_TEST_FAILED=1
    fi
    echo ""

else
    echo "=========================================="
    echo "跳过测试执行"
    echo "=========================================="
    echo "原因分析:"
    echo "- 测试编译状态: $([ $TEST_COMPILATION_SUCCESS -eq 1 ] && echo '✓ 成功' || echo '❌ 失败')"
    echo "- 已编译测试类: $([ $HAS_COMPILED_TESTS -eq 1 ] && echo '✓ 存在' || echo '❌ 不存在')"
    echo "- 项目编译状态: $([ $COMPILATION_SUCCESS -eq 1 ] && echo '✓ 成功' || echo '❌ 失败')"
    
    # 设置所有测试为失败状态
    PACKING_TEST_FAILED=1
    SOWING_TEST_FAILED=1
    COMPARISON_TEST_FAILED=1
    SUITE_TEST_FAILED=1
fi

# 生成测试报告
echo "步骤8: 生成测试报告..."
mvn surefire-report:report -DfailIfNoTests=false
if [ $? -eq 0 ]; then
    echo "✓ 测试报告生成成功，位置: target/site/surefire-report.html"
else
    echo "⚠ 测试报告生成失败，但不影响测试结果"
fi
echo ""

# 计算失败的测试数量
TOTAL_FAILED=0
if [ $PACKING_TEST_FAILED -eq 1 ]; then TOTAL_FAILED=$((TOTAL_FAILED + 1)); fi
if [ $SOWING_TEST_FAILED -eq 1 ]; then TOTAL_FAILED=$((TOTAL_FAILED + 1)); fi
if [ $COMPARISON_TEST_FAILED -eq 1 ]; then TOTAL_FAILED=$((TOTAL_FAILED + 1)); fi
if [ $SUITE_TEST_FAILED -eq 1 ]; then TOTAL_FAILED=$((TOTAL_FAILED + 1)); fi

# 测试结果汇总
echo "=========================================="
echo "测试结果汇总"
echo "=========================================="

if [ $PACKING_TEST_FAILED -eq 0 ]; then
    echo "✓ 包装逻辑测试: 通过"
else
    echo "❌ 包装逻辑测试: 失败"
fi

if [ $SOWING_TEST_FAILED -eq 0 ]; then
    echo "✓ 播种逻辑测试: 通过"
else
    echo "❌ 播种逻辑测试: 失败"
fi

if [ $COMPARISON_TEST_FAILED -eq 0 ]; then
    echo "✓ 对比验证测试: 通过"
else
    echo "❌ 对比验证测试: 失败"
fi

if [ $SUITE_TEST_FAILED -eq 0 ]; then
    echo "✓ 完整测试套件: 通过"
else
    echo "❌ 完整测试套件: 失败"
fi

echo ""
echo "环境信息:"
echo "- 编译状态: $([ $COMPILATION_SUCCESS -eq 1 ] && echo '✓ 成功' || echo '❌ 失败')"
echo "- 测试编译: $([ $TEST_COMPILATION_SUCCESS -eq 1 ] && echo '✓ 成功' || echo '❌ 失败')"
echo "- WSL Java: $(java -version 2>&1 | head -1)"
echo "- Windows Maven仓库: ✓ 可访问"
echo "- 私有依赖包: ✓ 可用"

echo ""
echo "=========================================="

if [ $TOTAL_FAILED -eq 0 ]; then
    echo "🎉 所有测试通过！海外仓头程单拆分逻辑验证成功"
    echo ""
    echo "验证结果："
    echo "- 海外仓单品任务包装逻辑与中转仓逻辑一致 ✓"
    echo "- 海外仓多品任务包装逻辑与中转仓逻辑一致 ✓"
    echo "- 海外仓播种APV类型验证正确 ✓"
    echo "- 页面复用不会产生业务逻辑冲突 ✓"
    echo ""
    echo "🚀 WSL环境测试成功运行！"
    exit 0
else
    echo "❌ 有 $TOTAL_FAILED 个测试模块失败"
    echo ""
    if [ $COMPILATION_SUCCESS -eq 0 ]; then
        echo "主要问题：项目编译失败"
        echo "建议检查："
        echo "1. 私有仓库服务状态: http://192.168.3.191:8082"
        echo "2. 网络连接是否正常"
        echo "3. Maven settings.xml配置"
    else
        echo "请检查以下问题："
        echo "1. APV类型过滤逻辑是否正确实现"
        echo "2. 任务类型验证是否按照设计执行"
        echo "3. 页面复用参数传递是否正确"
        echo "4. 播种逻辑是否与中转仓保持一致"
    fi
    echo ""
    echo "查看详细错误信息："
    echo "- 查看控制台输出"
    echo "- 检查 target/surefire-reports/ 目录下的测试报告"
    echo "- 查看 target/site/surefire-report.html（如果生成成功）"
    exit 1
fi 